// Extracted and formatted key parts from the minified js.js for analysis

// Module 9582 - loadScriptTags function (THE VULNERABILITY)
t.loadScriptTags = e => {
    if (!e || !e.length) return;
    const t = document.querySelector("head");
    if (t)
        for (let s of e) {
            let e = document.createElement("div");
            e.innerHTML = s;  // ⚠️ DANGEROUS: Unsanitized HTML injection
            const i = e.querySelector("script");
            if (!i || !i.attributes) continue;
            let n = document.createElement("script");
            for (let e = i.attributes.length; e--;)
                n.setAttribute(i.attributes[e].name, i.attributes[e].value);
            n.innerHTML = i.innerHTML;  // ⚠️ DANGEROUS: Script content injection
            t.appendChild(n)  // ⚠️ DANGEROUS: Executes the script
        }
}

// Module 4645 - ChildFrame class
class ChildFrame {
    constructor(e) {
        this.eventEmitter = new EventEmitter();
        const t = new URLSearchParams(window.location.search);
        
        // Get origin and placement from URL parameters
        if (this.endpoint = t.get("_origin"), !this.endpoint)
            throw new Error("CANT_VALIDATE_ORIGIN");
        if (this.parentPlacement = t.get("_placement"), !this.parentPlacement)
            throw new Error("CANT_VALIDATE_PLACEMENT");
            
        this.callback = e;
        this.eventEmitter.on("ready", this.onParentReady.bind(this));
        this.listeners = {};
        this.run = {};
        
        // Listen for postMessage events
        window.addEventListener("message", this.receiveEvent.bind(this))
    }
    
    receiveEvent(e) {
        // Origin validation
        if (e.origin === this.endpoint)
            try {
                const {command: t, payload: s, parentPlacement: n} = this.parseMessage(e);
                // Placement validation
                if (n !== this.parentPlacement) return;
                // If command is "ready", call onParentReady
                t === "ready" ? this.onParentReady(e.data) : this.eventEmitter.emit(t, s)
            } catch (e) {
                console.error(e)
            }
    }
    
    parseMessage(e) {
        return {
            command: e.data.command,
            payload: e.data.payload,
            parentPlacement: e.data.placement
        }
    }
    
    onParentReady(e) {
        const {availableListeners: t, availableMethods: s, scripts: i} = e;
        
        // Process listeners
        t && t.forEach((e => {
            this.listeners[e] = t => {
                this.eventEmitter.on(e, t)
            }
        }));
        
        // Process methods
        s && s.forEach((e => {
            this.run[e] = t => {
                this.sendCommand(e, t)
            }
        }));
        
        // ⚠️ CRITICAL VULNERABILITY: Load scripts without validation
        i && loadScriptTags(i);  // This calls the vulnerable function above!
        
        this.callback(e)
    }
    
    sendCommand(e, t) {
        const s = {
            command: e,
            payload: t,
            placement: this.parentPlacement
        };
        window.parent.postMessage(s, this.endpoint)
    }
}

// Module 3909 - ParentFrame class
class ParentFrame {
    constructor({childFrameNode: e, listeners: s, methods: a = {}, scripts: r}) {
        // ... initialization code ...
        this.scripts = r;
        // ... more code ...
        window.addEventListener("message", this.receiveEvent.bind(this));
        // ... send ready command ...
        this.send("ready", void 0)
    }
    
    receiveEvent(e) {
        if (this.creativeUrl.origin === e.origin)
            try {
                const {command: t, payload: s, placement: i} = this.parseMessage(e);
                if (this.placement !== i) return;
                this.eventEmitter.emit(t, s)
            } catch (e) {
                console.error(e)
            }
    }
    
    buildEventPayload(e, s) {
        const i = {};
        // When sending "ready" command, include scripts
        return e === "ready" && (
            i.availableListeners = this.listeners,
            i.availableMethods = this.methods,
            i.scripts = this.scripts  // ⚠️ Scripts are sent in ready message
        ), Object.assign(Object.assign({}, i), {
            command: e,
            payload: s,
            placement: this.placement
        })
    }
}
