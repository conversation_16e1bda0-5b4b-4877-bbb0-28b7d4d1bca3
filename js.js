HTTP/2 200 OK
Content-Type: application/javascript
Accept-Ranges: bytes
Last-Modified: Thu, 11 Sep 2025 15:41:45 GMT
X-Rgw-Object-Type: Normal
Etag: "378a1243db191894014d06d301b7e352"
Content-Length: 507769
Cache-Control: max-age=86400
Expires: Thu, 18 Sep 2025 19:46:57 GMT
Date: Wed, 17 Sep 2025 19:46:57 GMT
Vary: Accept-Encoding
Strict-Transport-Security: max-age=15768000 ; includeSubDomains ; preload
Access-Control-Expose-Headers: *
Access-Control-Allow-Methods: GET, HEAD, OPTIONS
Access-Control-Allow-Headers: cache-control, *
Access-Control-Allow-Origin: *
Akamai-Grn: 0.8e7d1302.1758138417.1b3ebe63

function t(t,e){e.forEach((function(e){e&&typeof e!=="string"&&!Array.isArray(e)&&Object.keys(e).forEach((function(i){if(i!=="default"&&!(i in t)){var r=Object.getOwnPropertyDescriptor(e,i);Object.defineProperty(t,i,r.get?r:{enumerable:true,get:function(){return e[i]}})}}))}));return Object.freeze(t)}function e(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t["default"]:t}function i(t){if(t.__esModule)return t;var e=t.default;if(typeof e=="function"){var i=function t(){if(this instanceof t){return Reflect.construct(e,arguments,this.constructor)}return e.apply(this,arguments)};i.prototype=e.prototype}else i={};Object.defineProperty(i,"__esModule",{value:true});Object.keys(t).forEach((function(e){var r=Object.getOwnPropertyDescriptor(t,e);Object.defineProperty(i,e,r.get?r:{enumerable:true,get:function(){return t[e]}})}));return i}var r={};var n={};var s={};var o={};var a={};var u={};var f={};var l;function c(){if(l)return f;l=1;Object.defineProperty(f,"__esModule",{value:true});f.now=t;function t(){return Math.floor(performance.now())}return f}var h;function d(){if(h)return u;h=1;Object.defineProperty(u,"__esModule",{value:true});u.originTime=u.loadedAsDeferredBrowserScript=u.isiOS=u.isWorkerScope=u.isBrowserScope=u.initiallyHidden=u.initialLocation=u.iOSBelow16=u.globalScope=u.ffVersion=void 0;var t=c();
/**
	 * @file Contains constants about the environment the agent is running
	 * within. These values are derived at the time the agent is first loaded.
	 * @copyright 2023 New Relic Corporation. All rights reserved.
	 * @license Apache-2.0
	 */const e=u.isBrowserScope=typeof window!=="undefined"&&!!window.document;u.isWorkerScope=typeof WorkerGlobalScope!=="undefined"&&(typeof self!=="undefined"&&self instanceof WorkerGlobalScope&&self.navigator instanceof WorkerNavigator||typeof globalThis!=="undefined"&&globalThis instanceof WorkerGlobalScope&&globalThis.navigator instanceof WorkerNavigator);const i=u.globalScope=e?window:typeof WorkerGlobalScope!=="undefined"&&(typeof self!=="undefined"&&self instanceof WorkerGlobalScope&&self||typeof globalThis!=="undefined"&&globalThis instanceof WorkerGlobalScope&&globalThis);u.loadedAsDeferredBrowserScript=i?.document?.readyState==="complete";u.initiallyHidden=Boolean(i?.document?.visibilityState==="hidden");u.initialLocation=""+i?.location;const r=u.isiOS=/iPad|iPhone|iPod/.test(i.navigator?.userAgent);u.iOSBelow16=r&&typeof SharedWorker==="undefined";u.ffVersion=(()=>{const t=i.navigator?.userAgent?.match(/Firefox[/\s](\d+\.\d+)/);if(Array.isArray(t)&&t.length>=2){return+t[1]}return 0})();u.originTime=Date.now()-(0,t.now)();return u}var v;function p(){if(v)return a;v=1;Object.defineProperty(a,"__esModule",{value:true});a.NREUMinitialized=p;a.addToNREUM=h;a.defaults=void 0;a.getNREUMInitializedAgent=l;a.gosCDN=m;a.gosNREUM=r;a.gosNREUMInfo=n;a.gosNREUMInit=o;a.gosNREUMLoaderConfig=s;a.gosNREUMOriginals=u;a.setNREUMInitializedAgent=f;var t=d();var e=c();const i=a.defaults={beacon:"bam.nr-data.net",errorBeacon:"bam.nr-data.net"};function r(){if(!t.globalScope.NREUM){t.globalScope.NREUM={}}if(typeof t.globalScope.newrelic==="undefined")t.globalScope.newrelic=t.globalScope.NREUM;return t.globalScope.NREUM}function n(){let t=r();const e=t.info||{};t.info={beacon:i.beacon,errorBeacon:i.errorBeacon,...e};return t}function s(){let t=r();const e=t.loader_config||{};t.loader_config={...e};return t}function o(){let t=r();const e=t.init||{};t.init={...e};return t}function u(){let e=r();if(!e.o){e.o={ST:t.globalScope.setTimeout,SI:t.globalScope.setImmediate,CT:t.globalScope.clearTimeout,XHR:t.globalScope.XMLHttpRequest,REQ:t.globalScope.Request,EV:t.globalScope.Event,PR:t.globalScope.Promise,MO:t.globalScope.MutationObserver,FETCH:t.globalScope.fetch,WS:t.globalScope.WebSocket}}return e}function f(t,i){let n=r();n.initializedAgents??={};i.initializedAt={ms:(0,e.now)(),date:new Date};n.initializedAgents[t]=i}function l(t){let e=r();return e.initializedAgents?.[t]}function h(t,e){let i=r();i[t]=e}function p(){const t=r();t.initialized=true}function m(){n();o();u();s();return r()}return a}var m={};var g;function y(){if(g)return m;g=1;Object.defineProperty(m,"__esModule",{value:true});m.getOrSet=e;var t=Object.prototype.hasOwnProperty;function e(e,i,r){if(t.call(e,i))return e[i];var n=r();if(Object.defineProperty&&Object.keys){try{Object.defineProperty(e,i,{value:n,writable:true,enumerable:false});return n}catch(t){}}e[i]=n;return n}return m}var b={};var w;function S(){if(w)return b;w=1;Object.defineProperty(b,"__esModule",{value:true});b.EventContext=void 0;class t{constructor(t){this.contextId=t}}b.EventContext=t;return b}var k={};var C={};var M;function I(){if(M)return C;M=1;Object.defineProperty(C,"__esModule",{value:true});C.generateRandomHexString=n;C.generateSpanId=s;C.generateTraceId=o;C.generateUuid=r;var t=d();const e="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx";function i(t,e){if(t){return t[e]&15}else{return Math.random()*16|0}}function r(){const r=t.globalScope?.crypto||t.globalScope?.msCrypto;let n;let s=0;if(r&&r.getRandomValues){n=r.getRandomValues(new Uint8Array(30))}return e.split("").map((t=>{if(t==="x"){return i(n,s++).toString(16)}else if(t==="y"){return(i()&3|8).toString(16)}else{return t}})).join("")}function n(e){const r=t.globalScope?.crypto||t.globalScope?.msCrypto;let n;let s=0;if(r&&r.getRandomValues){n=r.getRandomValues(new Uint8Array(e))}const o=[];for(var a=0;a<e;a++){o.push(i(n,s++).toString(16))}return o.join("")}function s(){return n(16)}function o(){return n(32)}return C}var O;function A(){if(O)return k;O=1;Object.defineProperty(k,"__esModule",{value:true});k.bundleId=void 0;var t=I();
/**
	 * @file Contains a unique identifier for the running agent bundle
	 * when loaded.
	 * @copyright 2023 New Relic Corporation. All rights reserved.
	 * @license Apache-2.0
	 */k.bundleId=(0,t.generateUuid)();return k}var T;function _(){if(T)return o;T=1;Object.defineProperty(o,"__esModule",{value:true});o.ee=o.contextId=void 0;var t=p();var e=y();var i=S();var r=A();const n=o.contextId="nr@context:".concat(r.bundleId);const s=o.ee=u(undefined,"globalEE");const a=(0,t.gosNREUM)();if(!a.ee){a.ee=s}function u(t,r){var o={};var f={};var l={};var c=false;try{c=r.length!==16?false:a.initializedAgents?.[r]?.runtime.isolatedBacklog}catch(t){}var h={on:m,addEventListener:m,removeEventListener:g,emit:p,get:b,listeners:y,context:v,buffer:w,abort:d,isBuffering:S,debugId:r,backlog:c?{}:t&&typeof t.backlog==="object"?t.backlog:{},isolatedBacklog:c};function d(){h._aborted=true;Object.keys(h.backlog).forEach((t=>{delete h.backlog[t]}))}Object.defineProperty(h,"aborted",{get:()=>{let e=h._aborted||false;if(e)return e;else if(t){e=t.aborted}return e}});return h;function v(t){if(t&&t instanceof i.EventContext){return t}else if(t){return(0,e.getOrSet)(t,n,(()=>new i.EventContext(n)))}else{return new i.EventContext(n)}}function p(e,i,r,n,o){if(o!==false)o=true;if(s.aborted&&!n){return}if(t&&o)t.emit(e,i,r);var a=v(r);var u=y(e);var l=u.length;for(var c=0;c<l;c++)u[c].apply(a,i);var d=k()[f[e]];if(d){d.push([h,e,i,a])}return a}function m(t,e){o[t]=y(t).concat(e)}function g(t,e){var i=o[t];if(!i)return;for(var r=0;r<i.length;r++){if(i[r]===e){i.splice(r,1)}}}function y(t){return o[t]||[]}function b(t){return l[t]=l[t]||u(h,t)}function w(t,e){const i=k();e=e||"feature";if(h.aborted)return;Object.entries(t||{}).forEach((([t,r])=>{f[r]=e;if(!(e in i)){i[e]=[]}}))}function S(t){var e=k()[f[t]];return!!e}function k(){return h.backlog}}return o}var E;function R(){if(E)return s;E=1;Object.defineProperty(s,"__esModule",{value:true});s.handle=i;s.handleEE=void 0;var t=_();var e=s.handleEE=t.ee.get("handle");function i(t,i,r,n,s){if(s){s.buffer([t],n);s.emit(t,i,r)}else{e.buffer([t],n);e.emit(t,i,r)}}return s}var x={};var j={};var N={};var D;function F(){if(D)return N;D=1;Object.defineProperty(N,"__esModule",{value:true});N.defaultRegister=N.registerHandler=i;var t=R();i.on=r;var e=i.handlers={};function i(i,n,s,o){r(o||t.handleEE,e,i,n,s)}function r(e,i,r,n,s){if(!s)s="feature";if(!e)e=t.handleEE;var o=i[s]=i[s]||{};var a=o[r]=o[r]||[];a.push([e,n])}return N}var L={};var P;function B(){if(P)return L;P=1;Object.defineProperty(L,"__esModule",{value:true});L.featurePriority=L.RUM=L.LOGS=L.JSERRORS=L.FEATURE_TO_ENDPOINT=L.FEATURE_NAMES=L.EVENTS=L.BLOBS=void 0;const t=L.EVENTS="events";const e=L.JSERRORS="jserrors";const i=L.BLOBS="browser/blobs";const r=L.RUM="rum";const n=L.LOGS="browser/logs";const s=L.FEATURE_NAMES={ajax:"ajax",genericEvents:"generic_events",jserrors:e,logging:"logging",metrics:"metrics",pageAction:"page_action",pageViewEvent:"page_view_event",pageViewTiming:"page_view_timing",sessionReplay:"session_replay",sessionTrace:"session_trace",softNav:"soft_navigations",spa:"spa"};L.featurePriority={[s.pageViewEvent]:1,[s.pageViewTiming]:2,[s.metrics]:3,[s.jserrors]:4,[s.spa]:5,[s.ajax]:6,[s.sessionTrace]:7,[s.softNav]:8,[s.sessionReplay]:9,[s.logging]:10,[s.genericEvents]:11};L.FEATURE_TO_ENDPOINT={[s.pageViewEvent]:r,[s.pageViewTiming]:t,[s.ajax]:t,[s.spa]:t,[s.softNav]:t,[s.metrics]:e,[s.jserrors]:e,[s.sessionTrace]:i,[s.sessionReplay]:i,[s.logging]:n,[s.genericEvents]:"ins"};return L}var U;function G(){if(U)return j;U=1;Object.defineProperty(j,"__esModule",{value:true});j.deregisterDrain=o;j.drain=u;j.registerDrain=s;var t=_();var e=F();var i=B();var r=S();const n={};function s(t,e){const r={staged:false,priority:i.featurePriority[e]||0};a(t);if(!n[t].get(e))n[t].set(e,r)}function o(t,e){if(!t||!n[t])return;if(n[t].get(e))n[t].delete(e);l(t,e,false);if(n[t].size)f(t)}function a(t){if(!t)throw new Error("agentIdentifier required");if(!n[t])n[t]=new Map}function u(t="",e="feature",i=false){a(t);if(!t||!n[t].get(e)||i)return l(t,e);n[t].get(e).staged=true;f(t)}function f(t){const e=Array.from(n[t]);if(e.every((([t,e])=>e.staged))){e.sort(((t,e)=>t[1].priority-e[1].priority));e.forEach((([e])=>{n[t].delete(e);l(t,e)}))}}function l(i,n,s=true){const o=i?t.ee.get(i):t.ee;const a=e.registerHandler.handlers;if(o.aborted||!o.backlog||!a)return;if(s){const t=o.backlog[n];const e=a[n];if(e){for(let i=0;t&&i<t.length;++i){c(t[i],e)}Object.entries(e).forEach((([t,e])=>{Object.values(e||{}).forEach((e=>{if(e[0]?.on&&e[0]?.context()instanceof r.EventContext)e[0].on(t,e[1])}))}))}}if(!o.isolatedBacklog)delete a[n];o.backlog[n]=null;o.emit("drain-"+n,[])}function c(t,e){var i=t[1];Object.values(e[i]||{}).forEach((e=>{var i=t[0];var r=e[0];if(r===i){var n=e[1];var s=t[3];var o=t[2];n.apply(s,o)}}))}return j}var W={};var z;function V(){if(z)return W;z=1;Object.defineProperty(W,"__esModule",{value:true});W.FeatureBase=void 0;var t=_();var e=G();class i{constructor(e,i){this.agentIdentifier=e;this.ee=t.ee.get(e);this.featureName=i;this.blocked=false}deregisterDrain(){(0,e.deregisterDrain)(this.agentIdentifier,this.featureName)}}W.FeatureBase=i;return W}var H={};var Y={};var Z;function X(){if(Z)return Y;Z=1;Object.defineProperty(Y,"__esModule",{value:true});Y.documentAddEventListener=i;Y.eventListenerOpts=t;Y.windowAddEventListener=e;function t(t,e){return{capture:t,passive:false,signal:e}}function e(e,i,r=false,n){window.addEventListener(e,i,t(r,n))}function i(e,i,r=false,n){document.addEventListener(e,i,t(r,n))}return Y}var $;function J(){if($)return H;$=1;Object.defineProperty(H,"__esModule",{value:true});H.checkState=e;H.onDOMContentLoaded=r;H.onWindowLoad=i;var t=X();function e(){return typeof document==="undefined"||document.readyState==="complete"}function i(i,r){if(e())return i();(0,t.windowAddEventListener)("load",i,r)}function r(i){if(e())return i();(0,t.documentAddEventListener)("DOMContentLoaded",i)}return H}var K={};var q;function Q(){if(q)return K;q=1;Object.defineProperty(K,"__esModule",{value:true});K.warn=t;function t(t,e){if(typeof console.debug!=="function")return;console.debug("New Relic Warning: https://github.com/newrelic/newrelic-browser-agent/blob/main/docs/warning-codes.md#".concat(t),e)}return K}var tt={};var et={};var it;function rt(){if(it)return et;it=1;Object.defineProperty(et,"__esModule",{value:true});et.canEnableSessionTracking=void 0;var t=d();const e=e=>t.isBrowserScope&&e?.privacy.cookies_enabled===true;et.canEnableSessionTracking=e;return et}var nt;function st(){if(nt)return tt;nt=1;Object.defineProperty(tt,"__esModule",{value:true});tt.buildNRMetaNode=s;tt.customMasker=o;tt.hasReplayPrerequisite=r;tt.isPreloadAllowed=n;var t=p();var e=rt();var i=d();function r(i){return!!(0,t.gosNREUMOriginals)().o.MO&&(0,e.canEnableSessionTracking)(i)&&i?.session_trace.enabled===true}function n(t){return t?.session_replay.preload===true&&r(t)}function s(t,e){const r=e.correctAbsoluteTimestamp(t);return{originalTimestamp:t,correctedTimestamp:r,timestampDiff:t-r,originTime:i.originTime,correctedOriginTime:e.correctedOriginTime,originTimeDiff:Math.floor(i.originTime-e.correctedOriginTime)}}function o(t,e){try{if(typeof e?.type==="string"){if(e.type.toLowerCase()==="password")return"*".repeat(t?.length||0);if(e?.dataset?.nrUnmask!==undefined||e?.classList?.contains("nr-unmask"))return t}}catch(t){}return typeof t==="string"?t.replace(/[\S]/g,"*"):"*".repeat(t?.length||0)}return tt}var ot={};var at;function ut(){if(at)return ot;at=1;Object.defineProperty(ot,"__esModule",{value:true});ot.debounce=t;ot.single=e;function t(t,e=500,i={}){const r=i?.leading||false;let n;return(...i)=>{if(r&&n===undefined){t.apply(this,i);n=setTimeout((()=>{n=clearTimeout(n)}),e)}if(!r){clearTimeout(n);n=setTimeout((()=>{t.apply(this,i)}),e)}}}function e(t){let e=false;return(...i)=>{if(!e){e=true;t.apply(this,i)}}}return ot}var ft={};var lt={};var ct={};var ht;function dt(){if(ht)return ct;ht=1;Object.defineProperty(ct,"__esModule",{value:true});ct.stringify=i;var t=_();const e=()=>{const t=new WeakSet;return(e,i)=>{if(typeof i==="object"&&i!==null){if(t.has(i)){return}t.add(i)}return i}};function i(i){try{return JSON.stringify(i,e())??""}catch(e){try{t.ee.emit("internal-error",[e])}catch(t){}return""}}return ct}var vt={};var pt;function mt(){if(pt)return vt;pt=1;Object.defineProperty(vt,"__esModule",{value:true});vt.Timer=void 0;class t{constructor(t,e){if(!t.onEnd)throw new Error("onEnd handler is required");if(!e)throw new Error("ms duration is required");this.onEnd=t.onEnd;this.initialMs=e;this.startTimestamp=Date.now();this.timer=this.create(this.onEnd,e)}create(t,e){if(this.timer)this.clear();return setTimeout((()=>t?t():this.onEnd()),e||this.initialMs)}clear(){clearTimeout(this.timer);this.timer=null}end(){this.clear();this.onEnd()}isValid(){return this.initialMs-(Date.now()-this.startTimestamp)>0}}vt.Timer=t;return vt}var gt={};var yt;function bt(){if(yt)return gt;yt=1;Object.defineProperty(gt,"__esModule",{value:true});gt.SESSION_EVENT_TYPES=gt.SESSION_EVENTS=gt.PREFIX=gt.MODE=gt.DEFAULT_KEY=gt.DEFAULT_INACTIVE_MS=gt.DEFAULT_EXPIRES_MS=void 0;gt.PREFIX="NRBA";gt.DEFAULT_KEY="SESSION";gt.DEFAULT_EXPIRES_MS=144e5;gt.DEFAULT_INACTIVE_MS=18e5;gt.SESSION_EVENTS={STARTED:"session-started",PAUSE:"session-pause",RESET:"session-reset",RESUME:"session-resume",UPDATE:"session-update"};gt.SESSION_EVENT_TYPES={SAME_TAB:"same-tab",CROSS_TAB:"cross-tab"};gt.MODE={OFF:0,FULL:1,ERROR:2};return gt}var wt={};var St={};var kt;function Ct(){if(kt)return St;kt=1;Object.defineProperty(St,"__esModule",{value:true});St.subscribeToVisibilityChange=e;var t=X();function e(e,i=false,r,n){(0,t.documentAddEventListener)("visibilitychange",s,r,n);function s(){if(i){if(document.visibilityState==="hidden")e();return}e(document.visibilityState)}}return St}var Mt;function It(){if(Mt)return wt;Mt=1;Object.defineProperty(wt,"__esModule",{value:true});wt.InteractionTimer=void 0;var t=mt();var e=Ct();var i=ut();var r=d();class n extends t.Timer{constructor(t,n){super(t,n);this.onPause=typeof t.onPause==="function"?t.onPause:()=>{};this.onRefresh=typeof t.onRefresh==="function"?t.onRefresh:()=>{};this.onResume=typeof t.onResume==="function"?t.onResume:()=>{};this.readStorage=t.readStorage;this.remainingMs=undefined;if(!t.refreshEvents)t.refreshEvents=["click","keydown","scroll"];try{this.abortController=new AbortController}catch(t){}if(r.isBrowserScope&&t.ee){if(t.ee){this.ee=t.ee;const e=(0,i.debounce)(this.refresh.bind(this),500,{leading:true});this.refreshHandler=i=>{if(t.refreshEvents.includes(i?.[0]?.type)){e()}};t.ee.on("fn-end",this.refreshHandler)}(0,e.subscribeToVisibilityChange)((t=>{if(t==="hidden")this.pause();else this.resume()}),false,false,this.abortController?.signal)}}abort(){this.clear();this.abortController?.abort();if(this.refreshHandler){this.ee.removeEventListener("fn-end",this.refreshHandler);this.refreshHandler=this.ee=null}}pause(){this.onPause();clearTimeout(this.timer);this.remainingMs=this.initialMs-(Date.now()-this.startTimestamp)}resume(){try{const e=this.readStorage();const i=typeof e==="string"?JSON.parse(e):e;if(t(i.expiresAt)||t(i.inactiveAt))this.end();else{this.refresh();this.onResume()}}catch(t){this.end()}function t(t){return Date.now()>t}}refresh(t,e){this.clear();this.timer=this.create(t,e);this.startTimestamp=Date.now();this.remainingMs=undefined;this.onRefresh()}}wt.InteractionTimer=n;return wt}var Ot={};var At={};var Tt;function _t(){if(Tt)return At;Tt=1;Object.defineProperty(At,"__esModule",{value:true});At.copy=a;At.createWrapperWithEmitter=s;At.flag=At.default=void 0;var t=_();var e=A();const i=At.flag="nr@original:".concat(e.bundleId);var r=Object.prototype.hasOwnProperty;var n=false;At.default=s;function s(e,r){e||(e=t.ee);s.inPlace=f;s.flag=i;return s;function s(t,r,n,s,f){if(u(t))return t;if(!r)r="";c[i]=t;a(t,c,e);return c;function c(){var i;var a;var u;var c;try{a=this;i=[...arguments];if(typeof n==="function"){u=n(i,a)}else{u=n||{}}}catch(t){o([t,"",[i,a,s],u],e)}l(r+"start",[i,a,s],u,f);try{c=t.apply(a,i);return c}catch(t){l(r+"err",[i,a,t],u,f);throw t}finally{l(r+"end",[i,a,c],u,f)}}}function f(t,e,i,r,n){if(!i)i="";const o=i.charAt(0)==="-";for(let a=0;a<e.length;a++){const f=e[a];const l=t[f];if(u(l))continue;t[f]=s(l,o?f+i:i,r,f,n)}}function l(t,i,s,a){if(n&&!r)return;var u=n;n=true;try{e.emit(t,i,s,r,a)}catch(r){o([r,t,i,s],e)}n=u}}function o(e,i){i||(i=t.ee);try{i.emit("internal-error",e)}catch(t){}}function a(t,e,i){if(Object.defineProperty&&Object.keys){try{var n=Object.keys(t);n.forEach((function(i){Object.defineProperty(e,i,{get:function(){return t[i]},set:function(e){t[i]=e;return e}})}));return e}catch(t){o([t],i)}}for(var s in t){if(r.call(t,s)){e[s]=t[s]}}return e}function u(t){return!(t&&typeof t==="function"&&t.apply&&!t[i])}return At}var Et;function Rt(){if(Et)return Ot;Et=1;Object.defineProperty(Ot,"__esModule",{value:true});Ot.scopedEE=c;Ot.wrapEvents=f;var t=_();var e=_t();var i=y();var r=d();const n={};const s=r.globalScope.XMLHttpRequest;const o="addEventListener";const a="removeEventListener";const u="nr@wrapped:".concat(t.contextId);function f(t){var f=c(t);if(n[f.debugId]++)return f;n[f.debugId]=1;var h=(0,e.createWrapperWithEmitter)(f,true);if("getPrototypeOf"in Object){if(r.isBrowserScope)l(document,d);if(s)l(s.prototype,d);l(r.globalScope,d)}f.on(o+"-start",(function(t,e){var r=t[1];if(r===null||typeof r!=="function"&&typeof r!=="object"){return}var n=(0,i.getOrSet)(r,u,(function(){var t={object:e,function:r}[typeof r];return t?h(t,"fn-",null,t.name||"anonymous"):r;function e(){if(typeof r.handleEvent!=="function")return;return r.handleEvent.apply(r,arguments)}}));this.wrapped=t[1]=n}));f.on(a+"-start",(function(t){t[1]=this.wrapped||t[1]}));function d(t){h.inPlace(t,[o,a],"-",v)}function v(t,e){return t[1]}return f}function l(t,e,...i){let r=t;while(typeof r==="object"&&!Object.prototype.hasOwnProperty.call(r,o)){r=Object.getPrototypeOf(r)}if(r)e(r,...i)}function c(e){return(e||t.ee).get("events")}return Ot}var xt={};var jt;function Nt(){if(jt)return xt;jt=1;Object.defineProperty(xt,"__esModule",{value:true});xt.getModeledObject=e;var t=Q();function e(i,r){try{if(!i||typeof i!=="object")return(0,t.warn)(3);if(!r||typeof r!=="object")return(0,t.warn)(4);const n=Object.create(Object.getPrototypeOf(r),Object.getOwnPropertyDescriptors(r));const s=Object.keys(n).length===0?i:n;for(let o in s){if(i[o]===undefined)continue;try{if(i[o]===null){n[o]=null;continue}if(Array.isArray(i[o])&&Array.isArray(r[o]))n[o]=Array.from(new Set([...i[o],...r[o]]));else if(typeof i[o]==="object"&&typeof r[o]==="object")n[o]=e(i[o],r[o]);else n[o]=i[o]}catch(e){(0,t.warn)(1,e)}}return n}catch(e){(0,t.warn)(2,e)}}return xt}var Dt={};var Ft={};var Lt;function Pt(){if(Lt)return Ft;Lt=1;Object.defineProperty(Ft,"__esModule",{value:true});Ft.WEBSOCKET_TAG=Ft.ADD_EVENT_LISTENER_TAG=void 0;Ft.wrapWebSocket=u;var t=d();var e=c();var i=J();var r=I();var n=p();const s=Ft.WEBSOCKET_TAG="websocket-";const o=Ft.ADD_EVENT_LISTENER_TAG="addEventListener";const a={};function u(u){if(a[u.debugId]++)return u;const f=(0,n.gosNREUMOriginals)().o;if(!f.WS)return u;function l(t){const r=(0,e.now)();return function(n,...o){const a=o[0]?.timeStamp||(0,e.now)();const f=(0,i.checkState)();u.emit(s+n,[a,a-r,f,t,...o])}}class c extends WebSocket{static name="WebSocket";constructor(...t){super(...t);const e=(0,r.generateRandomHexString)(6);this.report=l(e);this.report("new");const i=["message","error","open","close"];i.forEach((t=>{this.addEventListener(t,(function(e){this.report(o,{eventType:t,event:e})}))}))}send(...t){this.report("send",...t);try{return super.send(...t)}catch(e){this.report("send-err",...t);throw e}}}t.globalScope.WebSocket=c;return u}return Ft}var Bt;function Ut(){if(Bt)return Dt;Bt=1;Object.defineProperty(Dt,"__esModule",{value:true});Dt.WATCHABLE_WEB_SOCKET_EVENTS=Dt.SUPPORTABILITY_METRIC_CHANNEL=Dt.SUPPORTABILITY_METRIC=Dt.FEATURE_NAME=Dt.CUSTOM_METRIC_CHANNEL=Dt.CUSTOM_METRIC=void 0;var t=Pt();var e=B();Dt.FEATURE_NAME=e.FEATURE_NAMES.metrics;Dt.SUPPORTABILITY_METRIC="sm";Dt.CUSTOM_METRIC="cm";Dt.SUPPORTABILITY_METRIC_CHANNEL="storeSupportabilityMetrics";Dt.CUSTOM_METRIC_CHANNEL="storeEventMetrics";Dt.WATCHABLE_WEB_SOCKET_EVENTS=["new","send","close",t.ADD_EVENT_LISTENER_TAG];return Dt}var Gt={};var Wt;function zt(){if(Wt)return Gt;Wt=1;Object.defineProperty(Gt,"__esModule",{value:true});Gt.LOG_LEVELS=Gt.LOGGING_MODE=Gt.LOGGING_EVENT_EMITTER_CHANNEL=Gt.FEATURE_NAME=void 0;var t=B();Gt.LOG_LEVELS={ERROR:"ERROR",WARN:"WARN",INFO:"INFO",DEBUG:"DEBUG",TRACE:"TRACE"};Gt.LOGGING_MODE={OFF:0,ERROR:1,WARN:2,INFO:3,DEBUG:4,TRACE:5};Gt.LOGGING_EVENT_EMITTER_CHANNEL="log";Gt.FEATURE_NAME=t.FEATURE_NAMES.logging;return Gt}var Vt;function Ht(){if(Vt)return lt;Vt=1;Object.defineProperty(lt,"__esModule",{value:true});lt.SessionEntity=void 0;var t=I();var e=Q();var i=dt();var r=_();var n=mt();var s=d();var o=bt();var a=It();var u=Rt();var f=Nt();var l=R();var c=Ut();var h=B();var v=X();var p=zt();const m={value:"",inactiveAt:0,expiresAt:0,updatedAt:Date.now(),sessionReplayMode:o.MODE.OFF,sessionReplaySentFirstChunk:false,sessionTraceMode:o.MODE.OFF,traceHarvestStarted:false,loggingMode:p.LOGGING_MODE.OFF,serverTimeDiff:null,custom:{},numOfResets:0};class g{constructor(t){const{agentIdentifier:e,key:i,storage:n}=t;if(!e||!i||!n){throw new Error("Missing required field(s):".concat(!e?" agentID":"").concat(!i?" key":"").concat(!n?" storage":""))}this.agentIdentifier=e;this.storage=n;this.state={};this.key=i;this.ee=r.ee.get(e);(0,u.wrapEvents)(this.ee);this.setup(t);if(s.isBrowserScope){(0,v.windowAddEventListener)("storage",(t=>{if(t.key===this.lookupKey){const e=typeof t.newValue==="string"?JSON.parse(t.newValue):t.newValue;this.sync(e);this.ee.emit(o.SESSION_EVENTS.UPDATE,[o.SESSION_EVENT_TYPES.CROSS_TAB,this.state])}}))}}setup({value:e=(0,t.generateRandomHexString)(16),expiresMs:i=o.DEFAULT_EXPIRES_MS,inactiveMs:r=o.DEFAULT_INACTIVE_MS,numOfResets:s=0}){const u={serverTimeDiff:this.state.serverTimeDiff||m.serverTimeDiff};this.state={};this.sync({...m,...u});this.state.value=e;this.expiresMs=i;this.inactiveMs=r;const l=this.read();if(i){this.state.expiresAt=l?.expiresAt||this.getFutureTimestamp(i);this.state.numOfResets=l?.numOfResets||s;this.expiresTimer=new n.Timer({onEnd:()=>{this.collectSM("expired");this.collectSM("duration");this.reset()}},this.state.expiresAt-Date.now())}else{this.state.expiresAt=Infinity}if(r){this.state.inactiveAt=l?.inactiveAt||this.getFutureTimestamp(r);this.inactiveTimer=new a.InteractionTimer({onEnd:()=>{this.collectSM("inactive");this.collectSM("duration");this.reset()},onRefresh:this.refresh.bind(this),onResume:()=>{this.ee.emit(o.SESSION_EVENTS.RESUME)},onPause:()=>{if(this.initialized)this.ee.emit(o.SESSION_EVENTS.PAUSE);this.write((0,f.getModeledObject)(this.state,m))},ee:this.ee,refreshEvents:["click","keydown","scroll"],readStorage:()=>this.storage.get(this.lookupKey)},this.state.inactiveAt-Date.now())}else{this.state.inactiveAt=Infinity}this.isNew||=!Object.keys(l).length;if(this.isNew)this.write((0,f.getModeledObject)(this.state,m),true);else this.sync(l);this.initialized=true;this.ee.emit(o.SESSION_EVENTS.STARTED,[this.isNew])}get lookupKey(){return"".concat(o.PREFIX,"_").concat(this.key)}sync(t){Object.assign(this.state,t)}read(){try{const t=this.storage.get(this.lookupKey);if(!t)return{};const e=typeof t==="string"?JSON.parse(t):t;if(this.isInvalid(e))return{};if(this.isExpired(e.expiresAt)){this.collectSM("expired");this.collectSM("duration",e,true);return this.reset()}if(this.isExpired(e.inactiveAt)){this.collectSM("inactive");this.collectSM("duration",e,true);return this.reset()}return e}catch(t){(0,e.warn)(10,t);return{}}}write(t){try{if(!t||typeof t!=="object")return;t.updatedAt=Date.now();this.sync(t);this.storage.set(this.lookupKey,(0,i.stringify)(this.state));this.ee.emit(o.SESSION_EVENTS.UPDATE,[o.SESSION_EVENT_TYPES.SAME_TAB,this.state]);return t}catch(t){(0,e.warn)(11,t);return null}}reset(){try{if(this.initialized)this.ee.emit(o.SESSION_EVENTS.RESET);this.storage.remove(this.lookupKey);this.inactiveTimer?.abort?.();this.expiresTimer?.clear?.();delete this.isNew;this.setup({agentIdentifier:this.agentIdentifier,key:this.key,storage:this.storage,expiresMs:this.expiresMs,inactiveMs:this.inactiveMs,numOfResets:++this.state.numOfResets});return this.read()}catch(t){return{}}}refresh(){const t=this.read();this.write({...t,inactiveAt:this.getFutureTimestamp(this.inactiveMs)})}isAfterSessionExpiry(t){return this.state.numOfResets>0||typeof t==="number"&&typeof this.state.expiresAt==="number"&&t>=this.state.expiresAt}isExpired(t){return Date.now()>t}isInvalid(t){const e=Object.keys(m);return!e.every((e=>Object.keys(t).includes(e)))}collectSM(t,e,i){let r,n;if(t==="duration"){r=this.getDuration(e,i);n="Session/Duration/Ms"}if(t==="expired")n="Session/Expired/Seen";if(t==="inactive")n="Session/Inactive/Seen";if(n)(0,l.handle)(c.SUPPORTABILITY_METRIC_CHANNEL,[n,r],undefined,h.FEATURE_NAMES.metrics,this.ee)}getDuration(t=this.state,e){const i=t.expiresAt-this.expiresMs;const r=!e?t.updatedAt:Date.now();return r-i}getFutureTimestamp(t){return Date.now()+t}syncCustomAttribute(t,e){if(!s.isBrowserScope)return;if(e===null){const e=this.read();if(e.custom){delete e.custom[t];this.write({...e})}}else{const i=this.read();this.custom={...i?.custom||{},[t]:e};this.write({...i,custom:this.custom})}}}lt.SessionEntity=g;return lt}var Yt={};var Zt;function Xt(){if(Zt)return Yt;Zt=1;Object.defineProperty(Yt,"__esModule",{value:true});Yt.LocalStorage=void 0;class t{get(t){try{return localStorage.getItem(t)||undefined}catch(t){return""}}set(t,e){try{if(e===undefined||e===null)return this.remove(t);return localStorage.setItem(t,e)}catch(t){}}remove(t){try{localStorage.removeItem(t)}catch(t){}}}Yt.LocalStorage=t;return Yt}var $t;function Jt(){if($t)return ft;$t=1;Object.defineProperty(ft,"__esModule",{value:true});ft.setupAgentSession=o;var t=G();var e=_();var i=F();var r=Ht();var n=Xt();var s=bt();function o(o){if(o.runtime.session)return o.runtime.session;const a=o.init.session;o.runtime.session=new r.SessionEntity({agentIdentifier:o.agentIdentifier,key:s.DEFAULT_KEY,storage:new n.LocalStorage,expiresMs:a?.expiresMs,inactiveMs:a?.inactiveMs});const u=o.runtime.session.state.custom;if(u){o.info.jsAttributes={...o.info.jsAttributes,...u}}const f=e.ee.get(o.agentIdentifier);(0,i.registerHandler)("api-setCustomAttribute",((t,e,i)=>{o.runtime.session.syncCustomAttribute(e,i)}),"session",f);(0,i.registerHandler)("api-setUserId",((t,e,i)=>{o.runtime.session.syncCustomAttribute(e,i)}),"session",f);(0,t.drain)(o.agentIdentifier,"session");return o.runtime.session}return ft}var Kt={};var qt={};var Qt={};var te;function ee(){if(te)return Qt;te=1;Object.defineProperty(Qt,"__esModule",{value:true});Qt.hasUndefinedHostname=i;Qt.setDenyList=r;Qt.shouldCollectEvent=e;var t=[];function e(e){if(!e||i(e))return false;if(t.length===0)return true;for(var r=0;r<t.length;r++){var o=t[r];if(o.hostname==="*"){return false}if(n(o.hostname,e.hostname)&&s(o.pathname,e.pathname)){return false}}return true}function i(t){return t.hostname===undefined}function r(e){t=[];if(!e||!e.length){return}for(var i=0;i<e.length;i++){let r=e[i];if(!r)continue;if(r.indexOf("http://")===0){r=r.substring(7)}else if(r.indexOf("https://")===0){r=r.substring(8)}const n=r.indexOf("/");let s,o;if(n>0){s=r.substring(0,n);o=r.substring(n)}else{s=r;o=""}let[a]=s.split(":");t.push({hostname:a,pathname:o})}}function n(t,e){if(t.length>e.length){return false}return e.indexOf(t)===e.length-t.length}function s(t,e){if(t.indexOf("/")===0){t=t.substring(1)}if(e.indexOf("/")===0){e=e.substring(1)}if(t===""){return true}return t===e}return Qt}var ie={};var re;function ne(){if(re)return ie;re=1;Object.defineProperty(ie,"__esModule",{value:true});ie.FEATURE_NAME=void 0;var t=B();ie.FEATURE_NAME=t.FEATURE_NAMES.ajax;return ie}var se={};var oe={};var ae;function ue(){if(ae)return oe;ae=1;Object.defineProperty(oe,"__esModule",{value:true});oe.isValid=r;oe.mergeInfo=void 0;var t=p();var e=Nt();const i={beacon:t.defaults.beacon,errorBeacon:t.defaults.errorBeacon,licenseKey:undefined,applicationID:undefined,sa:undefined,queueTime:undefined,applicationTime:undefined,ttGuid:undefined,user:undefined,account:undefined,product:undefined,extra:undefined,jsAttributes:{},userAttributes:undefined,atts:undefined,transactionName:undefined,tNamePlain:undefined};function r(t){try{return!!t.licenseKey&&!!t.errorBeacon&&!!t.applicationID}catch(t){return false}}const n=t=>(0,e.getModeledObject)(t,i);oe.mergeInfo=n;return oe}var fe={};var le={};var ce={};var he={};var de;function ve(){if(de)return he;de=1;Object.defineProperty(he,"__esModule",{value:true});he.TRIGGERS=he.SR_EVENT_EMITTER_TYPES=he.RRWEB_EVENT_TYPES=he.QUERY_PARAM_PADDING=he.FEATURE_NAME=he.CHECKOUT_MS=he.AVG_COMPRESSION=he.ABORT_REASONS=void 0;var t=bt();var e=B();he.FEATURE_NAME=e.FEATURE_NAMES.sessionReplay;he.SR_EVENT_EMITTER_TYPES={RECORD:"recordReplay",PAUSE:"pauseReplay",REPLAY_RUNNING:"replayRunning",ERROR_DURING_REPLAY:"errorDuringReplay"};he.AVG_COMPRESSION=.12;he.RRWEB_EVENT_TYPES={DomContentLoaded:0,Load:1,FullSnapshot:2,IncrementalSnapshot:3,Meta:4,Custom:5};he.CHECKOUT_MS={[t.MODE.ERROR]:15e3,[t.MODE.FULL]:3e5,[t.MODE.OFF]:0};he.ABORT_REASONS={RESET:{message:"Session was reset",sm:"Reset"},IMPORT:{message:"Recorder failed to import",sm:"Import"},TOO_MANY:{message:"429: Too Many Requests",sm:"Too-Many"},TOO_BIG:{message:"Payload was too large",sm:"Too-Big"},CROSS_TAB:{message:"Session Entity was set to OFF on another tab",sm:"Cross-Tab"},ENTITLEMENTS:{message:"Session Replay is not allowed and will not be started",sm:"Entitlement"}};he.QUERY_PARAM_PADDING=5e3;he.TRIGGERS={API:"api"};return he}var pe;function me(){if(pe)return ce;pe=1;Object.defineProperty(ce,"__esModule",{value:true});ce.asyncApiMethods=ce.apiMethods=void 0;var t=ve();ce.apiMethods=["setErrorHandler","finished","addToTrace","addRelease","recordCustomEvent","addPageAction","setCurrentRouteName","setPageViewName","setCustomAttribute","interaction","noticeError","setUserId","setApplicationVersion","start",t.SR_EVENT_EMITTER_TYPES.RECORD,t.SR_EVENT_EMITTER_TYPES.PAUSE,"log","wrapLogger","register"];ce.asyncApiMethods=["setErrorHandler","finished","addToTrace","addRelease"];return ce}var ge={};var ye;function be(){if(ye)return ge;ye=1;Object.defineProperty(ge,"__esModule",{value:true});ge.bufferLog=s;ge.isValidLogLevel=o;var t=R();var e=c();var i=B();var r=Ut();var n=zt();function s(s,o,a={},u=n.LOG_LEVELS.INFO,f,l=(0,e.now)()){(0,t.handle)(r.SUPPORTABILITY_METRIC_CHANNEL,["API/logging/".concat(u.toLowerCase(),"/called")],undefined,i.FEATURE_NAMES.metrics,s);(0,t.handle)(n.LOGGING_EVENT_EMITTER_CHANNEL,[l,o,a,u,f],undefined,i.FEATURE_NAMES.logging,s)}function o(t){if(typeof t!=="string")return false;return Object.values(n.LOG_LEVELS).some((e=>e===t.toUpperCase().trim()))}return ge}var we={};var Se;function ke(){if(Se)return we;Se=1;Object.defineProperty(we,"__esModule",{value:true});we.scopedEE=o;we.wrapLogger=s;var t=_();var e=S();var i=Q();var r=_t();const n=new Map;function s(s,a,u,f){if(!(typeof a==="object"&&!!a&&typeof u==="string"&&!!u&&typeof a[u]==="function"))return(0,i.warn)(29);const l=o(s);const c=(0,r.createWrapperWithEmitter)(l);const h=new e.EventContext(t.contextId);h.level=f.level;h.customAttributes=f.customAttributes;const d=a[u]?.[r.flag]||a[u];n.set(d,h);c.inPlace(a,[u],"wrap-logger-",(()=>n.get(d)));return l}function o(e){return(e||t.ee).get("logger")}return we}var Ce={};var Me={};var Ie;function Oe(){if(Ie)return Me;Ie=1;Object.defineProperty(Me,"__esModule",{value:true});Me.isContainerAgentTarget=e;Me.isValidTarget=t;function t(t){if(!t)return true;return!!(t.licenseKey&&t.applicationID)}function e(t,e){if(!t)return true;return t.licenseKey===e.info.licenseKey&&t.applicationID===e.info.applicationID}return Me}var Ae;function Te(){if(Ae)return Ce;Ae=1;Object.defineProperty(Ce,"__esModule",{value:true});Ce.buildRegisterApi=o;var t=R();var e=Q();var i=Oe();var r=B();var n=c();var s=Ut();function o(o,a,u){const f={};(0,e.warn)(54,"newrelic.register");let l;let c;if(!o.init.api.allow_registered_children)l=()=>(0,e.warn)(55);if(!u||!(0,i.isValidTarget)(u))l=()=>(0,e.warn)(48,u);const h={addPageAction:(t,e={})=>{d(a.addPageAction,[t,{...f,...e}],u)},log:(t,e={})=>{d(a.log,[t,{...e,customAttributes:{...f,...e.customAttributes||{}}}],u)},noticeError:(t,e={})=>{d(a.noticeError,[t,{...f,...e}],u)},setApplicationVersion:t=>{f["application.version"]=t},setCustomAttribute:(t,e)=>{f[t]=e},setUserId:t=>{f["enduser.id"]=t},metadata:{customAttributes:f,target:u,get connected(){return c||Promise.reject(new Error("Failed to connect"))}}};if(l){l()}else{c=new Promise(((t,e)=>{try{const r=o.runtime?.entityManager;let n=!!r?.get().entityGuid;let s=r?.getEntityGuidFor(u.licenseKey,u.applicationID);let a=!!s;if(n&&a){u.entityGuid=s;t(h)}else{const l=setTimeout((()=>e(new Error("Failed to connect - Timeout"))),15e3);o.ee.emit("api-send-rum",[f,u]);o.ee.on("entity-added",c);function c(e){if((0,i.isContainerAgentTarget)(e,o))n||=true;else{if(u.licenseKey===e.licenseKey&&u.applicationID===e.applicationID){a=true;u.entityGuid=e.entityGuid}}if(n&&a){clearTimeout(l);o.ee.removeEventListener("entity-added",c);t(h)}}}}catch(d){e(d)}}))}const d=async(i,a,u)=>{if(l)return l();const f=(0,n.now)();(0,t.handle)(s.SUPPORTABILITY_METRIC_CHANNEL,["API/register/".concat(i.name,"/called")],undefined,r.FEATURE_NAMES.metrics,o.ee);try{await c;const t=o.init.api.duplicate_registered_data;if(t===true||Array.isArray(t)&&t.includes(u.entityGuid)){i(...a,undefined,f)}i(...a,u.entityGuid,f)}catch(t){(0,e.warn)(50,t)}};return h}return Ce}var _e={};var Ee;function Re(){if(Ee)return _e;Ee=1;Object.defineProperty(_e,"__esModule",{value:true});_e.dispatchGlobalEvent=i;var t=d();const e="newrelic";function i(i={}){try{t.globalScope.dispatchEvent(new CustomEvent(e,{detail:i}))}catch(t){}}return _e}var xe={};var je;function Ne(){if(je)return xe;je=1;Object.defineProperty(xe,"__esModule",{value:true});xe.activateFeatures=r;xe.activatedFeatures=void 0;var t=Re();const e=new Set;const i=xe.activatedFeatures={};function r(r,n){const s=n.agentIdentifier;i[s]??={};if(!r||typeof r!=="object")return;if(e.has(s))return;n.ee.emit("rumresp",[r]);i[s]=r;e.add(s);(0,t.dispatchGlobalEvent)({agentIdentifier:s,loaded:true,drained:true,type:"lifecycle",name:"load",feature:undefined,data:r})}return xe}var De={};var Fe;function Le(){if(Fe)return De;Fe=1;Object.defineProperty(De,"__esModule",{value:true});De.setAsyncAPI=o;var t=B();var e=R();var i=F();var r=ut();var n=Ut();var s=d();function o(o){const a={finished:(0,r.single)(u),setErrorHandler:l,addToTrace:f,addRelease:h};Object.entries(a).forEach((([t,e])=>(0,i.registerHandler)("api-"+t,e,"api",o.ee)));function u(i,r){const a=r?r-s.originTime:i;(0,e.handle)(n.CUSTOM_METRIC_CHANNEL,["finished",{time:a}],undefined,t.FEATURE_NAMES.metrics,o.ee);f(i,{name:"finished",start:a+s.originTime,origin:"nr"});(0,e.handle)("api-addPageAction",[a,"finished"],undefined,t.FEATURE_NAMES.genericEvents,o.ee)}function f(i,r){if(!(r&&typeof r==="object"&&r.name&&r.start))return;const n={n:r.name,s:r.start-s.originTime,e:(r.end||r.start)-s.originTime,o:r.origin||"",t:"api"};(0,e.handle)("bstApi",[n],undefined,t.FEATURE_NAMES.sessionTrace,o.ee)}function l(t,e){o.runtime.onerror=e}let c=0;function h(t,e,i){if(++c>10)return;o.runtime.releaseIds[e.slice(-200)]=(""+i).slice(-200)}}return De}var Pe;function Be(){if(Pe)return le;Pe=1;Object.defineProperty(le,"__esModule",{value:true});le.setAPI=I;le.setTopLevelCallers=C;var t=B();var e=R();var i=G();var r=J();var n=d();var s=Q();var o=Ut();var a=p();var u=me();var f=ve();var l=c();var h=bt();var v=zt();var m=be();var g=ke();var y=Te();var b=Re();var w=Ne();function S(t){if("function"!=typeof WeakMap)return null;var e=new WeakMap,i=new WeakMap;return(S=function(t){return t?i:e})(t)}function k(t,e){if(t&&t.__esModule)return t;if(null===t||"object"!=typeof t&&"function"!=typeof t)return{default:t};var i=S(e);if(i&&i.has(t))return i.get(t);var r={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in t)if("default"!==s&&{}.hasOwnProperty.call(t,s)){var o=n?Object.getOwnPropertyDescriptor(t,s):null;o&&(o.get||o.set)?Object.defineProperty(r,s,o):r[s]=t[s]}return r.default=t,i&&i.set(t,r),r}function C(){const t=(0,a.gosCDN)();u.apiMethods.forEach((i=>{t[i]=(...t)=>e(i,...t)}));function e(e,...i){let r=[];Object.values(t.initializedAgents).forEach((t=>{if(!t||!t.runtime){(0,s.warn)(38,e)}else if(t.exposed&&t[e]&&t.runtime.loaderType!=="micro-agent"){r.push(t[e](...i))}}));return r[0]}}const M={};function I(a,c){if(!c)(0,i.registerDrain)(a.agentIdentifier,"api");const d=a.ee.get("tracer");M[a.agentIdentifier]=h.MODE.OFF;a.ee.on(f.SR_EVENT_EMITTER_TYPES.REPLAY_RUNNING,(t=>{M[a.agentIdentifier]=t}));const p="api-";const S=p+"ixn-";const C={addPageAction:function(e,i,r,n=(0,l.now)()){T(p,"addPageAction",true,t.FEATURE_NAMES.genericEvents,n)(e,i,r)},log:function(i,{customAttributes:r={},level:n=v.LOG_LEVELS.INFO}={},s,u=(0,l.now)()){(0,e.handle)(o.SUPPORTABILITY_METRIC_CHANNEL,["API/log/called"],undefined,t.FEATURE_NAMES.metrics,a.ee);(0,m.bufferLog)(a.ee,i,r,n,s,u)},noticeError:function(i,r,n,s=(0,l.now)()){if(typeof i==="string")i=new Error(i);(0,e.handle)(o.SUPPORTABILITY_METRIC_CHANNEL,["API/noticeError/called"],undefined,t.FEATURE_NAMES.metrics,a.ee);(0,e.handle)("err",[i,s,false,r,!!M[a.agentIdentifier],undefined,n],undefined,t.FEATURE_NAMES.jserrors,a.ee)}};a.register=function(i){(0,e.handle)(o.SUPPORTABILITY_METRIC_CHANNEL,["API/register/called"],undefined,t.FEATURE_NAMES.metrics,a.ee);return(0,y.buildRegisterApi)(a,C,i)};a.log=function(t,e){C.log(t,e)};a.wrapLogger=(i,r,{customAttributes:n={},level:s=v.LOG_LEVELS.INFO}={})=>{(0,e.handle)(o.SUPPORTABILITY_METRIC_CHANNEL,["API/wrapLogger/called"],undefined,t.FEATURE_NAMES.metrics,a.ee);(0,g.wrapLogger)(a.ee,i,r,{customAttributes:n,level:s})};u.asyncApiMethods.forEach((t=>{a[t]=T(p,t,true,"api")}));a.addPageAction=function(t,e){C.addPageAction(t,e)};a.recordCustomEvent=T(p,"recordCustomEvent",true,t.FEATURE_NAMES.genericEvents);a.setPageViewName=function(t,e){if(typeof t!=="string")return;if(t.charAt(0)!=="/")t="/"+t;a.runtime.customTransaction=(e||"http://custom.transaction")+t;return T(p,"setPageViewName",true)()};function I(t,e,i,r){const n=a.info;if(e===null)delete n.jsAttributes[t];else n.jsAttributes[t]=e;return T(p,i,true,!!r||e===null?"session":undefined)(t,e)}a.setCustomAttribute=function(t,e,i=false){if(typeof t!=="string"){(0,s.warn)(39,typeof t);return}if(!(["string","number","boolean"].includes(typeof e)||e===null)){(0,s.warn)(40,typeof e);return}return I(t,e,"setCustomAttribute",i)};a.setUserId=function(t){if(!(typeof t==="string"||t===null)){(0,s.warn)(41,typeof t);return}return I("enduser.id",t,"setUserId",true)};a.setApplicationVersion=function(t){if(!(typeof t==="string"||t===null)){(0,s.warn)(42,typeof t);return}return I("application.version",t,"setApplicationVersion",false)};a.start=()=>{try{(0,e.handle)(o.SUPPORTABILITY_METRIC_CHANNEL,["API/start/called"],undefined,t.FEATURE_NAMES.metrics,a.ee);a.ee.emit("manual-start-all")}catch(t){(0,s.warn)(23,t)}};a[f.SR_EVENT_EMITTER_TYPES.RECORD]=function(){(0,e.handle)(o.SUPPORTABILITY_METRIC_CHANNEL,["API/recordReplay/called"],undefined,t.FEATURE_NAMES.metrics,a.ee);(0,e.handle)(f.SR_EVENT_EMITTER_TYPES.RECORD,[],undefined,t.FEATURE_NAMES.sessionReplay,a.ee)};a[f.SR_EVENT_EMITTER_TYPES.PAUSE]=function(){(0,e.handle)(o.SUPPORTABILITY_METRIC_CHANNEL,["API/pauseReplay/called"],undefined,t.FEATURE_NAMES.metrics,a.ee);(0,e.handle)(f.SR_EVENT_EMITTER_TYPES.PAUSE,[],undefined,t.FEATURE_NAMES.sessionReplay,a.ee)};a.interaction=function(t){return(new O).get(typeof t==="object"?t:{})};function O(){}const A=O.prototype={createTracer:function(i,r){var n={};var s=this;var u=typeof r==="function";(0,e.handle)(o.SUPPORTABILITY_METRIC_CHANNEL,["API/createTracer/called"],undefined,t.FEATURE_NAMES.metrics,a.ee);if(!a.runSoftNavOverSpa)(0,e.handle)(S+"tracer",[(0,l.now)(),i,n],s,t.FEATURE_NAMES.spa,a.ee);return function(){d.emit((u?"":"no-")+"fn-start",[(0,l.now)(),s,u],n);if(u){try{return r.apply(this,arguments)}catch(t){const e=typeof t==="string"?new Error(t):t;d.emit("fn-err",[arguments,this,e],n);throw e}finally{d.emit("fn-end",[(0,l.now)()],n)}}}}};["actionText","setName","setAttribute","save","ignore","onEnd","getContext","end","get"].forEach((e=>{A[e]=function(){return T.apply(this,[S,e,undefined,a.runSoftNavOverSpa?t.FEATURE_NAMES.softNav:t.FEATURE_NAMES.spa]).apply(this,arguments)}}));a.setCurrentRouteName=function(){return a.runSoftNavOverSpa?T(S,"routeName",undefined,t.FEATURE_NAMES.softNav)(...arguments):T(p,"routeName",true,t.FEATURE_NAMES.spa)(...arguments)};function T(i,r,n,s,u=(0,l.now)()){return function(){(0,e.handle)(o.SUPPORTABILITY_METRIC_CHANNEL,["API/"+r+"/called"],undefined,t.FEATURE_NAMES.metrics,a.ee);(0,b.dispatchGlobalEvent)({agentIdentifier:a.agentIdentifier,drained:!!w.activatedFeatures?.[a.agentIdentifier],type:"data",name:"api",feature:i+r,data:{notSpa:n,bufferGroup:s}});if(s)(0,e.handle)(i+r,[u,...arguments],n?null:this,s,a.ee);return n?undefined:this}}a.noticeError=function(t,e){C.noticeError(t,e)};if(!n.isBrowserScope)_();else(0,r.onWindowLoad)((()=>_()),true);function _(){Promise.resolve().then((()=>k(Le()))).then((({setAsyncAPI:t})=>{t(a);(0,i.drain)(a.agentIdentifier,"api")})).catch((t=>{(0,s.warn)(27,t);a.ee.abort()}))}return true}return le}var Ue={};var Ge={};var We;function ze(){if(We)return Ge;We=1;Object.defineProperty(Ge,"__esModule",{value:true});Ge.RESERVED_EVENT_TYPES=Ge.RAGE_CLICK_THRESHOLD_MS=Ge.RAGE_CLICK_THRESHOLD_EVENTS=Ge.OBSERVED_WINDOW_EVENTS=Ge.OBSERVED_EVENTS=Ge.MAX_PAYLOAD_SIZE=Ge.IDEAL_PAYLOAD_SIZE=Ge.FEATURE_NAME=Ge.FEATURE_FLAGS=void 0;var t=B();Ge.FEATURE_NAME=t.FEATURE_NAMES.genericEvents;Ge.IDEAL_PAYLOAD_SIZE=64e3;Ge.MAX_PAYLOAD_SIZE=1e6;Ge.OBSERVED_EVENTS=["auxclick","click","copy","keydown","paste","scrollend"];Ge.OBSERVED_WINDOW_EVENTS=["focus","blur"];Ge.RAGE_CLICK_THRESHOLD_EVENTS=4;Ge.RAGE_CLICK_THRESHOLD_MS=1e3;Ge.RESERVED_EVENT_TYPES=["PageAction","UserAction","BrowserPerformance"];Ge.FEATURE_FLAGS={MARKS:"experimental.marks",MEASURES:"experimental.measures",RESOURCES:"experimental.resources"};return Ge}var Ve={};var He;function Ye(){if(He)return Ve;He=1;Object.defineProperty(Ve,"__esModule",{value:true});Ve.isValidSelector=void 0;const t=t=>{if(!t||typeof t!=="string")return false;try{document.createDocumentFragment().querySelector(t)}catch{return false}return true};Ve.isValidSelector=t;return Ve}var Ze;function Xe(){if(Ze)return Ue;Ze=1;Object.defineProperty(Ue,"__esModule",{value:true});Ue.mergeInit=void 0;var t=ze();var e=Ye();var i=bt();var r=Q();var n=Nt();const s="[data-nr-mask]";const o=()=>{const n={feature_flags:[],experimental:{marks:false,measures:false,resources:false},mask_selector:"*",block_selector:"[data-nr-block]",mask_input_options:{color:false,date:false,"datetime-local":false,email:false,month:false,number:false,range:false,search:false,tel:false,text:false,time:false,url:false,week:false,textarea:false,select:false,password:true}};return{ajax:{deny_list:undefined,block_internal:true,enabled:true,autoStart:true},api:{allow_registered_children:true,duplicate_registered_data:false},distributed_tracing:{enabled:undefined,exclude_newrelic_header:undefined,cors_use_newrelic_header:undefined,cors_use_tracecontext_headers:undefined,allowed_origins:undefined},get feature_flags(){return n.feature_flags},set feature_flags(t){n.feature_flags=t},generic_events:{enabled:true,autoStart:true},harvest:{interval:30},jserrors:{enabled:true,autoStart:true},logging:{enabled:true,autoStart:true},metrics:{enabled:true,autoStart:true},obfuscate:undefined,page_action:{enabled:true},page_view_event:{enabled:true,autoStart:true},page_view_timing:{enabled:true,autoStart:true},performance:{get capture_marks(){return n.feature_flags.includes(t.FEATURE_FLAGS.MARKS)||n.experimental.marks},set capture_marks(t){n.experimental.marks=t},get capture_measures(){return n.feature_flags.includes(t.FEATURE_FLAGS.MEASURES)||n.experimental.measures},set capture_measures(t){n.experimental.measures=t},capture_detail:true,resources:{get enabled(){return n.feature_flags.includes(t.FEATURE_FLAGS.RESOURCES)||n.experimental.resources},set enabled(t){n.experimental.resources=t},asset_types:[],first_party_domains:[],ignore_newrelic:true}},privacy:{cookies_enabled:true},proxy:{assets:undefined,beacon:undefined},session:{expiresMs:i.DEFAULT_EXPIRES_MS,inactiveMs:i.DEFAULT_INACTIVE_MS},session_replay:{autoStart:true,enabled:false,preload:false,sampling_rate:10,error_sampling_rate:100,collect_fonts:false,inline_images:false,fix_stylesheets:true,mask_all_inputs:true,get mask_text_selector(){return n.mask_selector},set mask_text_selector(t){if((0,e.isValidSelector)(t))n.mask_selector="".concat(t,",").concat(s);else if(t===""||t===null)n.mask_selector=s;else(0,r.warn)(5,t)},get block_class(){return"nr-block"},get ignore_class(){return"nr-ignore"},get mask_text_class(){return"nr-mask"},get block_selector(){return n.block_selector},set block_selector(t){if((0,e.isValidSelector)(t))n.block_selector+=",".concat(t);else if(t!=="")(0,r.warn)(6,t)},get mask_input_options(){return n.mask_input_options},set mask_input_options(t){if(t&&typeof t==="object")n.mask_input_options={...t,password:true};else(0,r.warn)(7,t)}},session_trace:{enabled:true,autoStart:true},soft_navigations:{enabled:true,autoStart:true},spa:{enabled:true,autoStart:true},ssl:undefined,user_actions:{enabled:true,elementAttributes:["id","className","tagName","type"]}}};const a=t=>(0,n.getModeledObject)(t,o());Ue.mergeInit=a;return Ue}var $e={};var Je={};var Ke;function qe(){if(Ke)return Je;Ke=1;Object.defineProperty(Je,"__esModule",{value:true});Je.VERSION=Je.RRWEB_VERSION=Je.DIST_METHOD=Je.BUILD_ENV=void 0;Je.VERSION="1.289.0";Je.BUILD_ENV="NPM";Je.DIST_METHOD="NPM";Je.RRWEB_VERSION="^2.0.0-alpha.18";return Je}var Qe;function ti(){if(Qe)return $e;Qe=1;Object.defineProperty($e,"__esModule",{value:true});$e.mergeRuntime=void 0;var t=Nt();var e=d();var i=qe();let r=0;const n={buildEnv:i.BUILD_ENV,distMethod:i.DIST_METHOD,version:i.VERSION,originTime:e.originTime};const s={appMetadata:{},customTransaction:undefined,denyList:undefined,disabled:false,entityManager:undefined,harvester:undefined,isolatedBacklog:false,loaderType:undefined,maxBytes:3e4,obfuscator:undefined,onerror:undefined,ptid:undefined,releaseIds:{},session:undefined,timeKeeper:undefined,get harvestCount(){return++r}};const o=e=>{const i=(0,t.getModeledObject)(e,s);const r=Object.keys(n).reduce(((t,e)=>{t[e]={value:n[e],writable:false,configurable:true,enumerable:true};return t}),{});return Object.defineProperties(i,r)};$e.mergeRuntime=o;return $e}var ei={};var ii;function ri(){if(ii)return ei;ii=1;Object.defineProperty(ei,"__esModule",{value:true});ei.redefinePublicPath=void 0;const t=()=>{};ei.redefinePublicPath=t;return ei}var ni={};var si;function oi(){if(si)return ni;si=1;Object.defineProperty(ni,"__esModule",{value:true});ni.mergeLoaderConfig=void 0;var t=Nt();const e={accountID:undefined,trustKey:undefined,agentID:undefined,licenseKey:undefined,applicationID:undefined,xpid:undefined};const i=i=>(0,t.getModeledObject)(i,e);ni.mergeLoaderConfig=i;return ni}var ai;function ui(){if(ai)return fe;ai=1;Object.defineProperty(fe,"__esModule",{value:true});fe.configure=h;var t=Be();var e=p();var i=ue();var r=Xe();var n=ti();var s=Ne();var o=d();var a=ri();var u=_();var f=Re();var l=oi();const c=new Set;function h(h,d={},v,p){let{init:m,info:g,loader_config:y,runtime:b={},exposed:w=true}=d;if(!g){const t=(0,e.gosCDN)();m=t.init;g=t.info;y=t.loader_config}h.init=(0,r.mergeInit)(m||{});h.loader_config=(0,l.mergeLoaderConfig)(y||{});g.jsAttributes??={};if(o.isWorkerScope){g.jsAttributes.isWorker=true}h.info=(0,i.mergeInfo)(g);const S=h.init;const k=[g.beacon,g.errorBeacon];if(!c.has(h.agentIdentifier)){if(S.proxy.assets){(0,a.redefinePublicPath)(S.proxy.assets);k.push(S.proxy.assets)}if(S.proxy.beacon)k.push(S.proxy.beacon);(0,t.setTopLevelCallers)();(0,e.addToNREUM)("activatedFeatures",s.activatedFeatures);h.runSoftNavOverSpa&&=S.soft_navigations.enabled===true&&S.feature_flags.includes("soft_nav")}b.denyList=[...S.ajax.deny_list||[],...S.ajax.block_internal?k:[]];b.ptid=h.agentIdentifier;b.loaderType=v;h.runtime=(0,n.mergeRuntime)(b);if(!c.has(h.agentIdentifier)){h.ee=u.ee.get(h.agentIdentifier);h.exposed=w;(0,t.setAPI)(h,p);(0,f.dispatchGlobalEvent)({agentIdentifier:h.agentIdentifier,drained:!!s.activatedFeatures?.[h.agentIdentifier],type:"lifecycle",name:"initialize",feature:undefined,data:h.config})}c.add(h.agentIdentifier)}return fe}var fi={};var li={};var ci;function hi(){if(ci)return li;ci=1;Object.defineProperty(li,"__esModule",{value:true});li.isFileProtocol=e;var t=d();function e(){return Boolean(t.globalScope?.location?.protocol==="file:")}return li}var di;function vi(){if(di)return fi;di=1;Object.defineProperty(fi,"__esModule",{value:true});fi.Obfuscator=void 0;var t=hi();var e=Q();class i{constructor(t){this.agentRef=t;this.warnedRegexMissing=false;this.warnedInvalidRegex=false;this.warnedInvalidReplacement=false}get obfuscateConfigRules(){return this.agentRef.init.obfuscate||[]}obfuscateString(e){if(typeof e!=="string"||e.trim().length===0)return e;const i=this.obfuscateConfigRules.map((t=>this.validateObfuscationRule(t)));if((0,t.isFileProtocol)()){i.push({regex:/^file:\/\/(.*)/,replacement:atob("ZmlsZTovL09CRlVTQ0FURUQ=")})}return i.filter((t=>t.isValid)).reduce(((t,e)=>{const{rule:i}=e;return t.replace(i.regex,i.replacement||"*")}),e)}validateObfuscationRule(t){const i=Boolean(t.regex===undefined);const r=Boolean(t.regex!==undefined&&typeof t.regex!=="string"&&!(t.regex instanceof RegExp));const n=Boolean(t.replacement&&typeof t.replacement!=="string");if(i&&!this.warnedRegexMissing){(0,e.warn)(12,t);this.warnedRegexMissing=true}else if(r&&!this.warnedInvalidRegex){(0,e.warn)(13,t);this.warnedInvalidRegex=true}if(n&&!this.warnedInvalidReplacement){(0,e.warn)(14,t);this.warnedInvalidReplacement=true}return{rule:t,isValid:!i&&!r&&!n,errors:{regexMissingDetected:i,invalidRegexDetected:r,invalidReplacementDetected:n}}}}fi.Obfuscator=i;return fi}var pi={};var mi;function gi(){if(mi)return pi;mi=1;Object.defineProperty(pi,"__esModule",{value:true});pi.EventStoreManager=void 0;var t=Re();var e=Ne();var i=Oe();const r="NR_CONTAINER_AGENT";class n{constructor(t,e,i,n){this.agentRef=t;this.entityManager=t.runtime.entityManager;this.StorageClass=e;this.appStorageMap=new Map([[r,new this.StorageClass]]);this.featureName=n;this.setEventStore(i)}#t(t=r){if(!this.appStorageMap.has(t))this.setEventStore(t);return this.appStorageMap.get(t)}setEventStore(t){if(!t)return;const e=(0,i.isContainerAgentTarget)(this.entityManager.get(t),this.agentRef)?this.appStorageMap.get(r):new this.StorageClass;this.appStorageMap.set(t,e)}isEmpty(t,e){if(e)return this.#t(e).isEmpty(t);for(const e of this.appStorageMap.values()){if(!e.isEmpty(t))return false}return true}add(i,r){(0,t.dispatchGlobalEvent)({agentIdentifier:this.agentRef.agentIdentifier,drained:!!e.activatedFeatures?.[this.agentRef.agentIdentifier],type:"data",name:"buffer",feature:this.featureName,data:i});return this.#t(r).add(i)}addMetric(t,e,i,r){return this.#t().addMetric(t,e,i,r)}get(t,e){if(e)return[{targetApp:this.entityManager.get(e),data:this.#t(e).get(t)}];const i=[];this.appStorageMap.forEach(((e,r)=>{const n=this.entityManager.get(r);if(n)i.push({targetApp:n,data:e.get(t)})}));return i}byteSize(t){return this.#t(t).byteSize()}wouldExceedMaxSize(t,e){return this.#t(e).wouldExceedMaxSize(t)}save(t,e){if(e)return this.#t(e).save(t);this.appStorageMap.forEach((e=>e.save(t)))}clear(t,e){if(e)return this.#t(e).clear(t);this.appStorageMap.forEach((e=>e.clear(t)))}reloadSave(t,e){return this.#t(e).reloadSave(t)}clearSave(t,e){return this.#t(e).clearSave(t)}}pi.EventStoreManager=n;return pi}var yi={};var bi={};var wi;function Si(){if(wi)return bi;wi=1;Object.defineProperty(bi,"__esModule",{value:true});bi.subscribeToEOL=i;var t=d();var e=Ct();if(t.isWorkerScope){t.globalScope.cleanupTasks=[];const e=t.globalScope.close;t.globalScope.close=()=>{for(let e of t.globalScope.cleanupTasks){e()}e()}}function i(i,r){if(t.isBrowserScope){(0,e.subscribeToVisibilityChange)(i,true,r)}else if(t.isWorkerScope){t.globalScope.cleanupTasks.push(i)}}return bi}var ki={};var Ci;function Mi(){if(Ci)return ki;Ci=1;Object.defineProperty(ki,"__esModule",{value:true});ki.cleanURL=i;var t=/([^?#]*)[^#]*(#[^?]*|$).*/;var e=/([^?#]*)().*/;function i(i,r){return i.replace(r?t:e,"$1$2")}return ki}var Ii={};var Oi;function Ai(){if(Oi)return Ii;Oi=1;Object.defineProperty(Ii,"__esModule",{value:true});Ii.fromArray=o;Ii.obj=a;Ii.param=u;Ii.qs=s;var t=dt();var e={"%2C":",","%3A":":","%2F":"/","%40":"@","%24":"$","%3B":";"};var i=Object.keys(e);var r=new RegExp(i.join("|"),"g");function n(t){return e[t]}function s(t){if(t===null||t===undefined)return"null";return encodeURIComponent(t).replace(r,n)}function o(t,e){var i=0;for(var r=0;r<t.length;r++){i+=t[r].length;if(i>e)return t.slice(0,r).join("")}return t.join("")}function a(e,i){var r=0;var n="";Object.entries(e||{}).forEach((([e,o])=>{var a=[];var u;var f;if(typeof o==="string"||!Array.isArray(o)&&o!==null&&o!==undefined&&o.toString().length){u="&"+e+"="+s(o);r+=u.length;n+=u}else if(Array.isArray(o)&&o.length){r+=9;for(f=0;f<o.length;f++){u=s((0,t.stringify)(o[f]));r+=u.length;if(typeof i!=="undefined"&&r>=i)break;a.push(u)}n+="&"+e+"=%5B"+a.join(",")+"%5D"}}));return n}function u(t,e,i={}){if(Object.keys(i).includes(t))return"";if(e&&typeof e==="string"){return"&"+t+"="+s(e)}return""}return Ii}var Ti={};var _i;function Ei(){if(_i)return Ti;_i=1;Object.defineProperty(Ti,"__esModule",{value:true});Ti.beacon=n;Ti.getSubmitMethod=e;Ti.xhr=r;Ti.xhrFetch=i;var t=d();
/**
	 * @file Contains common methods used to transmit harvested data.
	 * @copyright 2023 New Relic Corporation. All rights reserved.
	 * @license Apache-2.0
	 */function e({isFinalHarvest:e=false}={}){if(e&&t.isBrowserScope){return n}if(typeof XMLHttpRequest!=="undefined"){return r}return i}function i({url:t,body:e=null,method:i="POST",headers:r=[{key:"content-type",value:"text/plain"}]}){const n={};for(const t of r){n[t.key]=t.value}return fetch(t,{headers:n,method:i,body:e,credentials:"include"})}function r({url:t,body:e=null,sync:i,method:r="POST",headers:n=[{key:"content-type",value:"text/plain"}]}){const s=new XMLHttpRequest;s.open(r,t,!i);try{if("withCredentials"in s)s.withCredentials=true}catch(t){}n.forEach((t=>{s.setRequestHeader(t.key,t.value)}));s.send(e);return s}function n({url:t,body:e}){try{const i=window.navigator.sendBeacon.bind(window.navigator);return i(t,e)}catch(t){return false}}return Ti}var Ri;function xi(){if(Ri)return yi;Ri=1;Object.defineProperty(yi,"__esModule",{value:true});yi.Harvester=void 0;var t=Ut();var e=B();var i=qe();var r=d();var n=R();var s=X();var o=bt();var a=c();var u=Si();var f=Mi();var l=Ai();var h=Q();var v=dt();var p=Ei();var m=Ne();var g=Re();const y="Harvester/Retry/Failed/";const b="Harvester/Retry/Succeeded/";class w{#e=false;initializedAggregates=[];constructor(t){this.agentRef=t;(0,u.subscribeToEOL)((()=>{this.initializedAggregates.forEach((t=>{if(typeof t.harvestOpts.beforeUnload==="function")t.harvestOpts.beforeUnload()}));this.initializedAggregates.forEach((t=>this.triggerHarvestFor(t,{isFinalHarvest:true})))}),false);t.ee.on(o.SESSION_EVENTS.RESET,(()=>this.initializedAggregates.forEach((t=>this.triggerHarvestFor(t,{forceNoRetry:true})))))}startTimer(t=this.agentRef.init.harvest.interval){if(this.#e)return;this.#e=true;const e=()=>{this.initializedAggregates.forEach((t=>this.triggerHarvestFor(t)));setTimeout(e,t*1e3)};setTimeout(e,t*1e3)}triggerHarvestFor(i,r={}){if(i.blocked)return false;const s=(0,p.getSubmitMethod)(r);if(!s)return false;const o=!r.isFinalHarvest&&s===p.xhr;let a;let u=false;if(!r.directSend){a=i.makeHarvestPayload(o,r);if(!a)return false}else a=[r.directSend];a.forEach((({targetApp:t,payload:n})=>{if(!n)return;k(this.agentRef,{endpoint:e.FEATURE_TO_ENDPOINT[i.featureName],targetApp:t,payload:n,localOpts:r,submitMethod:s,cbFinished:f,raw:i.harvestOpts.raw,featureName:i.featureName});u=true}));return u;function f(s){if(i.harvestOpts.prevAttemptCode){(0,n.handle)(t.SUPPORTABILITY_METRIC_CHANNEL,[(s.retry?y:b)+i.harvestOpts.prevAttemptCode],undefined,e.FEATURE_NAMES.metrics,i.ee);delete i.harvestOpts.prevAttemptCode}if(s.retry)i.harvestOpts.prevAttemptCode=s.status;if(r.forceNoRetry)s.retry=false;i.postHarvestCleanup(s)}}}yi.Harvester=w;const S={};function k(t,{endpoint:i,targetApp:n,payload:o,localOpts:a={},submitMethod:u,cbFinished:f,raw:c,featureName:d}){if(!t.info.errorBeacon)return false;let{body:y,qs:b}=C(o);if(Object.keys(y).length===0&&!a.sendEmptyBody){if(f)f({sent:false,targetApp:n});return false}const w=t.init.ssl===false?"http":"https";const k=t.init.proxy.beacon||t.info.errorBeacon;const I=c?"".concat(w,"://").concat(k,"/").concat(i):"".concat(w,"://").concat(k).concat(i!==e.RUM?"/"+i:"","/1/").concat(n.licenseKey);const O=!c?M(t,b,i,n.applicationID):"";let A=(0,l.obj)(b,t.runtime.maxBytes);if(O===""&&A.startsWith("&")){A=A.substring(1)}const T="".concat(I,"?").concat(O).concat(A);const _=!!b?.attributes?.includes("gzip");if(!_){if(i!==e.EVENTS)y=(0,v.stringify)(y);if(y.length>75e4&&(S[i]=(S[i]||0)+1)===1)(0,h.warn)(28,i)}if(!y||y.length===0||y==="{}"||y==="[]")y="";const E=[{key:"content-type",value:"text/plain"}];let R=u({url:T,body:y,sync:a.isFinalHarvest&&r.isWorkerScope,headers:E});if(!a.isFinalHarvest&&f){if(u===p.xhr){R.addEventListener("loadend",(function(){const t={sent:this.status!==0,status:this.status,retry:x(this.status),fullUrl:T,xhr:this,targetApp:n};if(a.needResponse)t.responseText=this.responseText;f(t)}),(0,s.eventListenerOpts)(false))}else if(u===p.xhrFetch){R.then((async function(t){const e=t.status;const i={sent:true,status:e,retry:x(e),fullUrl:T,fetchResponse:t,targetApp:n};if(a.needResponse)i.responseText=await t.text();f(i)}))}}(0,g.dispatchGlobalEvent)({agentIdentifier:t.agentIdentifier,drained:!!m.activatedFeatures?.[t.agentIdentifier],type:"data",name:"harvest",feature:d,data:{endpoint:i,headers:E,targetApp:n,payload:o,submitMethod:j(),raw:c,synchronousXhr:!!(a.isFinalHarvest&&r.isWorkerScope)}});return true;function x(t){switch(t){case 408:case 429:case 500:return true}return t>=502&&t<=504||t>=512&&t<=530}function j(){if(u===p.xhr)return"xhr";if(u===p.xhrFetch)return"fetch";return"beacon"}}function C(t={}){const e=t=>{if(typeof Uint8Array!=="undefined"&&t instanceof Uint8Array||Array.isArray(t))return t;if(typeof t==="string")return t.length>0?t:null;return Object.entries(t||{}).reduce(((t,[e,i])=>{if(typeof i==="number"||typeof i==="string"&&i.length>0||typeof i==="object"&&Object.keys(i||{}).length>0){t[e]=i}return t}),{})};return{body:e(t.body),qs:e(t.qs)}}function M(t,n,s,o){const u=t.runtime.obfuscator.obfuscateString((0,f.cleanURL)(""+r.globalScope.location));const c=t.runtime.session?.state.sessionReplayMode===1&&s!==e.JSERRORS;const h=t.runtime.session?.state.sessionTraceMode===1&&![e.LOGS,e.BLOBS].includes(s);const d=["a="+o,(0,l.param)("sa",t.info.sa?""+t.info.sa:""),(0,l.param)("v",i.VERSION),v(),(0,l.param)("ct",t.runtime.customTransaction),"&rst="+(0,a.now)(),"&ck=0","&s="+(t.runtime.session?.state.value||"0"),(0,l.param)("ref",u),(0,l.param)("ptid",t.runtime.ptid?""+t.runtime.ptid:"")];if(c)d.push((0,l.param)("hr","1",n));if(h)d.push((0,l.param)("ht","1",n));return d.join("");function v(){if(t.info.transactionName)return(0,l.param)("to",t.info.transactionName);return(0,l.param)("t",t.info.tNamePlain||"Unnamed Transaction")}}return yi}var ji={};var Ni;function Di(){if(Ni)return ji;Ni=1;Object.defineProperty(ji,"__esModule",{value:true});ji.EntityManager=void 0;class t{#i=new Map;#r={};#n=null;constructor(t){this.agentRef=t;this.#n={licenseKey:t.info.licenseKey,applicationID:t.info.applicationID}}get(t){if(!t)return this.#n;return this.#i.get(t)}getEntityGuidFor(t,e){if(!this.#r[t]||!this.#r[e])return;return this.#r[t].filter((t=>this.#r[e].includes(t)))[0]}set(t,e){if(this.#i.has(t))return;this.#i.set(t,e);this.#r[e.licenseKey]??=[];this.#r[e.licenseKey].push(t);this.#r[e.applicationID]??=[];this.#r[e.applicationID].push(t);this.agentRef.ee.emit("entity-added",[e])}clear(){this.#i.clear()}setDefaultEntity(t){this.#n=t}}ji.EntityManager=t;return ji}var Fi={};var Li={};var Pi;function Bi(){if(Pi)return Li;Pi=1;Object.defineProperty(Li,"__esModule",{value:true});Li.MAX_PAYLOAD_SIZE=Li.IDEAL_PAYLOAD_SIZE=void 0;Li.IDEAL_PAYLOAD_SIZE=64e3;Li.MAX_PAYLOAD_SIZE=1e6;return Li}var Ui;function Gi(){if(Ui)return Fi;Ui=1;Object.defineProperty(Fi,"__esModule",{value:true});Fi.EventBuffer=void 0;var t=dt();var e=Bi();class i{#s=[];#o=0;#a;#u;constructor(t=e.MAX_PAYLOAD_SIZE){this.maxPayloadSize=t}isEmpty(){return this.#s.length===0}get(){return this.#s}byteSize(){return this.#o}wouldExceedMaxSize(t){return this.#o+t>this.maxPayloadSize}add(e){const i=(0,t.stringify)(e)?.length||0;if(this.#o+i>this.maxPayloadSize)return false;this.#s.push(e);this.#o+=i;return true}clear(){this.#s=[];this.#o=0}save(){this.#a=this.#s;this.#u=this.#o}clearSave(){this.#a=undefined;this.#u=undefined}reloadSave(){if(!this.#a)return;if(this.#u+this.#o>this.maxPayloadSize)return;this.#s=[...this.#a,...this.#s];this.#o=this.#u+this.#o}}Fi.EventBuffer=i;return Fi}var Wi={};var zi={};var Vi;function Hi(){if(Vi)return zi;Vi=1;Object.defineProperty(zi,"__esModule",{value:true});zi.Aggregator=void 0;class t{constructor(){this.aggregatedData={}}store(t,i,r,n,s){var o=this.#f(t,i,r,s);o.metrics=e(n,o.metrics);return o}merge(t,e,r,s,o,a=false){var u=this.#f(t,e,s,o);if(a)u.params=s;if(!u.metrics){u.metrics=r;return}var f=u.metrics;f.count+=r.count;Object.keys(r||{}).forEach((t=>{if(t==="count")return;var e=f[t];var s=r[t];if(s&&!s.c){f[t]=i(s.t,e)}else{f[t]=n(s,f[t])}}))}storeMetric(t,e,r,n){var s=this.#f(t,e,r);s.stats=i(n,s.stats);return s}take(t,e=true){var i={};var r="";var n=false;for(var s=0;s<t.length;s++){r=t[s];i[r]=Object.values(this.aggregatedData[r]||{});if(i[r].length)n=true;if(e)delete this.aggregatedData[r]}return n?i:null}#f(t,e,i,r){if(!this.aggregatedData[t])this.aggregatedData[t]={};var n=this.aggregatedData[t][e];if(!n){n=this.aggregatedData[t][e]={params:i||{}};if(r){n.custom=r}}return n}}zi.Aggregator=t;function e(t,e){if(!e)e={count:0};e.count+=1;Object.entries(t||{}).forEach((([t,r])=>{e[t]=i(r,e[t])}));return e}function i(t,e){if(t==null){return r(e)}if(!e)return{t};if(!e.c){e=s(e.t)}e.c+=1;e.t+=t;e.sos+=t*t;if(t>e.max)e.max=t;if(t<e.min)e.min=t;return e}function r(t){if(!t){t={c:1}}else{t.c++}return t}function n(t,e){if(!e)return t;if(!e.c){e=s(e.t)}e.min=Math.min(t.min,e.min);e.max=Math.max(t.max,e.max);e.t+=t.t;e.sos+=t.sos;e.c+=t.c;return e}function s(t){return{t,min:t,max:t,sos:t*t,c:1}}return zi}var Yi;function Zi(){if(Yi)return Wi;Yi=1;Object.defineProperty(Wi,"__esModule",{value:true});Wi.EventAggregator=void 0;var t=Hi();class e{#l=new t.Aggregator;#c={};isEmpty({aggregatorTypes:t}){if(!t)return Object.keys(this.#l.aggregatedData).length===0;return t.every((t=>!this.#l.aggregatedData[t]))}add([t,e,i,r,n]){this.#l.store(t,e,i,r,n);return true}addMetric(t,e,i,r){this.#l.storeMetric(t,e,i,r);return true}save({aggregatorTypes:t}){const e=t.toString();const i={};t.forEach((t=>i[t]=this.#l.aggregatedData[t]));this.#c[e]=i}get(t){const e=Array.isArray(t)?t:t.aggregatorTypes;return this.#l.take(e,false)}clear({aggregatorTypes:t}={}){if(!t){this.#l.aggregatedData={};return}t.forEach((t=>delete this.#l.aggregatedData[t]))}reloadSave({aggregatorTypes:t}){const e=t.toString();const i=this.#c[e];t.forEach((t=>{Object.keys(i[t]||{}).forEach((e=>{const r=i[t][e];this.#l.merge(t,e,r.metrics,r.params,r.custom,true)}))}))}clearSave({aggregatorTypes:t}){const e=t.toString();delete this.#c[e]}}Wi.EventAggregator=e;return Wi}var Xi;function $i(){if(Xi)return se;Xi=1;Object.defineProperty(se,"__esModule",{value:true});se.AggregateBase=void 0;var t=V();var e=ue();var i=ui();var r=p();var n=G();var s=Ne();var o=vi();var a=B();var u=gi();var f=xi();var l=Q();var c=Di();var h=Gi();var d=R();var v=Ut();var m=Zi();class g extends t.FeatureBase{constructor(t,e){super(t.agentIdentifier,e);this.agentRef=t;this.checkConfiguration(t);this.doOnceForAllAggregate(t);this.harvestOpts={};const i=this.agentRef?.runtime?.appMetadata?.agents?.[0]?.entityGuid;this.#h(i);if(!i){this.ee.on("entity-added",(t=>{this.events?.setEventStore?.(t.entityGuid)}))}}#h(t){if(this.events)return;switch(this.featureName){case a.FEATURE_NAMES.sessionTrace:case a.FEATURE_NAMES.sessionReplay:break;case a.FEATURE_NAMES.jserrors:case a.FEATURE_NAMES.metrics:this.events=this.agentRef.sharedAggregator??=new u.EventStoreManager(this.agentRef,m.EventAggregator,t,"shared_aggregator");break;default:this.events=new u.EventStoreManager(this.agentRef,h.EventBuffer,t,this.featureName);break}}waitForFlags(t=[]){const e=new Promise(((e,i)=>{if(s.activatedFeatures[this.agentIdentifier]){e(r(s.activatedFeatures[this.agentIdentifier]))}else{this.ee.on("rumresp",((t={})=>{e(r(t))}))}function r(e){return t.map((t=>{if(!e[t])return 0;return e[t]}))}}));return e.catch((t=>{this.ee.emit("internal-error",[t]);this.blocked=true;this.deregisterDrain()}))}drain(){(0,n.drain)(this.agentIdentifier,this.featureName);this.drained=true}preHarvestChecks(t){return!this.blocked}makeHarvestPayload(t=false,e={}){if(!this.events||this.events.isEmpty(this.harvestOpts,e.targetEntityGuid))return;if(this.preHarvestChecks&&!this.preHarvestChecks(e))return;if(t)this.events.save(this.harvestOpts,e.targetEntityGuid);const i=this.events.get(this.harvestOpts,e.targetEntityGuid);if(!i.length)return(0,l.warn)(52);this.events.clear(this.harvestOpts,e.targetEntityGuid);return i.map((({targetApp:t,data:e})=>{const i=this.serializer?this.serializer(e,t?.entityGuid):e;const r={body:i};if(this.queryStringsBuilder)r.qs=this.queryStringsBuilder(e,t?.entityGuid);return{targetApp:t,payload:r}}))}postHarvestCleanup(t={}){const e=t.sent&&t.retry;if(e)this.events.reloadSave(this.harvestOpts,t.targetApp?.entityGuid);this.events.clearSave(this.harvestOpts,t.targetApp?.entityGuid)}checkConfiguration(t){if(!(0,e.isValid)(t.info)){const e=(0,r.gosCDN)();let n={...e.info?.jsAttributes};try{n={...n,...t.info?.jsAttributes}}catch(t){}(0,i.configure)(t,{...e,info:{...e.info,jsAttributes:n},runtime:t.runtime},t.runtime.loaderType)}}doOnceForAllAggregate(t){if(!t.runtime.obfuscator)t.runtime.obfuscator=new o.Obfuscator(t);this.obfuscator=t.runtime.obfuscator;if(!t.runtime.entityManager)t.runtime.entityManager=new c.EntityManager(this.agentRef);if(!t.runtime.harvester)t.runtime.harvester=new f.Harvester(t)}reportSupportabilityMetric(t,e){(0,d.handle)(v.SUPPORTABILITY_METRIC_CHANNEL,[t,e],undefined,a.FEATURE_NAMES.metrics,this.ee)}}se.AggregateBase=g;return se}var Ji={};var Ki={};var qi;function Qi(){if(qi)return Ki;qi=1;Object.defineProperty(Ki,"__esModule",{value:true});Ki.isPureObject=t;function t(t){return t?.constructor==={}.constructor}return Ki}var tr;function er(){if(tr)return Ji;tr=1;Object.defineProperty(Ji,"__esModule",{value:true});Ji.parseGQL=e;var t=Qi();function e({body:t,query:e}={}){if(!t&&!e)return;try{const o=r(n(t));if(o)return o;const a=i(s(e));if(a)return a}catch(t){}}function i(t){if(typeof t!=="object"||!t.query||typeof t.query!=="string")return;const e=t.query.trim().match(/^(query|mutation|subscription)\s?(\w*)/);const i=e?.[1];if(!i)return;const r=t.operationName||e?.[2]||"Anonymous";return{operationName:r,operationType:i,operationFramework:"GraphQL"}}function r(t){if(!t)return;if(!Array.isArray(t))t=[t];const e=[];const r=[];for(let n of t){const t=i(n);if(!t)continue;e.push(t.operationName);r.push(t.operationType)}if(!r.length)return;return{operationName:e.join(","),operationType:r.join(","),operationFramework:"GraphQL"}}function n(e){let i;if(!e||typeof e!=="string"&&typeof e!=="object")return;else if(typeof e==="string")i=JSON.parse(e);else i=e;if(!(0,t.isPureObject)(i)&&!Array.isArray(i))return;let r=false;if(Array.isArray(i))r=i.some((t=>o(t)));else r=o(i);if(!r)return;return i}function s(t){if(!t||typeof t!=="string")return;const e=new URLSearchParams(t);return n(Object.fromEntries(e))}function o(t){return!(typeof t!=="object"||!t.query||typeof t.query!=="string")}return Ji}var ir={};var rr;function nr(){if(rr)return ir;rr=1;Object.defineProperty(ir,"__esModule",{value:true});ir.addCustomAttributes=o;ir.getAddStringContext=s;ir.nullable=r;ir.numeric=n;var t=dt();var e=Object.prototype.hasOwnProperty;var i=64;function r(t,e,i){return t||t===0||t===""?e(t)+(i?",":""):"!"}function n(t,e){if(e){return Math.floor(t).toString(36)}return t===undefined||t===0?"":Math.floor(t).toString(36)}function s(t){let i=0;const r=Object.prototype.hasOwnProperty.call(Object,"create")?Object.create(null):{};return s;function s(s){if(typeof s==="undefined"||s==="")return"";s=t.obfuscateString(String(s));if(e.call(r,s)){return n(r[s],true)}else{r[s]=i++;return u(s)}}}function o(e,r){var n=[];Object.entries(e||{}).forEach((([e,s])=>{if(n.length>=i)return;var o=5;var a;e=r(e);switch(typeof s){case"object":if(s){a=r((0,t.stringify)(s))}else{o=9}break;case"number":o=6;a=s%1?s:s+".";break;case"boolean":o=s?7:8;break;case"undefined":o=9;break;default:a=r(s)}n.push([o,e+(a?","+a:"")])}));return n}var a=/([,\\;])/g;function u(t){return"'"+t.replace(a,"\\$1")}return ir}var sr;function or(){if(sr)return qt;sr=1;Object.defineProperty(qt,"__esModule",{value:true});qt.Aggregate=void 0;var t=F();var e=dt();var i=R();var r=ee();var n=ne();var s=B();var o=$i();var a=er();var u=nr();class f extends o.AggregateBase{static featureName=n.FEATURE_NAME;constructor(e){super(e,n.FEATURE_NAME);(0,r.setDenyList)(e.runtime.denyList);this.underSpaEvents={};const i=this;this.ee.on("interactionDone",((t,e)=>{if(!this.underSpaEvents[t.id])return;if(!e){this.underSpaEvents[t.id].forEach((t=>this.events.add(t)))}delete this.underSpaEvents[t.id]}));(0,t.registerHandler)("returnAjax",(t=>this.events.add(t)),this.featureName,this.ee);(0,t.registerHandler)("xhr",(function(){i.storeXhr(...arguments,this)}),this.featureName,this.ee);this.waitForFlags([]).then((()=>this.drain()))}storeXhr(t,n,o,u,f,l){n.time=o;let c;if(t.cat){c=(0,e.stringify)([t.status,t.cat])}else{c=(0,e.stringify)([t.status,t.host,t.pathname])}const h=(0,r.shouldCollectEvent)(t);const d=this.agentRef.init.feature_flags?.includes("ajax_metrics_deny_list");const v=Boolean(this.agentRef.features?.[s.FEATURE_NAMES.jserrors]);if(v&&(h||!d)){this.agentRef.sharedAggregator?.add(["xhr",c,t,n])}if(!h){if(t.hostname===this.agentRef.info.errorBeacon||this.agentRef.init.proxy?.beacon&&t.hostname===this.agentRef.init.proxy.beacon){this.reportSupportabilityMetric("Ajax/Events/Excluded/Agent");if(d)this.reportSupportabilityMetric("Ajax/Metrics/Excluded/Agent")}else{this.reportSupportabilityMetric("Ajax/Events/Excluded/App");if(d)this.reportSupportabilityMetric("Ajax/Metrics/Excluded/App")}return}(0,i.handle)("bstXhrAgg",["xhr",c,t,n],undefined,s.FEATURE_NAMES.sessionTrace,this.ee);const p={method:t.method,status:t.status,domain:t.host,path:t.pathname,requestSize:n.txSize,responseSize:n.rxSize,type:f,startTime:o,endTime:u,callbackDuration:n.cbTime};if(l.dt){p.spanId=l.dt.spanId;p.traceId=l.dt.traceId;p.spanTimestamp=Math.floor(this.agentRef.runtime.timeKeeper.correctAbsoluteTimestamp(l.dt.timestamp))}p.gql=t.gql=(0,a.parseGQL)({body:l.body,query:l.parsedOrigin?.search});if(p.gql)this.reportSupportabilityMetric("Ajax/Events/GraphQL/Bytes-Added",(0,e.stringify)(p.gql).length);const m=Boolean(this.agentRef.features?.[s.FEATURE_NAMES.softNav]);if(m){(0,i.handle)("ajax",[p],undefined,s.FEATURE_NAMES.softNav,this.ee)}else if(l.spaNode){const t=l.spaNode.interaction.id;this.underSpaEvents[t]??=[];this.underSpaEvents[t].push(p)}else{this.events.add(p)}}serializer(t){if(!t.length)return;const e=(0,u.getAddStringContext)(this.agentRef.runtime.obfuscator);let i="bel.7;";for(let r=0;r<t.length;r++){const n=t[r];const s=[(0,u.numeric)(n.startTime),(0,u.numeric)(n.endTime-n.startTime),(0,u.numeric)(0),(0,u.numeric)(0),e(n.method),(0,u.numeric)(n.status),e(n.domain),e(n.path),(0,u.numeric)(n.requestSize),(0,u.numeric)(n.responseSize),n.type==="fetch"?1:"",e(0),(0,u.nullable)(n.spanId,e,true)+(0,u.nullable)(n.traceId,e,true)+(0,u.nullable)(n.spanTimestamp,u.numeric,false)];let o="2,";const a=this.agentRef.info.jsAttributes;const f=(0,u.addCustomAttributes)({...a||{},...n.gql||{}},e);s.unshift((0,u.numeric)(f.length));o+=s.join(",");if(f&&f.length>0){o+=";"+f.join(";")}if(r+1<t.length)o+=";";i+=o}return i}}qt.Aggregate=f;return qt}var ar={};var ur={};var fr;function lr(){if(fr)return ur;fr=1;Object.defineProperty(ur,"__esModule",{value:true});ur.canonicalFunctionName=e;const t=/([a-z0-9]+)$/i;function e(e){if(!e)return;const i=e.match(t);if(i)return i[1]}return ur}var cr={};var hr={};var dr;function vr(){if(dr)return hr;dr=1;Object.defineProperty(hr,"__esModule",{value:true});hr.formatStackTrace=i;hr.truncateSize=n;var t=/^\n+|\n+$/g;var e=65530;function i(e){return r(e).replace(t,"")}function r(t){var e;if(t.length>100){var i=t.length-100;e=t.slice(0,50).join("\n");e+="\n< ...truncated "+i+" lines... >\n";e+=t.slice(-50).join("\n")}else{e=t.join("\n")}return e}function n(t){return t.length>e?t.substr(0,e):t}return hr}var pr={};var mr;function gr(){if(mr)return pr;mr=1;Object.defineProperty(pr,"__esModule",{value:true});pr.canonicalizeUrl=i;var t=d();var e=Mi();function i(i){if(typeof i!=="string")return"";const r=(0,e.cleanURL)(i);const n=(0,e.cleanURL)(t.initialLocation);if(r===n){return"<inline>"}else{return r}}return pr}var yr;function br(){if(yr)return cr;yr=1;Object.defineProperty(cr,"__esModule",{value:true});cr.computeStackTrace=a;var t=vr();var e=gr();var i=/function (.+?)\s*\(/;var r=/^\s*at (?:((?:\[object object\])?(?:[^(]*\([^)]*\))*[^()]*(?: \[as \S+\])?) )?\(?((?:file|http|https|chrome-extension):.*?)?:(\d+)(?::(\d+))?\)?\s*$/i;var n=/^\s*(?:(\S*|global code)(?:\(.*?\))?@)?((?:file|http|https|chrome|safari-extension).*?):(\d+)(?::(\d+))?\s*$/i;var s=/^\s*at .+ \(eval at \S+ \((?:(?:file|http|https):[^)]+)?\)(?:, [^:]*:\d+:\d+)?\)$/i;var o=/^\s*at Function code \(Function code:\d+:\d+\)\s*/i;function a(t){var e=null;try{e=u(t);if(e){return e}}catch(t){}try{e=c(t);if(e){return e}}catch(t){}try{e=h(t);if(e){return e}}catch(t){}return{mode:"failed",stackString:"",frames:[]}}function u(e){if(!e.stack){return null}var i=e.stack.split("\n").reduce(f,{frames:[],stackLines:[],wrapperSeen:false});if(!i.frames.length)return null;return{mode:"stack",name:e.name||d(e),message:e.message,stackString:(0,t.formatStackTrace)(i.stackLines),frames:i.frames}}function f(t,i){let r=l(i);if(!r){t.stackLines.push(i);return t}if(v(r.func))t.wrapperSeen=true;if(!t.wrapperSeen){let n=(0,e.canonicalizeUrl)(r.url);if(n!==r.url){i=i.replace(r.url,n);r.url=n}t.stackLines.push(i);t.frames.push(r)}return t}function l(t){var e=t.match(n);if(!e)e=t.match(r);if(e){return{url:e[2],func:e[1]!=="Anonymous function"&&e[1]!=="global code"&&e[1]||null,line:+e[3],column:e[4]?+e[4]:null}}if(t.match(s)||t.match(o)||t==="anonymous"){return{func:"evaluated code"}}}function c(t){if(!("line"in t))return null;var i=t.name||d(t);if(!t.sourceURL){return{mode:"sourceline",name:i,message:t.message,stackString:i+": "+t.message+"\n    in evaluated code",frames:[{func:"evaluated code"}]}}var r=(0,e.canonicalizeUrl)(t.sourceURL);var n=i+": "+t.message+"\n    at "+r;if(t.line){n+=":"+t.line;if(t.column){n+=":"+t.column}}return{mode:"sourceline",name:i,message:t.message,stackString:n,frames:[{url:r,line:t.line,column:t.column}]}}function h(t){var e=t.name||d(t);if(!e)return null;return{mode:"nameonly",name:e,message:t.message,stackString:e+": "+t.message,frames:[]}}function d(t){var e=i.exec(String(t.constructor));return e&&e.length>1?e[1]:"unknown"}function v(t){return t&&t.indexOf("nrWrapper")>=0}return cr}var wr={};var Sr;function kr(){if(Sr)return wr;Sr=1;Object.defineProperty(wr,"__esModule",{value:true});wr.stringHashCode=t;function t(t){var e=0;var i;if(!t||!t.length)return e;for(var r=0;r<t.length;r++){i=t.charCodeAt(r);e=(e<<5)-e+i;e=e|0}return e}return wr}var Cr={};var Mr;function Ir(){if(Mr)return Cr;Mr=1;Object.defineProperty(Cr,"__esModule",{value:true});Cr.FEATURE_NAME=void 0;var t=B();Cr.FEATURE_NAME=t.FEATURE_NAMES.jserrors;return Cr}var Or={};var Ar;function Tr(){if(Ar)return Or;Ar=1;Object.defineProperty(Or,"__esModule",{value:true});Or.applyFnToProps=t;function t(e,i,r="string",n=[]){if(!e||typeof e!=="object")return e;Object.keys(e).forEach((s=>{if(typeof e[s]==="object"){t(e[s],i,r,n)}else{if(typeof e[s]===r&&!n.includes(s))e[s]=i(e[s])}}));return e}return Or}var _r={};var Er;function Rr(){if(Er)return _r;Er=1;Object.defineProperty(_r,"__esModule",{value:true});_r.evaluateInternalError=i;const t="Rrweb";const e="Security-Policy";function i(i,r,n){const s={shouldSwallow:r||false,reason:n||"Other"};const o=i.frames?.[0];if(!o||typeof i?.message!=="string")return s;const a=o?.url?.match(/nr-(.*)-recorder.min.js/);const u=o?.url?.match(/rrweb/);const f=o?.url?.match(/recorder/);const l=i.message.toLowerCase().match(/an attempt was made to break through the security policy of the user agent/);if(!!a||!!u){s.shouldSwallow=true;s.reason=t;if(l)s.reason+="-"+e}else if(!!f&&l){s.shouldSwallow=true;s.reason=t+"-"+e}return s}return _r}var xr;function jr(){if(xr)return ar;xr=1;Object.defineProperty(ar,"__esModule",{value:true});ar.Aggregate=void 0;var t=lr();var e=br();var i=kr();var r=vr();var n=F();var s=dt();var o=R();var a=d();var u=Ir();var f=B();var l=$i();var h=c();var v=Tr();var p=Rr();var m=Oe();var g=Q();class y extends l.AggregateBase{static featureName=u.FEATURE_NAME;constructor(t){super(t,u.FEATURE_NAME);this.stackReported={};this.observedAt={};this.pageviewReported={};this.bufferedErrorsUnderSpa={};this.errorOnPage=false;this.ee.on("interactionDone",((t,e)=>this.onInteractionDone(t,e)));(0,n.registerHandler)("err",((...t)=>this.storeError(...t)),this.featureName,this.ee);(0,n.registerHandler)("ierr",((...t)=>this.storeError(...t)),this.featureName,this.ee);(0,n.registerHandler)("softNavFlush",((t,e,i)=>this.onSoftNavNotification(t,e,i)),this.featureName,this.ee);this.harvestOpts.aggregatorTypes=["err","ierr","xhr"];this.waitForFlags(["err"]).then((([t])=>{if(t){this.drain()}else{this.blocked=true;this.deregisterDrain()}}))}serializer(t){return(0,v.applyFnToProps)(t,this.obfuscator.obfuscateString.bind(this.obfuscator),"string")}queryStringsBuilder(t){const e={};const i=(0,s.stringify)(this.agentRef.runtime.releaseIds);if(i!=="{}")e.ri=i;if(t?.err?.length){if(!this.errorOnPage){e.pve="1";this.errorOnPage=true}if(!this.agentRef.features?.[f.FEATURE_NAMES.sessionReplay]?.featAggregate?.replayIsActive())t.err.forEach((t=>delete t.params.hasReplay))}return e}buildCanonicalStackString(e){var i="";for(var r=0;r<e.frames.length;r++){var n=e.frames[r];var s=(0,t.canonicalFunctionName)(n.func);if(i)i+="\n";if(s)i+=s+"@";if(typeof n.url==="string")i+=n.url;if(n.line)i+=":"+n.line}return i}storeError(t,n,u,l,c,d,v){if(!t)return;const y=this.agentRef.runtime.entityManager.get(v);if(!y)return(0,g.warn)(56,this.featureName);n=n||(0,h.now)();let b;if(!u&&this.agentRef.runtime.onerror){b=this.agentRef.runtime.onerror(t);if(b&&!(typeof b.group==="string"&&b.group.length)){return}}var w=(0,e.computeStackTrace)(t);const{shouldSwallow:S,reason:k}=(0,p.evaluateInternalError)(w,u,d);if(S){this.reportSupportabilityMetric("Internal/Error/"+k);return}var C=this.buildCanonicalStackString(w);const M={stackHash:(0,i.stringHashCode)(C),exceptionClass:w.name,request_uri:a.globalScope?.location.pathname};if(w.message)M.message=""+w.message;if(b?.group)M.errorGroup=b.group;if(c&&(0,m.isContainerAgentTarget)(y,this.agentRef))M.hasReplay=c;var I=(0,i.stringHashCode)("".concat(w.name,"_").concat(w.message,"_").concat(w.stackString,"_").concat(M.hasReplay?1:0));if(!this.stackReported[I]){this.stackReported[I]=true;M.stack_trace=(0,r.truncateSize)(w.stackString);this.observedAt[I]=Math.floor(this.agentRef.runtime.timeKeeper.correctRelativeTimestamp(n))}else{M.browser_stack_hash=(0,i.stringHashCode)(w.stackString)}M.releaseIds=(0,s.stringify)(this.agentRef.runtime.releaseIds);if(!this.pageviewReported[I]){M.pageview=1;this.pageviewReported[I]=true}M.firstOccurrenceTimestamp=this.observedAt[I];M.timestamp=Math.floor(this.agentRef.runtime.timeKeeper.correctRelativeTimestamp(n));var O="err";var A={time:n};const T=[O,I,M,A,l];if(this.shouldAllowMainAgentToCapture(v))(0,o.handle)("trace-jserror",T,undefined,f.FEATURE_NAMES.sessionTrace,this.ee);if(this.blocked)return;if(t?.__newrelic?.[this.agentIdentifier]){M._interactionId=t.__newrelic[this.agentIdentifier].interactionId;M._interactionNodeId=t.__newrelic[this.agentIdentifier].interactionNodeId}if(this.shouldAllowMainAgentToCapture(v)){const t=Boolean(this.agentRef.features?.[f.FEATURE_NAMES.softNav]);if(t)(0,o.handle)("jserror",[M,n],undefined,f.FEATURE_NAMES.softNav,this.ee);else(0,o.handle)("spa-jserror",T,undefined,f.FEATURE_NAMES.spa,this.ee);if(M.browserInteractionId&&!M._softNavFinished){this.bufferedErrorsUnderSpa[M.browserInteractionId]??=[];this.bufferedErrorsUnderSpa[M.browserInteractionId].push(T)}else if(M._interactionId!=null){this.bufferedErrorsUnderSpa[M._interactionId]=this.bufferedErrorsUnderSpa[M._interactionId]||[];this.bufferedErrorsUnderSpa[M._interactionId].push(T)}else{this.#d(T,M.browserInteractionId!==undefined,M._softNavAttributes)}}if(v)this.#d([...T,v],false,M._softNavAttributes)}#d(t,e,r={}){let[n,o,a,u,f,l]=t;const c={};if(e){Object.entries(r).forEach((([t,e])=>v(t,e)));o+=a.browserInteractionId;delete a._softNavAttributes;delete a._softNavFinished}else{Object.entries(this.agentRef.info.jsAttributes).forEach((([t,e])=>v(t,e)));delete a.browserInteractionId}if(f)Object.entries(f).forEach((([t,e])=>v(t,e)));const h=(0,i.stringHashCode)((0,s.stringify)(c));const d=o+":"+h;this.events.add([n,d,a,u,c],l);function v(t,e){c[t]=e&&typeof e==="object"?(0,s.stringify)(e):e}}shouldAllowMainAgentToCapture(t){return!t||this.agentRef.init.api.duplicate_registered_data}onInteractionDone(t,e){if(!this.bufferedErrorsUnderSpa[t.id]||this.blocked)return;this.bufferedErrorsUnderSpa[t.id].forEach((r=>{var n={};const o=r[4];Object.entries(t.root.attrs.custom||{}).forEach(c);Object.entries(o||{}).forEach(c);var a=r[2];if(e){a.browserInteractionId=t.root.attrs.id;if(a._interactionNodeId)a.parentNodeId=a._interactionNodeId.toString()}delete a._interactionId;delete a._interactionNodeId;var u=e?r[1]+t.root.attrs.id:r[1];var f=(0,i.stringHashCode)((0,s.stringify)(n));var l=u+":"+f;this.events.add([r[0],l,a,r[3],n],r[5]);function c([t,e]){n[t]=e&&typeof e==="object"?(0,s.stringify)(e):e}}));delete this.bufferedErrorsUnderSpa[t.id]}onSoftNavNotification(t,e,i){if(this.blocked)return;this.bufferedErrorsUnderSpa[t]?.forEach((t=>this.#d(t,e,i)));delete this.bufferedErrorsUnderSpa[t]}}ar.Aggregate=y;return ar}var Nr={};var Dr={};var Fr={};var Lr;function Pr(){if(Lr)return Fr;Lr=1;Object.defineProperty(Fr,"__esModule",{value:true});Fr.generateSelectorPath=void 0;const t=(t,e=[])=>{if(!t)return{path:undefined,nearestFields:{}};const i=t=>{try{let e=1;const{tagName:i}=t;while(t.previousElementSibling){if(t.previousElementSibling.tagName===i)e++;t=t.previousElementSibling}return e}catch(t){}};let r="";let n=i(t);const s={};try{while(t?.tagName){const{id:i,localName:n}=t;e.forEach((e=>{s[a(e)]||=t[e]?.baseVal||t[e]}));const o=[n,i?"#".concat(i):"",r?">".concat(r):""].join("");r=o;t=t.parentNode}}catch(t){}const o=r?n?"".concat(r,":nth-of-type(").concat(n,")"):r:undefined;return{path:o,nearestFields:s};function a(t){if(t==="tagName")t="tag";if(t==="className")t="class";return"nearest".concat(t.charAt(0).toUpperCase()+t.slice(1))}};Fr.generateSelectorPath=t;return Fr}var Br={};var Ur;function Gr(){if(Ur)return Br;Ur=1;Object.defineProperty(Br,"__esModule",{value:true});Br.AggregatedUserAction=void 0;var t=ze();class e{constructor(t,e,i){this.event=t;this.count=1;this.originMs=Math.floor(t.timeStamp);this.relativeMs=[0];this.selectorPath=e;this.rageClick=undefined;this.nearestTargetFields=i}aggregate(t){this.count++;this.relativeMs.push(Math.floor(t.timeStamp-this.originMs));if(this.isRageClick())this.rageClick=true}isRageClick(){const e=this.relativeMs.length;return this.event.type==="click"&&e>=t.RAGE_CLICK_THRESHOLD_EVENTS&&this.relativeMs[e-1]-this.relativeMs[e-t.RAGE_CLICK_THRESHOLD_EVENTS]<t.RAGE_CLICK_THRESHOLD_MS}}Br.AggregatedUserAction=e;return Br}var Wr;function zr(){if(Wr)return Dr;Wr=1;Object.defineProperty(Dr,"__esModule",{value:true});Dr.UserActionsAggregator=void 0;var t=Pr();var e=ze();var i=Gr();class r{#v=undefined;#p="";get aggregationEvent(){const t=this.#v;this.#p="";this.#v=undefined;return t}process(t,e){if(!t)return;const{selectorPath:r,nearestTargetFields:o}=n(t,e);const a=s(t,r);if(!!a&&a===this.#p){this.#v.aggregate(t)}else{const e=this.#v;this.#p=a;this.#v=new i.AggregatedUserAction(t,r,o);return e}}}Dr.UserActionsAggregator=r;function n(i,r){let n;let s={};if(e.OBSERVED_WINDOW_EVENTS.includes(i.type)||i.target===window)n="window";else if(i.target===document)n="document";else{const{path:e,nearestFields:o}=(0,t.generateSelectorPath)(i.target,r);n=e;s=o}return{selectorPath:n,nearestTargetFields:s}}function s(t,e){let i=t.type;if(t.type!=="scrollend")i+="-"+e;return i}return Dr}var Vr={};var Hr;function Yr(){if(Hr)return Vr;Hr=1;Object.defineProperty(Vr,"__esModule",{value:true});Vr.isIFrameWindow=t;function t(t){if(!t)return false;return t.self!==t.top}return Vr}var Zr;function Xr(){if(Zr)return Nr;Zr=1;Object.defineProperty(Nr,"__esModule",{value:true});Nr.Aggregate=void 0;var t=dt();var e=Mi();var i=ze();var r=d();var n=$i();var s=Q();var o=c();var a=F();var u=Ut();var f=Tr();var l=zr();var h=Yr();var v=Qi();class p extends n.AggregateBase{static featureName=i.FEATURE_NAME;constructor(t){super(t,i.FEATURE_NAME);this.eventsPerHarvest=1e3;this.referrerUrl=r.isBrowserScope&&document.referrer?(0,e.cleanURL)(document.referrer):undefined;this.waitForFlags(["ins"]).then((([n])=>{if(!n){this.blocked=true;this.deregisterDrain();return}this.trackSupportabilityMetrics();(0,a.registerHandler)("api-recordCustomEvent",((t,e,r)=>{if(i.RESERVED_EVENT_TYPES.includes(e))return(0,s.warn)(46);this.addEvent({eventType:e,timestamp:this.toEpoch(t),...r})}),this.featureName,this.ee);if(t.init.page_action.enabled){(0,a.registerHandler)("api-addPageAction",((t,e,i,n)=>{if(!this.agentRef.runtime.entityManager.get(n))return(0,s.warn)(56,this.featureName);this.addEvent({...i,eventType:"PageAction",timestamp:this.toEpoch(t),timeSinceLoad:t/1e3,actionName:e,referrerUrl:this.referrerUrl,...r.isBrowserScope&&{browserWidth:window.document.documentElement?.clientWidth,browserHeight:window.document.documentElement?.clientHeight}},n)}),this.featureName,this.ee)}let o=()=>{};if(r.isBrowserScope&&t.init.user_actions.enabled){this.userActionAggregator=new l.UserActionsAggregator;this.harvestOpts.beforeUnload=()=>o?.(this.userActionAggregator.aggregationEvent);o=t=>{try{if(t?.event){const{target:e,timeStamp:i,type:r}=t.event;this.addEvent({eventType:"UserAction",timestamp:this.toEpoch(i),action:r,actionCount:t.count,actionDuration:t.relativeMs[t.relativeMs.length-1],actionMs:t.relativeMs,rageClick:t.rageClick,target:t.selectorPath,...(0,h.isIFrameWindow)(window)&&{iframe:true},...this.agentRef.init.user_actions.elementAttributes.reduce(((t,i)=>{if(s(i))t[n(i)]=String(e[i]).trim().slice(0,128);return t}),{}),...t.nearestTargetFields});function n(t){if(t==="tagName")t="tag";if(t==="className")t="class";return"target".concat(t.charAt(0).toUpperCase()+t.slice(1))}function s(i){return!!(t.selectorPath!=="window"&&t.selectorPath!=="document"&&e instanceof HTMLElement&&e?.[i])}}}catch(o){}};(0,a.registerHandler)("ua",(t=>{o(this.userActionAggregator.process(t,this.agentRef.init.user_actions.elementAttributes))}),this.featureName,this.ee)}const u=[...t.init.performance.capture_marks?["mark"]:[],...t.init.performance.capture_measures?["measure"]:[]];if(u.length){try{u.forEach((i=>{if(PerformanceObserver.supportedEntryTypes.includes(i)){const r=new PerformanceObserver((r=>{r.getEntries().forEach((r=>{try{this.reportSupportabilityMetric("Generic/Performance/"+i+"/Seen");const n=t.init.performance.capture_detail?s(r.detail):{};this.addEvent({...n,eventType:"BrowserPerformance",timestamp:this.toEpoch(r.startTime),entryName:(0,e.cleanURL)(r.name),entryDuration:r.duration,entryType:i});function s(t){if(t===null||t===undefined)return{};else if(!(0,v.isPureObject)(t))return{entryDetail:t};else return e(t);function e(t,i="entryDetail"){let r={};if(t===null||t===undefined)return r;Object.keys(t).forEach((n=>{let s=i+"."+n;if((0,v.isPureObject)(t[n])){Object.assign(r,e(t[n],s))}else{if(t[n]!==null&&t[n]!==undefined)r[s]=t[n]}}));return r}}}catch(o){}}))}));r.observe({buffered:true,type:i})}}))}catch(t){}}if(r.isBrowserScope&&t.init.performance.resources.enabled){(0,a.registerHandler)("browserPerformance.resource",(e=>{try{const{name:i,duration:n,...s}=e.toJSON();let o=false;try{const e=new URL(i).hostname;const n=e.includes("newrelic.com")||e.includes("nr-data.net")||e.includes("nr-local.net");if(this.agentRef.init.performance.resources.ignore_newrelic&&n)return;if(this.agentRef.init.performance.resources.asset_types.length&&!this.agentRef.init.performance.resources.asset_types.includes(s.initiatorType))return;o=e===r.globalScope?.location.hostname||t.init.performance.resources.first_party_domains.includes(e);if(o)this.reportSupportabilityMetric("Generic/Performance/FirstPartyResource/Seen");if(n)this.reportSupportabilityMetric("Generic/Performance/NrResource/Seen")}catch(t){}this.reportSupportabilityMetric("Generic/Performance/Resource/Seen");const a={...s,eventType:"BrowserPerformance",timestamp:Math.floor(t.runtime.timeKeeper.correctRelativeTimestamp(s.startTime)),entryName:i,entryDuration:n,firstParty:o};this.addEvent(a)}catch(t){this.ee.emit("internal-error",[t,"GenericEvents-Resource"])}}),this.featureName,this.ee)}t.runtime.harvester.triggerHarvestFor(this);this.drain()}))}addEvent(i={},n){if(!i||!Object.keys(i).length)return;if(!i.eventType){(0,s.warn)(44);return}for(let e in i){let r=i[e];i[e]=r&&typeof r==="object"?(0,t.stringify)(r):r}const a={timestamp:Math.floor(this.agentRef.runtime.timeKeeper.correctRelativeTimestamp((0,o.now)())),pageUrl:(0,e.cleanURL)(""+r.initialLocation),currentUrl:(0,e.cleanURL)(""+location)};const f={...this.agentRef.info.jsAttributes||{},...a,...i};const l=this.events.add(f,n);if(!l&&!this.events.isEmpty(undefined,n)){this.ee.emit(u.SUPPORTABILITY_METRIC_CHANNEL,["GenericEvents/Harvest/Max/Seen"]);this.agentRef.runtime.harvester.triggerHarvestFor(this,{targetEntityGuid:n});this.events.add(f)}}serializer(t){return(0,f.applyFnToProps)({ins:t},this.obfuscator.obfuscateString.bind(this.obfuscator),"string")}queryStringsBuilder(){return{ua:this.agentRef.info.userAttributes,at:this.agentRef.info.atts}}toEpoch(t){return Math.floor(this.agentRef.runtime.timeKeeper.correctRelativeTimestamp(t))}trackSupportabilityMetrics(){const t="Config/Performance/";if(this.agentRef.init.performance.capture_marks)this.reportSupportabilityMetric(t+"CaptureMarks/Enabled");if(this.agentRef.init.performance.capture_measures)this.reportSupportabilityMetric(t+"CaptureMeasures/Enabled");if(this.agentRef.init.performance.resources.enabled)this.reportSupportabilityMetric(t+"Resources/Enabled");if(this.agentRef.init.performance.resources.asset_types?.length!==0)this.reportSupportabilityMetric(t+"Resources/AssetTypes/Changed");if(this.agentRef.init.performance.resources.first_party_domains?.length!==0)this.reportSupportabilityMetric(t+"Resources/FirstPartyDomains/Changed");if(this.agentRef.init.performance.resources.ignore_newrelic===false)this.reportSupportabilityMetric(t+"Resources/IgnoreNewrelic/Changed")}}Nr.Aggregate=p;return Nr}var $r={};var Jr={};var Kr;function qr(){if(Kr)return Jr;Kr=1;Object.defineProperty(Jr,"__esModule",{value:true});Jr.Log=void 0;var t=d();var e=Mi();var i=zt();class r{timestamp;message;attributes;level;constructor(r,n,s={},o=i.LOG_LEVELS.INFO){this.timestamp=r;this.message=n;this.attributes={...s,pageUrl:(0,e.cleanURL)(""+t.initialLocation)};this.level=o.toUpperCase()}}Jr.Log=r;return Jr}var Qr;function tn(){if(Qr)return $r;Qr=1;Object.defineProperty($r,"__esModule",{value:true});$r.Aggregate=void 0;var t=F();var e=Q();var i=dt();var r=$i();var n=zt();var s=qr();var o=be();var a=Tr();var u=Bi();var f=Oe();var l=bt();var c=ve();var h=rt();class d extends r.AggregateBase{static featureName=n.FEATURE_NAME;constructor(e){super(e,n.FEATURE_NAME);this.isSessionTrackingEnabled=(0,h.canEnableSessionTracking)(e.init)&&e.runtime.session;this.ee.on(l.SESSION_EVENTS.RESET,(()=>{this.abort(c.ABORT_REASONS.RESET)}));this.ee.on(l.SESSION_EVENTS.UPDATE,((t,e)=>{if(this.blocked||t!==l.SESSION_EVENT_TYPES.CROSS_TAB)return;if(this.loggingMode!==n.LOGGING_MODE.OFF&&e.loggingMode===n.LOGGING_MODE.OFF)this.abort(c.ABORT_REASONS.CROSS_TAB);else this.loggingMode=e.loggingMode}));this.harvestOpts.raw=true;this.waitForFlags(["log"]).then((([i])=>{const r=this.agentRef.runtime.session??{};if(this.loggingMode===n.LOGGING_MODE.OFF||r.isNew&&i===n.LOGGING_MODE.OFF){this.blocked=true;this.deregisterDrain();return}if(r.isNew||!this.isSessionTrackingEnabled){this.updateLoggingMode(i)}else{this.loggingMode=r.state.loggingMode}(0,t.registerHandler)(n.LOGGING_EVENT_EMITTER_CHANNEL,this.handleLog.bind(this),this.featureName,this.ee);this.drain();e.runtime.harvester.triggerHarvestFor(this)}))}updateLoggingMode(t){this.loggingMode=t;this.syncWithSessionManager({loggingMode:this.loggingMode})}handleLog(t,r,a={},f=n.LOG_LEVELS.INFO,l){if(!this.agentRef.runtime.entityManager.get(l))return(0,e.warn)(56,this.featureName);if(this.blocked||!this.loggingMode)return;if(!a||typeof a!=="object")a={};if(typeof f==="string")f=f.toUpperCase();if(!(0,o.isValidLogLevel)(f))return(0,e.warn)(30,f);if(this.loggingMode<(n.LOGGING_MODE[f]||Infinity)){this.reportSupportabilityMetric("Logging/Event/Dropped/Sampling");return}try{if(typeof r!=="string"){const t=(0,i.stringify)(r);if(!!t&&t!=="{}")r=t;else r=String(r)}}catch(t){(0,e.warn)(16,r);this.reportSupportabilityMetric("Logging/Event/Dropped/Casting");return}if(typeof r!=="string"||!r)return(0,e.warn)(32);const c=new s.Log(Math.floor(this.agentRef.runtime.timeKeeper.correctRelativeTimestamp(t)),r,a,f);const h=c.message.length+(0,i.stringify)(c.attributes).length+c.level.length+10;const d="Logging/Harvest/Failed/Seen";if(h>u.MAX_PAYLOAD_SIZE){this.reportSupportabilityMetric(d,h);(0,e.warn)(31,c.message.slice(0,25)+"...");return}if(this.events.wouldExceedMaxSize(h,l)){this.reportSupportabilityMetric("Logging/Harvest/Early/Seen",this.events.byteSize()+h);this.agentRef.runtime.harvester.triggerHarvestFor(this,{targetEntityGuid:l})}if(!this.events.add(c,l)){this.reportSupportabilityMetric(d,h);(0,e.warn)(31,c.message.slice(0,25)+"...")}else{this.reportSupportabilityMetric("Logging/Event/Added/Seen")}}serializer(t,e){const i=this.agentRef.runtime.entityManager.get(e);const r=this.agentRef.runtime.session;return[{common:{attributes:{"entity.guid":i.entityGuid,...r&&{session:r.state.value||"0",hasReplay:r.state.sessionReplayMode===1&&(0,f.isContainerAgentTarget)(i,this.agentRef),hasTrace:r.state.sessionTraceMode===1},ptid:this.agentRef.runtime.ptid,appId:i.applicationID||this.agentRef.info.applicationID,standalone:Boolean(this.agentRef.info.sa),agentVersion:this.agentRef.runtime.version,"instrumentation.provider":"browser","instrumentation.version":this.agentRef.runtime.version,"instrumentation.name":this.agentRef.runtime.loaderType,...this.agentRef.info.jsAttributes}},logs:(0,a.applyFnToProps)(t,this.obfuscator.obfuscateString.bind(this.obfuscator),"string")}]}queryStringsBuilder(t,e){const i=this.agentRef.runtime.entityManager.get(e);return{browser_monitoring_key:i.licenseKey}}abort(t={}){this.reportSupportabilityMetric("Logging/Abort/".concat(t.sm));this.blocked=true;if(this.events){this.events.clear();this.events.clearSave()}this.updateLoggingMode(n.LOGGING_MODE.OFF);this.deregisterDrain()}syncWithSessionManager(t={}){if(this.isSessionTrackingEnabled){this.agentRef.runtime.session.write(t)}}}$r.Aggregate=d;return $r}var en={};var rn={};var nn;function sn(){if(nn)return rn;nn=1;Object.defineProperty(rn,"__esModule",{value:true});rn.getFrameworks=i;var t=d();const e={REACT:"React",NEXTJS:"NextJS",VUE:"Vue",NUXTJS:"NuxtJS",ANGULAR:"Angular",ANGULARUNIVERSAL:"AngularUniversal",SVELTE:"Svelte",SVELTEKIT:"SvelteKit",PREACT:"Preact",PREACTSSR:"PreactSSR",ANGULARJS:"AngularJS",BACKBONE:"Backbone",EMBER:"Ember",METEOR:"Meteor",ZEPTO:"Zepto",JQUERY:"Jquery",MOOTOOLS:"MooTools",QWIK:"Qwik",ELECTRON:"Electron"};function i(){if(!t.isBrowserScope)return[];const i=[];try{if(r()){i.push(e.REACT);if(n())i.push(e.NEXTJS)}if(s()){i.push(e.VUE);if(o())i.push(e.NUXTJS)}if(a()){i.push(e.ANGULAR);if(u())i.push(e.ANGULARUNIVERSAL)}if(f()){i.push(e.SVELTE);if(l())i.push(e.SVELTEKIT)}if(c()){i.push(e.PREACT);if(h())i.push(e.PREACTSSR)}if(v())i.push(e.ANGULARJS);if(Object.prototype.hasOwnProperty.call(window,"Backbone"))i.push(e.BACKBONE);if(Object.prototype.hasOwnProperty.call(window,"Ember"))i.push(e.EMBER);if(Object.prototype.hasOwnProperty.call(window,"Meteor"))i.push(e.METEOR);if(Object.prototype.hasOwnProperty.call(window,"Zepto"))i.push(e.ZEPTO);if(Object.prototype.hasOwnProperty.call(window,"jQuery"))i.push(e.JQUERY);if(Object.prototype.hasOwnProperty.call(window,"MooTools"))i.push(e.MOOTOOLS);if(Object.prototype.hasOwnProperty.call(window,"qwikevents"))i.push(e.QWIK);if(p())i.push(e.ELECTRON)}catch(t){}return i}function r(){try{return Object.prototype.hasOwnProperty.call(window,"React")||Object.prototype.hasOwnProperty.call(window,"ReactDOM")||Object.prototype.hasOwnProperty.call(window,"ReactRedux")||document.querySelector("[data-reactroot], [data-reactid]")||(()=>{const t=document.querySelectorAll("body > div");for(let e=0;e<t.length;e++){if(Object.prototype.hasOwnProperty.call(t[e],"_reactRootContainer")){return true}}})()}catch(t){return false}}function n(){try{return Object.prototype.hasOwnProperty.call(window,"next")&&Object.prototype.hasOwnProperty.call(window.next,"version")}catch(t){return false}}function s(){try{return Object.prototype.hasOwnProperty.call(window,"Vue")}catch(t){return false}}function o(){try{return Object.prototype.hasOwnProperty.call(window,"$nuxt")&&Object.prototype.hasOwnProperty.call(window.$nuxt,"nuxt")}catch(t){return false}}function a(){try{return Object.prototype.hasOwnProperty.call(window,"ng")||document.querySelector("[ng-version]")}catch(t){return false}}function u(){try{return document.querySelector("[ng-server-context]")}catch(t){return false}}function f(){try{return Object.prototype.hasOwnProperty.call(window,"__svelte")}catch(t){return false}}function l(){try{return!!Object.keys(window).find((t=>t.startsWith("__sveltekit")))}catch(t){return false}}function c(){try{return Object.prototype.hasOwnProperty.call(window,"preact")}catch(t){return false}}function h(){try{return document.querySelector('script[type="__PREACT_CLI_DATA__"]')}catch(t){return false}}function v(){try{return Object.prototype.hasOwnProperty.call(window,"angular")||document.querySelector(".ng-binding, [ng-app], [data-ng-app], [ng-controller], [data-ng-controller], [ng-repeat], [data-ng-repeat]")||document.querySelector('script[src*="angular.js"], script[src*="angular.min.js"]')}catch(t){return false}}function p(){try{return typeof navigator==="object"&&typeof navigator.userAgent==="string"&&navigator.userAgent.indexOf("Electron")>=0}catch(t){return false}}return rn}var on;function an(){if(on)return en;on=1;Object.defineProperty(en,"__esModule",{value:true});en.Aggregate=void 0;var t=F();var e=Ut();var i=sn();var r=hi();var n=J();var s=X();var o=d();var a=$i();var u=Yr();class f extends a.AggregateBase{static featureName=e.FEATURE_NAME;constructor(i){super(i,e.FEATURE_NAME);this.harvestOpts.aggregatorTypes=["cm","sm"];this.agentNonce=o.isBrowserScope&&document.currentScript?.nonce;this.waitForFlags(["err"]).then((([t])=>{if(t){this.singleChecks();this.eachSessionChecks();this.drain()}else{this.blocked=true;this.deregisterDrain()}}));(0,t.registerHandler)(e.SUPPORTABILITY_METRIC_CHANNEL,this.storeSupportabilityMetrics.bind(this),this.featureName,this.ee);(0,t.registerHandler)(e.CUSTOM_METRIC_CHANNEL,this.storeEventMetrics.bind(this),this.featureName,this.ee)}preHarvestChecks(t){return this.drained&&t.isFinalHarvest}storeSupportabilityMetrics(t,i){if(this.blocked)return;const r=e.SUPPORTABILITY_METRIC;const n={name:t};this.events.addMetric(r,t,n,i)}storeEventMetrics(t,i){if(this.blocked)return;const r=e.CUSTOM_METRIC;const n={name:t};this.events.add([r,t,n,i])}singleChecks(){const{distMethod:t,loaderType:e}=this.agentRef.runtime;const{proxy:s,privacy:a}=this.agentRef.init;if(e)this.storeSupportabilityMetrics("Generic/LoaderType/".concat(e,"/Detected"));if(t)this.storeSupportabilityMetrics("Generic/DistMethod/".concat(t,"/Detected"));if(o.isBrowserScope){this.storeSupportabilityMetrics("Generic/Runtime/Browser/Detected");if(this.agentNonce&&this.agentNonce!==""){this.storeSupportabilityMetrics("Generic/Runtime/Nonce/Detected")}(0,n.onDOMContentLoaded)((()=>{(0,i.getFrameworks)().forEach((t=>{this.storeSupportabilityMetrics("Framework/"+t+"/Detected")}))}));if(!a.cookies_enabled)this.storeSupportabilityMetrics("Config/SessionTracking/Disabled")}else if(o.isWorkerScope){this.storeSupportabilityMetrics("Generic/Runtime/Worker/Detected")}else{this.storeSupportabilityMetrics("Generic/Runtime/Unknown/Detected")}if((0,r.isFileProtocol)()){this.storeSupportabilityMetrics("Generic/FileProtocol/Detected")}if(this.obfuscator.obfuscateConfigRules.length>0){this.storeSupportabilityMetrics("Generic/Obfuscate/Detected")}if(s.assets)this.storeSupportabilityMetrics("Config/AssetsUrl/Changed");if(s.beacon)this.storeSupportabilityMetrics("Config/BeaconUrl/Changed");if(o.isBrowserScope&&window.MutationObserver){if((0,u.isIFrameWindow)(window)){this.storeSupportabilityMetrics("Generic/Runtime/IFrame/Detected")}const t=window.document.querySelectorAll("video").length;if(t)this.storeSupportabilityMetrics("Generic/VideoElement/Added",t);const e=window.document.querySelectorAll("iframe").length;if(e)this.storeSupportabilityMetrics("Generic/IFrame/Added",e);const i=new MutationObserver((t=>{t.forEach((t=>{t.addedNodes.forEach((t=>{if(t instanceof HTMLVideoElement){this.storeSupportabilityMetrics("Generic/VideoElement/Added",1)}if(t instanceof HTMLIFrameElement){this.storeSupportabilityMetrics("Generic/IFrame/Added",1)}}))}))}));i.observe(window.document.body,{childList:true,subtree:true})}if(navigator.webdriver)this.storeSupportabilityMetrics("Generic/WebDriver/Detected")}eachSessionChecks(){if(!o.isBrowserScope)return;(0,s.windowAddEventListener)("pageshow",(t=>{if(t?.persisted){this.storeSupportabilityMetrics("Generic/BFCache/PageRestored")}}))}}en.Aggregate=f;return en}var un={};var fn={};var ln;function cn(){if(ln)return fn;ln=1;Object.defineProperty(fn,"__esModule",{value:true});fn.addPN=d;fn.addPT=h;fn.navTimingValues=void 0;var t="Start";var e="End";var i="unloadEvent";var r="redirect";var n="domainLookup";var s="onnect";var o="request";var a="response";var u="loadEvent";var f="domContentLoadedEvent";const l=fn.navTimingValues=[];function c(t){if(typeof t==="number")return t;const e={navigate:undefined,reload:1,back_forward:2,prerender:3};return e[t]}function h(l,c,h={},d=false){if(!c)return;h.of=l;v(h.of,h,"n",true);v(c[i+t],h,"u",d);v(c[r+t],h,"r",d);v(c[i+e],h,"ue",d);v(c[r+e],h,"re",d);v(c["fetch"+t],h,"f",d);v(c[n+t],h,"dn",d);v(c[n+e],h,"dne",d);v(c["c"+s+t],h,"c",d);v(c["secureC"+s+"ion"+t],h,"s",d);v(c["c"+s+e],h,"ce",d);v(c[o+t],h,"rq",d);v(c[a+t],h,"rp",d);v(c[a+e],h,"rpe",d);v(c.domLoading,h,"dl",d);v(c.domInteractive,h,"di",d);v(c[f+t],h,"ds",d);v(c[f+e],h,"de",d);v(c.domComplete,h,"dc",d);v(c[u+t],h,"l",d);v(c[u+e],h,"le",d);return h}function d(t,e){v(c(t.type),e,"ty");v(t.redirectCount,e,"rc");return e}function v(t,e,i,r){if(typeof t==="number"&&t>0){if(r){const i=e?.of>0?e.of:0;t=Math.max(t-i,0)}t=Math.round(t);e[i]=t;l.push(t)}else l.push(undefined)}return fn}var hn={};var dn;function vn(){if(dn)return hn;dn=1;Object.defineProperty(hn,"__esModule",{value:true});hn.FEATURE_NAME=void 0;var t=B();hn.FEATURE_NAME=t.FEATURE_NAMES.pageViewEvent;return hn}var pn={};var mn;function gn(){if(mn)return pn;mn=1;Object.defineProperty(pn,"__esModule",{value:true});pn.getActivatedFeaturesFlags=i;var t=B();var e=p();function i(i){const r=[];const n=(0,e.gosNREUM)();try{Object.keys(n.initializedAgents[i].features).forEach((e=>{switch(e){case t.FEATURE_NAMES.ajax:r.push("xhr");break;case t.FEATURE_NAMES.jserrors:r.push("err");break;case t.FEATURE_NAMES.genericEvents:r.push("ins");break;case t.FEATURE_NAMES.sessionTrace:r.push("stn");break;case t.FEATURE_NAMES.softNav:case t.FEATURE_NAMES.spa:r.push("spa");break}}))}catch(t){}return r}return pn}var yn={};var bn={exports:{}};var wn=bn.exports;var Sn;function kn(){if(Sn)return bn.exports;Sn=1;(function(t,e){!function(t,i){i(e)}(wn,(function(t){var e,i,r=function(){var t=self.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0];if(t&&t.responseStart>0&&t.responseStart<performance.now())return t},n=function(t){if("loading"===document.readyState)return"loading";var e=r();if(e){if(t<e.domInteractive)return"loading";if(0===e.domContentLoadedEventStart||t<e.domContentLoadedEventStart)return"dom-interactive";if(0===e.domComplete||t<e.domComplete)return"dom-content-loaded"}return"complete"},s=function(t){var e=t.nodeName;return 1===t.nodeType?e.toLowerCase():e.toUpperCase().replace(/^#/,"")},o=function(t,e){var i="";try{for(;t&&9!==t.nodeType;){var r=t,n=r.id?"#"+r.id:s(r)+(r.classList&&r.classList.value&&r.classList.value.trim()&&r.classList.value.trim().length?"."+r.classList.value.trim().replace(/\s+/g,"."):"");if(i.length+n.length>100-1)return i||n;if(i=i?n+">"+i:n,r.id)break;t=r.parentNode}}catch(t){}return i},a=-1,u=function(){return a},f=function(t){addEventListener("pageshow",(function(e){e.persisted&&(a=e.timeStamp,t(e))}),true)},l=function(){var t=r();return t&&t.activationStart||0},c=function(t,e){var i=r(),n="navigate";u()>=0?n="back-forward-cache":i&&(document.prerendering||l()>0?n="prerender":document.wasDiscarded?n="restore":i.type&&(n=i.type.replace(/_/g,"-")));return{name:t,value:void 0===e?-1:e,rating:"good",delta:0,entries:[],id:"v4-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:n}},h=function(t,e,i){try{if(PerformanceObserver.supportedEntryTypes.includes(t)){var r=new PerformanceObserver((function(t){Promise.resolve().then((function(){e(t.getEntries())}))}));return r.observe(Object.assign({type:t,buffered:true},i||{})),r}}catch(t){}},d=function(t,e,i,r){var n,s;return function(o){e.value>=0&&(o||r)&&((s=e.value-(n||0))||void 0===n)&&(n=e.value,e.delta=s,e.rating=function(t,e){return t>e[1]?"poor":t>e[0]?"needs-improvement":"good"}(e.value,i),t(e))}},v=function(t){requestAnimationFrame((function(){return requestAnimationFrame((function(){return t()}))}))},p=function(t){document.addEventListener("visibilitychange",(function(){"hidden"===document.visibilityState&&t()}))},m=function(t){var e=false;return function(){e||(t(),e=true)}},g=-1,y=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},b=function(t){"hidden"===document.visibilityState&&g>-1&&(g="visibilitychange"===t.type?t.timeStamp:0,S())},w=function(){addEventListener("visibilitychange",b,true),addEventListener("prerenderingchange",b,true)},S=function(){removeEventListener("visibilitychange",b,true),removeEventListener("prerenderingchange",b,true)},k=function(){return g<0&&(g=y(),w(),f((function(){setTimeout((function(){g=y(),w()}),0)}))),{get firstHiddenTime(){return g}}},C=function(t){document.prerendering?addEventListener("prerenderingchange",(function(){return t()}),true):t()},M=[1800,3e3],I=function(t,e){e=e||{},C((function(){var i,r=k(),n=c("FCP"),s=h("paint",(function(t){t.forEach((function(t){"first-contentful-paint"===t.name&&(s.disconnect(),t.startTime<r.firstHiddenTime&&(n.value=Math.max(t.startTime-l(),0),n.entries.push(t),i(true)))}))}));s&&(i=d(t,n,M,e.reportAllChanges),f((function(r){n=c("FCP"),i=d(t,n,M,e.reportAllChanges),v((function(){n.value=performance.now()-r.timeStamp,i(true)}))})))}))},O=[.1,.25],A=0,T=1/0,_=0,E=function(t){t.forEach((function(t){t.interactionId&&(T=Math.min(T,t.interactionId),_=Math.max(_,t.interactionId),A=_?(_-T)/7+1:0)}))},R=function(){return e?A:performance.interactionCount||0},x=function(){"interactionCount"in performance||e||(e=h("event",E,{type:"event",buffered:true,durationThreshold:0}))},j=[],N=new Map,D=0,F=function(){var t=Math.min(j.length-1,Math.floor((R()-D)/50));return j[t]},L=[],P=function(t){if(L.forEach((function(e){return e(t)})),t.interactionId||"first-input"===t.entryType){var e=j[j.length-1],i=N.get(t.interactionId);if(i||j.length<10||t.duration>e.latency){if(i)t.duration>i.latency?(i.entries=[t],i.latency=t.duration):t.duration===i.latency&&t.startTime===i.entries[0].startTime&&i.entries.push(t);else{var r={id:t.interactionId,latency:t.duration,entries:[t]};N.set(r.id,r),j.push(r)}j.sort((function(t,e){return e.latency-t.latency})),j.length>10&&j.splice(10).forEach((function(t){return N.delete(t.id)}))}}},B=function(t){var e=self.requestIdleCallback||self.setTimeout,i=-1;return t=m(t),"hidden"===document.visibilityState?t():(i=e(t),p(t)),i},U=[200,500],G=function(t,e){"PerformanceEventTiming"in self&&"interactionId"in PerformanceEventTiming.prototype&&(e=e||{},C((function(){var i;x();var r,n=c("INP"),s=function(t){B((function(){t.forEach(P);var e=F();e&&e.latency!==n.value&&(n.value=e.latency,n.entries=e.entries,r())}))},o=h("event",s,{durationThreshold:null!==(i=e.durationThreshold)&&void 0!==i?i:40});r=d(t,n,U,e.reportAllChanges),o&&(o.observe({type:"first-input",buffered:true}),p((function(){s(o.takeRecords()),r(true)})),f((function(){D=R(),j.length=0,N.clear(),n=c("INP"),r=d(t,n,U,e.reportAllChanges)})))})))},W=[],z=[],V=0,H=new WeakMap,Y=new Map,Z=-1,X=function(t){W=W.concat(t),$()},$=function(){Z<0&&(Z=B(J))},J=function(){Y.size>10&&Y.forEach((function(t,e){N.has(e)||Y.delete(e)}));var t=j.map((function(t){return H.get(t.entries[0])})),e=z.length-50;z=z.filter((function(i,r){return r>=e||t.includes(i)}));for(var i=new Set,r=0;r<z.length;r++){var n=z[r];et(n.startTime,n.processingEnd).forEach((function(t){i.add(t)}))}var s=W.length-1-50;W=W.filter((function(t,e){return t.startTime>V&&e>s||i.has(t)})),Z=-1};L.push((function(t){t.interactionId&&t.target&&!Y.has(t.interactionId)&&Y.set(t.interactionId,t.target)}),(function(t){var e,i=t.startTime+t.duration;V=Math.max(V,t.processingEnd);for(var r=z.length-1;r>=0;r--){var n=z[r];if(Math.abs(i-n.renderTime)<=8){(e=n).startTime=Math.min(t.startTime,e.startTime),e.processingStart=Math.min(t.processingStart,e.processingStart),e.processingEnd=Math.max(t.processingEnd,e.processingEnd),e.entries.push(t);break}}e||(e={startTime:t.startTime,processingStart:t.processingStart,processingEnd:t.processingEnd,renderTime:i,entries:[t]},z.push(e)),(t.interactionId||"first-input"===t.entryType)&&H.set(t,e),$()}));var K,q,Q,tt,et=function(t,e){for(var i,r=[],n=0;i=W[n];n++)if(!(i.startTime+i.duration<t)){if(i.startTime>e)break;r.push(i)}return r},it=[2500,4e3],rt={},nt=[800,1800],st=function t(e){document.prerendering?C((function(){return t(e)})):"complete"!==document.readyState?addEventListener("load",(function(){return t(e)}),true):setTimeout(e,0)},ot=function(t,e){e=e||{};var i=c("TTFB"),n=d(t,i,nt,e.reportAllChanges);st((function(){var s=r();s&&(i.value=Math.max(s.responseStart-l(),0),i.entries=[s],n(true),f((function(){i=c("TTFB",0),(n=d(t,i,nt,e.reportAllChanges))(true)})))}))},at={passive:true,capture:true},ut=new Date,ft=function(t,e){K||(K=e,q=t,Q=new Date,ht(removeEventListener),lt())},lt=function(){if(q>=0&&q<Q-ut){var t={entryType:"first-input",name:K.type,target:K.target,cancelable:K.cancelable,startTime:K.timeStamp,processingStart:K.timeStamp+q};tt.forEach((function(e){e(t)})),tt=[]}},ct=function(t){if(t.cancelable){var e=(t.timeStamp>1e12?new Date:performance.now())-t.timeStamp;"pointerdown"==t.type?function(t,e){var i=function(){ft(t,e),n()},r=function(){n()},n=function(){removeEventListener("pointerup",i,at),removeEventListener("pointercancel",r,at)};addEventListener("pointerup",i,at),addEventListener("pointercancel",r,at)}(e,t):ft(e,t)}},ht=function(t){["mousedown","keydown","touchstart","pointerdown"].forEach((function(e){return t(e,ct,at)}))},dt=[100,300],vt=function(t,e){e=e||{},C((function(){var i,r=k(),n=c("FID"),s=function(t){t.startTime<r.firstHiddenTime&&(n.value=t.processingStart-t.startTime,n.entries.push(t),i(true))},o=function(t){t.forEach(s)},a=h("first-input",o);i=d(t,n,dt,e.reportAllChanges),a&&(p(m((function(){o(a.takeRecords()),a.disconnect()}))),f((function(){var r;n=c("FID"),i=d(t,n,dt,e.reportAllChanges),tt=[],q=-1,K=null,ht(addEventListener),r=s,tt.push(r),lt()})))}))};t.CLSThresholds=O,t.FCPThresholds=M,t.FIDThresholds=dt,t.INPThresholds=U,t.LCPThresholds=it,t.TTFBThresholds=nt,t.onCLS=function(t,e){!function(t,e){e=e||{},I(m((function(){var i,r=c("CLS",0),n=0,s=[],o=function(t){t.forEach((function(t){if(!t.hadRecentInput){var e=s[0],i=s[s.length-1];n&&t.startTime-i.startTime<1e3&&t.startTime-e.startTime<5e3?(n+=t.value,s.push(t)):(n=t.value,s=[t])}})),n>r.value&&(r.value=n,r.entries=s,i())},a=h("layout-shift",o);a&&(i=d(t,r,O,e.reportAllChanges),p((function(){o(a.takeRecords()),i(true)})),f((function(){n=0,r=c("CLS",0),i=d(t,r,O,e.reportAllChanges),v((function(){return i()}))})),setTimeout(i,0))})))}((function(e){var i=function(t){var e,i={};if(t.entries.length){var r=t.entries.reduce((function(t,e){return t&&t.value>e.value?t:e}));if(r&&r.sources&&r.sources.length){var s=(e=r.sources).find((function(t){return t.node&&1===t.node.nodeType}))||e[0];s&&(i={largestShiftTarget:o(s.node),largestShiftTime:r.startTime,largestShiftValue:r.value,largestShiftSource:s,largestShiftEntry:r,loadState:n(r.startTime)})}}return Object.assign(t,{attribution:i})}(e);t(i)}),e)},t.onFCP=function(t,e){I((function(e){var i=function(t){var e={timeToFirstByte:0,firstByteToFCP:t.value,loadState:n(u())};if(t.entries.length){var i=r(),s=t.entries[t.entries.length-1];if(i){var o=i.activationStart||0,a=Math.max(0,i.responseStart-o);e={timeToFirstByte:a,firstByteToFCP:t.value-a,loadState:n(t.entries[0].startTime),navigationEntry:i,fcpEntry:s}}}return Object.assign(t,{attribution:e})}(e);t(i)}),e)},t.onFID=function(t,e){vt((function(e){var i=function(t){var e=t.entries[0],i={eventTarget:o(e.target),eventType:e.name,eventTime:e.startTime,eventEntry:e,loadState:n(e.startTime)};return Object.assign(t,{attribution:i})}(e);t(i)}),e)},t.onINP=function(t,e){i||(i=h("long-animation-frame",X)),G((function(e){var i=function(t){var e=t.entries[0],i=H.get(e),r=e.processingStart,s=i.processingEnd,a=i.entries.sort((function(t,e){return t.processingStart-e.processingStart})),u=et(e.startTime,s),f=t.entries.find((function(t){return t.target})),l=f&&f.target||Y.get(e.interactionId),c=[e.startTime+e.duration,s].concat(u.map((function(t){return t.startTime+t.duration}))),h=Math.max.apply(Math,c),d={interactionTarget:o(l),interactionTargetElement:l,interactionType:e.name.startsWith("key")?"keyboard":"pointer",interactionTime:e.startTime,nextPaintTime:h,processedEventEntries:a,longAnimationFrameEntries:u,inputDelay:r-e.startTime,processingDuration:s-r,presentationDelay:Math.max(h-s,0),loadState:n(e.startTime)};return Object.assign(t,{attribution:d})}(e);t(i)}),e)},t.onLCP=function(t,e){!function(t,e){e=e||{},C((function(){var i,r=k(),n=c("LCP"),s=function(t){e.reportAllChanges||(t=t.slice(-1)),t.forEach((function(t){t.startTime<r.firstHiddenTime&&(n.value=Math.max(t.startTime-l(),0),n.entries=[t],i())}))},o=h("largest-contentful-paint",s);if(o){i=d(t,n,it,e.reportAllChanges);var a=m((function(){rt[n.id]||(s(o.takeRecords()),o.disconnect(),rt[n.id]=true,i(true))}));["keydown","click"].forEach((function(t){addEventListener(t,(function(){return B(a)}),{once:true,capture:true})})),p(a),f((function(r){n=c("LCP"),i=d(t,n,it,e.reportAllChanges),v((function(){n.value=performance.now()-r.timeStamp,rt[n.id]=true,i(true)}))}))}}))}((function(e){var i=function(t){var e={timeToFirstByte:0,resourceLoadDelay:0,resourceLoadDuration:0,elementRenderDelay:t.value};if(t.entries.length){var i=r();if(i){var n=i.activationStart||0,s=t.entries[t.entries.length-1],a=s.url&&performance.getEntriesByType("resource").filter((function(t){return t.name===s.url}))[0],u=Math.max(0,i.responseStart-n),f=Math.max(u,a?(a.requestStart||a.startTime)-n:0),l=Math.max(f,a?a.responseEnd-n:0),c=Math.max(l,s.startTime-n);e={element:o(s.element),timeToFirstByte:u,resourceLoadDelay:f-u,resourceLoadDuration:l-f,elementRenderDelay:c-l,navigationEntry:i,lcpEntry:s},s.url&&(e.url=s.url),a&&(e.lcpResourceEntry=a)}}return Object.assign(t,{attribution:e})}(e);t(i)}),e)},t.onTTFB=function(t,e){ot((function(e){var i=function(t){var e={waitingDuration:0,cacheDuration:0,dnsDuration:0,connectionDuration:0,requestDuration:0};if(t.entries.length){var i=t.entries[0],r=i.activationStart||0,n=Math.max((i.workerStart||i.fetchStart)-r,0),s=Math.max(i.domainLookupStart-r,0),o=Math.max(i.connectStart-r,0),a=Math.max(i.connectEnd-r,0);e={waitingDuration:n,cacheDuration:s-n,dnsDuration:o-s,connectionDuration:a-o,requestDuration:t.value-a,navigationEntry:i}}return Object.assign(t,{attribution:e})}(e);t(i)}),e)}}))})(bn,bn.exports);return bn.exports}var Cn={};var Mn;function In(){if(Mn)return Cn;Mn=1;Object.defineProperty(Cn,"__esModule",{value:true});Cn.VITAL_NAMES=void 0;Cn.VITAL_NAMES={FIRST_PAINT:"fp",FIRST_CONTENTFUL_PAINT:"fcp",FIRST_INTERACTION:"fi",LARGEST_CONTENTFUL_PAINT:"lcp",CUMULATIVE_LAYOUT_SHIFT:"cls",INTERACTION_TO_NEXT_PAINT:"inp",TIME_TO_FIRST_BYTE:"ttfb"};return Cn}var On={};var An;function Tn(){if(An)return On;An=1;Object.defineProperty(On,"__esModule",{value:true});On.VitalMetric=void 0;class t{#m=new Set;history=[];constructor(t,e){this.name=t;this.attrs={};this.roundingMethod=typeof e==="function"?e:Math.floor}update({value:t,attrs:e={}}){if(t===undefined||t===null||t<0)return;const i={value:this.roundingMethod(t),name:this.name,attrs:e};this.history.push(i);this.#m.forEach((t=>{try{t(i)}catch(t){}}))}get current(){return this.history[this.history.length-1]||{value:undefined,name:this.name,attrs:{}}}get isValid(){return this.current.value>=0}subscribe(t,e=true){if(typeof t!=="function")return;this.#m.add(t);if(this.isValid&&!!e)this.history.forEach((e=>{t(e)}));return()=>{this.#m.delete(t)}}}On.VitalMetric=t;return On}var _n;function En(){if(_n)return yn;_n=1;Object.defineProperty(yn,"__esModule",{value:true});yn.firstContentfulPaint=void 0;var t=kn();var e=d();var i=In();var r=Tn();const n=yn.firstContentfulPaint=new r.VitalMetric(i.VITAL_NAMES.FIRST_CONTENTFUL_PAINT);if(e.isBrowserScope){if(e.iOSBelow16){try{if(!e.initiallyHidden){const t=performance.getEntriesByType("paint");t.forEach((t=>{if(t.name==="first-contentful-paint"){n.update({value:Math.floor(t.startTime)})}}))}}catch(t){}}else{(0,t.onFCP)((({value:t,attribution:i})=>{if(e.initiallyHidden||n.isValid)return;const r={timeToFirstByte:i.timeToFirstByte,firstByteToFCP:i.firstByteToFCP,loadState:i.loadState};n.update({value:t,attrs:r})}))}}return yn}var Rn={};var xn;function jn(){if(xn)return Rn;xn=1;Object.defineProperty(Rn,"__esModule",{value:true});Rn.firstPaint=void 0;var t=d();var e=In();var i=Tn();const r=Rn.firstPaint=new i.VitalMetric(e.VITAL_NAMES.FIRST_PAINT);if(t.isBrowserScope){const e=t=>{t.forEach((t=>{if(t.name==="first-paint"&&!r.isValid){i.disconnect();r.update({value:t.startTime})}}))};let i;try{if(PerformanceObserver.supportedEntryTypes.includes("paint")&&!t.initiallyHidden){i=new PerformanceObserver((t=>{Promise.resolve().then((()=>{e(t.getEntries())}))}));i.observe({type:"paint",buffered:true})}}catch(t){}}return Rn}var Nn={};var Dn;function Fn(){if(Dn)return Nn;Dn=1;Object.defineProperty(Nn,"__esModule",{value:true});Nn.timeToFirstByte=void 0;var t=d();var e=In();var i=Tn();var r=kn();const n=Nn.timeToFirstByte=new i.VitalMetric(e.VITAL_NAMES.TIME_TO_FIRST_BYTE);if(t.isBrowserScope&&typeof PerformanceNavigationTiming!=="undefined"&&!t.isiOS&&window===window.parent){(0,r.onTTFB)((({value:t,attribution:e})=>{if(n.isValid)return;n.update({value:t,attrs:{navigationEntry:e.navigationEntry}})}))}else{if(!n.isValid){const e={};for(let i in t.globalScope?.performance?.timing||{})e[i]=Math.max(t.globalScope?.performance?.timing[i]-t.originTime,0);n.update({value:e.responseStart,attrs:{navigationEntry:e}})}}return Nn}var Ln={};var Pn;function Bn(){if(Pn)return Ln;Pn=1;Object.defineProperty(Ln,"__esModule",{value:true});Ln.TimeKeeper=void 0;var t=d();class e{#g;#y;#b;#w=false;constructor(t){this.#g=t;this.processStoredDiff()}get ready(){return this.#w}get correctedOriginTime(){return this.#y}get localTimeDiff(){return this.#b}processRumRequest(e,i,r,n){this.processStoredDiff();if(this.#w)return;if(!n)throw new Error("nrServerTime not found");const s=(r-i)/2;const o=i+s;this.#y=Math.floor(n-o);this.#b=t.originTime-this.#y;if(isNaN(this.#y)){throw new Error("Failed to correct browser time to server time")}this.#g?.write({serverTimeDiff:this.#b});this.#w=true}convertRelativeTimestamp(e){return t.originTime+e}convertAbsoluteTimestamp(e){return e-t.originTime}correctAbsoluteTimestamp(t){return t-this.#b}correctRelativeTimestamp(t){return this.correctAbsoluteTimestamp(this.convertRelativeTimestamp(t))}processStoredDiff(){if(this.#w)return;const e=this.#g?.read()?.serverTimeDiff;if(typeof e==="number"&&!isNaN(e)){this.#b=e;this.#y=t.originTime-this.#b;this.#w=true}}}Ln.TimeKeeper=e;return Ln}var Un;function Gn(){if(Un)return un;Un=1;Object.defineProperty(un,"__esModule",{value:true});un.Aggregate=void 0;var t=d();var e=cn();var i=dt();var r=ue();var n=w(vn());var s=gn();var o=Ne();var a=Q();var u=$i();var f=En();var l=jn();var h=Fn();var v=c();var p=Bn();var m=Tr();var g=F();var y=Oe();function b(t){if("function"!=typeof WeakMap)return null;var e=new WeakMap,i=new WeakMap;return(b=function(t){return t?i:e})(t)}function w(t,e){if(t&&t.__esModule)return t;if(null===t||"object"!=typeof t&&"function"!=typeof t)return{default:t};var i=b(e);if(i&&i.has(t))return i.get(t);var r={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in t)if("default"!==s&&{}.hasOwnProperty.call(t,s)){var o=n?Object.getOwnPropertyDescriptor(t,s):null;o&&(o.get||o.set)?Object.defineProperty(r,s,o):r[s]=t[s]}return r.default=t,i&&i.set(t,r),r}class S extends u.AggregateBase{static featureName=n.FEATURE_NAME;constructor(e){super(e,n.FEATURE_NAME);this.timeToFirstByte=0;this.firstByteToWindowLoad=0;this.firstByteToDomContent=0;(0,g.registerHandler)("send-rum",((t,e)=>{this.sendRum(t,e)}),this.featureName,this.ee);if(!(0,r.isValid)(e.info)){this.ee.abort();return(0,a.warn)(43)}e.runtime.timeKeeper=new p.TimeKeeper(e.runtime.session);if(t.isBrowserScope){h.timeToFirstByte.subscribe((({value:t,attrs:e})=>{const i=e.navigationEntry;this.timeToFirstByte=Math.max(t,this.timeToFirstByte);this.firstByteToWindowLoad=Math.max(Math.round(i.loadEventEnd-this.timeToFirstByte),this.firstByteToWindowLoad);this.firstByteToDomContent=Math.max(Math.round(i.domContentLoadedEventEnd-this.timeToFirstByte),this.firstByteToDomContent);this.sendRum()}))}else{this.sendRum()}}sendRum(r=this.agentRef.info.jsAttributes,n={licenseKey:this.agentRef.info.licenseKey,applicationID:this.agentRef.info.applicationID}){const o=this.agentRef.info;const a={};if(o.queueTime)a.qt=o.queueTime;if(o.applicationTime)a.ap=o.applicationTime;a.be=this.timeToFirstByte;a.fe=this.firstByteToWindowLoad;a.dc=this.firstByteToDomContent;const u={tt:o.ttGuid,us:o.user,ac:o.account,pr:o.product,af:(0,s.getActivatedFeaturesFlags)(this.agentIdentifier).join(","),...a,xx:o.extra,ua:o.userAttributes,at:o.atts};if(this.agentRef.runtime.session)u.fsh=Number(this.agentRef.runtime.session.isNew);let c;if(typeof r==="object"&&Object.keys(r).length>0){c=(0,m.applyFnToProps)({ja:r},this.obfuscator.obfuscateString.bind(this.obfuscator),"string")}if(t.globalScope.performance){if(typeof PerformanceNavigationTiming!=="undefined"){const r=t.globalScope?.performance?.getEntriesByType("navigation")?.[0];const n={timing:(0,e.addPT)(t.originTime,r,{}),navigation:(0,e.addPN)(r,{})};u.perf=(0,i.stringify)(n)}else if(typeof PerformanceTiming!=="undefined"){const r={timing:(0,e.addPT)(t.originTime,t.globalScope.performance.timing,{},true),navigation:(0,e.addPN)(t.globalScope.performance.navigation,{})};u.perf=(0,i.stringify)(r)}}u.fp=l.firstPaint.current.value;u.fcp=f.firstContentfulPaint.current.value;const h=this.agentRef.runtime.timeKeeper;if(h?.ready){u.timestamp=Math.floor(h.correctRelativeTimestamp((0,v.now)()))}this.rumStartTime=(0,v.now)();this.agentRef.runtime.harvester.triggerHarvestFor(this,{directSend:{targetApp:n,payload:{qs:u,body:c}},needResponse:true,sendEmptyBody:true})}postHarvestCleanup({status:t,responseText:e,xhr:i,targetApp:r}){const n=(0,v.now)();let s,u;try{({app:s,...u}=JSON.parse(e));this.processEntities(s.agents,r)}catch(t){(0,a.warn)(53,t)}if(!(0,y.isContainerAgentTarget)(r,this.agentRef))return;if(t>=400||t===0){(0,a.warn)(18,t);this.ee.abort();return}try{this.agentRef.runtime.timeKeeper.processRumRequest(i,this.rumStartTime,n,s.nrServerTime);if(!this.agentRef.runtime.timeKeeper.ready)throw new Error("TimeKeeper not ready")}catch(t){this.ee.abort();(0,a.warn)(17,t);return}if(!Object.keys(this.agentRef.runtime.appMetadata).length)this.agentRef.runtime.appMetadata=s;this.drain();this.agentRef.runtime.harvester.startTimer();(0,o.activateFeatures)(u,this.agentRef)}processEntities(t,e){if(!t||!e)return;t.forEach((t=>{const i=this.agentRef.runtime.entityManager;const r=t.entityGuid;const n=i.get(r);if(n)return;if((0,y.isContainerAgentTarget)(e,this.agentRef)){i.setDefaultEntity({...e,entityGuid:r})}i.set(t.entityGuid,{...e,entityGuid:r})}))}}un.Aggregate=S;return un}var Wn={};var zn={};var Vn;function Hn(){if(Vn)return zn;Vn=1;Object.defineProperty(zn,"__esModule",{value:true});zn.FEATURE_NAME=void 0;var t=B();zn.FEATURE_NAME=t.FEATURE_NAMES.pageViewTiming;return zn}var Yn={};var Zn;function Xn(){if(Zn)return Yn;Zn=1;Object.defineProperty(Yn,"__esModule",{value:true});Yn.cumulativeLayoutShift=void 0;var t=kn();var e=In();var i=Tn();var r=d();const n=Yn.cumulativeLayoutShift=new i.VitalMetric(e.VITAL_NAMES.CUMULATIVE_LAYOUT_SHIFT,(t=>t));if(r.isBrowserScope){(0,t.onCLS)((({value:t,attribution:e,id:i})=>{const r={metricId:i,largestShiftTarget:e.largestShiftTarget,largestShiftTime:e.largestShiftTime,largestShiftValue:e.largestShiftValue,loadState:e.loadState};n.update({value:t,attrs:r})}),{reportAllChanges:true})}return Yn}var $n={};var Jn;function Kn(){if(Jn)return $n;Jn=1;Object.defineProperty($n,"__esModule",{value:true});$n.interactionToNextPaint=void 0;var t=kn();var e=Tn();var i=In();var r=d();const n=$n.interactionToNextPaint=new e.VitalMetric(i.VITAL_NAMES.INTERACTION_TO_NEXT_PAINT);if(r.isBrowserScope){(0,t.onINP)((({value:t,attribution:e,id:i})=>{const r={metricId:i,eventTarget:e.interactionTarget,eventTime:e.interactionTime,interactionTarget:e.interactionTarget,interactionTime:e.interactionTime,interactionType:e.interactionType,inputDelay:e.inputDelay,nextPaintTime:e.nextPaintTime,processingDuration:e.processingDuration,presentationDelay:e.presentationDelay,loadState:e.loadState};n.update({value:t,attrs:r})}))}return $n}var qn={};var Qn;function ts(){if(Qn)return qn;Qn=1;Object.defineProperty(qn,"__esModule",{value:true});qn.largestContentfulPaint=void 0;var t=kn();var e=Tn();var i=In();var r=d();var n=Mi();const s=qn.largestContentfulPaint=new e.VitalMetric(i.VITAL_NAMES.LARGEST_CONTENTFUL_PAINT);if(r.isBrowserScope){(0,t.onLCP)((({value:t,attribution:e})=>{if(r.initiallyHidden||s.isValid)return;let i;const o=e.lcpEntry;if(o){i={size:o.size,eid:o.id,element:e.element,timeToFirstByte:e.timeToFirstByte,resourceLoadDelay:e.resourceLoadDelay,resourceLoadDuration:e.resourceLoadDuration,resourceLoadTime:e.resourceLoadDuration,elementRenderDelay:e.elementRenderDelay};if(e.url)i.elUrl=(0,n.cleanURL)(e.url);if(o.element?.tagName)i.elTag=o.element.tagName}s.update({value:t,attrs:i})}))}return qn}var es={};var is;function rs(){if(is)return es;is=1;Object.defineProperty(es,"__esModule",{value:true});es.eventOrigin=t;function t(t,e,i){let r="unknown";if(t&&t instanceof XMLHttpRequest){const e=i.context(t).params;if(!e||!e.status||!e.method||!e.host||!e.pathname)return"xhrOriginMissing";r=e.status+" "+e.method+": "+e.host+e.pathname}else if(t&&typeof t.tagName==="string"){r=t.tagName.toLowerCase();if(t.id)r+="#"+t.id;if(t.className){for(let e=0;e<t.classList.length;e++)r+="."+t.classList[e]}}if(r==="unknown"){if(typeof e==="string")r=e;else if(e===document)r="document";else if(e===window)r="window";else if(e instanceof FileReader)r="FileReader"}return r}return es}var ns;function ss(){if(ns)return Wn;ns=1;Object.defineProperty(Wn,"__esModule",{value:true});Wn.Aggregate=void 0;var t=nr();var e=F();var i=R();var r=Hn();var n=B();var s=$i();var o=Xn();var a=En();var u=jn();var f=Kn();var l=ts();var c=Fn();var h=Ct();var v=In();var p=d();var m=rs();class g extends s.AggregateBase{static featureName=r.FEATURE_NAME;#S=({name:t,value:e,attrs:i})=>{this.addTiming(t,e,i)};constructor(t){super(t,r.FEATURE_NAME);this.curSessEndRecorded=false;this.firstIxnRecorded=false;(0,e.registerHandler)("docHidden",(t=>this.endCurrentSession(t)),this.featureName,this.ee);(0,e.registerHandler)("winPagehide",(t=>this.addTiming("unload",t,null)),this.featureName,this.ee);this.waitForFlags([]).then((()=>{u.firstPaint.subscribe(this.#S);a.firstContentfulPaint.subscribe(this.#S);l.largestContentfulPaint.subscribe(this.#S);f.interactionToNextPaint.subscribe(this.#S);c.timeToFirstByte.subscribe((({attrs:t})=>{this.addTiming("load",Math.round(t.navigationEntry.loadEventEnd))}));(0,h.subscribeToVisibilityChange)((()=>{const{name:t,value:e,attrs:i}=o.cumulativeLayoutShift.current;if(e===undefined)return;this.addTiming(t,e*1e3,i)}),true,true);this.drain()}))}endCurrentSession(t){if(!this.curSessEndRecorded){this.addTiming("pageHide",t,null);this.curSessEndRecorded=true}}addTiming(t,e,r){r=r||{};y(r);if(t!==v.VITAL_NAMES.CUMULATIVE_LAYOUT_SHIFT&&o.cumulativeLayoutShift.current.value>=0){r.cls=o.cumulativeLayoutShift.current.value}const s={name:t,value:e,attrs:r};this.events.add(s);(0,i.handle)("pvtAdded",[t,e,r],undefined,n.FEATURE_NAMES.sessionTrace,this.ee);this.checkForFirstInteraction();return s}checkForFirstInteraction(){if(this.firstIxnRecorded||p.initiallyHidden||!performance)return;const t=performance.getEntriesByType("first-input")[0];if(!t)return;this.firstIxnRecorded=true;this.addTiming("fi",t.startTime,{type:t.name,eventTarget:(0,m.eventOrigin)(t.target),loadState:document.readyState})}appendGlobalCustomAttributes(t){var e=t.attrs||{};var i=["size","eid","cls","type","fid","elTag","elUrl","net-type","net-etype","net-rtt","net-dlink"];Object.entries(this.agentRef.info.jsAttributes||{}).forEach((([t,r])=>{if(i.indexOf(t)<0){e[t]=r}}))}preHarvestChecks(){this.checkForFirstInteraction();return super.preHarvestChecks()}serializer(e){var i=(0,t.getAddStringContext)(this.agentRef.runtime.obfuscator);var r="bel.6;";for(var n=0;n<e.length;n++){var s=e[n];r+="e,";r+=i(s.name)+",";r+=(0,t.nullable)(s.value,t.numeric,false)+",";this.appendGlobalCustomAttributes(s);var o=(0,t.addCustomAttributes)(s.attrs,i);if(o&&o.length>0){r+=(0,t.numeric)(o.length)+";"+o.join(";")}if(n+1<e.length)r+=";"}return r}}Wn.Aggregate=g;function y(t){var e=navigator.connection||navigator.mozConnection||navigator.webkitConnection;if(!e)return;if(e.type)t["net-type"]=e.type;if(e.effectiveType)t["net-etype"]=e.effectiveType;if(e.rtt)t["net-rtt"]=e.rtt;if(e.downlink)t["net-dlink"]=e.downlink}return Wn}var os={};var as={};var us;function fs(){if(us)return as;us=1;Object.defineProperty(as,"__esModule",{value:true});as.sharedChannel=void 0;let t;const e=new Promise((e=>{t=e}));as.sharedChannel=Object.freeze({onReplayReady:t,sessionReplayInitialized:e});return as}var ls={};var cs;function hs(){if(cs)return ls;cs=1;Object.defineProperty(ls,"__esModule",{value:true});ls.stylesheetEvaluator=void 0;var t=p();var e=d();class i{#k=new WeakSet;#C=[];invalidStylesheetsDetected=false;failedToFix=0;evaluate(){let t=0;this.#C=[];if(e.isBrowserScope){for(let t=0;t<Object.keys(document.styleSheets).length;t++){if(!this.#k.has(document.styleSheets[t])&&document.styleSheets[t]instanceof CSSStyleSheet){this.#k.add(document.styleSheets[t])}}}return t}async fix(){await Promise.all(this.#C.map((t=>this.#M(t))));this.#C=[];const t=this.failedToFix;this.failedToFix=0;return t}async#M(e){if(!e?.href)return;try{const i=await(0,t.gosNREUMOriginals)().o.FETCH.bind(window)(e.href);if(!i.ok){this.failedToFix++;return}const r=await i.text();try{const t=new CSSStyleSheet;await t.replace(r);Object.defineProperty(e,"cssRules",{get(){return t.cssRules}});Object.defineProperty(e,"rules",{get(){return t.rules}})}catch(t){Object.defineProperty(e,"cssText",{get(){return r}});this.failedToFix++}}catch(t){this.failedToFix++}}}ls.stylesheetEvaluator=new i;return ls}var ds={};var vs=Object.defineProperty;var ps=(t,e,i)=>e in t?vs(t,e,{enumerable:true,configurable:true,writable:true,value:i}):t[e]=i;var ms=(t,e,i)=>ps(t,typeof e!=="symbol"?e+"":e,i);var gs;var ys=Object.defineProperty;var bs=(t,e,i)=>e in t?ys(t,e,{enumerable:true,configurable:true,writable:true,value:i}):t[e]=i;var ws=(t,e,i)=>bs(t,typeof e!=="symbol"?e+"":e,i);var Ss=(t=>{t[t["Document"]=0]="Document";t[t["DocumentType"]=1]="DocumentType";t[t["Element"]=2]="Element";t[t["Text"]=3]="Text";t[t["CDATA"]=4]="CDATA";t[t["Comment"]=5]="Comment";return t})(Ss||{});const ks={Node:["childNodes","parentNode","parentElement","textContent"],ShadowRoot:["host","styleSheets"],Element:["shadowRoot","querySelector","querySelectorAll"],MutationObserver:[]};const Cs={Node:["contains","getRootNode"],ShadowRoot:["getSelection"],Element:[],MutationObserver:["constructor"]};const Ms={};const Is=()=>!!globalThis.Zone;function Os(t){if(Ms[t])return Ms[t];const e=globalThis[t];const i=e.prototype;const r=t in ks?ks[t]:void 0;const n=Boolean(r&&r.every((t=>{var e,r;return Boolean((r=(e=Object.getOwnPropertyDescriptor(i,t))==null?void 0:e.get)==null?void 0:r.toString().includes("[native code]"))})));const s=t in Cs?Cs[t]:void 0;const o=Boolean(s&&s.every((t=>{var e;return typeof i[t]==="function"&&((e=i[t])==null?void 0:e.toString().includes("[native code]"))})));if(n&&o&&!Is()){Ms[t]=e.prototype;return e.prototype}try{const r=document.createElement("iframe");document.body.appendChild(r);const n=r.contentWindow;if(!n)return e.prototype;const s=n[t].prototype;document.body.removeChild(r);if(!s)return i;return Ms[t]=s}catch{return i}}const As={};function Ts(t,e,i){var r;const n=`${t}.${String(i)}`;if(As[n])return As[n].call(e);const s=Os(t);const o=(r=Object.getOwnPropertyDescriptor(s,i))==null?void 0:r.get;if(!o)return e[i];As[n]=o;return o.call(e)}const _s={};function Es(t,e,i){const r=`${t}.${String(i)}`;if(_s[r])return _s[r].bind(e);const n=Os(t);const s=n[i];if(typeof s!=="function")return e[i];_s[r]=s;return s.bind(e)}function Rs(t){return Ts("Node",t,"childNodes")}function xs(t){return Ts("Node",t,"parentNode")}function js(t){return Ts("Node",t,"parentElement")}function Ns(t){return Ts("Node",t,"textContent")}function Ds(t,e){return Es("Node",t,"contains")(e)}function Fs(t){return Es("Node",t,"getRootNode")()}function Ls(t){if(!t||!("host"in t))return null;return Ts("ShadowRoot",t,"host")}function Ps(t){return t.styleSheets}function Bs(t){if(!t||!("shadowRoot"in t))return null;return Ts("Element",t,"shadowRoot")}function Us(t,e){return Ts("Element",t,"querySelector")(e)}function Gs(t,e){return Ts("Element",t,"querySelectorAll")(e)}function Ws(){return Os("MutationObserver").constructor}const zs={childNodes:Rs,parentNode:xs,parentElement:js,textContent:Ns,contains:Ds,getRootNode:Fs,host:Ls,styleSheets:Ps,shadowRoot:Bs,querySelector:Us,querySelectorAll:Gs,mutationObserver:Ws};function Vs(t){return t.nodeType===t.ELEMENT_NODE}function Hs(t){const e=t&&"host"in t&&"mode"in t&&zs.host(t)||null;return Boolean(e&&"shadowRoot"in e&&zs.shadowRoot(e)===t)}function Ys(t){return Object.prototype.toString.call(t)==="[object ShadowRoot]"}function Zs(t){if(t.includes(" background-clip: text;")&&!t.includes(" -webkit-background-clip: text;")){t=t.replace(/\sbackground-clip:\s*text;/g," -webkit-background-clip: text; background-clip: text;")}return t}function Xs(t){const{cssText:e}=t;if(e.split('"').length<3)return e;const i=["@import",`url(${JSON.stringify(t.href)})`];if(t.layerName===""){i.push(`layer`)}else if(t.layerName){i.push(`layer(${t.layerName})`)}if(t.supportsText){i.push(`supports(${t.supportsText})`)}if(t.media.length){i.push(t.media.mediaText)}return i.join(" ")+";"}function $s(t){try{const e=t.rules||t.cssRules;if(!e){return null}let i=t.href;if(!i&&t.ownerNode&&t.ownerNode.ownerDocument){i=t.ownerNode.ownerDocument.location.href}const r=Array.from(e,(t=>Js(t,i))).join("");return Zs(r)}catch(t){return null}}function Js(t,e){if(qs(t)){let e;try{e=$s(t.styleSheet)||Xs(t)}catch(i){e=t.cssText}if(t.styleSheet.href){return po(e,t.styleSheet.href)}return e}else{let i=t.cssText;if(Qs(t)&&t.selectorText.includes(":")){i=Ks(i)}if(e){return po(i,e)}return i}}function Ks(t){const e=/(\[(?:[\w-]+)[^\\])(:(?:[\w-]+)\])/gm;return t.replace(e,"$1\\$2")}function qs(t){return"styleSheet"in t}function Qs(t){return"selectorText"in t}class to{constructor(){ws(this,"idNodeMap",new Map);ws(this,"nodeMetaMap",new WeakMap)}getId(t){var e;if(!t)return-1;const i=(e=this.getMeta(t))==null?void 0:e.id;return i??-1}getNode(t){return this.idNodeMap.get(t)||null}getIds(){return Array.from(this.idNodeMap.keys())}getMeta(t){return this.nodeMetaMap.get(t)||null}removeNodeFromMap(t){const e=this.getId(t);this.idNodeMap.delete(e);if(t.childNodes){t.childNodes.forEach((t=>this.removeNodeFromMap(t)))}}has(t){return this.idNodeMap.has(t)}hasNode(t){return this.nodeMetaMap.has(t)}add(t,e){const i=e.id;this.idNodeMap.set(i,t);this.nodeMetaMap.set(t,e)}replace(t,e){const i=this.getNode(t);if(i){const t=this.nodeMetaMap.get(i);if(t)this.nodeMetaMap.set(e,t)}this.idNodeMap.set(t,e)}reset(){this.idNodeMap=new Map;this.nodeMetaMap=new WeakMap}}function eo(){return new to}function io({element:t,maskInputOptions:e,tagName:i,type:r,value:n,maskInputFn:s}){let o=n||"";const a=r&&ro(r);if(e[i.toLowerCase()]||a&&e[a]){if(s){o=s(o,t)}else{o="*".repeat(o.length)}}return o}function ro(t){return t.toLowerCase()}const no="__rrweb_original__";function so(t){const e=t.getContext("2d");if(!e)return true;const i=50;for(let r=0;r<t.width;r+=i){for(let n=0;n<t.height;n+=i){const s=e.getImageData;const o=no in s?s[no]:s;const a=new Uint32Array(o.call(e,r,n,Math.min(i,t.width-r),Math.min(i,t.height-n)).data.buffer);if(a.some((t=>t!==0)))return false}}return true}function oo(t,e){if(!t||!e||t.type!==e.type)return false;if(t.type===Ss.Document)return t.compatMode===e.compatMode;else if(t.type===Ss.DocumentType)return t.name===e.name&&t.publicId===e.publicId&&t.systemId===e.systemId;else if(t.type===Ss.Comment||t.type===Ss.Text||t.type===Ss.CDATA)return t.textContent===e.textContent;else if(t.type===Ss.Element)return t.tagName===e.tagName&&JSON.stringify(t.attributes)===JSON.stringify(e.attributes)&&t.isSVG===e.isSVG&&t.needBlock===e.needBlock;return false}function ao(t){const e=t.type;return t.hasAttribute("data-rr-is-password")?"password":e?ro(e):null}function uo(t,e){let i;try{i=new URL(t,window.location.href)}catch(t){return null}const r=/\.([0-9a-z]+)(?:$)/i;const n=i.pathname.match(r);return(n==null?void 0:n[1])??null}function fo(t){let e="";if(t.indexOf("//")>-1){e=t.split("/").slice(0,3).join("/")}else{e=t.split("/")[0]}e=e.split("?")[0];return e}const lo=/url\((?:(')([^']*)'|(")(.*?)"|([^)]*))\)/gm;const co=/^(?:[a-z+]+:)?\/\//i;const ho=/^www\..*/i;const vo=/^(data:)([^,]*),(.*)/i;function po(t,e){return(t||"").replace(lo,((t,i,r,n,s,o)=>{const a=r||s||o;const u=i||n||"";if(!a){return t}if(co.test(a)||ho.test(a)){return`url(${u}${a}${u})`}if(vo.test(a)){return`url(${u}${a}${u})`}if(a[0]==="/"){return`url(${u}${fo(e)+a}${u})`}const f=e.split("/");const l=a.split("/");f.pop();for(const t of l){if(t==="."){continue}else if(t===".."){f.pop()}else{f.push(t)}}return`url(${u}${f.join("/")}${u})`}))}function mo(t){return t.replace(/(\/\*[^*]*\*\/)|[\s;]/g,"")}function go(t,e){const i=Array.from(e.childNodes);const r=[];if(i.length>1&&t&&typeof t==="string"){const e=mo(t);for(let n=1;n<i.length;n++){if(i[n].textContent&&typeof i[n].textContent==="string"){const s=mo(i[n].textContent);for(let i=3;i<s.length;i++){const n=s.substring(0,i);if(e.split(n).length===2){const i=e.indexOf(n);for(let e=i;e<t.length;e++){if(mo(t.substring(0,e)).length===i){r.push(t.substring(0,e));t=t.substring(e);break}}break}}}}}r.push(t);return r}function yo(t,e){return go(t,e).join("/* rr_split */")}let bo=1;const wo=new RegExp("[^a-z0-9-_:]");const So=-2;function ko(){return bo++}function Co(t){if(t instanceof HTMLFormElement){return"form"}const e=ro(t.tagName);if(wo.test(e)){return"div"}return e}let Mo;let Io;const Oo=/^[^ \t\n\r\u000c]+/;const Ao=/^[, \t\n\r\u000c]+/;function To(t,e){if(e.trim()===""){return e}let i=0;function r(t){let r;const n=t.exec(e.substring(i));if(n){r=n[0];i+=r.length;return r}return""}const n=[];while(true){r(Ao);if(i>=e.length){break}let s=r(Oo);if(s.slice(-1)===","){s=Eo(t,s.substring(0,s.length-1));n.push(s)}else{let r="";s=Eo(t,s);let o=false;while(true){const t=e.charAt(i);if(t===""){n.push((s+r).trim());break}else if(!o){if(t===","){i+=1;n.push((s+r).trim());break}else if(t==="("){o=true}}else{if(t===")"){o=false}}r+=t;i+=1}}}return n.join(", ")}const _o=new WeakMap;function Eo(t,e){if(!e||e.trim()===""){return e}return xo(t,e)}function Ro(t){return Boolean(t.tagName==="svg"||t.ownerSVGElement)}function xo(t,e){let i=_o.get(t);if(!i){i=t.createElement("a");_o.set(t,i)}if(!e){e=""}else if(e.startsWith("blob:")||e.startsWith("data:")){return e}i.setAttribute("href",e);return i.href}function jo(t,e,i,r){if(!r){return r}if(i==="src"||i==="href"&&!(e==="use"&&r[0]==="#")){return Eo(t,r)}else if(i==="xlink:href"&&r[0]!=="#"){return Eo(t,r)}else if(i==="background"&&(e==="table"||e==="td"||e==="th")){return Eo(t,r)}else if(i==="srcset"){return To(t,r)}else if(i==="style"){return po(r,xo(t))}else if(e==="object"&&i==="data"){return Eo(t,r)}return r}function No(t,e,i){return(t==="video"||t==="audio")&&e==="autoplay"}function Do(t,e,i){try{if(typeof e==="string"){if(t.classList.contains(e)){return true}}else{for(let i=t.classList.length;i--;){const r=t.classList[i];if(e.test(r)){return true}}}if(i){return t.matches(i)}}catch(t){}return false}function Fo(t,e,i){if(!t)return false;if(t.nodeType!==t.ELEMENT_NODE){if(!i)return false;return Fo(zs.parentNode(t),e,i)}for(let i=t.classList.length;i--;){const r=t.classList[i];if(e.test(r)){return true}}if(!i)return false;return Fo(zs.parentNode(t),e,i)}function Lo(t,e,i,r){let n;if(Vs(t)){n=t;if(!zs.childNodes(n).length){return false}}else if(zs.parentElement(t)===null){return false}else{n=zs.parentElement(t)}try{if(typeof e==="string"){if(r){if(n.closest(`.${e}`))return true}else{if(n.classList.contains(e))return true}}else{if(Fo(n,e,r))return true}if(i){if(r){if(n.closest(i))return true}else{if(n.matches(i))return true}}}catch(t){}return false}function Po(t,e,i){const r=t.contentWindow;if(!r){return}let n=false;let s;try{s=r.document.readyState}catch(t){return}if(s!=="complete"){const r=setTimeout((()=>{if(!n){e();n=true}}),i);t.addEventListener("load",(()=>{clearTimeout(r);n=true;e()}));return}const o="about:blank";if(r.location.href!==o||t.src===o||t.src===""){setTimeout(e,0);return t.addEventListener("load",e)}t.addEventListener("load",e)}function Bo(t,e,i){let r=false;let n;try{n=t.sheet}catch(t){return}if(n)return;const s=setTimeout((()=>{if(!r){e();r=true}}),i);t.addEventListener("load",(()=>{clearTimeout(s);r=true;e()}))}function Uo(t,e){const{doc:i,mirror:r,blockClass:n,blockSelector:s,needsMask:o,inlineStylesheet:a,maskInputOptions:u={},maskTextFn:f,maskInputFn:l,dataURLOptions:c={},inlineImages:h,recordCanvas:d,keepIframeSrcFn:v,newlyAddedElement:p=false,cssCaptured:m=false}=e;const g=Go(i,r);switch(t.nodeType){case t.DOCUMENT_NODE:if(t.compatMode!=="CSS1Compat"){return{type:Ss.Document,childNodes:[],compatMode:t.compatMode}}else{return{type:Ss.Document,childNodes:[]}}case t.DOCUMENT_TYPE_NODE:return{type:Ss.DocumentType,name:t.name,publicId:t.publicId,systemId:t.systemId,rootId:g};case t.ELEMENT_NODE:return zo(t,{doc:i,blockClass:n,blockSelector:s,inlineStylesheet:a,maskInputOptions:u,maskInputFn:l,dataURLOptions:c,inlineImages:h,recordCanvas:d,keepIframeSrcFn:v,newlyAddedElement:p,rootId:g});case t.TEXT_NODE:return Wo(t,{doc:i,needsMask:o,maskTextFn:f,rootId:g,cssCaptured:m});case t.CDATA_SECTION_NODE:return{type:Ss.CDATA,textContent:"",rootId:g};case t.COMMENT_NODE:return{type:Ss.Comment,textContent:zs.textContent(t)||"",rootId:g};default:return false}}function Go(t,e){if(!e.hasNode(t))return void 0;const i=e.getId(t);return i===1?void 0:i}function Wo(t,e){const{needsMask:i,maskTextFn:r,rootId:n,cssCaptured:s}=e;const o=zs.parentNode(t);const a=o&&o.tagName;let u="";const f=a==="STYLE"?true:void 0;const l=a==="SCRIPT"?true:void 0;if(l){u="SCRIPT_PLACEHOLDER"}else if(!s){u=zs.textContent(t);if(f&&u){u=po(u,xo(e.doc))}}if(!f&&!l&&u&&i){u=r?r(u,zs.parentElement(t)):u.replace(/[\S]/g,"*")}return{type:Ss.Text,textContent:u||"",rootId:n}}function zo(t,e){const{doc:i,blockClass:r,blockSelector:n,inlineStylesheet:s,maskInputOptions:o={},maskInputFn:a,dataURLOptions:u={},inlineImages:f,recordCanvas:l,keepIframeSrcFn:c,newlyAddedElement:h=false,rootId:d}=e;const v=Do(t,r,n);const p=Co(t);let m={};const g=t.attributes.length;for(let e=0;e<g;e++){const r=t.attributes[e];if(!No(p,r.name)){m[r.name]=jo(i,p,ro(r.name),r.value)}}if(p==="link"&&s){const e=Array.from(i.styleSheets).find((e=>e.href===t.href));let r=null;if(e){r=$s(e)}if(r){delete m.rel;delete m.href;m._cssText=r}}if(p==="style"&&t.sheet){let e=$s(t.sheet);if(e){if(t.childNodes.length>1){e=yo(e,t)}m._cssText=e}}if(p==="input"||p==="textarea"||p==="select"){const e=t.value;const i=t.checked;if(m.type!=="radio"&&m.type!=="checkbox"&&m.type!=="submit"&&m.type!=="button"&&e){m.value=io({element:t,type:ao(t),tagName:p,value:e,maskInputOptions:o,maskInputFn:a})}else if(i){m.checked=i}}if(p==="option"){if(t.selected&&!o["select"]){m.selected=true}else{delete m.selected}}if(p==="dialog"&&t.open){m.rr_open_mode=t.matches("dialog:modal")?"modal":"non-modal"}if(p==="canvas"&&l){if(t.__context==="2d"){if(!so(t)){m.rr_dataURL=t.toDataURL(u.type,u.quality)}}else if(!("__context"in t)){const e=t.toDataURL(u.type,u.quality);const r=i.createElement("canvas");r.width=t.width;r.height=t.height;const n=r.toDataURL(u.type,u.quality);if(e!==n){m.rr_dataURL=e}}}if(p==="img"&&f){if(!Mo){Mo=i.createElement("canvas");Io=Mo.getContext("2d")}const e=t;const r=e.currentSrc||e.getAttribute("src")||"<unknown-src>";const n=e.crossOrigin;const s=()=>{e.removeEventListener("load",s);try{Mo.width=e.naturalWidth;Mo.height=e.naturalHeight;Io.drawImage(e,0,0);m.rr_dataURL=Mo.toDataURL(u.type,u.quality)}catch(t){if(e.crossOrigin!=="anonymous"){e.crossOrigin="anonymous";if(e.complete&&e.naturalWidth!==0)s();else e.addEventListener("load",s);return}else{console.warn(`Cannot inline img src=${r}! Error: ${t}`)}}if(e.crossOrigin==="anonymous"){n?m.crossOrigin=n:e.removeAttribute("crossorigin")}};if(e.complete&&e.naturalWidth!==0)s();else e.addEventListener("load",s)}if(p==="audio"||p==="video"){const e=m;e.rr_mediaState=t.paused?"paused":"played";e.rr_mediaCurrentTime=t.currentTime;e.rr_mediaPlaybackRate=t.playbackRate;e.rr_mediaMuted=t.muted;e.rr_mediaLoop=t.loop;e.rr_mediaVolume=t.volume}if(!h){if(t.scrollLeft){m.rr_scrollLeft=t.scrollLeft}if(t.scrollTop){m.rr_scrollTop=t.scrollTop}}if(v){const{width:e,height:i}=t.getBoundingClientRect();m={class:m.class,rr_width:`${e}px`,rr_height:`${i}px`}}if(p==="iframe"&&!c(m.src)){if(!t.contentDocument){m.rr_src=m.src}delete m.src}let y;try{if(customElements.get(p))y=true}catch(t){}return{type:Ss.Element,tagName:p,attributes:m,childNodes:[],isSVG:Ro(t)||void 0,needBlock:v,rootId:d,isCustom:y}}function Vo(t){if(t===void 0||t===null){return""}else{return t.toLowerCase()}}function Ho(t,e){if(e.comment&&t.type===Ss.Comment){return true}else if(t.type===Ss.Element){if(e.script&&(t.tagName==="script"||t.tagName==="link"&&(t.attributes.rel==="preload"||t.attributes.rel==="modulepreload")&&t.attributes.as==="script"||t.tagName==="link"&&t.attributes.rel==="prefetch"&&typeof t.attributes.href==="string"&&uo(t.attributes.href)==="js")){return true}else if(e.headFavicon&&(t.tagName==="link"&&t.attributes.rel==="shortcut icon"||t.tagName==="meta"&&(Vo(t.attributes.name).match(/^msapplication-tile(image|color)$/)||Vo(t.attributes.name)==="application-name"||Vo(t.attributes.rel)==="icon"||Vo(t.attributes.rel)==="apple-touch-icon"||Vo(t.attributes.rel)==="shortcut icon"))){return true}else if(t.tagName==="meta"){if(e.headMetaDescKeywords&&Vo(t.attributes.name).match(/^description|keywords$/)){return true}else if(e.headMetaSocial&&(Vo(t.attributes.property).match(/^(og|twitter|fb):/)||Vo(t.attributes.name).match(/^(og|twitter):/)||Vo(t.attributes.name)==="pinterest")){return true}else if(e.headMetaRobots&&(Vo(t.attributes.name)==="robots"||Vo(t.attributes.name)==="googlebot"||Vo(t.attributes.name)==="bingbot")){return true}else if(e.headMetaHttpEquiv&&t.attributes["http-equiv"]!==void 0){return true}else if(e.headMetaAuthorship&&(Vo(t.attributes.name)==="author"||Vo(t.attributes.name)==="generator"||Vo(t.attributes.name)==="framework"||Vo(t.attributes.name)==="publisher"||Vo(t.attributes.name)==="progid"||Vo(t.attributes.property).match(/^article:/)||Vo(t.attributes.property).match(/^product:/))){return true}else if(e.headMetaVerification&&(Vo(t.attributes.name)==="google-site-verification"||Vo(t.attributes.name)==="yandex-verification"||Vo(t.attributes.name)==="csrf-token"||Vo(t.attributes.name)==="p:domain_verify"||Vo(t.attributes.name)==="verify-v1"||Vo(t.attributes.name)==="verification"||Vo(t.attributes.name)==="shopify-checkout-api-token")){return true}}}return false}function Yo(t,e){const{doc:i,mirror:r,blockClass:n,blockSelector:s,maskTextClass:o,maskTextSelector:a,skipChild:u=false,inlineStylesheet:f=true,maskInputOptions:l={},maskTextFn:c,maskInputFn:h,slimDOMOptions:d,dataURLOptions:v={},inlineImages:p=false,recordCanvas:m=false,onSerialize:g,onIframeLoad:y,iframeLoadTimeout:b=5e3,onStylesheetLoad:w,stylesheetLoadTimeout:S=5e3,keepIframeSrcFn:k=()=>false,newlyAddedElement:C=false,cssCaptured:M=false}=e;let{needsMask:I}=e;let{preserveWhiteSpace:O=true}=e;if(!I){const e=I===void 0;I=Lo(t,o,a,e)}const A=Uo(t,{doc:i,mirror:r,blockClass:n,blockSelector:s,needsMask:I,inlineStylesheet:f,maskInputOptions:l,maskTextFn:c,maskInputFn:h,dataURLOptions:v,inlineImages:p,recordCanvas:m,keepIframeSrcFn:k,newlyAddedElement:C,cssCaptured:M});if(!A){console.warn(t,"not serialized");return null}let T;if(r.hasNode(t)){T=r.getId(t)}else if(Ho(A,d)||!O&&A.type===Ss.Text&&!A.textContent.replace(/^\s+|\s+$/gm,"").length){T=So}else{T=ko()}const _=Object.assign(A,{id:T});r.add(t,_);if(T===So){return null}if(g){g(t)}let E=!u;if(_.type===Ss.Element){E=E&&!_.needBlock;delete _.needBlock;const e=zs.shadowRoot(t);if(e&&Ys(e))_.isShadowHost=true}if((_.type===Ss.Document||_.type===Ss.Element)&&E){if(d.headWhitespace&&_.type===Ss.Element&&_.tagName==="head"){O=false}const e={doc:i,mirror:r,blockClass:n,blockSelector:s,needsMask:I,maskTextClass:o,maskTextSelector:a,skipChild:u,inlineStylesheet:f,maskInputOptions:l,maskTextFn:c,maskInputFn:h,slimDOMOptions:d,dataURLOptions:v,inlineImages:p,recordCanvas:m,preserveWhiteSpace:O,onSerialize:g,onIframeLoad:y,iframeLoadTimeout:b,onStylesheetLoad:w,stylesheetLoadTimeout:S,keepIframeSrcFn:k,cssCaptured:false};if(_.type===Ss.Element&&_.tagName==="textarea"&&_.attributes.value!==void 0);else{if(_.type===Ss.Element&&_.attributes._cssText!==void 0&&typeof _.attributes._cssText==="string"){e.cssCaptured=true}for(const i of Array.from(zs.childNodes(t))){const t=Yo(i,e);if(t){_.childNodes.push(t)}}}let C=null;if(Vs(t)&&(C=zs.shadowRoot(t))){for(const t of Array.from(zs.childNodes(C))){const i=Yo(t,e);if(i){Ys(C)&&(i.isShadow=true);_.childNodes.push(i)}}}}const R=zs.parentNode(t);if(R&&Hs(R)&&Ys(R)){_.isShadow=true}if(_.type===Ss.Element&&_.tagName==="iframe"){Po(t,(()=>{const e=t.contentDocument;if(e&&y){const i=Yo(e,{doc:e,mirror:r,blockClass:n,blockSelector:s,needsMask:I,maskTextClass:o,maskTextSelector:a,skipChild:false,inlineStylesheet:f,maskInputOptions:l,maskTextFn:c,maskInputFn:h,slimDOMOptions:d,dataURLOptions:v,inlineImages:p,recordCanvas:m,preserveWhiteSpace:O,onSerialize:g,onIframeLoad:y,iframeLoadTimeout:b,onStylesheetLoad:w,stylesheetLoadTimeout:S,keepIframeSrcFn:k});if(i){y(t,i)}}}),b)}if(_.type===Ss.Element&&_.tagName==="link"&&typeof _.attributes.rel==="string"&&(_.attributes.rel==="stylesheet"||_.attributes.rel==="preload"&&typeof _.attributes.href==="string"&&uo(_.attributes.href)==="css")){Bo(t,(()=>{if(w){const e=Yo(t,{doc:i,mirror:r,blockClass:n,blockSelector:s,needsMask:I,maskTextClass:o,maskTextSelector:a,skipChild:false,inlineStylesheet:f,maskInputOptions:l,maskTextFn:c,maskInputFn:h,slimDOMOptions:d,dataURLOptions:v,inlineImages:p,recordCanvas:m,preserveWhiteSpace:O,onSerialize:g,onIframeLoad:y,iframeLoadTimeout:b,onStylesheetLoad:w,stylesheetLoadTimeout:S,keepIframeSrcFn:k});if(e){w(t,e)}}}),S)}return _}function Zo(t,e){const{mirror:i=new to,blockClass:r="rr-block",blockSelector:n=null,maskTextClass:s="rr-mask",maskTextSelector:o=null,inlineStylesheet:a=true,inlineImages:u=false,recordCanvas:f=false,maskAllInputs:l=false,maskTextFn:c,maskInputFn:h,slimDOM:d=false,dataURLOptions:v,preserveWhiteSpace:p,onSerialize:m,onIframeLoad:g,iframeLoadTimeout:y,onStylesheetLoad:b,stylesheetLoadTimeout:w,keepIframeSrcFn:S=()=>false}=e||{};const k=l===true?{color:true,date:true,"datetime-local":true,email:true,month:true,number:true,range:true,search:true,tel:true,text:true,time:true,url:true,week:true,textarea:true,select:true,password:true}:l===false?{password:true}:l;const C=d===true||d==="all"?{script:true,comment:true,headFavicon:true,headWhitespace:true,headMetaDescKeywords:d==="all",headMetaSocial:true,headMetaRobots:true,headMetaHttpEquiv:true,headMetaAuthorship:true,headMetaVerification:true}:d===false?{}:d;return Yo(t,{doc:t,mirror:i,blockClass:r,blockSelector:n,maskTextClass:s,maskTextSelector:o,skipChild:false,inlineStylesheet:a,maskInputOptions:k,maskTextFn:c,maskInputFn:h,slimDOMOptions:C,dataURLOptions:v,inlineImages:u,recordCanvas:f,preserveWhiteSpace:p,onSerialize:m,onIframeLoad:g,iframeLoadTimeout:y,onStylesheetLoad:b,stylesheetLoadTimeout:w,keepIframeSrcFn:S,newlyAddedElement:false})}const Xo=/(max|min)-device-(width|height)/;const $o=new RegExp(Xo.source,"g");const Jo={postcssPlugin:"postcss-custom-selectors",prepare(){return{postcssPlugin:"postcss-custom-selectors",AtRule:function(t){if(t.params.match($o)){t.params=t.params.replace($o,"$1-$2")}}}}};const Ko={postcssPlugin:"postcss-hover-classes",prepare:function(){const t=[];return{Rule:function(e){if(t.indexOf(e)!==-1){return}t.push(e);e.selectors.forEach((function(t){if(t.includes(":hover")){e.selector+=",\n"+t.replace(/:hover/g,".\\:hover")}}))}}}};function qo(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t["default"]:t}function Qo(t){if(t.__esModule)return t;var e=t.default;if(typeof e=="function"){var i=function t(){if(this instanceof t){return Reflect.construct(e,arguments,this.constructor)}return e.apply(this,arguments)};i.prototype=e.prototype}else i={};Object.defineProperty(i,"__esModule",{value:true});Object.keys(t).forEach((function(e){var r=Object.getOwnPropertyDescriptor(t,e);Object.defineProperty(i,e,r.get?r:{enumerable:true,get:function(){return t[e]}})}));return i}var ta={exports:{}};var ea=String;var ia=function(){return{isColorSupported:false,reset:ea,bold:ea,dim:ea,italic:ea,underline:ea,inverse:ea,hidden:ea,strikethrough:ea,black:ea,red:ea,green:ea,yellow:ea,blue:ea,magenta:ea,cyan:ea,white:ea,gray:ea,bgBlack:ea,bgRed:ea,bgGreen:ea,bgYellow:ea,bgBlue:ea,bgMagenta:ea,bgCyan:ea,bgWhite:ea}};ta.exports=ia();ta.exports.createColors=ia;var ra=ta.exports;const na={};const sa=Object.freeze(Object.defineProperty({__proto__:null,default:na},Symbol.toStringTag,{value:"Module"}));const oa=Qo(sa);let aa=ra;let ua=oa;let fa=class t extends Error{constructor(e,i,r,n,s,o){super(e);this.name="CssSyntaxError";this.reason=e;if(s){this.file=s}if(n){this.source=n}if(o){this.plugin=o}if(typeof i!=="undefined"&&typeof r!=="undefined"){if(typeof i==="number"){this.line=i;this.column=r}else{this.line=i.line;this.column=i.column;this.endLine=r.line;this.endColumn=r.column}}this.setMessage();if(Error.captureStackTrace){Error.captureStackTrace(this,t)}}setMessage(){this.message=this.plugin?this.plugin+": ":"";this.message+=this.file?this.file:"<css input>";if(typeof this.line!=="undefined"){this.message+=":"+this.line+":"+this.column}this.message+=": "+this.reason}showSourceCode(t){if(!this.source)return"";let e=this.source;if(t==null)t=aa.isColorSupported;if(ua){if(t)e=ua(e)}let i=e.split(/\r?\n/);let r=Math.max(this.line-3,0);let n=Math.min(this.line+2,i.length);let s=String(n).length;let o,a;if(t){let{bold:t,gray:e,red:i}=aa.createColors(true);o=e=>t(i(e));a=t=>e(t)}else{o=a=t=>t}return i.slice(r,n).map(((t,e)=>{let i=r+1+e;let n=" "+(" "+i).slice(-s)+" | ";if(i===this.line){let e=a(n.replace(/\d/g," "))+t.slice(0,this.column-1).replace(/[^\t]/g," ");return o(">")+a(n)+t+"\n "+e+o("^")}return" "+a(n)+t})).join("\n")}toString(){let t=this.showSourceCode();if(t){t="\n\n"+t+"\n"}return this.name+": "+this.message+t}};var la=fa;fa.default=fa;var ca={};ca.isClean=Symbol("isClean");ca.my=Symbol("my");const ha={after:"\n",beforeClose:"\n",beforeComment:"\n",beforeDecl:"\n",beforeOpen:" ",beforeRule:"\n",colon:": ",commentLeft:" ",commentRight:" ",emptyBody:"",indent:"    ",semicolon:false};function da(t){return t[0].toUpperCase()+t.slice(1)}let va=class t{constructor(t){this.builder=t}atrule(t,e){let i="@"+t.name;let r=t.params?this.rawValue(t,"params"):"";if(typeof t.raws.afterName!=="undefined"){i+=t.raws.afterName}else if(r){i+=" "}if(t.nodes){this.block(t,i+r)}else{let n=(t.raws.between||"")+(e?";":"");this.builder(i+r+n,t)}}beforeAfter(t,e){let i;if(t.type==="decl"){i=this.raw(t,null,"beforeDecl")}else if(t.type==="comment"){i=this.raw(t,null,"beforeComment")}else if(e==="before"){i=this.raw(t,null,"beforeRule")}else{i=this.raw(t,null,"beforeClose")}let r=t.parent;let n=0;while(r&&r.type!=="root"){n+=1;r=r.parent}if(i.includes("\n")){let e=this.raw(t,null,"indent");if(e.length){for(let t=0;t<n;t++)i+=e}}return i}block(t,e){let i=this.raw(t,"between","beforeOpen");this.builder(e+i+"{",t,"start");let r;if(t.nodes&&t.nodes.length){this.body(t);r=this.raw(t,"after")}else{r=this.raw(t,"after","emptyBody")}if(r)this.builder(r);this.builder("}",t,"end")}body(t){let e=t.nodes.length-1;while(e>0){if(t.nodes[e].type!=="comment")break;e-=1}let i=this.raw(t,"semicolon");for(let r=0;r<t.nodes.length;r++){let n=t.nodes[r];let s=this.raw(n,"before");if(s)this.builder(s);this.stringify(n,e!==r||i)}}comment(t){let e=this.raw(t,"left","commentLeft");let i=this.raw(t,"right","commentRight");this.builder("/*"+e+t.text+i+"*/",t)}decl(t,e){let i=this.raw(t,"between","colon");let r=t.prop+i+this.rawValue(t,"value");if(t.important){r+=t.raws.important||" !important"}if(e)r+=";";this.builder(r,t)}document(t){this.body(t)}raw(t,e,i){let r;if(!i)i=e;if(e){r=t.raws[e];if(typeof r!=="undefined")return r}let n=t.parent;if(i==="before"){if(!n||n.type==="root"&&n.first===t){return""}if(n&&n.type==="document"){return""}}if(!n)return ha[i];let s=t.root();if(!s.rawCache)s.rawCache={};if(typeof s.rawCache[i]!=="undefined"){return s.rawCache[i]}if(i==="before"||i==="after"){return this.beforeAfter(t,i)}else{let n="raw"+da(i);if(this[n]){r=this[n](s,t)}else{s.walk((t=>{r=t.raws[e];if(typeof r!=="undefined")return false}))}}if(typeof r==="undefined")r=ha[i];s.rawCache[i]=r;return r}rawBeforeClose(t){let e;t.walk((t=>{if(t.nodes&&t.nodes.length>0){if(typeof t.raws.after!=="undefined"){e=t.raws.after;if(e.includes("\n")){e=e.replace(/[^\n]+$/,"")}return false}}}));if(e)e=e.replace(/\S/g,"");return e}rawBeforeComment(t,e){let i;t.walkComments((t=>{if(typeof t.raws.before!=="undefined"){i=t.raws.before;if(i.includes("\n")){i=i.replace(/[^\n]+$/,"")}return false}}));if(typeof i==="undefined"){i=this.raw(e,null,"beforeDecl")}else if(i){i=i.replace(/\S/g,"")}return i}rawBeforeDecl(t,e){let i;t.walkDecls((t=>{if(typeof t.raws.before!=="undefined"){i=t.raws.before;if(i.includes("\n")){i=i.replace(/[^\n]+$/,"")}return false}}));if(typeof i==="undefined"){i=this.raw(e,null,"beforeRule")}else if(i){i=i.replace(/\S/g,"")}return i}rawBeforeOpen(t){let e;t.walk((t=>{if(t.type!=="decl"){e=t.raws.between;if(typeof e!=="undefined")return false}}));return e}rawBeforeRule(t){let e;t.walk((i=>{if(i.nodes&&(i.parent!==t||t.first!==i)){if(typeof i.raws.before!=="undefined"){e=i.raws.before;if(e.includes("\n")){e=e.replace(/[^\n]+$/,"")}return false}}}));if(e)e=e.replace(/\S/g,"");return e}rawColon(t){let e;t.walkDecls((t=>{if(typeof t.raws.between!=="undefined"){e=t.raws.between.replace(/[^\s:]/g,"");return false}}));return e}rawEmptyBody(t){let e;t.walk((t=>{if(t.nodes&&t.nodes.length===0){e=t.raws.after;if(typeof e!=="undefined")return false}}));return e}rawIndent(t){if(t.raws.indent)return t.raws.indent;let e;t.walk((i=>{let r=i.parent;if(r&&r!==t&&r.parent&&r.parent===t){if(typeof i.raws.before!=="undefined"){let t=i.raws.before.split("\n");e=t[t.length-1];e=e.replace(/\S/g,"");return false}}}));return e}rawSemicolon(t){let e;t.walk((t=>{if(t.nodes&&t.nodes.length&&t.last.type==="decl"){e=t.raws.semicolon;if(typeof e!=="undefined")return false}}));return e}rawValue(t,e){let i=t[e];let r=t.raws[e];if(r&&r.value===i){return r.raw}return i}root(t){this.body(t);if(t.raws.after)this.builder(t.raws.after)}rule(t){this.block(t,this.rawValue(t,"selector"));if(t.raws.ownSemicolon){this.builder(t.raws.ownSemicolon,t,"end")}}stringify(t,e){if(!this[t.type]){throw new Error("Unknown AST node type "+t.type+". Maybe you need to change PostCSS stringifier.")}this[t.type](t,e)}};var pa=va;va.default=va;let ma=pa;function ga(t,e){let i=new ma(e);i.stringify(t)}var ya=ga;ga.default=ga;let{isClean:ba,my:wa}=ca;let Sa=la;let ka=pa;let Ca=ya;function Ma(t,e){let i=new t.constructor;for(let r in t){if(!Object.prototype.hasOwnProperty.call(t,r)){continue}if(r==="proxyCache")continue;let n=t[r];let s=typeof n;if(r==="parent"&&s==="object"){if(e)i[r]=e}else if(r==="source"){i[r]=n}else if(Array.isArray(n)){i[r]=n.map((t=>Ma(t,i)))}else{if(s==="object"&&n!==null)n=Ma(n);i[r]=n}}return i}let Ia=class t{constructor(t={}){this.raws={};this[ba]=false;this[wa]=true;for(let e in t){if(e==="nodes"){this.nodes=[];for(let i of t[e]){if(typeof i.clone==="function"){this.append(i.clone())}else{this.append(i)}}}else{this[e]=t[e]}}}addToError(t){t.postcssNode=this;if(t.stack&&this.source&&/\n\s{4}at /.test(t.stack)){let e=this.source;t.stack=t.stack.replace(/\n\s{4}at /,`$&${e.input.from}:${e.start.line}:${e.start.column}$&`)}return t}after(t){this.parent.insertAfter(this,t);return this}assign(t={}){for(let e in t){this[e]=t[e]}return this}before(t){this.parent.insertBefore(this,t);return this}cleanRaws(t){delete this.raws.before;delete this.raws.after;if(!t)delete this.raws.between}clone(t={}){let e=Ma(this);for(let i in t){e[i]=t[i]}return e}cloneAfter(t={}){let e=this.clone(t);this.parent.insertAfter(this,e);return e}cloneBefore(t={}){let e=this.clone(t);this.parent.insertBefore(this,e);return e}error(t,e={}){if(this.source){let{end:i,start:r}=this.rangeBy(e);return this.source.input.error(t,{column:r.column,line:r.line},{column:i.column,line:i.line},e)}return new Sa(t)}getProxyProcessor(){return{get(t,e){if(e==="proxyOf"){return t}else if(e==="root"){return()=>t.root().toProxy()}else{return t[e]}},set(t,e,i){if(t[e]===i)return true;t[e]=i;if(e==="prop"||e==="value"||e==="name"||e==="params"||e==="important"||e==="text"){t.markDirty()}return true}}}markDirty(){if(this[ba]){this[ba]=false;let t=this;while(t=t.parent){t[ba]=false}}}next(){if(!this.parent)return void 0;let t=this.parent.index(this);return this.parent.nodes[t+1]}positionBy(t,e){let i=this.source.start;if(t.index){i=this.positionInside(t.index,e)}else if(t.word){e=this.toString();let r=e.indexOf(t.word);if(r!==-1)i=this.positionInside(r,e)}return i}positionInside(t,e){let i=e||this.toString();let r=this.source.start.column;let n=this.source.start.line;for(let e=0;e<t;e++){if(i[e]==="\n"){r=1;n+=1}else{r+=1}}return{column:r,line:n}}prev(){if(!this.parent)return void 0;let t=this.parent.index(this);return this.parent.nodes[t-1]}rangeBy(t){let e={column:this.source.start.column,line:this.source.start.line};let i=this.source.end?{column:this.source.end.column+1,line:this.source.end.line}:{column:e.column+1,line:e.line};if(t.word){let r=this.toString();let n=r.indexOf(t.word);if(n!==-1){e=this.positionInside(n,r);i=this.positionInside(n+t.word.length,r)}}else{if(t.start){e={column:t.start.column,line:t.start.line}}else if(t.index){e=this.positionInside(t.index)}if(t.end){i={column:t.end.column,line:t.end.line}}else if(typeof t.endIndex==="number"){i=this.positionInside(t.endIndex)}else if(t.index){i=this.positionInside(t.index+1)}}if(i.line<e.line||i.line===e.line&&i.column<=e.column){i={column:e.column+1,line:e.line}}return{end:i,start:e}}raw(t,e){let i=new ka;return i.raw(this,t,e)}remove(){if(this.parent){this.parent.removeChild(this)}this.parent=void 0;return this}replaceWith(...t){if(this.parent){let e=this;let i=false;for(let r of t){if(r===this){i=true}else if(i){this.parent.insertAfter(e,r);e=r}else{this.parent.insertBefore(e,r)}}if(!i){this.remove()}}return this}root(){let t=this;while(t.parent&&t.parent.type!=="document"){t=t.parent}return t}toJSON(t,e){let i={};let r=e==null;e=e||new Map;let n=0;for(let t in this){if(!Object.prototype.hasOwnProperty.call(this,t)){continue}if(t==="parent"||t==="proxyCache")continue;let r=this[t];if(Array.isArray(r)){i[t]=r.map((t=>{if(typeof t==="object"&&t.toJSON){return t.toJSON(null,e)}else{return t}}))}else if(typeof r==="object"&&r.toJSON){i[t]=r.toJSON(null,e)}else if(t==="source"){let s=e.get(r.input);if(s==null){s=n;e.set(r.input,n);n++}i[t]={end:r.end,inputId:s,start:r.start}}else{i[t]=r}}if(r){i.inputs=[...e.keys()].map((t=>t.toJSON()))}return i}toProxy(){if(!this.proxyCache){this.proxyCache=new Proxy(this,this.getProxyProcessor())}return this.proxyCache}toString(t=Ca){if(t.stringify)t=t.stringify;let e="";t(this,(t=>{e+=t}));return e}warn(t,e,i){let r={node:this};for(let t in i)r[t]=i[t];return t.warn(e,r)}get proxyOf(){return this}};var Oa=Ia;Ia.default=Ia;let Aa=Oa;let Ta=class t extends Aa{constructor(t){if(t&&typeof t.value!=="undefined"&&typeof t.value!=="string"){t={...t,value:String(t.value)}}super(t);this.type="decl"}get variable(){return this.prop.startsWith("--")||this.prop[0]==="$"}};var _a=Ta;Ta.default=Ta;let Ea="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict";let Ra=(t=21)=>{let e="";let i=t;while(i--){e+=Ea[Math.random()*64|0]}return e};var xa={nanoid:Ra};let{SourceMapConsumer:ja,SourceMapGenerator:Na}=oa;let{existsSync:Da,readFileSync:Fa}=oa;let{dirname:La,join:Pa}=oa;function Ba(t){if(Buffer){return Buffer.from(t,"base64").toString()}else{return window.atob(t)}}let Ua=class t{constructor(t,e){if(e.map===false)return;this.loadAnnotation(t);this.inline=this.startWith(this.annotation,"data:");let i=e.map?e.map.prev:void 0;let r=this.loadMap(e.from,i);if(!this.mapFile&&e.from){this.mapFile=e.from}if(this.mapFile)this.root=La(this.mapFile);if(r)this.text=r}consumer(){if(!this.consumerCache){this.consumerCache=new ja(this.text)}return this.consumerCache}decodeInline(t){let e=/^data:application\/json;charset=utf-?8;base64,/;let i=/^data:application\/json;base64,/;let r=/^data:application\/json;charset=utf-?8,/;let n=/^data:application\/json,/;if(r.test(t)||n.test(t)){return decodeURIComponent(t.substr(RegExp.lastMatch.length))}if(e.test(t)||i.test(t)){return Ba(t.substr(RegExp.lastMatch.length))}let s=t.match(/data:application\/json;([^,]+),/)[1];throw new Error("Unsupported source map encoding "+s)}getAnnotationURL(t){return t.replace(/^\/\*\s*# sourceMappingURL=/,"").trim()}isMap(t){if(typeof t!=="object")return false;return typeof t.mappings==="string"||typeof t._mappings==="string"||Array.isArray(t.sections)}loadAnnotation(t){let e=t.match(/\/\*\s*# sourceMappingURL=/gm);if(!e)return;let i=t.lastIndexOf(e.pop());let r=t.indexOf("*/",i);if(i>-1&&r>-1){this.annotation=this.getAnnotationURL(t.substring(i,r))}}loadFile(t){this.root=La(t);if(Da(t)){this.mapFile=t;return Fa(t,"utf-8").toString().trim()}}loadMap(t,e){if(e===false)return false;if(e){if(typeof e==="string"){return e}else if(typeof e==="function"){let i=e(t);if(i){let t=this.loadFile(i);if(!t){throw new Error("Unable to load previous source map: "+i.toString())}return t}}else if(e instanceof ja){return Na.fromSourceMap(e).toString()}else if(e instanceof Na){return e.toString()}else if(this.isMap(e)){return JSON.stringify(e)}else{throw new Error("Unsupported previous source map format: "+e.toString())}}else if(this.inline){return this.decodeInline(this.annotation)}else if(this.annotation){let e=this.annotation;if(t)e=Pa(La(t),e);return this.loadFile(e)}}startWith(t,e){if(!t)return false;return t.substr(0,e.length)===e}withContent(){return!!(this.consumer().sourcesContent&&this.consumer().sourcesContent.length>0)}};var Ga=Ua;Ua.default=Ua;let{SourceMapConsumer:Wa,SourceMapGenerator:za}=oa;let{fileURLToPath:Va,pathToFileURL:Ha}=oa;let{isAbsolute:Ya,resolve:Za}=oa;let{nanoid:Xa}=xa;let $a=oa;let Ja=la;let Ka=Ga;let qa=Symbol("fromOffsetCache");let Qa=Boolean(Wa&&za);let tu=Boolean(Za&&Ya);let eu=class t{constructor(t,e={}){if(t===null||typeof t==="undefined"||typeof t==="object"&&!t.toString){throw new Error(`PostCSS received ${t} instead of CSS string`)}this.css=t.toString();if(this.css[0]==="\ufeff"||this.css[0]==="￾"){this.hasBOM=true;this.css=this.css.slice(1)}else{this.hasBOM=false}if(e.from){if(!tu||/^\w+:\/\//.test(e.from)||Ya(e.from)){this.file=e.from}else{this.file=Za(e.from)}}if(tu&&Qa){let t=new Ka(this.css,e);if(t.text){this.map=t;let e=t.consumer().file;if(!this.file&&e)this.file=this.mapResolve(e)}}if(!this.file){this.id="<input css "+Xa(6)+">"}if(this.map)this.map.file=this.from}error(t,e,i,r={}){let n,s,o;if(e&&typeof e==="object"){let t=e;let r=i;if(typeof t.offset==="number"){let r=this.fromOffset(t.offset);e=r.line;i=r.col}else{e=t.line;i=t.column}if(typeof r.offset==="number"){let t=this.fromOffset(r.offset);s=t.line;o=t.col}else{s=r.line;o=r.column}}else if(!i){let t=this.fromOffset(e);e=t.line;i=t.col}let a=this.origin(e,i,s,o);if(a){n=new Ja(t,a.endLine===void 0?a.line:{column:a.column,line:a.line},a.endLine===void 0?a.column:{column:a.endColumn,line:a.endLine},a.source,a.file,r.plugin)}else{n=new Ja(t,s===void 0?e:{column:i,line:e},s===void 0?i:{column:o,line:s},this.css,this.file,r.plugin)}n.input={column:i,endColumn:o,endLine:s,line:e,source:this.css};if(this.file){if(Ha){n.input.url=Ha(this.file).toString()}n.input.file=this.file}return n}fromOffset(t){let e,i;if(!this[qa]){let t=this.css.split("\n");i=new Array(t.length);let e=0;for(let r=0,n=t.length;r<n;r++){i[r]=e;e+=t[r].length+1}this[qa]=i}else{i=this[qa]}e=i[i.length-1];let r=0;if(t>=e){r=i.length-1}else{let e=i.length-2;let n;while(r<e){n=r+(e-r>>1);if(t<i[n]){e=n-1}else if(t>=i[n+1]){r=n+1}else{r=n;break}}}return{col:t-i[r]+1,line:r+1}}mapResolve(t){if(/^\w+:\/\//.test(t)){return t}return Za(this.map.consumer().sourceRoot||this.map.root||".",t)}origin(t,e,i,r){if(!this.map)return false;let n=this.map.consumer();let s=n.originalPositionFor({column:e,line:t});if(!s.source)return false;let o;if(typeof i==="number"){o=n.originalPositionFor({column:r,line:i})}let a;if(Ya(s.source)){a=Ha(s.source)}else{a=new URL(s.source,this.map.consumer().sourceRoot||Ha(this.map.mapFile))}let u={column:s.column,endColumn:o&&o.column,endLine:o&&o.line,line:s.line,url:a.toString()};if(a.protocol==="file:"){if(Va){u.file=Va(a)}else{throw new Error(`file: protocol is not available in this PostCSS build`)}}let f=n.sourceContentFor(s.source);if(f)u.source=f;return u}toJSON(){let t={};for(let e of["hasBOM","css","file","id"]){if(this[e]!=null){t[e]=this[e]}}if(this.map){t.map={...this.map};if(t.map.consumerCache){t.map.consumerCache=void 0}}return t}get from(){return this.file||this.id}};var iu=eu;eu.default=eu;if($a&&$a.registerInput){$a.registerInput(eu)}let{SourceMapConsumer:ru,SourceMapGenerator:nu}=oa;let{dirname:su,relative:ou,resolve:au,sep:uu}=oa;let{pathToFileURL:fu}=oa;let lu=iu;let cu=Boolean(ru&&nu);let hu=Boolean(su&&au&&ou&&uu);let du=class t{constructor(t,e,i,r){this.stringify=t;this.mapOpts=i.map||{};this.root=e;this.opts=i;this.css=r;this.originalCSS=r;this.usesFileUrls=!this.mapOpts.from&&this.mapOpts.absolute;this.memoizedFileURLs=new Map;this.memoizedPaths=new Map;this.memoizedURLs=new Map}addAnnotation(){let t;if(this.isInline()){t="data:application/json;base64,"+this.toBase64(this.map.toString())}else if(typeof this.mapOpts.annotation==="string"){t=this.mapOpts.annotation}else if(typeof this.mapOpts.annotation==="function"){t=this.mapOpts.annotation(this.opts.to,this.root)}else{t=this.outputFile()+".map"}let e="\n";if(this.css.includes("\r\n"))e="\r\n";this.css+=e+"/*# sourceMappingURL="+t+" */"}applyPrevMaps(){for(let t of this.previous()){let e=this.toUrl(this.path(t.file));let i=t.root||su(t.file);let r;if(this.mapOpts.sourcesContent===false){r=new ru(t.text);if(r.sourcesContent){r.sourcesContent=null}}else{r=t.consumer()}this.map.applySourceMap(r,e,this.toUrl(this.path(i)))}}clearAnnotation(){if(this.mapOpts.annotation===false)return;if(this.root){let t;for(let e=this.root.nodes.length-1;e>=0;e--){t=this.root.nodes[e];if(t.type!=="comment")continue;if(t.text.indexOf("# sourceMappingURL=")===0){this.root.removeChild(e)}}}else if(this.css){this.css=this.css.replace(/\n*?\/\*#[\S\s]*?\*\/$/gm,"")}}generate(){this.clearAnnotation();if(hu&&cu&&this.isMap()){return this.generateMap()}else{let t="";this.stringify(this.root,(e=>{t+=e}));return[t]}}generateMap(){if(this.root){this.generateString()}else if(this.previous().length===1){let t=this.previous()[0].consumer();t.file=this.outputFile();this.map=nu.fromSourceMap(t,{ignoreInvalidMapping:true})}else{this.map=new nu({file:this.outputFile(),ignoreInvalidMapping:true});this.map.addMapping({generated:{column:0,line:1},original:{column:0,line:1},source:this.opts.from?this.toUrl(this.path(this.opts.from)):"<no source>"})}if(this.isSourcesContent())this.setSourcesContent();if(this.root&&this.previous().length>0)this.applyPrevMaps();if(this.isAnnotation())this.addAnnotation();if(this.isInline()){return[this.css]}else{return[this.css,this.map]}}generateString(){this.css="";this.map=new nu({file:this.outputFile(),ignoreInvalidMapping:true});let t=1;let e=1;let i="<no source>";let r={generated:{column:0,line:0},original:{column:0,line:0},source:""};let n,s;this.stringify(this.root,((o,a,u)=>{this.css+=o;if(a&&u!=="end"){r.generated.line=t;r.generated.column=e-1;if(a.source&&a.source.start){r.source=this.sourcePath(a);r.original.line=a.source.start.line;r.original.column=a.source.start.column-1;this.map.addMapping(r)}else{r.source=i;r.original.line=1;r.original.column=0;this.map.addMapping(r)}}n=o.match(/\n/g);if(n){t+=n.length;s=o.lastIndexOf("\n");e=o.length-s}else{e+=o.length}if(a&&u!=="start"){let n=a.parent||{raws:{}};let s=a.type==="decl"||a.type==="atrule"&&!a.nodes;if(!s||a!==n.last||n.raws.semicolon){if(a.source&&a.source.end){r.source=this.sourcePath(a);r.original.line=a.source.end.line;r.original.column=a.source.end.column-1;r.generated.line=t;r.generated.column=e-2;this.map.addMapping(r)}else{r.source=i;r.original.line=1;r.original.column=0;r.generated.line=t;r.generated.column=e-1;this.map.addMapping(r)}}}}))}isAnnotation(){if(this.isInline()){return true}if(typeof this.mapOpts.annotation!=="undefined"){return this.mapOpts.annotation}if(this.previous().length){return this.previous().some((t=>t.annotation))}return true}isInline(){if(typeof this.mapOpts.inline!=="undefined"){return this.mapOpts.inline}let t=this.mapOpts.annotation;if(typeof t!=="undefined"&&t!==true){return false}if(this.previous().length){return this.previous().some((t=>t.inline))}return true}isMap(){if(typeof this.opts.map!=="undefined"){return!!this.opts.map}return this.previous().length>0}isSourcesContent(){if(typeof this.mapOpts.sourcesContent!=="undefined"){return this.mapOpts.sourcesContent}if(this.previous().length){return this.previous().some((t=>t.withContent()))}return true}outputFile(){if(this.opts.to){return this.path(this.opts.to)}else if(this.opts.from){return this.path(this.opts.from)}else{return"to.css"}}path(t){if(this.mapOpts.absolute)return t;if(t.charCodeAt(0)===60)return t;if(/^\w+:\/\//.test(t))return t;let e=this.memoizedPaths.get(t);if(e)return e;let i=this.opts.to?su(this.opts.to):".";if(typeof this.mapOpts.annotation==="string"){i=su(au(i,this.mapOpts.annotation))}let r=ou(i,t);this.memoizedPaths.set(t,r);return r}previous(){if(!this.previousMaps){this.previousMaps=[];if(this.root){this.root.walk((t=>{if(t.source&&t.source.input.map){let e=t.source.input.map;if(!this.previousMaps.includes(e)){this.previousMaps.push(e)}}}))}else{let t=new lu(this.originalCSS,this.opts);if(t.map)this.previousMaps.push(t.map)}}return this.previousMaps}setSourcesContent(){let t={};if(this.root){this.root.walk((e=>{if(e.source){let i=e.source.input.from;if(i&&!t[i]){t[i]=true;let r=this.usesFileUrls?this.toFileUrl(i):this.toUrl(this.path(i));this.map.setSourceContent(r,e.source.input.css)}}}))}else if(this.css){let t=this.opts.from?this.toUrl(this.path(this.opts.from)):"<no source>";this.map.setSourceContent(t,this.css)}}sourcePath(t){if(this.mapOpts.from){return this.toUrl(this.mapOpts.from)}else if(this.usesFileUrls){return this.toFileUrl(t.source.input.from)}else{return this.toUrl(this.path(t.source.input.from))}}toBase64(t){if(Buffer){return Buffer.from(t).toString("base64")}else{return window.btoa(unescape(encodeURIComponent(t)))}}toFileUrl(t){let e=this.memoizedFileURLs.get(t);if(e)return e;if(fu){let e=fu(t).toString();this.memoizedFileURLs.set(t,e);return e}else{throw new Error("`map.absolute` option is not available in this PostCSS build")}}toUrl(t){let e=this.memoizedURLs.get(t);if(e)return e;if(uu==="\\"){t=t.replace(/\\/g,"/")}let i=encodeURI(t).replace(/[#?]/g,encodeURIComponent);this.memoizedURLs.set(t,i);return i}};var vu=du;let pu=Oa;let mu=class t extends pu{constructor(t){super(t);this.type="comment"}};var gu=mu;mu.default=mu;let{isClean:yu,my:bu}=ca;let wu=_a;let Su=gu;let ku=Oa;let Cu,Mu,Iu,Ou;function Au(t){return t.map((t=>{if(t.nodes)t.nodes=Au(t.nodes);delete t.source;return t}))}function Tu(t){t[yu]=false;if(t.proxyOf.nodes){for(let e of t.proxyOf.nodes){Tu(e)}}}let _u=class t extends ku{append(...t){for(let e of t){let t=this.normalize(e,this.last);for(let e of t)this.proxyOf.nodes.push(e)}this.markDirty();return this}cleanRaws(t){super.cleanRaws(t);if(this.nodes){for(let e of this.nodes)e.cleanRaws(t)}}each(t){if(!this.proxyOf.nodes)return void 0;let e=this.getIterator();let i,r;while(this.indexes[e]<this.proxyOf.nodes.length){i=this.indexes[e];r=t(this.proxyOf.nodes[i],i);if(r===false)break;this.indexes[e]+=1}delete this.indexes[e];return r}every(t){return this.nodes.every(t)}getIterator(){if(!this.lastEach)this.lastEach=0;if(!this.indexes)this.indexes={};this.lastEach+=1;let t=this.lastEach;this.indexes[t]=0;return t}getProxyProcessor(){return{get(t,e){if(e==="proxyOf"){return t}else if(!t[e]){return t[e]}else if(e==="each"||typeof e==="string"&&e.startsWith("walk")){return(...i)=>t[e](...i.map((t=>{if(typeof t==="function"){return(e,i)=>t(e.toProxy(),i)}else{return t}})))}else if(e==="every"||e==="some"){return i=>t[e](((t,...e)=>i(t.toProxy(),...e)))}else if(e==="root"){return()=>t.root().toProxy()}else if(e==="nodes"){return t.nodes.map((t=>t.toProxy()))}else if(e==="first"||e==="last"){return t[e].toProxy()}else{return t[e]}},set(t,e,i){if(t[e]===i)return true;t[e]=i;if(e==="name"||e==="params"||e==="selector"){t.markDirty()}return true}}}index(t){if(typeof t==="number")return t;if(t.proxyOf)t=t.proxyOf;return this.proxyOf.nodes.indexOf(t)}insertAfter(t,e){let i=this.index(t);let r=this.normalize(e,this.proxyOf.nodes[i]).reverse();i=this.index(t);for(let t of r)this.proxyOf.nodes.splice(i+1,0,t);let n;for(let t in this.indexes){n=this.indexes[t];if(i<n){this.indexes[t]=n+r.length}}this.markDirty();return this}insertBefore(t,e){let i=this.index(t);let r=i===0?"prepend":false;let n=this.normalize(e,this.proxyOf.nodes[i],r).reverse();i=this.index(t);for(let t of n)this.proxyOf.nodes.splice(i,0,t);let s;for(let t in this.indexes){s=this.indexes[t];if(i<=s){this.indexes[t]=s+n.length}}this.markDirty();return this}normalize(e,i){if(typeof e==="string"){e=Au(Cu(e).nodes)}else if(typeof e==="undefined"){e=[]}else if(Array.isArray(e)){e=e.slice(0);for(let t of e){if(t.parent)t.parent.removeChild(t,"ignore")}}else if(e.type==="root"&&this.type!=="document"){e=e.nodes.slice(0);for(let t of e){if(t.parent)t.parent.removeChild(t,"ignore")}}else if(e.type){e=[e]}else if(e.prop){if(typeof e.value==="undefined"){throw new Error("Value field is missed in node creation")}else if(typeof e.value!=="string"){e.value=String(e.value)}e=[new wu(e)]}else if(e.selector){e=[new Mu(e)]}else if(e.name){e=[new Iu(e)]}else if(e.text){e=[new Su(e)]}else{throw new Error("Unknown node type in node creation")}let r=e.map((e=>{if(!e[bu])t.rebuild(e);e=e.proxyOf;if(e.parent)e.parent.removeChild(e);if(e[yu])Tu(e);if(typeof e.raws.before==="undefined"){if(i&&typeof i.raws.before!=="undefined"){e.raws.before=i.raws.before.replace(/\S/g,"")}}e.parent=this.proxyOf;return e}));return r}prepend(...t){t=t.reverse();for(let e of t){let t=this.normalize(e,this.first,"prepend").reverse();for(let e of t)this.proxyOf.nodes.unshift(e);for(let e in this.indexes){this.indexes[e]=this.indexes[e]+t.length}}this.markDirty();return this}push(t){t.parent=this;this.proxyOf.nodes.push(t);return this}removeAll(){for(let t of this.proxyOf.nodes)t.parent=void 0;this.proxyOf.nodes=[];this.markDirty();return this}removeChild(t){t=this.index(t);this.proxyOf.nodes[t].parent=void 0;this.proxyOf.nodes.splice(t,1);let e;for(let i in this.indexes){e=this.indexes[i];if(e>=t){this.indexes[i]=e-1}}this.markDirty();return this}replaceValues(t,e,i){if(!i){i=e;e={}}this.walkDecls((r=>{if(e.props&&!e.props.includes(r.prop))return;if(e.fast&&!r.value.includes(e.fast))return;r.value=r.value.replace(t,i)}));this.markDirty();return this}some(t){return this.nodes.some(t)}walk(t){return this.each(((e,i)=>{let r;try{r=t(e,i)}catch(t){throw e.addToError(t)}if(r!==false&&e.walk){r=e.walk(t)}return r}))}walkAtRules(t,e){if(!e){e=t;return this.walk(((t,i)=>{if(t.type==="atrule"){return e(t,i)}}))}if(t instanceof RegExp){return this.walk(((i,r)=>{if(i.type==="atrule"&&t.test(i.name)){return e(i,r)}}))}return this.walk(((i,r)=>{if(i.type==="atrule"&&i.name===t){return e(i,r)}}))}walkComments(t){return this.walk(((e,i)=>{if(e.type==="comment"){return t(e,i)}}))}walkDecls(t,e){if(!e){e=t;return this.walk(((t,i)=>{if(t.type==="decl"){return e(t,i)}}))}if(t instanceof RegExp){return this.walk(((i,r)=>{if(i.type==="decl"&&t.test(i.prop)){return e(i,r)}}))}return this.walk(((i,r)=>{if(i.type==="decl"&&i.prop===t){return e(i,r)}}))}walkRules(t,e){if(!e){e=t;return this.walk(((t,i)=>{if(t.type==="rule"){return e(t,i)}}))}if(t instanceof RegExp){return this.walk(((i,r)=>{if(i.type==="rule"&&t.test(i.selector)){return e(i,r)}}))}return this.walk(((i,r)=>{if(i.type==="rule"&&i.selector===t){return e(i,r)}}))}get first(){if(!this.proxyOf.nodes)return void 0;return this.proxyOf.nodes[0]}get last(){if(!this.proxyOf.nodes)return void 0;return this.proxyOf.nodes[this.proxyOf.nodes.length-1]}};_u.registerParse=t=>{Cu=t};_u.registerRule=t=>{Mu=t};_u.registerAtRule=t=>{Iu=t};_u.registerRoot=t=>{Ou=t};var Eu=_u;_u.default=_u;_u.rebuild=t=>{if(t.type==="atrule"){Object.setPrototypeOf(t,Iu.prototype)}else if(t.type==="rule"){Object.setPrototypeOf(t,Mu.prototype)}else if(t.type==="decl"){Object.setPrototypeOf(t,wu.prototype)}else if(t.type==="comment"){Object.setPrototypeOf(t,Su.prototype)}else if(t.type==="root"){Object.setPrototypeOf(t,Ou.prototype)}t[bu]=true;if(t.nodes){t.nodes.forEach((t=>{_u.rebuild(t)}))}};let Ru=Eu;let xu,ju;let Nu=class t extends Ru{constructor(t){super({type:"document",...t});if(!this.nodes){this.nodes=[]}}toResult(t={}){let e=new xu(new ju,this,t);return e.stringify()}};Nu.registerLazyResult=t=>{xu=t};Nu.registerProcessor=t=>{ju=t};var Du=Nu;Nu.default=Nu;let Fu=class t{constructor(t,e={}){this.type="warning";this.text=t;if(e.node&&e.node.source){let t=e.node.rangeBy(e);this.line=t.start.line;this.column=t.start.column;this.endLine=t.end.line;this.endColumn=t.end.column}for(let t in e)this[t]=e[t]}toString(){if(this.node){return this.node.error(this.text,{index:this.index,plugin:this.plugin,word:this.word}).message}if(this.plugin){return this.plugin+": "+this.text}return this.text}};var Lu=Fu;Fu.default=Fu;let Pu=Lu;let Bu=class t{constructor(t,e,i){this.processor=t;this.messages=[];this.root=e;this.opts=i;this.css=void 0;this.map=void 0}toString(){return this.css}warn(t,e={}){if(!e.plugin){if(this.lastPlugin&&this.lastPlugin.postcssPlugin){e.plugin=this.lastPlugin.postcssPlugin}}let i=new Pu(t,e);this.messages.push(i);return i}warnings(){return this.messages.filter((t=>t.type==="warning"))}get content(){return this.css}};var Uu=Bu;Bu.default=Bu;const Gu="'".charCodeAt(0);const Wu='"'.charCodeAt(0);const zu="\\".charCodeAt(0);const Vu="/".charCodeAt(0);const Hu="\n".charCodeAt(0);const Yu=" ".charCodeAt(0);const Zu="\f".charCodeAt(0);const Xu="\t".charCodeAt(0);const $u="\r".charCodeAt(0);const Ju="[".charCodeAt(0);const Ku="]".charCodeAt(0);const qu="(".charCodeAt(0);const Qu=")".charCodeAt(0);const tf="{".charCodeAt(0);const ef="}".charCodeAt(0);const rf=";".charCodeAt(0);const nf="*".charCodeAt(0);const sf=":".charCodeAt(0);const of="@".charCodeAt(0);const af=/[\t\n\f\r "#'()/;[\\\]{}]/g;const uf=/[\t\n\f\r !"#'():;@[\\\]{}]|\/(?=\*)/g;const ff=/.[\r\n"'(/\\]/;const lf=/[\da-f]/i;var cf=function t(e,i={}){let r=e.css.valueOf();let n=i.ignoreErrors;let s,o,a,u,f;let l,c,h,d,v;let p=r.length;let m=0;let g=[];let y=[];function b(){return m}function w(t){throw e.error("Unclosed "+t,m)}function S(){return y.length===0&&m>=p}function k(t){if(y.length)return y.pop();if(m>=p)return;let e=t?t.ignoreUnclosed:false;s=r.charCodeAt(m);switch(s){case Hu:case Yu:case Xu:case $u:case Zu:{o=m;do{o+=1;s=r.charCodeAt(o)}while(s===Yu||s===Hu||s===Xu||s===$u||s===Zu);v=["space",r.slice(m,o)];m=o-1;break}case Ju:case Ku:case tf:case ef:case sf:case rf:case Qu:{let t=String.fromCharCode(s);v=[t,t,m];break}case qu:{h=g.length?g.pop()[1]:"";d=r.charCodeAt(m+1);if(h==="url"&&d!==Gu&&d!==Wu&&d!==Yu&&d!==Hu&&d!==Xu&&d!==Zu&&d!==$u){o=m;do{l=false;o=r.indexOf(")",o+1);if(o===-1){if(n||e){o=m;break}else{w("bracket")}}c=o;while(r.charCodeAt(c-1)===zu){c-=1;l=!l}}while(l);v=["brackets",r.slice(m,o+1),m,o];m=o}else{o=r.indexOf(")",m+1);u=r.slice(m,o+1);if(o===-1||ff.test(u)){v=["(","(",m]}else{v=["brackets",u,m,o];m=o}}break}case Gu:case Wu:{a=s===Gu?"'":'"';o=m;do{l=false;o=r.indexOf(a,o+1);if(o===-1){if(n||e){o=m+1;break}else{w("string")}}c=o;while(r.charCodeAt(c-1)===zu){c-=1;l=!l}}while(l);v=["string",r.slice(m,o+1),m,o];m=o;break}case of:{af.lastIndex=m+1;af.test(r);if(af.lastIndex===0){o=r.length-1}else{o=af.lastIndex-2}v=["at-word",r.slice(m,o+1),m,o];m=o;break}case zu:{o=m;f=true;while(r.charCodeAt(o+1)===zu){o+=1;f=!f}s=r.charCodeAt(o+1);if(f&&s!==Vu&&s!==Yu&&s!==Hu&&s!==Xu&&s!==$u&&s!==Zu){o+=1;if(lf.test(r.charAt(o))){while(lf.test(r.charAt(o+1))){o+=1}if(r.charCodeAt(o+1)===Yu){o+=1}}}v=["word",r.slice(m,o+1),m,o];m=o;break}default:{if(s===Vu&&r.charCodeAt(m+1)===nf){o=r.indexOf("*/",m+2)+1;if(o===0){if(n||e){o=r.length}else{w("comment")}}v=["comment",r.slice(m,o+1),m,o];m=o}else{uf.lastIndex=m+1;uf.test(r);if(uf.lastIndex===0){o=r.length-1}else{o=uf.lastIndex-2}v=["word",r.slice(m,o+1),m,o];g.push(v);m=o}break}}m++;return v}function C(t){y.push(t)}return{back:C,endOfFile:S,nextToken:k,position:b}};let hf=Eu;let df=class t extends hf{constructor(t){super(t);this.type="atrule"}append(...t){if(!this.proxyOf.nodes)this.nodes=[];return super.append(...t)}prepend(...t){if(!this.proxyOf.nodes)this.nodes=[];return super.prepend(...t)}};var vf=df;df.default=df;hf.registerAtRule(df);let pf=Eu;let mf,gf;let yf=class t extends pf{constructor(t){super(t);this.type="root";if(!this.nodes)this.nodes=[]}normalize(t,e,i){let r=super.normalize(t);if(e){if(i==="prepend"){if(this.nodes.length>1){e.raws.before=this.nodes[1].raws.before}else{delete e.raws.before}}else if(this.first!==e){for(let t of r){t.raws.before=e.raws.before}}}return r}removeChild(t,e){let i=this.index(t);if(!e&&i===0&&this.nodes.length>1){this.nodes[1].raws.before=this.nodes[i].raws.before}return super.removeChild(t)}toResult(t={}){let e=new mf(new gf,this,t);return e.stringify()}};yf.registerLazyResult=t=>{mf=t};yf.registerProcessor=t=>{gf=t};var bf=yf;yf.default=yf;pf.registerRoot(yf);let wf={comma(t){return wf.split(t,[","],true)},space(t){let e=[" ","\n","\t"];return wf.split(t,e)},split(t,e,i){let r=[];let n="";let s=false;let o=0;let a=false;let u="";let f=false;for(let i of t){if(f){f=false}else if(i==="\\"){f=true}else if(a){if(i===u){a=false}}else if(i==='"'||i==="'"){a=true;u=i}else if(i==="("){o+=1}else if(i===")"){if(o>0)o-=1}else if(o===0){if(e.includes(i))s=true}if(s){if(n!=="")r.push(n.trim());n="";s=false}else{n+=i}}if(i||n!=="")r.push(n.trim());return r}};var Sf=wf;wf.default=wf;let kf=Eu;let Cf=Sf;let Mf=class t extends kf{constructor(t){super(t);this.type="rule";if(!this.nodes)this.nodes=[]}get selectors(){return Cf.comma(this.selector)}set selectors(t){let e=this.selector?this.selector.match(/,\s*/):null;let i=e?e[0]:","+this.raw("between","beforeOpen");this.selector=t.join(i)}};var If=Mf;Mf.default=Mf;kf.registerRule(Mf);let Of=_a;let Af=cf;let Tf=gu;let _f=vf;let Ef=bf;let Rf=If;const xf={empty:true,space:true};function jf(t){for(let e=t.length-1;e>=0;e--){let i=t[e];let r=i[3]||i[2];if(r)return r}}let Nf=class t{constructor(t){this.input=t;this.root=new Ef;this.current=this.root;this.spaces="";this.semicolon=false;this.createTokenizer();this.root.source={input:t,start:{column:1,line:1,offset:0}}}atrule(t){let e=new _f;e.name=t[1].slice(1);if(e.name===""){this.unnamedAtrule(e,t)}this.init(e,t[2]);let i;let r;let n;let s=false;let o=false;let a=[];let u=[];while(!this.tokenizer.endOfFile()){t=this.tokenizer.nextToken();i=t[0];if(i==="("||i==="["){u.push(i==="("?")":"]")}else if(i==="{"&&u.length>0){u.push("}")}else if(i===u[u.length-1]){u.pop()}if(u.length===0){if(i===";"){e.source.end=this.getPosition(t[2]);e.source.end.offset++;this.semicolon=true;break}else if(i==="{"){o=true;break}else if(i==="}"){if(a.length>0){n=a.length-1;r=a[n];while(r&&r[0]==="space"){r=a[--n]}if(r){e.source.end=this.getPosition(r[3]||r[2]);e.source.end.offset++}}this.end(t);break}else{a.push(t)}}else{a.push(t)}if(this.tokenizer.endOfFile()){s=true;break}}e.raws.between=this.spacesAndCommentsFromEnd(a);if(a.length){e.raws.afterName=this.spacesAndCommentsFromStart(a);this.raw(e,"params",a);if(s){t=a[a.length-1];e.source.end=this.getPosition(t[3]||t[2]);e.source.end.offset++;this.spaces=e.raws.between;e.raws.between=""}}else{e.raws.afterName="";e.params=""}if(o){e.nodes=[];this.current=e}}checkMissedSemicolon(t){let e=this.colon(t);if(e===false)return;let i=0;let r;for(let n=e-1;n>=0;n--){r=t[n];if(r[0]!=="space"){i+=1;if(i===2)break}}throw this.input.error("Missed semicolon",r[0]==="word"?r[3]+1:r[2])}colon(t){let e=0;let i,r,n;for(let[s,o]of t.entries()){i=o;r=i[0];if(r==="("){e+=1}if(r===")"){e-=1}if(e===0&&r===":"){if(!n){this.doubleColon(i)}else if(n[0]==="word"&&n[1]==="progid"){continue}else{return s}}n=i}return false}comment(t){let e=new Tf;this.init(e,t[2]);e.source.end=this.getPosition(t[3]||t[2]);e.source.end.offset++;let i=t[1].slice(2,-2);if(/^\s*$/.test(i)){e.text="";e.raws.left=i;e.raws.right=""}else{let t=i.match(/^(\s*)([^]*\S)(\s*)$/);e.text=t[2];e.raws.left=t[1];e.raws.right=t[3]}}createTokenizer(){this.tokenizer=Af(this.input)}decl(t,e){let i=new Of;this.init(i,t[0][2]);let r=t[t.length-1];if(r[0]===";"){this.semicolon=true;t.pop()}i.source.end=this.getPosition(r[3]||r[2]||jf(t));i.source.end.offset++;while(t[0][0]!=="word"){if(t.length===1)this.unknownWord(t);i.raws.before+=t.shift()[1]}i.source.start=this.getPosition(t[0][2]);i.prop="";while(t.length){let e=t[0][0];if(e===":"||e==="space"||e==="comment"){break}i.prop+=t.shift()[1]}i.raws.between="";let n;while(t.length){n=t.shift();if(n[0]===":"){i.raws.between+=n[1];break}else{if(n[0]==="word"&&/\w/.test(n[1])){this.unknownWord([n])}i.raws.between+=n[1]}}if(i.prop[0]==="_"||i.prop[0]==="*"){i.raws.before+=i.prop[0];i.prop=i.prop.slice(1)}let s=[];let o;while(t.length){o=t[0][0];if(o!=="space"&&o!=="comment")break;s.push(t.shift())}this.precheckMissedSemicolon(t);for(let e=t.length-1;e>=0;e--){n=t[e];if(n[1].toLowerCase()==="!important"){i.important=true;let r=this.stringFrom(t,e);r=this.spacesFromEnd(t)+r;if(r!==" !important")i.raws.important=r;break}else if(n[1].toLowerCase()==="important"){let r=t.slice(0);let n="";for(let t=e;t>0;t--){let e=r[t][0];if(n.trim().indexOf("!")===0&&e!=="space"){break}n=r.pop()[1]+n}if(n.trim().indexOf("!")===0){i.important=true;i.raws.important=n;t=r}}if(n[0]!=="space"&&n[0]!=="comment"){break}}let a=t.some((t=>t[0]!=="space"&&t[0]!=="comment"));if(a){i.raws.between+=s.map((t=>t[1])).join("");s=[]}this.raw(i,"value",s.concat(t),e);if(i.value.includes(":")&&!e){this.checkMissedSemicolon(t)}}doubleColon(t){throw this.input.error("Double colon",{offset:t[2]},{offset:t[2]+t[1].length})}emptyRule(t){let e=new Rf;this.init(e,t[2]);e.selector="";e.raws.between="";this.current=e}end(t){if(this.current.nodes&&this.current.nodes.length){this.current.raws.semicolon=this.semicolon}this.semicolon=false;this.current.raws.after=(this.current.raws.after||"")+this.spaces;this.spaces="";if(this.current.parent){this.current.source.end=this.getPosition(t[2]);this.current.source.end.offset++;this.current=this.current.parent}else{this.unexpectedClose(t)}}endFile(){if(this.current.parent)this.unclosedBlock();if(this.current.nodes&&this.current.nodes.length){this.current.raws.semicolon=this.semicolon}this.current.raws.after=(this.current.raws.after||"")+this.spaces;this.root.source.end=this.getPosition(this.tokenizer.position())}freeSemicolon(t){this.spaces+=t[1];if(this.current.nodes){let t=this.current.nodes[this.current.nodes.length-1];if(t&&t.type==="rule"&&!t.raws.ownSemicolon){t.raws.ownSemicolon=this.spaces;this.spaces=""}}}getPosition(t){let e=this.input.fromOffset(t);return{column:e.col,line:e.line,offset:t}}init(t,e){this.current.push(t);t.source={input:this.input,start:this.getPosition(e)};t.raws.before=this.spaces;this.spaces="";if(t.type!=="comment")this.semicolon=false}other(t){let e=false;let i=null;let r=false;let n=null;let s=[];let o=t[1].startsWith("--");let a=[];let u=t;while(u){i=u[0];a.push(u);if(i==="("||i==="["){if(!n)n=u;s.push(i==="("?")":"]")}else if(o&&r&&i==="{"){if(!n)n=u;s.push("}")}else if(s.length===0){if(i===";"){if(r){this.decl(a,o);return}else{break}}else if(i==="{"){this.rule(a);return}else if(i==="}"){this.tokenizer.back(a.pop());e=true;break}else if(i===":"){r=true}}else if(i===s[s.length-1]){s.pop();if(s.length===0)n=null}u=this.tokenizer.nextToken()}if(this.tokenizer.endOfFile())e=true;if(s.length>0)this.unclosedBracket(n);if(e&&r){if(!o){while(a.length){u=a[a.length-1][0];if(u!=="space"&&u!=="comment")break;this.tokenizer.back(a.pop())}}this.decl(a,o)}else{this.unknownWord(a)}}parse(){let t;while(!this.tokenizer.endOfFile()){t=this.tokenizer.nextToken();switch(t[0]){case"space":this.spaces+=t[1];break;case";":this.freeSemicolon(t);break;case"}":this.end(t);break;case"comment":this.comment(t);break;case"at-word":this.atrule(t);break;case"{":this.emptyRule(t);break;default:this.other(t);break}}this.endFile()}precheckMissedSemicolon(){}raw(t,e,i,r){let n,s;let o=i.length;let a="";let u=true;let f,l;for(let t=0;t<o;t+=1){n=i[t];s=n[0];if(s==="space"&&t===o-1&&!r){u=false}else if(s==="comment"){l=i[t-1]?i[t-1][0]:"empty";f=i[t+1]?i[t+1][0]:"empty";if(!xf[l]&&!xf[f]){if(a.slice(-1)===","){u=false}else{a+=n[1]}}else{u=false}}else{a+=n[1]}}if(!u){let r=i.reduce(((t,e)=>t+e[1]),"");t.raws[e]={raw:r,value:a}}t[e]=a}rule(t){t.pop();let e=new Rf;this.init(e,t[0][2]);e.raws.between=this.spacesAndCommentsFromEnd(t);this.raw(e,"selector",t);this.current=e}spacesAndCommentsFromEnd(t){let e;let i="";while(t.length){e=t[t.length-1][0];if(e!=="space"&&e!=="comment")break;i=t.pop()[1]+i}return i}spacesAndCommentsFromStart(t){let e;let i="";while(t.length){e=t[0][0];if(e!=="space"&&e!=="comment")break;i+=t.shift()[1]}return i}spacesFromEnd(t){let e;let i="";while(t.length){e=t[t.length-1][0];if(e!=="space")break;i=t.pop()[1]+i}return i}stringFrom(t,e){let i="";for(let r=e;r<t.length;r++){i+=t[r][1]}t.splice(e,t.length-e);return i}unclosedBlock(){let t=this.current.source.start;throw this.input.error("Unclosed block",t.line,t.column)}unclosedBracket(t){throw this.input.error("Unclosed bracket",{offset:t[2]},{offset:t[2]+1})}unexpectedClose(t){throw this.input.error("Unexpected }",{offset:t[2]},{offset:t[2]+1})}unknownWord(t){throw this.input.error("Unknown word",{offset:t[0][2]},{offset:t[0][2]+t[0][1].length})}unnamedAtrule(t,e){throw this.input.error("At-rule without name",{offset:e[2]},{offset:e[2]+e[1].length})}};var Df=Nf;let Ff=Eu;let Lf=Df;let Pf=iu;function Bf(t,e){let i=new Pf(t,e);let r=new Lf(i);try{r.parse()}catch(t){throw t}return r.root}var Uf=Bf;Bf.default=Bf;Ff.registerParse(Bf);let{isClean:Gf,my:Wf}=ca;let zf=vu;let Vf=ya;let Hf=Eu;let Yf=Du;let Zf=Uu;let Xf=Uf;let $f=bf;const Jf={atrule:"AtRule",comment:"Comment",decl:"Declaration",document:"Document",root:"Root",rule:"Rule"};const Kf={AtRule:true,AtRuleExit:true,Comment:true,CommentExit:true,Declaration:true,DeclarationExit:true,Document:true,DocumentExit:true,Once:true,OnceExit:true,postcssPlugin:true,prepare:true,Root:true,RootExit:true,Rule:true,RuleExit:true};const qf={Once:true,postcssPlugin:true,prepare:true};const Qf=0;function tl(t){return typeof t==="object"&&typeof t.then==="function"}function el(t){let e=false;let i=Jf[t.type];if(t.type==="decl"){e=t.prop.toLowerCase()}else if(t.type==="atrule"){e=t.name.toLowerCase()}if(e&&t.append){return[i,i+"-"+e,Qf,i+"Exit",i+"Exit-"+e]}else if(e){return[i,i+"-"+e,i+"Exit",i+"Exit-"+e]}else if(t.append){return[i,Qf,i+"Exit"]}else{return[i,i+"Exit"]}}function il(t){let e;if(t.type==="document"){e=["Document",Qf,"DocumentExit"]}else if(t.type==="root"){e=["Root",Qf,"RootExit"]}else{e=el(t)}return{eventIndex:0,events:e,iterator:0,node:t,visitorIndex:0,visitors:[]}}function rl(t){t[Gf]=false;if(t.nodes)t.nodes.forEach((t=>rl(t)));return t}let nl={};let sl=class t{constructor(e,i,r){this.stringified=false;this.processed=false;let n;if(typeof i==="object"&&i!==null&&(i.type==="root"||i.type==="document")){n=rl(i)}else if(i instanceof t||i instanceof Zf){n=rl(i.root);if(i.map){if(typeof r.map==="undefined")r.map={};if(!r.map.inline)r.map.inline=false;r.map.prev=i.map}}else{let t=Xf;if(r.syntax)t=r.syntax.parse;if(r.parser)t=r.parser;if(t.parse)t=t.parse;try{n=t(i,r)}catch(t){this.processed=true;this.error=t}if(n&&!n[Wf]){Hf.rebuild(n)}}this.result=new Zf(e,n,r);this.helpers={...nl,postcss:nl,result:this.result};this.plugins=this.processor.plugins.map((t=>{if(typeof t==="object"&&t.prepare){return{...t,...t.prepare(this.result)}}else{return t}}))}async(){if(this.error)return Promise.reject(this.error);if(this.processed)return Promise.resolve(this.result);if(!this.processing){this.processing=this.runAsync()}return this.processing}catch(t){return this.async().catch(t)}finally(t){return this.async().then(t,t)}getAsyncError(){throw new Error("Use process(css).then(cb) to work with async plugins")}handleError(t,e){let i=this.result.lastPlugin;try{if(e)e.addToError(t);this.error=t;if(t.name==="CssSyntaxError"&&!t.plugin){t.plugin=i.postcssPlugin;t.setMessage()}}catch(t){if(console&&console.error)console.error(t)}return t}prepareVisitors(){this.listeners={};let t=(t,e,i)=>{if(!this.listeners[e])this.listeners[e]=[];this.listeners[e].push([t,i])};for(let e of this.plugins){if(typeof e==="object"){for(let i in e){if(!Kf[i]&&/^[A-Z]/.test(i)){throw new Error(`Unknown event ${i} in ${e.postcssPlugin}. Try to update PostCSS (${this.processor.version} now).`)}if(!qf[i]){if(typeof e[i]==="object"){for(let r in e[i]){if(r==="*"){t(e,i,e[i][r])}else{t(e,i+"-"+r.toLowerCase(),e[i][r])}}}else if(typeof e[i]==="function"){t(e,i,e[i])}}}}}this.hasListener=Object.keys(this.listeners).length>0}async runAsync(){this.plugin=0;for(let t=0;t<this.plugins.length;t++){let e=this.plugins[t];let i=this.runOnRoot(e);if(tl(i)){try{await i}catch(t){throw this.handleError(t)}}}this.prepareVisitors();if(this.hasListener){let t=this.result.root;while(!t[Gf]){t[Gf]=true;let e=[il(t)];while(e.length>0){let t=this.visitTick(e);if(tl(t)){try{await t}catch(t){let i=e[e.length-1].node;throw this.handleError(t,i)}}}}if(this.listeners.OnceExit){for(let[e,i]of this.listeners.OnceExit){this.result.lastPlugin=e;try{if(t.type==="document"){let e=t.nodes.map((t=>i(t,this.helpers)));await Promise.all(e)}else{await i(t,this.helpers)}}catch(t){throw this.handleError(t)}}}}this.processed=true;return this.stringify()}runOnRoot(t){this.result.lastPlugin=t;try{if(typeof t==="object"&&t.Once){if(this.result.root.type==="document"){let e=this.result.root.nodes.map((e=>t.Once(e,this.helpers)));if(tl(e[0])){return Promise.all(e)}return e}return t.Once(this.result.root,this.helpers)}else if(typeof t==="function"){return t(this.result.root,this.result)}}catch(t){throw this.handleError(t)}}stringify(){if(this.error)throw this.error;if(this.stringified)return this.result;this.stringified=true;this.sync();let t=this.result.opts;let e=Vf;if(t.syntax)e=t.syntax.stringify;if(t.stringifier)e=t.stringifier;if(e.stringify)e=e.stringify;let i=new zf(e,this.result.root,this.result.opts);let r=i.generate();this.result.css=r[0];this.result.map=r[1];return this.result}sync(){if(this.error)throw this.error;if(this.processed)return this.result;this.processed=true;if(this.processing){throw this.getAsyncError()}for(let t of this.plugins){let e=this.runOnRoot(t);if(tl(e)){throw this.getAsyncError()}}this.prepareVisitors();if(this.hasListener){let t=this.result.root;while(!t[Gf]){t[Gf]=true;this.walkSync(t)}if(this.listeners.OnceExit){if(t.type==="document"){for(let e of t.nodes){this.visitSync(this.listeners.OnceExit,e)}}else{this.visitSync(this.listeners.OnceExit,t)}}}return this.result}then(t,e){return this.async().then(t,e)}toString(){return this.css}visitSync(t,e){for(let[i,r]of t){this.result.lastPlugin=i;let t;try{t=r(e,this.helpers)}catch(t){throw this.handleError(t,e.proxyOf)}if(e.type!=="root"&&e.type!=="document"&&!e.parent){return true}if(tl(t)){throw this.getAsyncError()}}}visitTick(t){let e=t[t.length-1];let{node:i,visitors:r}=e;if(i.type!=="root"&&i.type!=="document"&&!i.parent){t.pop();return}if(r.length>0&&e.visitorIndex<r.length){let[t,n]=r[e.visitorIndex];e.visitorIndex+=1;if(e.visitorIndex===r.length){e.visitors=[];e.visitorIndex=0}this.result.lastPlugin=t;try{return n(i.toProxy(),this.helpers)}catch(t){throw this.handleError(t,i)}}if(e.iterator!==0){let r=e.iterator;let n;while(n=i.nodes[i.indexes[r]]){i.indexes[r]+=1;if(!n[Gf]){n[Gf]=true;t.push(il(n));return}}e.iterator=0;delete i.indexes[r]}let n=e.events;while(e.eventIndex<n.length){let t=n[e.eventIndex];e.eventIndex+=1;if(t===Qf){if(i.nodes&&i.nodes.length){i[Gf]=true;e.iterator=i.getIterator()}return}else if(this.listeners[t]){e.visitors=this.listeners[t];return}}t.pop()}walkSync(t){t[Gf]=true;let e=el(t);for(let i of e){if(i===Qf){if(t.nodes){t.each((t=>{if(!t[Gf])this.walkSync(t)}))}}else{let e=this.listeners[i];if(e){if(this.visitSync(e,t.toProxy()))return}}}}warnings(){return this.sync().warnings()}get content(){return this.stringify().content}get css(){return this.stringify().css}get map(){return this.stringify().map}get messages(){return this.sync().messages}get opts(){return this.result.opts}get processor(){return this.result.processor}get root(){return this.sync().root}get[Symbol.toStringTag](){return"LazyResult"}};sl.registerPostcss=t=>{nl=t};var ol=sl;sl.default=sl;$f.registerLazyResult(sl);Yf.registerLazyResult(sl);let al=vu;let ul=ya;let fl=Uf;const ll=Uu;let cl=class t{constructor(t,e,i){e=e.toString();this.stringified=false;this._processor=t;this._css=e;this._opts=i;this._map=void 0;let r;let n=ul;this.result=new ll(this._processor,r,this._opts);this.result.css=e;let s=this;Object.defineProperty(this.result,"root",{get(){return s.root}});let o=new al(n,r,this._opts,e);if(o.isMap()){let[t,e]=o.generate();if(t){this.result.css=t}if(e){this.result.map=e}}else{o.clearAnnotation();this.result.css=o.css}}async(){if(this.error)return Promise.reject(this.error);return Promise.resolve(this.result)}catch(t){return this.async().catch(t)}finally(t){return this.async().then(t,t)}sync(){if(this.error)throw this.error;return this.result}then(t,e){return this.async().then(t,e)}toString(){return this._css}warnings(){return[]}get content(){return this.result.css}get css(){return this.result.css}get map(){return this.result.map}get messages(){return[]}get opts(){return this.result.opts}get processor(){return this.result.processor}get root(){if(this._root){return this._root}let t;let e=fl;try{t=e(this._css,this._opts)}catch(t){this.error=t}if(this.error){throw this.error}else{this._root=t;return t}}get[Symbol.toStringTag](){return"NoWorkResult"}};var hl=cl;cl.default=cl;let dl=hl;let vl=ol;let pl=Du;let ml=bf;let gl=class t{constructor(t=[]){this.version="8.4.38";this.plugins=this.normalize(t)}normalize(t){let e=[];for(let i of t){if(i.postcss===true){i=i()}else if(i.postcss){i=i.postcss}if(typeof i==="object"&&Array.isArray(i.plugins)){e=e.concat(i.plugins)}else if(typeof i==="object"&&i.postcssPlugin){e.push(i)}else if(typeof i==="function"){e.push(i)}else if(typeof i==="object"&&(i.parse||i.stringify));else{throw new Error(i+" is not a PostCSS plugin")}}return e}process(t,e={}){if(!this.plugins.length&&!e.parser&&!e.stringifier&&!e.syntax){return new dl(this,t,e)}else{return new vl(this,t,e)}}use(t){this.plugins=this.plugins.concat(this.normalize([t]));return this}};var yl=gl;gl.default=gl;ml.registerProcessor(gl);pl.registerProcessor(gl);let bl=_a;let wl=Ga;let Sl=gu;let kl=vf;let Cl=iu;let Ml=bf;let Il=If;function Ol(t,e){if(Array.isArray(t))return t.map((t=>Ol(t)));let{inputs:i,...r}=t;if(i){e=[];for(let t of i){let i={...t,__proto__:Cl.prototype};if(i.map){i.map={...i.map,__proto__:wl.prototype}}e.push(i)}}if(r.nodes){r.nodes=t.nodes.map((t=>Ol(t,e)))}if(r.source){let{inputId:t,...i}=r.source;r.source=i;if(t!=null){r.source.input=e[t]}}if(r.type==="root"){return new Ml(r)}else if(r.type==="decl"){return new bl(r)}else if(r.type==="rule"){return new Il(r)}else if(r.type==="comment"){return new Sl(r)}else if(r.type==="atrule"){return new kl(r)}else{throw new Error("Unknown node type: "+t.type)}}var Al=Ol;Ol.default=Ol;let Tl=la;let _l=_a;let El=ol;let Rl=Eu;let xl=yl;let jl=ya;let Nl=Al;let Dl=Du;let Fl=Lu;let Ll=gu;let Pl=vf;let Bl=Uu;let Ul=iu;let Gl=Uf;let Wl=Sf;let zl=If;let Vl=bf;let Hl=Oa;function Yl(...t){if(t.length===1&&Array.isArray(t[0])){t=t[0]}return new xl(t)}Yl.plugin=function t(e,i){let r=false;function n(...t){if(console&&console.warn&&!r){r=true;console.warn(e+": postcss.plugin was deprecated. Migration guide:\nhttps://evilmartians.com/chronicles/postcss-8-plugin-migration");if(process.env.LANG&&process.env.LANG.startsWith("cn")){console.warn(e+": 里面 postcss.plugin 被弃用. 迁移指南:\nhttps://www.w3ctech.com/topic/2226")}}let n=i(...t);n.postcssPlugin=e;n.postcssVersion=(new xl).version;return n}let s;Object.defineProperty(n,"postcss",{get(){if(!s)s=n();return s}});n.process=function(t,e,i){return Yl([n(i)]).process(t,e)};return n};Yl.stringify=jl;Yl.parse=Gl;Yl.fromJSON=Nl;Yl.list=Wl;Yl.comment=t=>new Ll(t);Yl.atRule=t=>new Pl(t);Yl.decl=t=>new _l(t);Yl.rule=t=>new zl(t);Yl.root=t=>new Vl(t);Yl.document=t=>new Dl(t);Yl.CssSyntaxError=Tl;Yl.Declaration=_l;Yl.Container=Rl;Yl.Processor=xl;Yl.Document=Dl;Yl.Comment=Ll;Yl.Warning=Fl;Yl.AtRule=Pl;Yl.Result=Bl;Yl.Input=Ul;Yl.Rule=zl;Yl.Root=Vl;Yl.Node=Hl;El.registerPostcss(Yl);var Zl=Yl;Yl.default=Yl;const Xl=qo(Zl);const $l={script:"noscript",altglyph:"altGlyph",altglyphdef:"altGlyphDef",altglyphitem:"altGlyphItem",animatecolor:"animateColor",animatemotion:"animateMotion",animatetransform:"animateTransform",clippath:"clipPath",feblend:"feBlend",fecolormatrix:"feColorMatrix",fecomponenttransfer:"feComponentTransfer",fecomposite:"feComposite",feconvolvematrix:"feConvolveMatrix",fediffuselighting:"feDiffuseLighting",fedisplacementmap:"feDisplacementMap",fedistantlight:"feDistantLight",fedropshadow:"feDropShadow",feflood:"feFlood",fefunca:"feFuncA",fefuncb:"feFuncB",fefuncg:"feFuncG",fefuncr:"feFuncR",fegaussianblur:"feGaussianBlur",feimage:"feImage",femerge:"feMerge",femergenode:"feMergeNode",femorphology:"feMorphology",feoffset:"feOffset",fepointlight:"fePointLight",fespecularlighting:"feSpecularLighting",fespotlight:"feSpotLight",fetile:"feTile",feturbulence:"feTurbulence",foreignobject:"foreignObject",glyphref:"glyphRef",lineargradient:"linearGradient",radialgradient:"radialGradient"};function Jl(t){let e=$l[t.tagName]?$l[t.tagName]:t.tagName;if(e==="link"&&t.attributes._cssText){e="style"}return e}function Kl(t,e){const i=e==null?void 0:e.stylesWithHoverClass.get(t);if(i)return i;const r=Xl([Jo,Ko]).process(t);const n=r.css;e==null?void 0:e.stylesWithHoverClass.set(t,n);return n}function ql(){const t=new Map;return{stylesWithHoverClass:t}}function Ql(t,e,i,r){const n=[];for(const e of t.childNodes){if(e.type===Ss.Text){n.push(e)}}const s=e.split("/* rr_split */");while(s.length>1&&s.length>n.length){s.splice(-2,2,s.slice(-2).join(""))}for(let t=0;t<n.length;t++){const e=n[t];const o=s[t];if(e&&o){e.textContent=i?Kl(o,r):o}}}function tc(t,e,i,r){const{doc:n,hackCss:s,cache:o}=r;if(t.childNodes.length){Ql(t,i,s,o)}else{if(s){i=Kl(i,o)}e.appendChild(n.createTextNode(i))}}function ec(t,e){var i;const{doc:r,hackCss:n,cache:s}=e;switch(t.type){case Ss.Document:return r.implementation.createDocument(null,"",null);case Ss.DocumentType:return r.implementation.createDocumentType(t.name||"html",t.publicId,t.systemId);case Ss.Element:{const n=Jl(t);let s;if(t.isSVG){s=r.createElementNS("http://www.w3.org/2000/svg",n)}else{if(t.isCustom&&((i=r.defaultView)==null?void 0:i.customElements)&&!r.defaultView.customElements.get(t.tagName))r.defaultView.customElements.define(t.tagName,class extends r.defaultView.HTMLElement{});s=r.createElement(n)}const o={};for(const i in t.attributes){if(!Object.prototype.hasOwnProperty.call(t.attributes,i)){continue}let a=t.attributes[i];if(n==="option"&&i==="selected"&&a===false){continue}if(a===null){continue}if(a===true)a="";if(i.startsWith("rr_")){o[i]=a;continue}if(typeof a!=="string");else if(n==="style"&&i==="_cssText"){tc(t,s,a,e);continue}else if(n==="textarea"&&i==="value"){s.appendChild(r.createTextNode(a));t.childNodes=[];continue}try{if(t.isSVG&&i==="xlink:href"){s.setAttributeNS("http://www.w3.org/1999/xlink",i,a.toString())}else if(i==="onload"||i==="onclick"||i.substring(0,7)==="onmouse"){s.setAttribute("_"+i,a.toString())}else if(n==="meta"&&t.attributes["http-equiv"]==="Content-Security-Policy"&&i==="content"){s.setAttribute("csp-content",a.toString());continue}else if(n==="link"&&(t.attributes.rel==="preload"||t.attributes.rel==="modulepreload")&&t.attributes.as==="script");else if(n==="link"&&t.attributes.rel==="prefetch"&&typeof t.attributes.href==="string"&&t.attributes.href.endsWith(".js"));else if(n==="img"&&t.attributes.srcset&&t.attributes.rr_dataURL){s.setAttribute("rrweb-original-srcset",t.attributes.srcset)}else{s.setAttribute(i,a.toString())}}catch(t){}}for(const e in o){const i=o[e];if(n==="canvas"&&e==="rr_dataURL"){const t=r.createElement("img");t.onload=()=>{const e=s.getContext("2d");if(e){e.drawImage(t,0,0,t.width,t.height)}};t.src=i.toString();if(s.RRNodeType)s.rr_dataURL=i.toString()}else if(n==="img"&&e==="rr_dataURL"){const e=s;if(!e.currentSrc.startsWith("data:")){e.setAttribute("rrweb-original-src",t.attributes.src);e.src=i.toString()}}if(e==="rr_width"){s.style.setProperty("width",i.toString())}else if(e==="rr_height"){s.style.setProperty("height",i.toString())}else if(e==="rr_mediaCurrentTime"&&typeof i==="number"){s.currentTime=i}else if(e==="rr_mediaState"){switch(i){case"played":s.play().catch((t=>console.warn("media playback error",t)));break;case"paused":s.pause();break}}else if(e==="rr_mediaPlaybackRate"&&typeof i==="number"){s.playbackRate=i}else if(e==="rr_mediaMuted"&&typeof i==="boolean"){s.muted=i}else if(e==="rr_mediaLoop"&&typeof i==="boolean"){s.loop=i}else if(e==="rr_mediaVolume"&&typeof i==="number"){s.volume=i}else if(e==="rr_open_mode"){s.setAttribute("rr_open_mode",i)}}if(t.isShadowHost){if(!s.shadowRoot){s.attachShadow({mode:"open"})}else{while(s.shadowRoot.firstChild){s.shadowRoot.removeChild(s.shadowRoot.firstChild)}}}return s}case Ss.Text:if(t.isStyle&&n){return r.createTextNode(Kl(t.textContent,s))}return r.createTextNode(t.textContent);case Ss.CDATA:return r.createCDATASection(t.textContent);case Ss.Comment:return r.createComment(t.textContent);default:return null}}function ic(t,e){const{doc:i,mirror:r,skipChild:n=false,hackCss:s=true,afterAppend:o,cache:a}=e;if(r.has(t.id)){const e=r.getNode(t.id);const i=r.getMeta(e);if(oo(i,t))return r.getNode(t.id)}let u=ec(t,{doc:i,hackCss:s,cache:a});if(!u){return null}if(t.rootId&&r.getNode(t.rootId)!==i){r.replace(t.rootId,i)}if(t.type===Ss.Document){i.close();i.open();if(t.compatMode==="BackCompat"&&t.childNodes&&t.childNodes[0].type!==Ss.DocumentType){if(t.childNodes[0].type===Ss.Element&&"xmlns"in t.childNodes[0].attributes&&t.childNodes[0].attributes.xmlns==="http://www.w3.org/1999/xhtml"){i.write('<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "">')}else{i.write('<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "">')}}u=i}r.add(u,t);if((t.type===Ss.Document||t.type===Ss.Element)&&!n){for(const e of t.childNodes){const n=ic(e,{doc:i,mirror:r,skipChild:false,hackCss:s,afterAppend:o,cache:a});if(!n){console.warn("Failed to rebuild",e);continue}if(e.isShadow&&Vs(u)&&u.shadowRoot){u.shadowRoot.appendChild(n)}else if(t.type===Ss.Document&&e.type==Ss.Element){const t=n;let e=null;t.childNodes.forEach((t=>{if(t.nodeName==="BODY")e=t}));if(e){t.removeChild(e);u.appendChild(n);t.appendChild(e)}else{u.appendChild(n)}}else{u.appendChild(n)}if(o){o(n,e.id)}}}return u}function rc(t,e){function i(t){e(t)}for(const e of t.getIds()){if(t.has(e)){i(t.getNode(e))}}}function nc(t,e){const i=e.getMeta(t);if((i==null?void 0:i.type)!==Ss.Element){return}const r=t;for(const t in i.attributes){if(!(Object.prototype.hasOwnProperty.call(i.attributes,t)&&t.startsWith("rr_"))){continue}const e=i.attributes[t];if(t==="rr_scrollLeft"){r.scrollLeft=e}if(t==="rr_scrollTop"){r.scrollTop=e}}}function sc(t,e){const{doc:i,onVisit:r,hackCss:n=true,afterAppend:s,cache:o,mirror:a=new to}=e;const u=ic(t,{doc:i,mirror:a,skipChild:false,hackCss:n,afterAppend:s,cache:o});rc(a,(t=>{if(r){r(t)}nc(t,a)}));return u}var oc=Object.defineProperty;var ac=(t,e,i)=>e in t?oc(t,e,{enumerable:true,configurable:true,writable:true,value:i}):t[e]=i;var uc=(t,e,i)=>ac(t,typeof e!=="symbol"?e+"":e,i);var fc=Object.defineProperty;var lc=(t,e,i)=>e in t?fc(t,e,{enumerable:true,configurable:true,writable:true,value:i}):t[e]=i;var cc=(t,e,i)=>lc(t,typeof e!=="symbol"?e+"":e,i);let hc=class t{constructor(){cc(this,"idNodeMap",new Map);cc(this,"nodeMetaMap",new WeakMap)}getId(t){var e;if(!t)return-1;const i=(e=this.getMeta(t))==null?void 0:e.id;return i??-1}getNode(t){return this.idNodeMap.get(t)||null}getIds(){return Array.from(this.idNodeMap.keys())}getMeta(t){return this.nodeMetaMap.get(t)||null}removeNodeFromMap(t){const e=this.getId(t);this.idNodeMap.delete(e);if(t.childNodes){t.childNodes.forEach((t=>this.removeNodeFromMap(t)))}}has(t){return this.idNodeMap.has(t)}hasNode(t){return this.nodeMetaMap.has(t)}add(t,e){const i=e.id;this.idNodeMap.set(i,t);this.nodeMetaMap.set(t,e)}replace(t,e){const i=this.getNode(t);if(i){const t=this.nodeMetaMap.get(i);if(t)this.nodeMetaMap.set(e,t)}this.idNodeMap.set(t,e)}reset(){this.idNodeMap=new Map;this.nodeMetaMap=new WeakMap}};function dc(){return new hc}function vc(t){if(t.__esModule)return t;var e=t.default;if(typeof e=="function"){var i=function t(){if(this instanceof t){return Reflect.construct(e,arguments,this.constructor)}return e.apply(this,arguments)};i.prototype=e.prototype}else i={};Object.defineProperty(i,"__esModule",{value:true});Object.keys(t).forEach((function(e){var r=Object.getOwnPropertyDescriptor(t,e);Object.defineProperty(i,e,r.get?r:{enumerable:true,get:function(){return t[e]}})}));return i}var pc={exports:{}};var mc=String;var gc=function(){return{isColorSupported:false,reset:mc,bold:mc,dim:mc,italic:mc,underline:mc,inverse:mc,hidden:mc,strikethrough:mc,black:mc,red:mc,green:mc,yellow:mc,blue:mc,magenta:mc,cyan:mc,white:mc,gray:mc,bgBlack:mc,bgRed:mc,bgGreen:mc,bgYellow:mc,bgBlue:mc,bgMagenta:mc,bgCyan:mc,bgWhite:mc}};pc.exports=gc();pc.exports.createColors=gc;var yc=pc.exports;const bc={};const wc=Object.freeze(Object.defineProperty({__proto__:null,default:bc},Symbol.toStringTag,{value:"Module"}));const Sc=vc(wc);let kc=yc;let Cc=Sc;let Mc=class t extends Error{constructor(e,i,r,n,s,o){super(e);this.name="CssSyntaxError";this.reason=e;if(s){this.file=s}if(n){this.source=n}if(o){this.plugin=o}if(typeof i!=="undefined"&&typeof r!=="undefined"){if(typeof i==="number"){this.line=i;this.column=r}else{this.line=i.line;this.column=i.column;this.endLine=r.line;this.endColumn=r.column}}this.setMessage();if(Error.captureStackTrace){Error.captureStackTrace(this,t)}}setMessage(){this.message=this.plugin?this.plugin+": ":"";this.message+=this.file?this.file:"<css input>";if(typeof this.line!=="undefined"){this.message+=":"+this.line+":"+this.column}this.message+=": "+this.reason}showSourceCode(t){if(!this.source)return"";let e=this.source;if(t==null)t=kc.isColorSupported;if(Cc){if(t)e=Cc(e)}let i=e.split(/\r?\n/);let r=Math.max(this.line-3,0);let n=Math.min(this.line+2,i.length);let s=String(n).length;let o,a;if(t){let{bold:t,gray:e,red:i}=kc.createColors(true);o=e=>t(i(e));a=t=>e(t)}else{o=a=t=>t}return i.slice(r,n).map(((t,e)=>{let i=r+1+e;let n=" "+(" "+i).slice(-s)+" | ";if(i===this.line){let e=a(n.replace(/\d/g," "))+t.slice(0,this.column-1).replace(/[^\t]/g," ");return o(">")+a(n)+t+"\n "+e+o("^")}return" "+a(n)+t})).join("\n")}toString(){let t=this.showSourceCode();if(t){t="\n\n"+t+"\n"}return this.name+": "+this.message+t}};var Ic=Mc;Mc.default=Mc;var Oc={};Oc.isClean=Symbol("isClean");Oc.my=Symbol("my");const Ac={after:"\n",beforeClose:"\n",beforeComment:"\n",beforeDecl:"\n",beforeOpen:" ",beforeRule:"\n",colon:": ",commentLeft:" ",commentRight:" ",emptyBody:"",indent:"    ",semicolon:false};function Tc(t){return t[0].toUpperCase()+t.slice(1)}let _c=class t{constructor(t){this.builder=t}atrule(t,e){let i="@"+t.name;let r=t.params?this.rawValue(t,"params"):"";if(typeof t.raws.afterName!=="undefined"){i+=t.raws.afterName}else if(r){i+=" "}if(t.nodes){this.block(t,i+r)}else{let n=(t.raws.between||"")+(e?";":"");this.builder(i+r+n,t)}}beforeAfter(t,e){let i;if(t.type==="decl"){i=this.raw(t,null,"beforeDecl")}else if(t.type==="comment"){i=this.raw(t,null,"beforeComment")}else if(e==="before"){i=this.raw(t,null,"beforeRule")}else{i=this.raw(t,null,"beforeClose")}let r=t.parent;let n=0;while(r&&r.type!=="root"){n+=1;r=r.parent}if(i.includes("\n")){let e=this.raw(t,null,"indent");if(e.length){for(let t=0;t<n;t++)i+=e}}return i}block(t,e){let i=this.raw(t,"between","beforeOpen");this.builder(e+i+"{",t,"start");let r;if(t.nodes&&t.nodes.length){this.body(t);r=this.raw(t,"after")}else{r=this.raw(t,"after","emptyBody")}if(r)this.builder(r);this.builder("}",t,"end")}body(t){let e=t.nodes.length-1;while(e>0){if(t.nodes[e].type!=="comment")break;e-=1}let i=this.raw(t,"semicolon");for(let r=0;r<t.nodes.length;r++){let n=t.nodes[r];let s=this.raw(n,"before");if(s)this.builder(s);this.stringify(n,e!==r||i)}}comment(t){let e=this.raw(t,"left","commentLeft");let i=this.raw(t,"right","commentRight");this.builder("/*"+e+t.text+i+"*/",t)}decl(t,e){let i=this.raw(t,"between","colon");let r=t.prop+i+this.rawValue(t,"value");if(t.important){r+=t.raws.important||" !important"}if(e)r+=";";this.builder(r,t)}document(t){this.body(t)}raw(t,e,i){let r;if(!i)i=e;if(e){r=t.raws[e];if(typeof r!=="undefined")return r}let n=t.parent;if(i==="before"){if(!n||n.type==="root"&&n.first===t){return""}if(n&&n.type==="document"){return""}}if(!n)return Ac[i];let s=t.root();if(!s.rawCache)s.rawCache={};if(typeof s.rawCache[i]!=="undefined"){return s.rawCache[i]}if(i==="before"||i==="after"){return this.beforeAfter(t,i)}else{let n="raw"+Tc(i);if(this[n]){r=this[n](s,t)}else{s.walk((t=>{r=t.raws[e];if(typeof r!=="undefined")return false}))}}if(typeof r==="undefined")r=Ac[i];s.rawCache[i]=r;return r}rawBeforeClose(t){let e;t.walk((t=>{if(t.nodes&&t.nodes.length>0){if(typeof t.raws.after!=="undefined"){e=t.raws.after;if(e.includes("\n")){e=e.replace(/[^\n]+$/,"")}return false}}}));if(e)e=e.replace(/\S/g,"");return e}rawBeforeComment(t,e){let i;t.walkComments((t=>{if(typeof t.raws.before!=="undefined"){i=t.raws.before;if(i.includes("\n")){i=i.replace(/[^\n]+$/,"")}return false}}));if(typeof i==="undefined"){i=this.raw(e,null,"beforeDecl")}else if(i){i=i.replace(/\S/g,"")}return i}rawBeforeDecl(t,e){let i;t.walkDecls((t=>{if(typeof t.raws.before!=="undefined"){i=t.raws.before;if(i.includes("\n")){i=i.replace(/[^\n]+$/,"")}return false}}));if(typeof i==="undefined"){i=this.raw(e,null,"beforeRule")}else if(i){i=i.replace(/\S/g,"")}return i}rawBeforeOpen(t){let e;t.walk((t=>{if(t.type!=="decl"){e=t.raws.between;if(typeof e!=="undefined")return false}}));return e}rawBeforeRule(t){let e;t.walk((i=>{if(i.nodes&&(i.parent!==t||t.first!==i)){if(typeof i.raws.before!=="undefined"){e=i.raws.before;if(e.includes("\n")){e=e.replace(/[^\n]+$/,"")}return false}}}));if(e)e=e.replace(/\S/g,"");return e}rawColon(t){let e;t.walkDecls((t=>{if(typeof t.raws.between!=="undefined"){e=t.raws.between.replace(/[^\s:]/g,"");return false}}));return e}rawEmptyBody(t){let e;t.walk((t=>{if(t.nodes&&t.nodes.length===0){e=t.raws.after;if(typeof e!=="undefined")return false}}));return e}rawIndent(t){if(t.raws.indent)return t.raws.indent;let e;t.walk((i=>{let r=i.parent;if(r&&r!==t&&r.parent&&r.parent===t){if(typeof i.raws.before!=="undefined"){let t=i.raws.before.split("\n");e=t[t.length-1];e=e.replace(/\S/g,"");return false}}}));return e}rawSemicolon(t){let e;t.walk((t=>{if(t.nodes&&t.nodes.length&&t.last.type==="decl"){e=t.raws.semicolon;if(typeof e!=="undefined")return false}}));return e}rawValue(t,e){let i=t[e];let r=t.raws[e];if(r&&r.value===i){return r.raw}return i}root(t){this.body(t);if(t.raws.after)this.builder(t.raws.after)}rule(t){this.block(t,this.rawValue(t,"selector"));if(t.raws.ownSemicolon){this.builder(t.raws.ownSemicolon,t,"end")}}stringify(t,e){if(!this[t.type]){throw new Error("Unknown AST node type "+t.type+". Maybe you need to change PostCSS stringifier.")}this[t.type](t,e)}};var Ec=_c;_c.default=_c;let Rc=Ec;function xc(t,e){let i=new Rc(e);i.stringify(t)}var jc=xc;xc.default=xc;let{isClean:Nc,my:Dc}=Oc;let Fc=Ic;let Lc=Ec;let Pc=jc;function Bc(t,e){let i=new t.constructor;for(let r in t){if(!Object.prototype.hasOwnProperty.call(t,r)){continue}if(r==="proxyCache")continue;let n=t[r];let s=typeof n;if(r==="parent"&&s==="object"){if(e)i[r]=e}else if(r==="source"){i[r]=n}else if(Array.isArray(n)){i[r]=n.map((t=>Bc(t,i)))}else{if(s==="object"&&n!==null)n=Bc(n);i[r]=n}}return i}let Uc=class t{constructor(t={}){this.raws={};this[Nc]=false;this[Dc]=true;for(let e in t){if(e==="nodes"){this.nodes=[];for(let i of t[e]){if(typeof i.clone==="function"){this.append(i.clone())}else{this.append(i)}}}else{this[e]=t[e]}}}addToError(t){t.postcssNode=this;if(t.stack&&this.source&&/\n\s{4}at /.test(t.stack)){let e=this.source;t.stack=t.stack.replace(/\n\s{4}at /,`$&${e.input.from}:${e.start.line}:${e.start.column}$&`)}return t}after(t){this.parent.insertAfter(this,t);return this}assign(t={}){for(let e in t){this[e]=t[e]}return this}before(t){this.parent.insertBefore(this,t);return this}cleanRaws(t){delete this.raws.before;delete this.raws.after;if(!t)delete this.raws.between}clone(t={}){let e=Bc(this);for(let i in t){e[i]=t[i]}return e}cloneAfter(t={}){let e=this.clone(t);this.parent.insertAfter(this,e);return e}cloneBefore(t={}){let e=this.clone(t);this.parent.insertBefore(this,e);return e}error(t,e={}){if(this.source){let{end:i,start:r}=this.rangeBy(e);return this.source.input.error(t,{column:r.column,line:r.line},{column:i.column,line:i.line},e)}return new Fc(t)}getProxyProcessor(){return{get(t,e){if(e==="proxyOf"){return t}else if(e==="root"){return()=>t.root().toProxy()}else{return t[e]}},set(t,e,i){if(t[e]===i)return true;t[e]=i;if(e==="prop"||e==="value"||e==="name"||e==="params"||e==="important"||e==="text"){t.markDirty()}return true}}}markDirty(){if(this[Nc]){this[Nc]=false;let t=this;while(t=t.parent){t[Nc]=false}}}next(){if(!this.parent)return void 0;let t=this.parent.index(this);return this.parent.nodes[t+1]}positionBy(t,e){let i=this.source.start;if(t.index){i=this.positionInside(t.index,e)}else if(t.word){e=this.toString();let r=e.indexOf(t.word);if(r!==-1)i=this.positionInside(r,e)}return i}positionInside(t,e){let i=e||this.toString();let r=this.source.start.column;let n=this.source.start.line;for(let e=0;e<t;e++){if(i[e]==="\n"){r=1;n+=1}else{r+=1}}return{column:r,line:n}}prev(){if(!this.parent)return void 0;let t=this.parent.index(this);return this.parent.nodes[t-1]}rangeBy(t){let e={column:this.source.start.column,line:this.source.start.line};let i=this.source.end?{column:this.source.end.column+1,line:this.source.end.line}:{column:e.column+1,line:e.line};if(t.word){let r=this.toString();let n=r.indexOf(t.word);if(n!==-1){e=this.positionInside(n,r);i=this.positionInside(n+t.word.length,r)}}else{if(t.start){e={column:t.start.column,line:t.start.line}}else if(t.index){e=this.positionInside(t.index)}if(t.end){i={column:t.end.column,line:t.end.line}}else if(typeof t.endIndex==="number"){i=this.positionInside(t.endIndex)}else if(t.index){i=this.positionInside(t.index+1)}}if(i.line<e.line||i.line===e.line&&i.column<=e.column){i={column:e.column+1,line:e.line}}return{end:i,start:e}}raw(t,e){let i=new Lc;return i.raw(this,t,e)}remove(){if(this.parent){this.parent.removeChild(this)}this.parent=void 0;return this}replaceWith(...t){if(this.parent){let e=this;let i=false;for(let r of t){if(r===this){i=true}else if(i){this.parent.insertAfter(e,r);e=r}else{this.parent.insertBefore(e,r)}}if(!i){this.remove()}}return this}root(){let t=this;while(t.parent&&t.parent.type!=="document"){t=t.parent}return t}toJSON(t,e){let i={};let r=e==null;e=e||new Map;let n=0;for(let t in this){if(!Object.prototype.hasOwnProperty.call(this,t)){continue}if(t==="parent"||t==="proxyCache")continue;let r=this[t];if(Array.isArray(r)){i[t]=r.map((t=>{if(typeof t==="object"&&t.toJSON){return t.toJSON(null,e)}else{return t}}))}else if(typeof r==="object"&&r.toJSON){i[t]=r.toJSON(null,e)}else if(t==="source"){let s=e.get(r.input);if(s==null){s=n;e.set(r.input,n);n++}i[t]={end:r.end,inputId:s,start:r.start}}else{i[t]=r}}if(r){i.inputs=[...e.keys()].map((t=>t.toJSON()))}return i}toProxy(){if(!this.proxyCache){this.proxyCache=new Proxy(this,this.getProxyProcessor())}return this.proxyCache}toString(t=Pc){if(t.stringify)t=t.stringify;let e="";t(this,(t=>{e+=t}));return e}warn(t,e,i){let r={node:this};for(let t in i)r[t]=i[t];return t.warn(e,r)}get proxyOf(){return this}};var Gc=Uc;Uc.default=Uc;let Wc=Gc;let zc=class t extends Wc{constructor(t){if(t&&typeof t.value!=="undefined"&&typeof t.value!=="string"){t={...t,value:String(t.value)}}super(t);this.type="decl"}get variable(){return this.prop.startsWith("--")||this.prop[0]==="$"}};var Vc=zc;zc.default=zc;let Hc="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict";let Yc=(t=21)=>{let e="";let i=t;while(i--){e+=Hc[Math.random()*64|0]}return e};var Zc={nanoid:Yc};let{SourceMapConsumer:Xc,SourceMapGenerator:$c}=Sc;let{existsSync:Jc,readFileSync:Kc}=Sc;let{dirname:qc,join:Qc}=Sc;function th(t){if(Buffer){return Buffer.from(t,"base64").toString()}else{return window.atob(t)}}let eh=class t{constructor(t,e){if(e.map===false)return;this.loadAnnotation(t);this.inline=this.startWith(this.annotation,"data:");let i=e.map?e.map.prev:void 0;let r=this.loadMap(e.from,i);if(!this.mapFile&&e.from){this.mapFile=e.from}if(this.mapFile)this.root=qc(this.mapFile);if(r)this.text=r}consumer(){if(!this.consumerCache){this.consumerCache=new Xc(this.text)}return this.consumerCache}decodeInline(t){let e=/^data:application\/json;charset=utf-?8;base64,/;let i=/^data:application\/json;base64,/;let r=/^data:application\/json;charset=utf-?8,/;let n=/^data:application\/json,/;if(r.test(t)||n.test(t)){return decodeURIComponent(t.substr(RegExp.lastMatch.length))}if(e.test(t)||i.test(t)){return th(t.substr(RegExp.lastMatch.length))}let s=t.match(/data:application\/json;([^,]+),/)[1];throw new Error("Unsupported source map encoding "+s)}getAnnotationURL(t){return t.replace(/^\/\*\s*# sourceMappingURL=/,"").trim()}isMap(t){if(typeof t!=="object")return false;return typeof t.mappings==="string"||typeof t._mappings==="string"||Array.isArray(t.sections)}loadAnnotation(t){let e=t.match(/\/\*\s*# sourceMappingURL=/gm);if(!e)return;let i=t.lastIndexOf(e.pop());let r=t.indexOf("*/",i);if(i>-1&&r>-1){this.annotation=this.getAnnotationURL(t.substring(i,r))}}loadFile(t){this.root=qc(t);if(Jc(t)){this.mapFile=t;return Kc(t,"utf-8").toString().trim()}}loadMap(t,e){if(e===false)return false;if(e){if(typeof e==="string"){return e}else if(typeof e==="function"){let i=e(t);if(i){let t=this.loadFile(i);if(!t){throw new Error("Unable to load previous source map: "+i.toString())}return t}}else if(e instanceof Xc){return $c.fromSourceMap(e).toString()}else if(e instanceof $c){return e.toString()}else if(this.isMap(e)){return JSON.stringify(e)}else{throw new Error("Unsupported previous source map format: "+e.toString())}}else if(this.inline){return this.decodeInline(this.annotation)}else if(this.annotation){let e=this.annotation;if(t)e=Qc(qc(t),e);return this.loadFile(e)}}startWith(t,e){if(!t)return false;return t.substr(0,e.length)===e}withContent(){return!!(this.consumer().sourcesContent&&this.consumer().sourcesContent.length>0)}};var ih=eh;eh.default=eh;let{SourceMapConsumer:rh,SourceMapGenerator:nh}=Sc;let{fileURLToPath:sh,pathToFileURL:oh}=Sc;let{isAbsolute:ah,resolve:uh}=Sc;let{nanoid:fh}=Zc;let lh=Sc;let ch=Ic;let hh=ih;let dh=Symbol("fromOffsetCache");let vh=Boolean(rh&&nh);let ph=Boolean(uh&&ah);let mh=class t{constructor(t,e={}){if(t===null||typeof t==="undefined"||typeof t==="object"&&!t.toString){throw new Error(`PostCSS received ${t} instead of CSS string`)}this.css=t.toString();if(this.css[0]==="\ufeff"||this.css[0]==="￾"){this.hasBOM=true;this.css=this.css.slice(1)}else{this.hasBOM=false}if(e.from){if(!ph||/^\w+:\/\//.test(e.from)||ah(e.from)){this.file=e.from}else{this.file=uh(e.from)}}if(ph&&vh){let t=new hh(this.css,e);if(t.text){this.map=t;let e=t.consumer().file;if(!this.file&&e)this.file=this.mapResolve(e)}}if(!this.file){this.id="<input css "+fh(6)+">"}if(this.map)this.map.file=this.from}error(t,e,i,r={}){let n,s,o;if(e&&typeof e==="object"){let t=e;let r=i;if(typeof t.offset==="number"){let r=this.fromOffset(t.offset);e=r.line;i=r.col}else{e=t.line;i=t.column}if(typeof r.offset==="number"){let t=this.fromOffset(r.offset);s=t.line;o=t.col}else{s=r.line;o=r.column}}else if(!i){let t=this.fromOffset(e);e=t.line;i=t.col}let a=this.origin(e,i,s,o);if(a){n=new ch(t,a.endLine===void 0?a.line:{column:a.column,line:a.line},a.endLine===void 0?a.column:{column:a.endColumn,line:a.endLine},a.source,a.file,r.plugin)}else{n=new ch(t,s===void 0?e:{column:i,line:e},s===void 0?i:{column:o,line:s},this.css,this.file,r.plugin)}n.input={column:i,endColumn:o,endLine:s,line:e,source:this.css};if(this.file){if(oh){n.input.url=oh(this.file).toString()}n.input.file=this.file}return n}fromOffset(t){let e,i;if(!this[dh]){let t=this.css.split("\n");i=new Array(t.length);let e=0;for(let r=0,n=t.length;r<n;r++){i[r]=e;e+=t[r].length+1}this[dh]=i}else{i=this[dh]}e=i[i.length-1];let r=0;if(t>=e){r=i.length-1}else{let e=i.length-2;let n;while(r<e){n=r+(e-r>>1);if(t<i[n]){e=n-1}else if(t>=i[n+1]){r=n+1}else{r=n;break}}}return{col:t-i[r]+1,line:r+1}}mapResolve(t){if(/^\w+:\/\//.test(t)){return t}return uh(this.map.consumer().sourceRoot||this.map.root||".",t)}origin(t,e,i,r){if(!this.map)return false;let n=this.map.consumer();let s=n.originalPositionFor({column:e,line:t});if(!s.source)return false;let o;if(typeof i==="number"){o=n.originalPositionFor({column:r,line:i})}let a;if(ah(s.source)){a=oh(s.source)}else{a=new URL(s.source,this.map.consumer().sourceRoot||oh(this.map.mapFile))}let u={column:s.column,endColumn:o&&o.column,endLine:o&&o.line,line:s.line,url:a.toString()};if(a.protocol==="file:"){if(sh){u.file=sh(a)}else{throw new Error(`file: protocol is not available in this PostCSS build`)}}let f=n.sourceContentFor(s.source);if(f)u.source=f;return u}toJSON(){let t={};for(let e of["hasBOM","css","file","id"]){if(this[e]!=null){t[e]=this[e]}}if(this.map){t.map={...this.map};if(t.map.consumerCache){t.map.consumerCache=void 0}}return t}get from(){return this.file||this.id}};var gh=mh;mh.default=mh;if(lh&&lh.registerInput){lh.registerInput(mh)}let{SourceMapConsumer:yh,SourceMapGenerator:bh}=Sc;let{dirname:wh,relative:Sh,resolve:kh,sep:Ch}=Sc;let{pathToFileURL:Mh}=Sc;let Ih=gh;let Oh=Boolean(yh&&bh);let Ah=Boolean(wh&&kh&&Sh&&Ch);let Th=class t{constructor(t,e,i,r){this.stringify=t;this.mapOpts=i.map||{};this.root=e;this.opts=i;this.css=r;this.originalCSS=r;this.usesFileUrls=!this.mapOpts.from&&this.mapOpts.absolute;this.memoizedFileURLs=new Map;this.memoizedPaths=new Map;this.memoizedURLs=new Map}addAnnotation(){let t;if(this.isInline()){t="data:application/json;base64,"+this.toBase64(this.map.toString())}else if(typeof this.mapOpts.annotation==="string"){t=this.mapOpts.annotation}else if(typeof this.mapOpts.annotation==="function"){t=this.mapOpts.annotation(this.opts.to,this.root)}else{t=this.outputFile()+".map"}let e="\n";if(this.css.includes("\r\n"))e="\r\n";this.css+=e+"/*# sourceMappingURL="+t+" */"}applyPrevMaps(){for(let t of this.previous()){let e=this.toUrl(this.path(t.file));let i=t.root||wh(t.file);let r;if(this.mapOpts.sourcesContent===false){r=new yh(t.text);if(r.sourcesContent){r.sourcesContent=null}}else{r=t.consumer()}this.map.applySourceMap(r,e,this.toUrl(this.path(i)))}}clearAnnotation(){if(this.mapOpts.annotation===false)return;if(this.root){let t;for(let e=this.root.nodes.length-1;e>=0;e--){t=this.root.nodes[e];if(t.type!=="comment")continue;if(t.text.indexOf("# sourceMappingURL=")===0){this.root.removeChild(e)}}}else if(this.css){this.css=this.css.replace(/\n*?\/\*#[\S\s]*?\*\/$/gm,"")}}generate(){this.clearAnnotation();if(Ah&&Oh&&this.isMap()){return this.generateMap()}else{let t="";this.stringify(this.root,(e=>{t+=e}));return[t]}}generateMap(){if(this.root){this.generateString()}else if(this.previous().length===1){let t=this.previous()[0].consumer();t.file=this.outputFile();this.map=bh.fromSourceMap(t,{ignoreInvalidMapping:true})}else{this.map=new bh({file:this.outputFile(),ignoreInvalidMapping:true});this.map.addMapping({generated:{column:0,line:1},original:{column:0,line:1},source:this.opts.from?this.toUrl(this.path(this.opts.from)):"<no source>"})}if(this.isSourcesContent())this.setSourcesContent();if(this.root&&this.previous().length>0)this.applyPrevMaps();if(this.isAnnotation())this.addAnnotation();if(this.isInline()){return[this.css]}else{return[this.css,this.map]}}generateString(){this.css="";this.map=new bh({file:this.outputFile(),ignoreInvalidMapping:true});let t=1;let e=1;let i="<no source>";let r={generated:{column:0,line:0},original:{column:0,line:0},source:""};let n,s;this.stringify(this.root,((o,a,u)=>{this.css+=o;if(a&&u!=="end"){r.generated.line=t;r.generated.column=e-1;if(a.source&&a.source.start){r.source=this.sourcePath(a);r.original.line=a.source.start.line;r.original.column=a.source.start.column-1;this.map.addMapping(r)}else{r.source=i;r.original.line=1;r.original.column=0;this.map.addMapping(r)}}n=o.match(/\n/g);if(n){t+=n.length;s=o.lastIndexOf("\n");e=o.length-s}else{e+=o.length}if(a&&u!=="start"){let n=a.parent||{raws:{}};let s=a.type==="decl"||a.type==="atrule"&&!a.nodes;if(!s||a!==n.last||n.raws.semicolon){if(a.source&&a.source.end){r.source=this.sourcePath(a);r.original.line=a.source.end.line;r.original.column=a.source.end.column-1;r.generated.line=t;r.generated.column=e-2;this.map.addMapping(r)}else{r.source=i;r.original.line=1;r.original.column=0;r.generated.line=t;r.generated.column=e-1;this.map.addMapping(r)}}}}))}isAnnotation(){if(this.isInline()){return true}if(typeof this.mapOpts.annotation!=="undefined"){return this.mapOpts.annotation}if(this.previous().length){return this.previous().some((t=>t.annotation))}return true}isInline(){if(typeof this.mapOpts.inline!=="undefined"){return this.mapOpts.inline}let t=this.mapOpts.annotation;if(typeof t!=="undefined"&&t!==true){return false}if(this.previous().length){return this.previous().some((t=>t.inline))}return true}isMap(){if(typeof this.opts.map!=="undefined"){return!!this.opts.map}return this.previous().length>0}isSourcesContent(){if(typeof this.mapOpts.sourcesContent!=="undefined"){return this.mapOpts.sourcesContent}if(this.previous().length){return this.previous().some((t=>t.withContent()))}return true}outputFile(){if(this.opts.to){return this.path(this.opts.to)}else if(this.opts.from){return this.path(this.opts.from)}else{return"to.css"}}path(t){if(this.mapOpts.absolute)return t;if(t.charCodeAt(0)===60)return t;if(/^\w+:\/\//.test(t))return t;let e=this.memoizedPaths.get(t);if(e)return e;let i=this.opts.to?wh(this.opts.to):".";if(typeof this.mapOpts.annotation==="string"){i=wh(kh(i,this.mapOpts.annotation))}let r=Sh(i,t);this.memoizedPaths.set(t,r);return r}previous(){if(!this.previousMaps){this.previousMaps=[];if(this.root){this.root.walk((t=>{if(t.source&&t.source.input.map){let e=t.source.input.map;if(!this.previousMaps.includes(e)){this.previousMaps.push(e)}}}))}else{let t=new Ih(this.originalCSS,this.opts);if(t.map)this.previousMaps.push(t.map)}}return this.previousMaps}setSourcesContent(){let t={};if(this.root){this.root.walk((e=>{if(e.source){let i=e.source.input.from;if(i&&!t[i]){t[i]=true;let r=this.usesFileUrls?this.toFileUrl(i):this.toUrl(this.path(i));this.map.setSourceContent(r,e.source.input.css)}}}))}else if(this.css){let t=this.opts.from?this.toUrl(this.path(this.opts.from)):"<no source>";this.map.setSourceContent(t,this.css)}}sourcePath(t){if(this.mapOpts.from){return this.toUrl(this.mapOpts.from)}else if(this.usesFileUrls){return this.toFileUrl(t.source.input.from)}else{return this.toUrl(this.path(t.source.input.from))}}toBase64(t){if(Buffer){return Buffer.from(t).toString("base64")}else{return window.btoa(unescape(encodeURIComponent(t)))}}toFileUrl(t){let e=this.memoizedFileURLs.get(t);if(e)return e;if(Mh){let e=Mh(t).toString();this.memoizedFileURLs.set(t,e);return e}else{throw new Error("`map.absolute` option is not available in this PostCSS build")}}toUrl(t){let e=this.memoizedURLs.get(t);if(e)return e;if(Ch==="\\"){t=t.replace(/\\/g,"/")}let i=encodeURI(t).replace(/[#?]/g,encodeURIComponent);this.memoizedURLs.set(t,i);return i}};var _h=Th;let Eh=Gc;let Rh=class t extends Eh{constructor(t){super(t);this.type="comment"}};var xh=Rh;Rh.default=Rh;let{isClean:jh,my:Nh}=Oc;let Dh=Vc;let Fh=xh;let Lh=Gc;let Ph,Bh,Uh,Gh;function Wh(t){return t.map((t=>{if(t.nodes)t.nodes=Wh(t.nodes);delete t.source;return t}))}function zh(t){t[jh]=false;if(t.proxyOf.nodes){for(let e of t.proxyOf.nodes){zh(e)}}}let Vh=class t extends Lh{append(...t){for(let e of t){let t=this.normalize(e,this.last);for(let e of t)this.proxyOf.nodes.push(e)}this.markDirty();return this}cleanRaws(t){super.cleanRaws(t);if(this.nodes){for(let e of this.nodes)e.cleanRaws(t)}}each(t){if(!this.proxyOf.nodes)return void 0;let e=this.getIterator();let i,r;while(this.indexes[e]<this.proxyOf.nodes.length){i=this.indexes[e];r=t(this.proxyOf.nodes[i],i);if(r===false)break;this.indexes[e]+=1}delete this.indexes[e];return r}every(t){return this.nodes.every(t)}getIterator(){if(!this.lastEach)this.lastEach=0;if(!this.indexes)this.indexes={};this.lastEach+=1;let t=this.lastEach;this.indexes[t]=0;return t}getProxyProcessor(){return{get(t,e){if(e==="proxyOf"){return t}else if(!t[e]){return t[e]}else if(e==="each"||typeof e==="string"&&e.startsWith("walk")){return(...i)=>t[e](...i.map((t=>{if(typeof t==="function"){return(e,i)=>t(e.toProxy(),i)}else{return t}})))}else if(e==="every"||e==="some"){return i=>t[e](((t,...e)=>i(t.toProxy(),...e)))}else if(e==="root"){return()=>t.root().toProxy()}else if(e==="nodes"){return t.nodes.map((t=>t.toProxy()))}else if(e==="first"||e==="last"){return t[e].toProxy()}else{return t[e]}},set(t,e,i){if(t[e]===i)return true;t[e]=i;if(e==="name"||e==="params"||e==="selector"){t.markDirty()}return true}}}index(t){if(typeof t==="number")return t;if(t.proxyOf)t=t.proxyOf;return this.proxyOf.nodes.indexOf(t)}insertAfter(t,e){let i=this.index(t);let r=this.normalize(e,this.proxyOf.nodes[i]).reverse();i=this.index(t);for(let t of r)this.proxyOf.nodes.splice(i+1,0,t);let n;for(let t in this.indexes){n=this.indexes[t];if(i<n){this.indexes[t]=n+r.length}}this.markDirty();return this}insertBefore(t,e){let i=this.index(t);let r=i===0?"prepend":false;let n=this.normalize(e,this.proxyOf.nodes[i],r).reverse();i=this.index(t);for(let t of n)this.proxyOf.nodes.splice(i,0,t);let s;for(let t in this.indexes){s=this.indexes[t];if(i<=s){this.indexes[t]=s+n.length}}this.markDirty();return this}normalize(e,i){if(typeof e==="string"){e=Wh(Ph(e).nodes)}else if(typeof e==="undefined"){e=[]}else if(Array.isArray(e)){e=e.slice(0);for(let t of e){if(t.parent)t.parent.removeChild(t,"ignore")}}else if(e.type==="root"&&this.type!=="document"){e=e.nodes.slice(0);for(let t of e){if(t.parent)t.parent.removeChild(t,"ignore")}}else if(e.type){e=[e]}else if(e.prop){if(typeof e.value==="undefined"){throw new Error("Value field is missed in node creation")}else if(typeof e.value!=="string"){e.value=String(e.value)}e=[new Dh(e)]}else if(e.selector){e=[new Bh(e)]}else if(e.name){e=[new Uh(e)]}else if(e.text){e=[new Fh(e)]}else{throw new Error("Unknown node type in node creation")}let r=e.map((e=>{if(!e[Nh])t.rebuild(e);e=e.proxyOf;if(e.parent)e.parent.removeChild(e);if(e[jh])zh(e);if(typeof e.raws.before==="undefined"){if(i&&typeof i.raws.before!=="undefined"){e.raws.before=i.raws.before.replace(/\S/g,"")}}e.parent=this.proxyOf;return e}));return r}prepend(...t){t=t.reverse();for(let e of t){let t=this.normalize(e,this.first,"prepend").reverse();for(let e of t)this.proxyOf.nodes.unshift(e);for(let e in this.indexes){this.indexes[e]=this.indexes[e]+t.length}}this.markDirty();return this}push(t){t.parent=this;this.proxyOf.nodes.push(t);return this}removeAll(){for(let t of this.proxyOf.nodes)t.parent=void 0;this.proxyOf.nodes=[];this.markDirty();return this}removeChild(t){t=this.index(t);this.proxyOf.nodes[t].parent=void 0;this.proxyOf.nodes.splice(t,1);let e;for(let i in this.indexes){e=this.indexes[i];if(e>=t){this.indexes[i]=e-1}}this.markDirty();return this}replaceValues(t,e,i){if(!i){i=e;e={}}this.walkDecls((r=>{if(e.props&&!e.props.includes(r.prop))return;if(e.fast&&!r.value.includes(e.fast))return;r.value=r.value.replace(t,i)}));this.markDirty();return this}some(t){return this.nodes.some(t)}walk(t){return this.each(((e,i)=>{let r;try{r=t(e,i)}catch(t){throw e.addToError(t)}if(r!==false&&e.walk){r=e.walk(t)}return r}))}walkAtRules(t,e){if(!e){e=t;return this.walk(((t,i)=>{if(t.type==="atrule"){return e(t,i)}}))}if(t instanceof RegExp){return this.walk(((i,r)=>{if(i.type==="atrule"&&t.test(i.name)){return e(i,r)}}))}return this.walk(((i,r)=>{if(i.type==="atrule"&&i.name===t){return e(i,r)}}))}walkComments(t){return this.walk(((e,i)=>{if(e.type==="comment"){return t(e,i)}}))}walkDecls(t,e){if(!e){e=t;return this.walk(((t,i)=>{if(t.type==="decl"){return e(t,i)}}))}if(t instanceof RegExp){return this.walk(((i,r)=>{if(i.type==="decl"&&t.test(i.prop)){return e(i,r)}}))}return this.walk(((i,r)=>{if(i.type==="decl"&&i.prop===t){return e(i,r)}}))}walkRules(t,e){if(!e){e=t;return this.walk(((t,i)=>{if(t.type==="rule"){return e(t,i)}}))}if(t instanceof RegExp){return this.walk(((i,r)=>{if(i.type==="rule"&&t.test(i.selector)){return e(i,r)}}))}return this.walk(((i,r)=>{if(i.type==="rule"&&i.selector===t){return e(i,r)}}))}get first(){if(!this.proxyOf.nodes)return void 0;return this.proxyOf.nodes[0]}get last(){if(!this.proxyOf.nodes)return void 0;return this.proxyOf.nodes[this.proxyOf.nodes.length-1]}};Vh.registerParse=t=>{Ph=t};Vh.registerRule=t=>{Bh=t};Vh.registerAtRule=t=>{Uh=t};Vh.registerRoot=t=>{Gh=t};var Hh=Vh;Vh.default=Vh;Vh.rebuild=t=>{if(t.type==="atrule"){Object.setPrototypeOf(t,Uh.prototype)}else if(t.type==="rule"){Object.setPrototypeOf(t,Bh.prototype)}else if(t.type==="decl"){Object.setPrototypeOf(t,Dh.prototype)}else if(t.type==="comment"){Object.setPrototypeOf(t,Fh.prototype)}else if(t.type==="root"){Object.setPrototypeOf(t,Gh.prototype)}t[Nh]=true;if(t.nodes){t.nodes.forEach((t=>{Vh.rebuild(t)}))}};let Yh=Hh;let Zh,Xh;let $h=class t extends Yh{constructor(t){super({type:"document",...t});if(!this.nodes){this.nodes=[]}}toResult(t={}){let e=new Zh(new Xh,this,t);return e.stringify()}};$h.registerLazyResult=t=>{Zh=t};$h.registerProcessor=t=>{Xh=t};var Jh=$h;$h.default=$h;let Kh=class t{constructor(t,e={}){this.type="warning";this.text=t;if(e.node&&e.node.source){let t=e.node.rangeBy(e);this.line=t.start.line;this.column=t.start.column;this.endLine=t.end.line;this.endColumn=t.end.column}for(let t in e)this[t]=e[t]}toString(){if(this.node){return this.node.error(this.text,{index:this.index,plugin:this.plugin,word:this.word}).message}if(this.plugin){return this.plugin+": "+this.text}return this.text}};var qh=Kh;Kh.default=Kh;let Qh=qh;let td=class t{constructor(t,e,i){this.processor=t;this.messages=[];this.root=e;this.opts=i;this.css=void 0;this.map=void 0}toString(){return this.css}warn(t,e={}){if(!e.plugin){if(this.lastPlugin&&this.lastPlugin.postcssPlugin){e.plugin=this.lastPlugin.postcssPlugin}}let i=new Qh(t,e);this.messages.push(i);return i}warnings(){return this.messages.filter((t=>t.type==="warning"))}get content(){return this.css}};var ed=td;td.default=td;const id="'".charCodeAt(0);const rd='"'.charCodeAt(0);const nd="\\".charCodeAt(0);const sd="/".charCodeAt(0);const od="\n".charCodeAt(0);const ad=" ".charCodeAt(0);const ud="\f".charCodeAt(0);const fd="\t".charCodeAt(0);const ld="\r".charCodeAt(0);const cd="[".charCodeAt(0);const hd="]".charCodeAt(0);const dd="(".charCodeAt(0);const vd=")".charCodeAt(0);const pd="{".charCodeAt(0);const md="}".charCodeAt(0);const gd=";".charCodeAt(0);const yd="*".charCodeAt(0);const bd=":".charCodeAt(0);const wd="@".charCodeAt(0);const Sd=/[\t\n\f\r "#'()/;[\\\]{}]/g;const kd=/[\t\n\f\r !"#'():;@[\\\]{}]|\/(?=\*)/g;const Cd=/.[\r\n"'(/\\]/;const Md=/[\da-f]/i;var Id=function t(e,i={}){let r=e.css.valueOf();let n=i.ignoreErrors;let s,o,a,u,f;let l,c,h,d,v;let p=r.length;let m=0;let g=[];let y=[];function b(){return m}function w(t){throw e.error("Unclosed "+t,m)}function S(){return y.length===0&&m>=p}function k(t){if(y.length)return y.pop();if(m>=p)return;let e=t?t.ignoreUnclosed:false;s=r.charCodeAt(m);switch(s){case od:case ad:case fd:case ld:case ud:{o=m;do{o+=1;s=r.charCodeAt(o)}while(s===ad||s===od||s===fd||s===ld||s===ud);v=["space",r.slice(m,o)];m=o-1;break}case cd:case hd:case pd:case md:case bd:case gd:case vd:{let t=String.fromCharCode(s);v=[t,t,m];break}case dd:{h=g.length?g.pop()[1]:"";d=r.charCodeAt(m+1);if(h==="url"&&d!==id&&d!==rd&&d!==ad&&d!==od&&d!==fd&&d!==ud&&d!==ld){o=m;do{l=false;o=r.indexOf(")",o+1);if(o===-1){if(n||e){o=m;break}else{w("bracket")}}c=o;while(r.charCodeAt(c-1)===nd){c-=1;l=!l}}while(l);v=["brackets",r.slice(m,o+1),m,o];m=o}else{o=r.indexOf(")",m+1);u=r.slice(m,o+1);if(o===-1||Cd.test(u)){v=["(","(",m]}else{v=["brackets",u,m,o];m=o}}break}case id:case rd:{a=s===id?"'":'"';o=m;do{l=false;o=r.indexOf(a,o+1);if(o===-1){if(n||e){o=m+1;break}else{w("string")}}c=o;while(r.charCodeAt(c-1)===nd){c-=1;l=!l}}while(l);v=["string",r.slice(m,o+1),m,o];m=o;break}case wd:{Sd.lastIndex=m+1;Sd.test(r);if(Sd.lastIndex===0){o=r.length-1}else{o=Sd.lastIndex-2}v=["at-word",r.slice(m,o+1),m,o];m=o;break}case nd:{o=m;f=true;while(r.charCodeAt(o+1)===nd){o+=1;f=!f}s=r.charCodeAt(o+1);if(f&&s!==sd&&s!==ad&&s!==od&&s!==fd&&s!==ld&&s!==ud){o+=1;if(Md.test(r.charAt(o))){while(Md.test(r.charAt(o+1))){o+=1}if(r.charCodeAt(o+1)===ad){o+=1}}}v=["word",r.slice(m,o+1),m,o];m=o;break}default:{if(s===sd&&r.charCodeAt(m+1)===yd){o=r.indexOf("*/",m+2)+1;if(o===0){if(n||e){o=r.length}else{w("comment")}}v=["comment",r.slice(m,o+1),m,o];m=o}else{kd.lastIndex=m+1;kd.test(r);if(kd.lastIndex===0){o=r.length-1}else{o=kd.lastIndex-2}v=["word",r.slice(m,o+1),m,o];g.push(v);m=o}break}}m++;return v}function C(t){y.push(t)}return{back:C,endOfFile:S,nextToken:k,position:b}};let Od=Hh;let Ad=class t extends Od{constructor(t){super(t);this.type="atrule"}append(...t){if(!this.proxyOf.nodes)this.nodes=[];return super.append(...t)}prepend(...t){if(!this.proxyOf.nodes)this.nodes=[];return super.prepend(...t)}};var Td=Ad;Ad.default=Ad;Od.registerAtRule(Ad);let _d=Hh;let Ed,Rd;let xd=class t extends _d{constructor(t){super(t);this.type="root";if(!this.nodes)this.nodes=[]}normalize(t,e,i){let r=super.normalize(t);if(e){if(i==="prepend"){if(this.nodes.length>1){e.raws.before=this.nodes[1].raws.before}else{delete e.raws.before}}else if(this.first!==e){for(let t of r){t.raws.before=e.raws.before}}}return r}removeChild(t,e){let i=this.index(t);if(!e&&i===0&&this.nodes.length>1){this.nodes[1].raws.before=this.nodes[i].raws.before}return super.removeChild(t)}toResult(t={}){let e=new Ed(new Rd,this,t);return e.stringify()}};xd.registerLazyResult=t=>{Ed=t};xd.registerProcessor=t=>{Rd=t};var jd=xd;xd.default=xd;_d.registerRoot(xd);let Nd={comma(t){return Nd.split(t,[","],true)},space(t){let e=[" ","\n","\t"];return Nd.split(t,e)},split(t,e,i){let r=[];let n="";let s=false;let o=0;let a=false;let u="";let f=false;for(let i of t){if(f){f=false}else if(i==="\\"){f=true}else if(a){if(i===u){a=false}}else if(i==='"'||i==="'"){a=true;u=i}else if(i==="("){o+=1}else if(i===")"){if(o>0)o-=1}else if(o===0){if(e.includes(i))s=true}if(s){if(n!=="")r.push(n.trim());n="";s=false}else{n+=i}}if(i||n!=="")r.push(n.trim());return r}};var Dd=Nd;Nd.default=Nd;let Fd=Hh;let Ld=Dd;let Pd=class t extends Fd{constructor(t){super(t);this.type="rule";if(!this.nodes)this.nodes=[]}get selectors(){return Ld.comma(this.selector)}set selectors(t){let e=this.selector?this.selector.match(/,\s*/):null;let i=e?e[0]:","+this.raw("between","beforeOpen");this.selector=t.join(i)}};var Bd=Pd;Pd.default=Pd;Fd.registerRule(Pd);let Ud=Vc;let Gd=Id;let Wd=xh;let zd=Td;let Vd=jd;let Hd=Bd;const Yd={empty:true,space:true};function Zd(t){for(let e=t.length-1;e>=0;e--){let i=t[e];let r=i[3]||i[2];if(r)return r}}let Xd=class t{constructor(t){this.input=t;this.root=new Vd;this.current=this.root;this.spaces="";this.semicolon=false;this.createTokenizer();this.root.source={input:t,start:{column:1,line:1,offset:0}}}atrule(t){let e=new zd;e.name=t[1].slice(1);if(e.name===""){this.unnamedAtrule(e,t)}this.init(e,t[2]);let i;let r;let n;let s=false;let o=false;let a=[];let u=[];while(!this.tokenizer.endOfFile()){t=this.tokenizer.nextToken();i=t[0];if(i==="("||i==="["){u.push(i==="("?")":"]")}else if(i==="{"&&u.length>0){u.push("}")}else if(i===u[u.length-1]){u.pop()}if(u.length===0){if(i===";"){e.source.end=this.getPosition(t[2]);e.source.end.offset++;this.semicolon=true;break}else if(i==="{"){o=true;break}else if(i==="}"){if(a.length>0){n=a.length-1;r=a[n];while(r&&r[0]==="space"){r=a[--n]}if(r){e.source.end=this.getPosition(r[3]||r[2]);e.source.end.offset++}}this.end(t);break}else{a.push(t)}}else{a.push(t)}if(this.tokenizer.endOfFile()){s=true;break}}e.raws.between=this.spacesAndCommentsFromEnd(a);if(a.length){e.raws.afterName=this.spacesAndCommentsFromStart(a);this.raw(e,"params",a);if(s){t=a[a.length-1];e.source.end=this.getPosition(t[3]||t[2]);e.source.end.offset++;this.spaces=e.raws.between;e.raws.between=""}}else{e.raws.afterName="";e.params=""}if(o){e.nodes=[];this.current=e}}checkMissedSemicolon(t){let e=this.colon(t);if(e===false)return;let i=0;let r;for(let n=e-1;n>=0;n--){r=t[n];if(r[0]!=="space"){i+=1;if(i===2)break}}throw this.input.error("Missed semicolon",r[0]==="word"?r[3]+1:r[2])}colon(t){let e=0;let i,r,n;for(let[s,o]of t.entries()){i=o;r=i[0];if(r==="("){e+=1}if(r===")"){e-=1}if(e===0&&r===":"){if(!n){this.doubleColon(i)}else if(n[0]==="word"&&n[1]==="progid"){continue}else{return s}}n=i}return false}comment(t){let e=new Wd;this.init(e,t[2]);e.source.end=this.getPosition(t[3]||t[2]);e.source.end.offset++;let i=t[1].slice(2,-2);if(/^\s*$/.test(i)){e.text="";e.raws.left=i;e.raws.right=""}else{let t=i.match(/^(\s*)([^]*\S)(\s*)$/);e.text=t[2];e.raws.left=t[1];e.raws.right=t[3]}}createTokenizer(){this.tokenizer=Gd(this.input)}decl(t,e){let i=new Ud;this.init(i,t[0][2]);let r=t[t.length-1];if(r[0]===";"){this.semicolon=true;t.pop()}i.source.end=this.getPosition(r[3]||r[2]||Zd(t));i.source.end.offset++;while(t[0][0]!=="word"){if(t.length===1)this.unknownWord(t);i.raws.before+=t.shift()[1]}i.source.start=this.getPosition(t[0][2]);i.prop="";while(t.length){let e=t[0][0];if(e===":"||e==="space"||e==="comment"){break}i.prop+=t.shift()[1]}i.raws.between="";let n;while(t.length){n=t.shift();if(n[0]===":"){i.raws.between+=n[1];break}else{if(n[0]==="word"&&/\w/.test(n[1])){this.unknownWord([n])}i.raws.between+=n[1]}}if(i.prop[0]==="_"||i.prop[0]==="*"){i.raws.before+=i.prop[0];i.prop=i.prop.slice(1)}let s=[];let o;while(t.length){o=t[0][0];if(o!=="space"&&o!=="comment")break;s.push(t.shift())}this.precheckMissedSemicolon(t);for(let e=t.length-1;e>=0;e--){n=t[e];if(n[1].toLowerCase()==="!important"){i.important=true;let r=this.stringFrom(t,e);r=this.spacesFromEnd(t)+r;if(r!==" !important")i.raws.important=r;break}else if(n[1].toLowerCase()==="important"){let r=t.slice(0);let n="";for(let t=e;t>0;t--){let e=r[t][0];if(n.trim().indexOf("!")===0&&e!=="space"){break}n=r.pop()[1]+n}if(n.trim().indexOf("!")===0){i.important=true;i.raws.important=n;t=r}}if(n[0]!=="space"&&n[0]!=="comment"){break}}let a=t.some((t=>t[0]!=="space"&&t[0]!=="comment"));if(a){i.raws.between+=s.map((t=>t[1])).join("");s=[]}this.raw(i,"value",s.concat(t),e);if(i.value.includes(":")&&!e){this.checkMissedSemicolon(t)}}doubleColon(t){throw this.input.error("Double colon",{offset:t[2]},{offset:t[2]+t[1].length})}emptyRule(t){let e=new Hd;this.init(e,t[2]);e.selector="";e.raws.between="";this.current=e}end(t){if(this.current.nodes&&this.current.nodes.length){this.current.raws.semicolon=this.semicolon}this.semicolon=false;this.current.raws.after=(this.current.raws.after||"")+this.spaces;this.spaces="";if(this.current.parent){this.current.source.end=this.getPosition(t[2]);this.current.source.end.offset++;this.current=this.current.parent}else{this.unexpectedClose(t)}}endFile(){if(this.current.parent)this.unclosedBlock();if(this.current.nodes&&this.current.nodes.length){this.current.raws.semicolon=this.semicolon}this.current.raws.after=(this.current.raws.after||"")+this.spaces;this.root.source.end=this.getPosition(this.tokenizer.position())}freeSemicolon(t){this.spaces+=t[1];if(this.current.nodes){let t=this.current.nodes[this.current.nodes.length-1];if(t&&t.type==="rule"&&!t.raws.ownSemicolon){t.raws.ownSemicolon=this.spaces;this.spaces=""}}}getPosition(t){let e=this.input.fromOffset(t);return{column:e.col,line:e.line,offset:t}}init(t,e){this.current.push(t);t.source={input:this.input,start:this.getPosition(e)};t.raws.before=this.spaces;this.spaces="";if(t.type!=="comment")this.semicolon=false}other(t){let e=false;let i=null;let r=false;let n=null;let s=[];let o=t[1].startsWith("--");let a=[];let u=t;while(u){i=u[0];a.push(u);if(i==="("||i==="["){if(!n)n=u;s.push(i==="("?")":"]")}else if(o&&r&&i==="{"){if(!n)n=u;s.push("}")}else if(s.length===0){if(i===";"){if(r){this.decl(a,o);return}else{break}}else if(i==="{"){this.rule(a);return}else if(i==="}"){this.tokenizer.back(a.pop());e=true;break}else if(i===":"){r=true}}else if(i===s[s.length-1]){s.pop();if(s.length===0)n=null}u=this.tokenizer.nextToken()}if(this.tokenizer.endOfFile())e=true;if(s.length>0)this.unclosedBracket(n);if(e&&r){if(!o){while(a.length){u=a[a.length-1][0];if(u!=="space"&&u!=="comment")break;this.tokenizer.back(a.pop())}}this.decl(a,o)}else{this.unknownWord(a)}}parse(){let t;while(!this.tokenizer.endOfFile()){t=this.tokenizer.nextToken();switch(t[0]){case"space":this.spaces+=t[1];break;case";":this.freeSemicolon(t);break;case"}":this.end(t);break;case"comment":this.comment(t);break;case"at-word":this.atrule(t);break;case"{":this.emptyRule(t);break;default:this.other(t);break}}this.endFile()}precheckMissedSemicolon(){}raw(t,e,i,r){let n,s;let o=i.length;let a="";let u=true;let f,l;for(let t=0;t<o;t+=1){n=i[t];s=n[0];if(s==="space"&&t===o-1&&!r){u=false}else if(s==="comment"){l=i[t-1]?i[t-1][0]:"empty";f=i[t+1]?i[t+1][0]:"empty";if(!Yd[l]&&!Yd[f]){if(a.slice(-1)===","){u=false}else{a+=n[1]}}else{u=false}}else{a+=n[1]}}if(!u){let r=i.reduce(((t,e)=>t+e[1]),"");t.raws[e]={raw:r,value:a}}t[e]=a}rule(t){t.pop();let e=new Hd;this.init(e,t[0][2]);e.raws.between=this.spacesAndCommentsFromEnd(t);this.raw(e,"selector",t);this.current=e}spacesAndCommentsFromEnd(t){let e;let i="";while(t.length){e=t[t.length-1][0];if(e!=="space"&&e!=="comment")break;i=t.pop()[1]+i}return i}spacesAndCommentsFromStart(t){let e;let i="";while(t.length){e=t[0][0];if(e!=="space"&&e!=="comment")break;i+=t.shift()[1]}return i}spacesFromEnd(t){let e;let i="";while(t.length){e=t[t.length-1][0];if(e!=="space")break;i=t.pop()[1]+i}return i}stringFrom(t,e){let i="";for(let r=e;r<t.length;r++){i+=t[r][1]}t.splice(e,t.length-e);return i}unclosedBlock(){let t=this.current.source.start;throw this.input.error("Unclosed block",t.line,t.column)}unclosedBracket(t){throw this.input.error("Unclosed bracket",{offset:t[2]},{offset:t[2]+1})}unexpectedClose(t){throw this.input.error("Unexpected }",{offset:t[2]},{offset:t[2]+1})}unknownWord(t){throw this.input.error("Unknown word",{offset:t[0][2]},{offset:t[0][2]+t[0][1].length})}unnamedAtrule(t,e){throw this.input.error("At-rule without name",{offset:e[2]},{offset:e[2]+e[1].length})}};var $d=Xd;let Jd=Hh;let Kd=$d;let qd=gh;function Qd(t,e){let i=new qd(t,e);let r=new Kd(i);try{r.parse()}catch(t){throw t}return r.root}var tv=Qd;Qd.default=Qd;Jd.registerParse(Qd);let{isClean:ev,my:iv}=Oc;let rv=_h;let nv=jc;let sv=Hh;let ov=Jh;let av=ed;let uv=tv;let fv=jd;const lv={atrule:"AtRule",comment:"Comment",decl:"Declaration",document:"Document",root:"Root",rule:"Rule"};const cv={AtRule:true,AtRuleExit:true,Comment:true,CommentExit:true,Declaration:true,DeclarationExit:true,Document:true,DocumentExit:true,Once:true,OnceExit:true,postcssPlugin:true,prepare:true,Root:true,RootExit:true,Rule:true,RuleExit:true};const hv={Once:true,postcssPlugin:true,prepare:true};const dv=0;function vv(t){return typeof t==="object"&&typeof t.then==="function"}function pv(t){let e=false;let i=lv[t.type];if(t.type==="decl"){e=t.prop.toLowerCase()}else if(t.type==="atrule"){e=t.name.toLowerCase()}if(e&&t.append){return[i,i+"-"+e,dv,i+"Exit",i+"Exit-"+e]}else if(e){return[i,i+"-"+e,i+"Exit",i+"Exit-"+e]}else if(t.append){return[i,dv,i+"Exit"]}else{return[i,i+"Exit"]}}function mv(t){let e;if(t.type==="document"){e=["Document",dv,"DocumentExit"]}else if(t.type==="root"){e=["Root",dv,"RootExit"]}else{e=pv(t)}return{eventIndex:0,events:e,iterator:0,node:t,visitorIndex:0,visitors:[]}}function gv(t){t[ev]=false;if(t.nodes)t.nodes.forEach((t=>gv(t)));return t}let yv={};let bv=class t{constructor(e,i,r){this.stringified=false;this.processed=false;let n;if(typeof i==="object"&&i!==null&&(i.type==="root"||i.type==="document")){n=gv(i)}else if(i instanceof t||i instanceof av){n=gv(i.root);if(i.map){if(typeof r.map==="undefined")r.map={};if(!r.map.inline)r.map.inline=false;r.map.prev=i.map}}else{let t=uv;if(r.syntax)t=r.syntax.parse;if(r.parser)t=r.parser;if(t.parse)t=t.parse;try{n=t(i,r)}catch(t){this.processed=true;this.error=t}if(n&&!n[iv]){sv.rebuild(n)}}this.result=new av(e,n,r);this.helpers={...yv,postcss:yv,result:this.result};this.plugins=this.processor.plugins.map((t=>{if(typeof t==="object"&&t.prepare){return{...t,...t.prepare(this.result)}}else{return t}}))}async(){if(this.error)return Promise.reject(this.error);if(this.processed)return Promise.resolve(this.result);if(!this.processing){this.processing=this.runAsync()}return this.processing}catch(t){return this.async().catch(t)}finally(t){return this.async().then(t,t)}getAsyncError(){throw new Error("Use process(css).then(cb) to work with async plugins")}handleError(t,e){let i=this.result.lastPlugin;try{if(e)e.addToError(t);this.error=t;if(t.name==="CssSyntaxError"&&!t.plugin){t.plugin=i.postcssPlugin;t.setMessage()}}catch(t){if(console&&console.error)console.error(t)}return t}prepareVisitors(){this.listeners={};let t=(t,e,i)=>{if(!this.listeners[e])this.listeners[e]=[];this.listeners[e].push([t,i])};for(let e of this.plugins){if(typeof e==="object"){for(let i in e){if(!cv[i]&&/^[A-Z]/.test(i)){throw new Error(`Unknown event ${i} in ${e.postcssPlugin}. Try to update PostCSS (${this.processor.version} now).`)}if(!hv[i]){if(typeof e[i]==="object"){for(let r in e[i]){if(r==="*"){t(e,i,e[i][r])}else{t(e,i+"-"+r.toLowerCase(),e[i][r])}}}else if(typeof e[i]==="function"){t(e,i,e[i])}}}}}this.hasListener=Object.keys(this.listeners).length>0}async runAsync(){this.plugin=0;for(let t=0;t<this.plugins.length;t++){let e=this.plugins[t];let i=this.runOnRoot(e);if(vv(i)){try{await i}catch(t){throw this.handleError(t)}}}this.prepareVisitors();if(this.hasListener){let t=this.result.root;while(!t[ev]){t[ev]=true;let e=[mv(t)];while(e.length>0){let t=this.visitTick(e);if(vv(t)){try{await t}catch(t){let i=e[e.length-1].node;throw this.handleError(t,i)}}}}if(this.listeners.OnceExit){for(let[e,i]of this.listeners.OnceExit){this.result.lastPlugin=e;try{if(t.type==="document"){let e=t.nodes.map((t=>i(t,this.helpers)));await Promise.all(e)}else{await i(t,this.helpers)}}catch(t){throw this.handleError(t)}}}}this.processed=true;return this.stringify()}runOnRoot(t){this.result.lastPlugin=t;try{if(typeof t==="object"&&t.Once){if(this.result.root.type==="document"){let e=this.result.root.nodes.map((e=>t.Once(e,this.helpers)));if(vv(e[0])){return Promise.all(e)}return e}return t.Once(this.result.root,this.helpers)}else if(typeof t==="function"){return t(this.result.root,this.result)}}catch(t){throw this.handleError(t)}}stringify(){if(this.error)throw this.error;if(this.stringified)return this.result;this.stringified=true;this.sync();let t=this.result.opts;let e=nv;if(t.syntax)e=t.syntax.stringify;if(t.stringifier)e=t.stringifier;if(e.stringify)e=e.stringify;let i=new rv(e,this.result.root,this.result.opts);let r=i.generate();this.result.css=r[0];this.result.map=r[1];return this.result}sync(){if(this.error)throw this.error;if(this.processed)return this.result;this.processed=true;if(this.processing){throw this.getAsyncError()}for(let t of this.plugins){let e=this.runOnRoot(t);if(vv(e)){throw this.getAsyncError()}}this.prepareVisitors();if(this.hasListener){let t=this.result.root;while(!t[ev]){t[ev]=true;this.walkSync(t)}if(this.listeners.OnceExit){if(t.type==="document"){for(let e of t.nodes){this.visitSync(this.listeners.OnceExit,e)}}else{this.visitSync(this.listeners.OnceExit,t)}}}return this.result}then(t,e){return this.async().then(t,e)}toString(){return this.css}visitSync(t,e){for(let[i,r]of t){this.result.lastPlugin=i;let t;try{t=r(e,this.helpers)}catch(t){throw this.handleError(t,e.proxyOf)}if(e.type!=="root"&&e.type!=="document"&&!e.parent){return true}if(vv(t)){throw this.getAsyncError()}}}visitTick(t){let e=t[t.length-1];let{node:i,visitors:r}=e;if(i.type!=="root"&&i.type!=="document"&&!i.parent){t.pop();return}if(r.length>0&&e.visitorIndex<r.length){let[t,n]=r[e.visitorIndex];e.visitorIndex+=1;if(e.visitorIndex===r.length){e.visitors=[];e.visitorIndex=0}this.result.lastPlugin=t;try{return n(i.toProxy(),this.helpers)}catch(t){throw this.handleError(t,i)}}if(e.iterator!==0){let r=e.iterator;let n;while(n=i.nodes[i.indexes[r]]){i.indexes[r]+=1;if(!n[ev]){n[ev]=true;t.push(mv(n));return}}e.iterator=0;delete i.indexes[r]}let n=e.events;while(e.eventIndex<n.length){let t=n[e.eventIndex];e.eventIndex+=1;if(t===dv){if(i.nodes&&i.nodes.length){i[ev]=true;e.iterator=i.getIterator()}return}else if(this.listeners[t]){e.visitors=this.listeners[t];return}}t.pop()}walkSync(t){t[ev]=true;let e=pv(t);for(let i of e){if(i===dv){if(t.nodes){t.each((t=>{if(!t[ev])this.walkSync(t)}))}}else{let e=this.listeners[i];if(e){if(this.visitSync(e,t.toProxy()))return}}}}warnings(){return this.sync().warnings()}get content(){return this.stringify().content}get css(){return this.stringify().css}get map(){return this.stringify().map}get messages(){return this.sync().messages}get opts(){return this.result.opts}get processor(){return this.result.processor}get root(){return this.sync().root}get[Symbol.toStringTag](){return"LazyResult"}};bv.registerPostcss=t=>{yv=t};var wv=bv;bv.default=bv;fv.registerLazyResult(bv);ov.registerLazyResult(bv);let Sv=_h;let kv=jc;let Cv=tv;const Mv=ed;let Iv=class t{constructor(t,e,i){e=e.toString();this.stringified=false;this._processor=t;this._css=e;this._opts=i;this._map=void 0;let r;let n=kv;this.result=new Mv(this._processor,r,this._opts);this.result.css=e;let s=this;Object.defineProperty(this.result,"root",{get(){return s.root}});let o=new Sv(n,r,this._opts,e);if(o.isMap()){let[t,e]=o.generate();if(t){this.result.css=t}if(e){this.result.map=e}}else{o.clearAnnotation();this.result.css=o.css}}async(){if(this.error)return Promise.reject(this.error);return Promise.resolve(this.result)}catch(t){return this.async().catch(t)}finally(t){return this.async().then(t,t)}sync(){if(this.error)throw this.error;return this.result}then(t,e){return this.async().then(t,e)}toString(){return this._css}warnings(){return[]}get content(){return this.result.css}get css(){return this.result.css}get map(){return this.result.map}get messages(){return[]}get opts(){return this.result.opts}get processor(){return this.result.processor}get root(){if(this._root){return this._root}let t;let e=Cv;try{t=e(this._css,this._opts)}catch(t){this.error=t}if(this.error){throw this.error}else{this._root=t;return t}}get[Symbol.toStringTag](){return"NoWorkResult"}};var Ov=Iv;Iv.default=Iv;let Av=Ov;let Tv=wv;let _v=Jh;let Ev=jd;let Rv=class t{constructor(t=[]){this.version="8.4.38";this.plugins=this.normalize(t)}normalize(t){let e=[];for(let i of t){if(i.postcss===true){i=i()}else if(i.postcss){i=i.postcss}if(typeof i==="object"&&Array.isArray(i.plugins)){e=e.concat(i.plugins)}else if(typeof i==="object"&&i.postcssPlugin){e.push(i)}else if(typeof i==="function"){e.push(i)}else if(typeof i==="object"&&(i.parse||i.stringify));else{throw new Error(i+" is not a PostCSS plugin")}}return e}process(t,e={}){if(!this.plugins.length&&!e.parser&&!e.stringifier&&!e.syntax){return new Av(this,t,e)}else{return new Tv(this,t,e)}}use(t){this.plugins=this.plugins.concat(this.normalize([t]));return this}};var xv=Rv;Rv.default=Rv;Ev.registerProcessor(Rv);_v.registerProcessor(Rv);let jv=Vc;let Nv=ih;let Dv=xh;let Fv=Td;let Lv=gh;let Pv=jd;let Bv=Bd;function Uv(t,e){if(Array.isArray(t))return t.map((t=>Uv(t)));let{inputs:i,...r}=t;if(i){e=[];for(let t of i){let i={...t,__proto__:Lv.prototype};if(i.map){i.map={...i.map,__proto__:Nv.prototype}}e.push(i)}}if(r.nodes){r.nodes=t.nodes.map((t=>Uv(t,e)))}if(r.source){let{inputId:t,...i}=r.source;r.source=i;if(t!=null){r.source.input=e[t]}}if(r.type==="root"){return new Pv(r)}else if(r.type==="decl"){return new jv(r)}else if(r.type==="rule"){return new Bv(r)}else if(r.type==="comment"){return new Dv(r)}else if(r.type==="atrule"){return new Fv(r)}else{throw new Error("Unknown node type: "+t.type)}}var Gv=Uv;Uv.default=Uv;let Wv=Ic;let zv=Vc;let Vv=wv;let Hv=Hh;let Yv=xv;let Zv=jc;let Xv=Gv;let $v=Jh;let Jv=qh;let Kv=xh;let qv=Td;let Qv=ed;let tp=gh;let ep=tv;let ip=Dd;let rp=Bd;let np=jd;let sp=Gc;function op(...t){if(t.length===1&&Array.isArray(t[0])){t=t[0]}return new Yv(t)}op.plugin=function t(e,i){let r=false;function n(...t){if(console&&console.warn&&!r){r=true;console.warn(e+": postcss.plugin was deprecated. Migration guide:\nhttps://evilmartians.com/chronicles/postcss-8-plugin-migration");if(process.env.LANG&&process.env.LANG.startsWith("cn")){console.warn(e+": 里面 postcss.plugin 被弃用. 迁移指南:\nhttps://www.w3ctech.com/topic/2226")}}let n=i(...t);n.postcssPlugin=e;n.postcssVersion=(new Yv).version;return n}let s;Object.defineProperty(n,"postcss",{get(){if(!s)s=n();return s}});n.process=function(t,e,i){return op([n(i)]).process(t,e)};return n};op.stringify=Zv;op.parse=ep;op.fromJSON=Xv;op.list=ip;op.comment=t=>new Kv(t);op.atRule=t=>new qv(t);op.decl=t=>new zv(t);op.rule=t=>new rp(t);op.root=t=>new np(t);op.document=t=>new $v(t);op.CssSyntaxError=Wv;op.Declaration=zv;op.Container=Hv;op.Processor=Yv;op.Document=$v;op.Comment=Kv;op.Warning=Jv;op.AtRule=qv;op.Result=Qv;op.Input=tp;op.Rule=rp;op.Root=np;op.Node=sp;Vv.registerPostcss(op);op.default=op;var ap=(t=>{t[t["Document"]=0]="Document";t[t["DocumentType"]=1]="DocumentType";t[t["Element"]=2]="Element";t[t["Text"]=3]="Text";t[t["CDATA"]=4]="CDATA";t[t["Comment"]=5]="Comment";return t})(ap||{});function up(t){const e={};const i=/;(?![^(]*\))/g;const r=/:(.+)/;const n=/\/\*.*?\*\//g;t.replace(n,"").split(i).forEach((function(t){if(t){const i=t.split(r);i.length>1&&(e[hp(i[0].trim())]=i[1].trim())}}));return e}function fp(t){const e=[];for(const i in t){const r=t[i];if(typeof r!=="string")continue;const n=vp(i);e.push(`${n}: ${r};`)}return e.join(" ")}const lp=/-([a-z])/g;const cp=/^--[a-zA-Z0-9-]+$/;const hp=t=>{if(cp.test(t))return t;return t.replace(lp,((t,e)=>e?e.toUpperCase():""))};const dp=/\B([A-Z])/g;const vp=t=>t.replace(dp,"-$1").toLowerCase();class pp{constructor(...t){uc(this,"parentElement",null);uc(this,"parentNode",null);uc(this,"ownerDocument");uc(this,"firstChild",null);uc(this,"lastChild",null);uc(this,"previousSibling",null);uc(this,"nextSibling",null);uc(this,"ELEMENT_NODE",1);uc(this,"TEXT_NODE",3);uc(this,"nodeType");uc(this,"nodeName");uc(this,"RRNodeType")}get childNodes(){const t=[];let e=this.firstChild;while(e){t.push(e);e=e.nextSibling}return t}contains(t){if(!(t instanceof pp))return false;else if(t.ownerDocument!==this.ownerDocument)return false;else if(t===this)return true;while(t.parentNode){if(t.parentNode===this)return true;t=t.parentNode}return false}appendChild(t){throw new Error(`RRDomException: Failed to execute 'appendChild' on 'RRNode': This RRNode type does not support this method.`)}insertBefore(t,e){throw new Error(`RRDomException: Failed to execute 'insertBefore' on 'RRNode': This RRNode type does not support this method.`)}removeChild(t){throw new Error(`RRDomException: Failed to execute 'removeChild' on 'RRNode': This RRNode type does not support this method.`)}toString(){return"RRNode"}}class mp extends pp{constructor(...t){super(t);uc(this,"nodeType",9);uc(this,"nodeName","#document");uc(this,"compatMode","CSS1Compat");uc(this,"RRNodeType",ap.Document);uc(this,"textContent",null);this.ownerDocument=this}get documentElement(){return this.childNodes.find((t=>t.RRNodeType===ap.Element&&t.tagName==="HTML"))||null}get body(){var t;return((t=this.documentElement)==null?void 0:t.childNodes.find((t=>t.RRNodeType===ap.Element&&t.tagName==="BODY")))||null}get head(){var t;return((t=this.documentElement)==null?void 0:t.childNodes.find((t=>t.RRNodeType===ap.Element&&t.tagName==="HEAD")))||null}get implementation(){return this}get firstElementChild(){return this.documentElement}appendChild(t){const e=t.RRNodeType;if(e===ap.Element||e===ap.DocumentType){if(this.childNodes.some((t=>t.RRNodeType===e))){throw new Error(`RRDomException: Failed to execute 'appendChild' on 'RRNode': Only one ${e===ap.Element?"RRElement":"RRDoctype"} on RRDocument allowed.`)}}const i=Ip(this,t);i.parentElement=null;return i}insertBefore(t,e){const i=t.RRNodeType;if(i===ap.Element||i===ap.DocumentType){if(this.childNodes.some((t=>t.RRNodeType===i))){throw new Error(`RRDomException: Failed to execute 'insertBefore' on 'RRNode': Only one ${i===ap.Element?"RRElement":"RRDoctype"} on RRDocument allowed.`)}}const r=Op(this,t,e);r.parentElement=null;return r}removeChild(t){return Ap(this,t)}open(){this.firstChild=null;this.lastChild=null}close(){}write(t){let e;if(t==='<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "">')e="-//W3C//DTD XHTML 1.0 Transitional//EN";else if(t==='<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "">')e="-//W3C//DTD HTML 4.0 Transitional//EN";if(e){const t=this.createDocumentType("html",e,"");this.open();this.appendChild(t)}}createDocument(t,e,i){return new mp}createDocumentType(t,e,i){const r=new gp(t,e,i);r.ownerDocument=this;return r}createElement(t){const e=new yp(t);e.ownerDocument=this;return e}createElementNS(t,e){return this.createElement(e)}createTextNode(t){const e=new Sp(t);e.ownerDocument=this;return e}createComment(t){const e=new kp(t);e.ownerDocument=this;return e}createCDATASection(t){const e=new Cp(t);e.ownerDocument=this;return e}toString(){return"RRDocument"}}class gp extends pp{constructor(t,e,i){super();uc(this,"nodeType",10);uc(this,"RRNodeType",ap.DocumentType);uc(this,"name");uc(this,"publicId");uc(this,"systemId");uc(this,"textContent",null);this.name=t;this.publicId=e;this.systemId=i;this.nodeName=t}toString(){return"RRDocumentType"}}class yp extends pp{constructor(t){super();uc(this,"nodeType",1);uc(this,"RRNodeType",ap.Element);uc(this,"tagName");uc(this,"attributes",{});uc(this,"shadowRoot",null);uc(this,"scrollLeft");uc(this,"scrollTop");this.tagName=t.toUpperCase();this.nodeName=t.toUpperCase()}get textContent(){let t="";this.childNodes.forEach((e=>t+=e.textContent));return t}set textContent(t){this.firstChild=null;this.lastChild=null;this.appendChild(this.ownerDocument.createTextNode(t))}get classList(){return new Mp(this.attributes.class,(t=>{this.attributes.class=t}))}get id(){return this.attributes.id||""}get className(){return this.attributes.class||""}get style(){const t=this.attributes.style?up(this.attributes.style):{};const e=/\B([A-Z])/g;t.setProperty=(i,r,n)=>{if(e.test(i))return;const s=hp(i);if(!r)delete t[s];else t[s]=r;if(n==="important")t[s]+=" !important";this.attributes.style=fp(t)};t.removeProperty=i=>{if(e.test(i))return"";const r=hp(i);const n=t[r]||"";delete t[r];this.attributes.style=fp(t);return n};return t}getAttribute(t){if(this.attributes[t]===void 0)return null;return this.attributes[t]}setAttribute(t,e){this.attributes[t]=e}setAttributeNS(t,e,i){this.setAttribute(e,i)}removeAttribute(t){delete this.attributes[t]}appendChild(t){return Ip(this,t)}insertBefore(t,e){return Op(this,t,e)}removeChild(t){return Ap(this,t)}attachShadow(t){const e=this.ownerDocument.createElement("SHADOWROOT");this.shadowRoot=e;return e}dispatchEvent(t){return true}toString(){let t="";for(const e in this.attributes){t+=`${e}="${this.attributes[e]}" `}return`${this.tagName} ${t}`}}class bp extends yp{constructor(){super(...arguments);uc(this,"currentTime");uc(this,"volume");uc(this,"paused");uc(this,"muted");uc(this,"playbackRate");uc(this,"loop")}attachShadow(t){throw new Error(`RRDomException: Failed to execute 'attachShadow' on 'RRElement': This RRElement does not support attachShadow`)}play(){this.paused=false}pause(){this.paused=true}}class wp extends yp{constructor(){super(...arguments);uc(this,"tagName","DIALOG");uc(this,"nodeName","DIALOG")}get isModal(){return this.getAttribute("rr_open_mode")==="modal"}get open(){return this.getAttribute("open")!==null}close(){this.removeAttribute("open");this.removeAttribute("rr_open_mode")}show(){this.setAttribute("open","");this.setAttribute("rr_open_mode","non-modal")}showModal(){this.setAttribute("open","");this.setAttribute("rr_open_mode","modal")}}class Sp extends pp{constructor(t){super();uc(this,"nodeType",3);uc(this,"nodeName","#text");uc(this,"RRNodeType",ap.Text);uc(this,"data");this.data=t}get textContent(){return this.data}set textContent(t){this.data=t}toString(){return`RRText text=${JSON.stringify(this.data)}`}}class kp extends pp{constructor(t){super();uc(this,"nodeType",8);uc(this,"nodeName","#comment");uc(this,"RRNodeType",ap.Comment);uc(this,"data");this.data=t}get textContent(){return this.data}set textContent(t){this.data=t}toString(){return`RRComment text=${JSON.stringify(this.data)}`}}class Cp extends pp{constructor(t){super();uc(this,"nodeName","#cdata-section");uc(this,"nodeType",4);uc(this,"RRNodeType",ap.CDATA);uc(this,"data");this.data=t}get textContent(){return this.data}set textContent(t){this.data=t}toString(){return`RRCDATASection data=${JSON.stringify(this.data)}`}}class Mp{constructor(t,e){uc(this,"onChange");uc(this,"classes",[]);uc(this,"add",((...t)=>{for(const e of t){const t=String(e);if(this.classes.indexOf(t)>=0)continue;this.classes.push(t)}this.onChange&&this.onChange(this.classes.join(" "))}));uc(this,"remove",((...t)=>{this.classes=this.classes.filter((e=>t.indexOf(e)===-1));this.onChange&&this.onChange(this.classes.join(" "))}));if(t){const e=t.trim().split(/\s+/);this.classes.push(...e)}this.onChange=e}}function Ip(t,e){if(e.parentNode)e.parentNode.removeChild(e);if(t.lastChild){t.lastChild.nextSibling=e;e.previousSibling=t.lastChild}else{t.firstChild=e;e.previousSibling=null}t.lastChild=e;e.nextSibling=null;e.parentNode=t;e.parentElement=t;e.ownerDocument=t.ownerDocument;return e}function Op(t,e,i){if(!i)return Ip(t,e);if(i.parentNode!==t)throw new Error("Failed to execute 'insertBefore' on 'RRNode': The RRNode before which the new node is to be inserted is not a child of this RRNode.");if(e===i)return e;if(e.parentNode)e.parentNode.removeChild(e);e.previousSibling=i.previousSibling;i.previousSibling=e;e.nextSibling=i;if(e.previousSibling)e.previousSibling.nextSibling=e;else t.firstChild=e;e.parentElement=t;e.parentNode=t;e.ownerDocument=t.ownerDocument;return e}function Ap(t,e){if(e.parentNode!==t)throw new Error("Failed to execute 'removeChild' on 'RRNode': The RRNode to be removed is not a child of this RRNode.");if(e.previousSibling)e.previousSibling.nextSibling=e.nextSibling;else t.firstChild=e.nextSibling;if(e.nextSibling)e.nextSibling.previousSibling=e.previousSibling;else t.lastChild=e.previousSibling;e.previousSibling=null;e.nextSibling=null;e.parentElement=null;e.parentNode=null;return e}var Tp=(t=>{t[t["PLACEHOLDER"]=0]="PLACEHOLDER";t[t["ELEMENT_NODE"]=1]="ELEMENT_NODE";t[t["ATTRIBUTE_NODE"]=2]="ATTRIBUTE_NODE";t[t["TEXT_NODE"]=3]="TEXT_NODE";t[t["CDATA_SECTION_NODE"]=4]="CDATA_SECTION_NODE";t[t["ENTITY_REFERENCE_NODE"]=5]="ENTITY_REFERENCE_NODE";t[t["ENTITY_NODE"]=6]="ENTITY_NODE";t[t["PROCESSING_INSTRUCTION_NODE"]=7]="PROCESSING_INSTRUCTION_NODE";t[t["COMMENT_NODE"]=8]="COMMENT_NODE";t[t["DOCUMENT_NODE"]=9]="DOCUMENT_NODE";t[t["DOCUMENT_TYPE_NODE"]=10]="DOCUMENT_TYPE_NODE";t[t["DOCUMENT_FRAGMENT_NODE"]=11]="DOCUMENT_FRAGMENT_NODE";return t})(Tp||{});const _p={svg:"http://www.w3.org/2000/svg","xlink:href":"http://www.w3.org/1999/xlink",xmlns:"http://www.w3.org/2000/xmlns/"};const Ep={altglyph:"altGlyph",altglyphdef:"altGlyphDef",altglyphitem:"altGlyphItem",animatecolor:"animateColor",animatemotion:"animateMotion",animatetransform:"animateTransform",clippath:"clipPath",feblend:"feBlend",fecolormatrix:"feColorMatrix",fecomponenttransfer:"feComponentTransfer",fecomposite:"feComposite",feconvolvematrix:"feConvolveMatrix",fediffuselighting:"feDiffuseLighting",fedisplacementmap:"feDisplacementMap",fedistantlight:"feDistantLight",fedropshadow:"feDropShadow",feflood:"feFlood",fefunca:"feFuncA",fefuncb:"feFuncB",fefuncg:"feFuncG",fefuncr:"feFuncR",fegaussianblur:"feGaussianBlur",feimage:"feImage",femerge:"feMerge",femergenode:"feMergeNode",femorphology:"feMorphology",feoffset:"feOffset",fepointlight:"fePointLight",fespecularlighting:"feSpecularLighting",fespotlight:"feSpotLight",fetile:"feTile",feturbulence:"feTurbulence",foreignobject:"foreignObject",glyphref:"glyphRef",lineargradient:"linearGradient",radialgradient:"radialGradient"};let Rp=null;function xp(t,e,i,r=e.mirror||e.ownerDocument.mirror){t=jp(t,e,i,r);Fp(t,e,i,r);Np(t,e,i)}function jp(t,e,i,r){var n;if(i.afterAppend&&!Rp){Rp=new WeakSet;setTimeout((()=>{Rp=null}),0)}if(!Pp(t,e)){const s=Lp(e,i.mirror,r);(n=t.parentNode)==null?void 0:n.replaceChild(s,t);t=s}switch(e.RRNodeType){case ap.Document:{if(!Bp(t,e,i.mirror,r)){const n=r.getMeta(e);if(n){i.mirror.removeNodeFromMap(t);t.close();t.open();i.mirror.add(t,n);Rp==null?void 0:Rp.add(t)}}break}case ap.Element:{const n=t;const s=e;switch(s.tagName){case"IFRAME":{const n=t.contentDocument;if(!n)break;xp(n,e.contentDocument,i,r);break}}if(s.shadowRoot){if(!n.shadowRoot)n.attachShadow({mode:"open"});Fp(n.shadowRoot,s.shadowRoot,i,r)}Dp(n,s,r);break}}return t}function Np(t,e,i){var r;switch(e.RRNodeType){case ap.Document:{const t=e.scrollData;t&&i.applyScroll(t,true);break}case ap.Element:{const r=t;const n=e;n.scrollData&&i.applyScroll(n.scrollData,true);n.inputData&&i.applyInput(n.inputData);switch(n.tagName){case"AUDIO":case"VIDEO":{const e=t;const i=n;if(i.paused!==void 0)i.paused?void e.pause():void e.play();if(i.muted!==void 0)e.muted=i.muted;if(i.volume!==void 0)e.volume=i.volume;if(i.currentTime!==void 0)e.currentTime=i.currentTime;if(i.playbackRate!==void 0)e.playbackRate=i.playbackRate;if(i.loop!==void 0)e.loop=i.loop;break}case"CANVAS":{const n=e;if(n.rr_dataURL!==null){const t=document.createElement("img");t.onload=()=>{const e=r.getContext("2d");if(e){e.drawImage(t,0,0,t.width,t.height)}};t.src=n.rr_dataURL}n.canvasMutations.forEach((e=>i.applyCanvas(e.event,e.mutation,t)));break}case"STYLE":{const t=r.sheet;t&&e.rules.forEach((e=>i.applyStyleSheetMutation(e,t)));break}case"DIALOG":{const t=r;const e=n;const i=t.open;const s=t.matches("dialog:modal");const o=e.open;const a=e.isModal;const u=s!==a;const f=i!==o;if(u||i&&f)t.close();if(o&&(f||u)){try{if(a)t.showModal();else t.show()}catch(t){console.warn(t)}}break}}break}case ap.Text:case ap.Comment:case ap.CDATA:{if(t.textContent!==e.data)t.textContent=e.data;break}}if(Rp==null?void 0:Rp.has(t)){Rp.delete(t);(r=i.afterAppend)==null?void 0:r.call(i,t,i.mirror.getId(t))}}function Dp(t,e,i){const r=t.attributes;const n=e.attributes;for(const r in n){const s=n[r];const o=i.getMeta(e);if((o==null?void 0:o.isSVG)&&_p[r])t.setAttributeNS(_p[r],r,s);else if(e.tagName==="CANVAS"&&r==="rr_dataURL"){const e=document.createElement("img");e.src=s;e.onload=()=>{const i=t.getContext("2d");if(i){i.drawImage(e,0,0,e.width,e.height)}}}else if(e.tagName==="IFRAME"&&r==="srcdoc")continue;else{try{t.setAttribute(r,s)}catch(t){console.warn(t)}}}for(const{name:e}of Array.from(r))if(!(e in n))t.removeAttribute(e);e.scrollLeft&&(t.scrollLeft=e.scrollLeft);e.scrollTop&&(t.scrollTop=e.scrollTop)}function Fp(t,e,i,r){const n=Array.from(t.childNodes);const s=e.childNodes;if(n.length===0&&s.length===0)return;let o=0,a=n.length-1,u=0,f=s.length-1;let l=n[o],c=n[a],h=s[u],d=s[f];let v=void 0,p=void 0;while(o<=a&&u<=f){if(l===void 0){l=n[++o]}else if(c===void 0){c=n[--a]}else if(Bp(l,h,i.mirror,r)){l=n[++o];h=s[++u]}else if(Bp(c,d,i.mirror,r)){c=n[--a];d=s[--f]}else if(Bp(l,d,i.mirror,r)){try{t.insertBefore(l,c.nextSibling)}catch(t){console.warn(t)}l=n[++o];d=s[--f]}else if(Bp(c,h,i.mirror,r)){try{t.insertBefore(c,l)}catch(t){console.warn(t)}c=n[--a];h=s[++u]}else{if(!v){v={};for(let t=o;t<=a;t++){const e=n[t];if(e&&i.mirror.hasNode(e))v[i.mirror.getId(e)]=t}}p=v[r.getId(h)];const e=n[p];if(p!==void 0&&e&&Bp(e,h,i.mirror,r)){try{t.insertBefore(e,l)}catch(t){console.warn(t)}n[p]=void 0}else{const e=Lp(h,i.mirror,r);if(t.nodeName==="#document"&&l&&(e.nodeType===e.DOCUMENT_TYPE_NODE&&l.nodeType===l.DOCUMENT_TYPE_NODE||e.nodeType===e.ELEMENT_NODE&&l.nodeType===l.ELEMENT_NODE)){t.removeChild(l);i.mirror.removeNodeFromMap(l);l=n[++o]}try{t.insertBefore(e,l||null)}catch(t){console.warn(t)}}h=s[++u]}}if(o>a){const e=s[f+1];let n=null;if(e)n=i.mirror.getNode(r.getId(e));for(;u<=f;++u){const e=Lp(s[u],i.mirror,r);try{t.insertBefore(e,n)}catch(t){console.warn(t)}}}else if(u>f){for(;o<=a;o++){const e=n[o];if(!e||e.parentNode!==t)continue;try{t.removeChild(e);i.mirror.removeNodeFromMap(e)}catch(t){console.warn(t)}}}let m=t.firstChild;let g=e.firstChild;while(m!==null&&g!==null){xp(m,g,i,r);m=m.nextSibling;g=g.nextSibling}}function Lp(t,e,i){const r=i.getId(t);const n=i.getMeta(t);let s=null;if(r>-1)s=e.getNode(r);if(s!==null&&Pp(s,t))return s;switch(t.RRNodeType){case ap.Document:s=new Document;break;case ap.DocumentType:s=document.implementation.createDocumentType(t.name,t.publicId,t.systemId);break;case ap.Element:{let e=t.tagName.toLowerCase();e=Ep[e]||e;if(n&&"isSVG"in n&&(n==null?void 0:n.isSVG)){s=document.createElementNS(_p["svg"],e)}else s=document.createElement(t.tagName);break}case ap.Text:s=document.createTextNode(t.data);break;case ap.Comment:s=document.createComment(t.data);break;case ap.CDATA:s=document.createCDATASection(t.data);break}if(n)e.add(s,{...n});try{Rp==null?void 0:Rp.add(s)}catch(t){}return s}function Pp(t,e){if(t.nodeType!==e.nodeType)return false;return t.nodeType!==t.ELEMENT_NODE||t.tagName.toUpperCase()===e.tagName}function Bp(t,e,i,r){const n=i.getId(t);const s=r.getId(e);if(n===-1||n!==s)return false;return Pp(t,e)}class Up extends mp{constructor(t){super();uc(this,"UNSERIALIZED_STARTING_ID",-2);uc(this,"_unserializedId",this.UNSERIALIZED_STARTING_ID);uc(this,"mirror",tm());uc(this,"scrollData",null);if(t){this.mirror=t}}get unserializedId(){return this._unserializedId--}createDocument(t,e,i){return new Up}createDocumentType(t,e,i){const r=new Gp(t,e,i);r.ownerDocument=this;return r}createElement(t){const e=t.toUpperCase();let i;switch(e){case"AUDIO":case"VIDEO":i=new zp(e);break;case"IFRAME":i=new Zp(e,this.mirror);break;case"CANVAS":i=new Hp(e);break;case"STYLE":i=new Yp(e);break;case"DIALOG":i=new Vp(e);break;default:i=new Wp(e);break}i.ownerDocument=this;return i}createComment(t){const e=new $p(t);e.ownerDocument=this;return e}createCDATASection(t){const e=new Jp(t);e.ownerDocument=this;return e}createTextNode(t){const e=new Xp(t);e.ownerDocument=this;return e}destroyTree(){this.firstChild=null;this.lastChild=null;this.mirror.reset()}open(){super.open();this._unserializedId=this.UNSERIALIZED_STARTING_ID}}const Gp=gp;class Wp extends yp{constructor(){super(...arguments);uc(this,"inputData",null);uc(this,"scrollData",null)}}class zp extends bp{}class Vp extends wp{}class Hp extends Wp{constructor(){super(...arguments);uc(this,"rr_dataURL",null);uc(this,"canvasMutations",[])}getContext(){return null}}class Yp extends Wp{constructor(){super(...arguments);uc(this,"rules",[])}}class Zp extends Wp{constructor(t,e){super(t);uc(this,"contentDocument",new Up);this.contentDocument.mirror=e}}const Xp=Sp;const $p=kp;const Jp=Cp;function Kp(t){if(t instanceof HTMLFormElement){return"FORM"}return t.tagName.toUpperCase()}function qp(t,e,i,r){let n;switch(t.nodeType){case Tp.DOCUMENT_NODE:if(r&&r.nodeName==="IFRAME")n=r.contentDocument;else{n=e;n.compatMode=t.compatMode}break;case Tp.DOCUMENT_TYPE_NODE:{const i=t;n=e.createDocumentType(i.name,i.publicId,i.systemId);break}case Tp.ELEMENT_NODE:{const i=t;const r=Kp(i);n=e.createElement(r);const s=n;for(const{name:t,value:e}of Array.from(i.attributes)){s.attributes[t]=e}i.scrollLeft&&(s.scrollLeft=i.scrollLeft);i.scrollTop&&(s.scrollTop=i.scrollTop);break}case Tp.TEXT_NODE:n=e.createTextNode(t.textContent||"");break;case Tp.CDATA_SECTION_NODE:n=e.createCDATASection(t.data);break;case Tp.COMMENT_NODE:n=e.createComment(t.textContent||"");break;case Tp.DOCUMENT_FRAGMENT_NODE:n=r.attachShadow({mode:"open"});break;default:return null}let s=i.getMeta(t);if(e instanceof Up){if(!s){s=im(n,e.unserializedId);i.add(t,s)}e.mirror.add(n,{...s})}return n}function Qp(t,e=dc(),i=new Up){function r(t,n){const s=qp(t,i,e,n);if(s===null)return;if((n==null?void 0:n.nodeName)!=="IFRAME"&&t.nodeType!==Tp.DOCUMENT_FRAGMENT_NODE){n==null?void 0:n.appendChild(s);s.parentNode=n;s.parentElement=n}if(t.nodeName==="IFRAME"){const e=t.contentDocument;e&&r(e,s)}else if(t.nodeType===Tp.DOCUMENT_NODE||t.nodeType===Tp.ELEMENT_NODE||t.nodeType===Tp.DOCUMENT_FRAGMENT_NODE){if(t.nodeType===Tp.ELEMENT_NODE&&t.shadowRoot)r(t.shadowRoot,s);t.childNodes.forEach((t=>r(t,s)))}}r(t,null);return i}function tm(){return new em}class em{constructor(){uc(this,"idNodeMap",new Map);uc(this,"nodeMetaMap",new WeakMap)}getId(t){var e;if(!t)return-1;const i=(e=this.getMeta(t))==null?void 0:e.id;return i??-1}getNode(t){return this.idNodeMap.get(t)||null}getIds(){return Array.from(this.idNodeMap.keys())}getMeta(t){return this.nodeMetaMap.get(t)||null}removeNodeFromMap(t){const e=this.getId(t);this.idNodeMap.delete(e);if(t.childNodes){t.childNodes.forEach((t=>this.removeNodeFromMap(t)))}}has(t){return this.idNodeMap.has(t)}hasNode(t){return this.nodeMetaMap.has(t)}add(t,e){const i=e.id;this.idNodeMap.set(i,t);this.nodeMetaMap.set(t,e)}replace(t,e){const i=this.getNode(t);if(i){const t=this.nodeMetaMap.get(i);if(t)this.nodeMetaMap.set(e,t)}this.idNodeMap.set(t,e)}reset(){this.idNodeMap=new Map;this.nodeMetaMap=new WeakMap}}function im(t,e){switch(t.RRNodeType){case ap.Document:return{id:e,type:t.RRNodeType,childNodes:[]};case ap.DocumentType:{const i=t;return{id:e,type:t.RRNodeType,name:i.name,publicId:i.publicId,systemId:i.systemId}}case ap.Element:return{id:e,type:t.RRNodeType,tagName:t.tagName.toLowerCase(),attributes:{},childNodes:[]};case ap.Text:return{id:e,type:t.RRNodeType,textContent:t.textContent||""};case ap.Comment:return{id:e,type:t.RRNodeType,textContent:t.textContent||""};case ap.CDATA:return{id:e,type:t.RRNodeType,textContent:""}}}const rm={Node:["childNodes","parentNode","parentElement","textContent"],ShadowRoot:["host","styleSheets"],Element:["shadowRoot","querySelector","querySelectorAll"],MutationObserver:[]};const nm={Node:["contains","getRootNode"],ShadowRoot:["getSelection"],Element:[],MutationObserver:["constructor"]};const sm={};const om=()=>!!globalThis.Zone;function am(t){if(sm[t])return sm[t];const e=globalThis[t];const i=e.prototype;const r=t in rm?rm[t]:void 0;const n=Boolean(r&&r.every((t=>{var e,r;return Boolean((r=(e=Object.getOwnPropertyDescriptor(i,t))==null?void 0:e.get)==null?void 0:r.toString().includes("[native code]"))})));const s=t in nm?nm[t]:void 0;const o=Boolean(s&&s.every((t=>{var e;return typeof i[t]==="function"&&((e=i[t])==null?void 0:e.toString().includes("[native code]"))})));if(n&&o&&!om()){sm[t]=e.prototype;return e.prototype}try{const r=document.createElement("iframe");document.body.appendChild(r);const n=r.contentWindow;if(!n)return e.prototype;const s=n[t].prototype;document.body.removeChild(r);if(!s)return i;return sm[t]=s}catch{return i}}const um={};function fm(t,e,i){var r;const n=`${t}.${String(i)}`;if(um[n])return um[n].call(e);const s=am(t);const o=(r=Object.getOwnPropertyDescriptor(s,i))==null?void 0:r.get;if(!o)return e[i];um[n]=o;return o.call(e)}const lm={};function cm(t,e,i){const r=`${t}.${String(i)}`;if(lm[r])return lm[r].bind(e);const n=am(t);const s=n[i];if(typeof s!=="function")return e[i];lm[r]=s;return s.bind(e)}function hm(t){return fm("Node",t,"childNodes")}function dm(t){return fm("Node",t,"parentNode")}function vm(t){return fm("Node",t,"parentElement")}function pm(t){return fm("Node",t,"textContent")}function mm(t,e){return cm("Node",t,"contains")(e)}function gm(t){return cm("Node",t,"getRootNode")()}function ym(t){if(!t||!("host"in t))return null;return fm("ShadowRoot",t,"host")}function bm(t){return t.styleSheets}function wm(t){if(!t||!("shadowRoot"in t))return null;return fm("Element",t,"shadowRoot")}function Sm(t,e){return fm("Element",t,"querySelector")(e)}function km(t,e){return fm("Element",t,"querySelectorAll")(e)}function Cm(){return am("MutationObserver").constructor}const Mm={childNodes:hm,parentNode:dm,parentElement:vm,textContent:pm,contains:mm,getRootNode:gm,host:ym,styleSheets:bm,shadowRoot:wm,querySelector:Sm,querySelectorAll:km,mutationObserver:Cm};function Im(t,e,i=document){const r={capture:true,passive:true};i.addEventListener(t,e,r);return()=>i.removeEventListener(t,e,r)}const Om="Please stop import mirror directly. Instead of that,\r\nnow you can use replayer.getMirror() to access the mirror instance of a replayer,\r\nor you can use record.mirror to access the mirror instance during recording.";let Am={map:{},getId(){console.error(Om);return-1},getNode(){console.error(Om);return null},removeNodeFromMap(){console.error(Om)},has(){console.error(Om);return false},reset(){console.error(Om)}};if(typeof window!=="undefined"&&window.Proxy&&window.Reflect){Am=new Proxy(Am,{get(t,e,i){if(e==="map"){console.error(Om)}return Reflect.get(t,e,i)}})}function Tm(t,e,i={}){let r=null;let n=0;return function(...s){const o=Date.now();if(!n&&i.leading===false){n=o}const a=e-(o-n);const u=this;if(a<=0||a>e){if(r){clearTimeout(r);r=null}n=o;t.apply(u,s)}else if(!r&&i.trailing!==false){r=setTimeout((()=>{n=i.leading===false?0:Date.now();r=null;t.apply(u,s)}),a)}}}function _m(t,e,i,r,n=window){const s=n.Object.getOwnPropertyDescriptor(t,e);n.Object.defineProperty(t,e,r?i:{set(t){setTimeout((()=>{i.set.call(this,t)}),0);if(s&&s.set){s.set.call(this,t)}}});return()=>_m(t,e,s||{},true)}function Em(t,e,i){try{if(!(e in t)){return()=>{}}const r=t[e];const n=i(r);if(typeof n==="function"){n.prototype=n.prototype||{};Object.defineProperties(n,{__rrweb_original__:{enumerable:false,value:r}})}t[e]=n;return()=>{t[e]=r}}catch{return()=>{}}}let Rm=Date.now;if(!/[1-9][0-9]{12}/.test(Date.now().toString())){Rm=()=>(new Date).getTime()}function xm(t){var e,i,r,n;const s=t.document;return{left:s.scrollingElement?s.scrollingElement.scrollLeft:t.pageXOffset!==void 0?t.pageXOffset:s.documentElement.scrollLeft||(s==null?void 0:s.body)&&((e=Mm.parentElement(s.body))==null?void 0:e.scrollLeft)||((i=s==null?void 0:s.body)==null?void 0:i.scrollLeft)||0,top:s.scrollingElement?s.scrollingElement.scrollTop:t.pageYOffset!==void 0?t.pageYOffset:(s==null?void 0:s.documentElement.scrollTop)||(s==null?void 0:s.body)&&((r=Mm.parentElement(s.body))==null?void 0:r.scrollTop)||((n=s==null?void 0:s.body)==null?void 0:n.scrollTop)||0}}function jm(){return window.innerHeight||document.documentElement&&document.documentElement.clientHeight||document.body&&document.body.clientHeight}function Nm(){return window.innerWidth||document.documentElement&&document.documentElement.clientWidth||document.body&&document.body.clientWidth}function Dm(t){if(!t){return null}const e=t.nodeType===t.ELEMENT_NODE?t:Mm.parentElement(t);return e}function Fm(t,e,i,r){if(!t){return false}const n=Dm(t);if(!n){return false}try{if(typeof e==="string"){if(n.classList.contains(e))return true;if(r&&n.closest("."+e)!==null)return true}else{if(Fo(n,e,r))return true}}catch(t){}if(i){if(n.matches(i))return true;if(r&&n.closest(i)!==null)return true}return false}function Lm(t,e){return e.getId(t)!==-1}function Pm(t,e,i){if(t.tagName==="TITLE"&&i.headTitleMutations){return true}return e.getId(t)===So}function Bm(t,e){if(Hs(t)){return false}const i=e.getId(t);if(!e.has(i)){return true}const r=Mm.parentNode(t);if(r&&r.nodeType===t.DOCUMENT_NODE){return false}if(!r){return true}return Bm(r,e)}function Um(t){return Boolean(t.changedTouches)}function Gm(t=window){if("NodeList"in t&&!t.NodeList.prototype.forEach){t.NodeList.prototype.forEach=Array.prototype.forEach}if("DOMTokenList"in t&&!t.DOMTokenList.prototype.forEach){t.DOMTokenList.prototype.forEach=Array.prototype.forEach}}function Wm(t){const e={};const i=(t,i)=>{const r={value:t,parent:i,children:[]};e[t.node.id]=r;return r};const r=[];for(const n of t){const{nextId:t,parentId:s}=n;if(t&&t in e){const s=e[t];if(s.parent){const t=s.parent.children.indexOf(s);s.parent.children.splice(t,0,i(n,s.parent))}else{const t=r.indexOf(s);r.splice(t,0,i(n,null))}continue}if(s in e){const t=e[s];t.children.push(i(n,t));continue}r.push(i(n,null))}return r}function zm(t,e){e(t.value);for(let i=t.children.length-1;i>=0;i--){zm(t.children[i],e)}}function Vm(t,e){return Boolean(t.nodeName==="IFRAME"&&e.getMeta(t))}function Hm(t,e){return Boolean(t.nodeName==="LINK"&&t.nodeType===t.ELEMENT_NODE&&t.getAttribute&&t.getAttribute("rel")==="stylesheet"&&e.getMeta(t))}function Ym(t,e){var i,r;const n=(r=(i=t.ownerDocument)==null?void 0:i.defaultView)==null?void 0:r.frameElement;if(!n||n===e){return{x:0,y:0,relativeScale:1,absoluteScale:1}}const s=n.getBoundingClientRect();const o=Ym(n,e);const a=s.height/n.clientHeight;return{x:s.x*o.relativeScale+o.x,y:s.y*o.relativeScale+o.y,relativeScale:a,absoluteScale:o.absoluteScale*a}}function Zm(t){if(!t)return false;if(t instanceof pp&&"shadowRoot"in t){return Boolean(t.shadowRoot)}return Boolean(Mm.shadowRoot(t))}function Xm(t,e){const i=t[e[0]];if(e.length===1){return i}else{return Xm(i.cssRules[e[1]].cssRules,e.slice(2))}}function $m(t){const e=[...t];const i=e.pop();return{positions:e,index:i}}function Jm(t){const e=new Set;const i=[];for(let r=t.length;r--;){const n=t[r];if(!e.has(n.id)){i.push(n);e.add(n.id)}}return i}class Km{constructor(){ms(this,"id",1);ms(this,"styleIDMap",new WeakMap);ms(this,"idStyleMap",new Map)}getId(t){return this.styleIDMap.get(t)??-1}has(t){return this.styleIDMap.has(t)}add(t,e){if(this.has(t))return this.getId(t);let i;if(e===void 0){i=this.id++}else i=e;this.styleIDMap.set(t,i);this.idStyleMap.set(i,t);return i}getStyle(t){return this.idStyleMap.get(t)||null}reset(){this.styleIDMap=new WeakMap;this.idStyleMap=new Map;this.id=1}generateId(){return this.id++}}function qm(t){var e;let i=null;if("getRootNode"in t&&((e=Mm.getRootNode(t))==null?void 0:e.nodeType)===Node.DOCUMENT_FRAGMENT_NODE&&Mm.host(Mm.getRootNode(t)))i=Mm.host(Mm.getRootNode(t));return i}function Qm(t){let e=t;let i;while(i=qm(e))e=i;return e}function tg(t){const e=t.ownerDocument;if(!e)return false;const i=Qm(t);return Mm.contains(e,i)}function eg(t){const e=t.ownerDocument;if(!e)return false;return Mm.contains(e,t)||tg(t)}const ig=Object.freeze(Object.defineProperty({__proto__:null,StyleSheetMirror:Km,get _mirror(){return Am},closestElementOfNode:Dm,getBaseDimension:Ym,getNestedRule:Xm,getPositionsAndIndex:$m,getRootShadowHost:Qm,getShadowHost:qm,getWindowHeight:jm,getWindowScroll:xm,getWindowWidth:Nm,hasShadowRoot:Zm,hookSetter:_m,inDom:eg,isAncestorRemoved:Bm,isBlocked:Fm,isIgnored:Pm,isSerialized:Lm,isSerializedIframe:Vm,isSerializedStylesheet:Hm,iterateResolveTree:zm,legacy_isTouchEvent:Um,get nowTimestamp(){return Rm},on:Im,patch:Em,polyfill:Gm,queueToResolveTrees:Wm,shadowHostInDom:tg,throttle:Tm,uniqueTextMutations:Jm},Symbol.toStringTag,{value:"Module"}));var rg=(t=>{t[t["DomContentLoaded"]=0]="DomContentLoaded";t[t["Load"]=1]="Load";t[t["FullSnapshot"]=2]="FullSnapshot";t[t["IncrementalSnapshot"]=3]="IncrementalSnapshot";t[t["Meta"]=4]="Meta";t[t["Custom"]=5]="Custom";t[t["Plugin"]=6]="Plugin";return t})(rg||{});var ng=(t=>{t[t["Mutation"]=0]="Mutation";t[t["MouseMove"]=1]="MouseMove";t[t["MouseInteraction"]=2]="MouseInteraction";t[t["Scroll"]=3]="Scroll";t[t["ViewportResize"]=4]="ViewportResize";t[t["Input"]=5]="Input";t[t["TouchMove"]=6]="TouchMove";t[t["MediaInteraction"]=7]="MediaInteraction";t[t["StyleSheetRule"]=8]="StyleSheetRule";t[t["CanvasMutation"]=9]="CanvasMutation";t[t["Font"]=10]="Font";t[t["Log"]=11]="Log";t[t["Drag"]=12]="Drag";t[t["StyleDeclaration"]=13]="StyleDeclaration";t[t["Selection"]=14]="Selection";t[t["AdoptedStyleSheet"]=15]="AdoptedStyleSheet";t[t["CustomElement"]=16]="CustomElement";return t})(ng||{});var sg=(t=>{t[t["MouseUp"]=0]="MouseUp";t[t["MouseDown"]=1]="MouseDown";t[t["Click"]=2]="Click";t[t["ContextMenu"]=3]="ContextMenu";t[t["DblClick"]=4]="DblClick";t[t["Focus"]=5]="Focus";t[t["Blur"]=6]="Blur";t[t["TouchStart"]=7]="TouchStart";t[t["TouchMove_Departed"]=8]="TouchMove_Departed";t[t["TouchEnd"]=9]="TouchEnd";t[t["TouchCancel"]=10]="TouchCancel";return t})(sg||{});var og=(t=>{t[t["Mouse"]=0]="Mouse";t[t["Pen"]=1]="Pen";t[t["Touch"]=2]="Touch";return t})(og||{});var ag=(t=>{t[t["2D"]=0]="2D";t[t["WebGL"]=1]="WebGL";t[t["WebGL2"]=2]="WebGL2";return t})(ag||{});var ug=(t=>{t[t["Play"]=0]="Play";t[t["Pause"]=1]="Pause";t[t["Seeked"]=2]="Seeked";t[t["VolumeChange"]=3]="VolumeChange";t[t["RateChange"]=4]="RateChange";return t})(ug||{});var fg=(t=>{t["Start"]="start";t["Pause"]="pause";t["Resume"]="resume";t["Resize"]="resize";t["Finish"]="finish";t["FullsnapshotRebuilded"]="fullsnapshot-rebuilded";t["LoadStylesheetStart"]="load-stylesheet-start";t["LoadStylesheetEnd"]="load-stylesheet-end";t["SkipStart"]="skip-start";t["SkipEnd"]="skip-end";t["MouseInteraction"]="mouse-interaction";t["EventCast"]="event-cast";t["CustomEvent"]="custom-event";t["Flush"]="flush";t["StateChange"]="state-change";t["PlayBack"]="play-back";t["Destroy"]="destroy";return t})(fg||{});var lg=(t=>{t[t["Document"]=0]="Document";t[t["DocumentType"]=1]="DocumentType";t[t["Element"]=2]="Element";t[t["Text"]=3]="Text";t[t["CDATA"]=4]="CDATA";t[t["Comment"]=5]="Comment";return t})(lg||{});function cg(t){return"__ln"in t}class hg{constructor(){ms(this,"length",0);ms(this,"head",null);ms(this,"tail",null)}get(t){if(t>=this.length){throw new Error("Position outside of list range")}let e=this.head;for(let i=0;i<t;i++){e=(e==null?void 0:e.next)||null}return e}addNode(t){const e={value:t,previous:null,next:null};t.__ln=e;if(t.previousSibling&&cg(t.previousSibling)){const i=t.previousSibling.__ln.next;e.next=i;e.previous=t.previousSibling.__ln;t.previousSibling.__ln.next=e;if(i){i.previous=e}}else if(t.nextSibling&&cg(t.nextSibling)&&t.nextSibling.__ln.previous){const i=t.nextSibling.__ln.previous;e.previous=i;e.next=t.nextSibling.__ln;t.nextSibling.__ln.previous=e;if(i){i.next=e}}else{if(this.head){this.head.previous=e}e.next=this.head;this.head=e}if(e.next===null){this.tail=e}this.length++}removeNode(t){const e=t.__ln;if(!this.head){return}if(!e.previous){this.head=e.next;if(this.head){this.head.previous=null}else{this.tail=null}}else{e.previous.next=e.next;if(e.next){e.next.previous=e.previous}else{this.tail=e.previous}}if(t.__ln){delete t.__ln}this.length--}}const dg=(t,e)=>`${t}@${e}`;class vg{constructor(){ms(this,"frozen",false);ms(this,"locked",false);ms(this,"texts",[]);ms(this,"attributes",[]);ms(this,"attributeMap",new WeakMap);ms(this,"removes",[]);ms(this,"mapRemoves",[]);ms(this,"movedMap",{});ms(this,"addedSet",new Set);ms(this,"movedSet",new Set);ms(this,"droppedSet",new Set);ms(this,"removesSubTreeCache",new Set);ms(this,"mutationCb");ms(this,"blockClass");ms(this,"blockSelector");ms(this,"maskTextClass");ms(this,"maskTextSelector");ms(this,"inlineStylesheet");ms(this,"maskInputOptions");ms(this,"maskTextFn");ms(this,"maskInputFn");ms(this,"keepIframeSrcFn");ms(this,"recordCanvas");ms(this,"inlineImages");ms(this,"slimDOMOptions");ms(this,"dataURLOptions");ms(this,"doc");ms(this,"mirror");ms(this,"iframeManager");ms(this,"stylesheetManager");ms(this,"shadowDomManager");ms(this,"canvasManager");ms(this,"processedNodeManager");ms(this,"unattachedDoc");ms(this,"processMutations",(t=>{t.forEach(this.processMutation);this.emit()}));ms(this,"emit",(()=>{if(this.frozen||this.locked){return}const t=[];const e=new Set;const i=new hg;const r=t=>{let e=t;let i=So;while(i===So){e=e&&e.nextSibling;i=e&&this.mirror.getId(e)}return i};const n=n=>{const s=Mm.parentNode(n);if(!s||!eg(n)){return}let o=false;if(n.nodeType===Node.TEXT_NODE){const t=s.tagName;if(t==="TEXTAREA"){return}else if(t==="STYLE"&&this.addedSet.has(s)){o=true}}const a=Hs(s)?this.mirror.getId(qm(n)):this.mirror.getId(s);const u=r(n);if(a===-1||u===-1){return i.addNode(n)}const f=Yo(n,{doc:this.doc,mirror:this.mirror,blockClass:this.blockClass,blockSelector:this.blockSelector,maskTextClass:this.maskTextClass,maskTextSelector:this.maskTextSelector,skipChild:true,newlyAddedElement:true,inlineStylesheet:this.inlineStylesheet,maskInputOptions:this.maskInputOptions,maskTextFn:this.maskTextFn,maskInputFn:this.maskInputFn,slimDOMOptions:this.slimDOMOptions,dataURLOptions:this.dataURLOptions,recordCanvas:this.recordCanvas,inlineImages:this.inlineImages,onSerialize:t=>{if(Vm(t,this.mirror)){this.iframeManager.addIframe(t)}if(Hm(t,this.mirror)){this.stylesheetManager.trackLinkElement(t)}if(Zm(n)){this.shadowDomManager.addShadowRoot(Mm.shadowRoot(n),this.doc)}},onIframeLoad:(t,e)=>{this.iframeManager.attachIframe(t,e);this.shadowDomManager.observeAttachShadow(t)},onStylesheetLoad:(t,e)=>{this.stylesheetManager.attachLinkElement(t,e)},cssCaptured:o});if(f){t.push({parentId:a,nextId:u,node:f});e.add(f.id)}};while(this.mapRemoves.length){this.mirror.removeNodeFromMap(this.mapRemoves.shift())}for(const t of this.movedSet){if(gg(this.removesSubTreeCache,t)&&!this.movedSet.has(Mm.parentNode(t))){continue}n(t)}for(const t of this.addedSet){if(!bg(this.droppedSet,t)&&!gg(this.removesSubTreeCache,t)){n(t)}else if(bg(this.movedSet,t)){n(t)}else{this.droppedSet.add(t)}}let s=null;while(i.length){let t=null;if(s){const e=this.mirror.getId(Mm.parentNode(s.value));const i=r(s.value);if(e!==-1&&i!==-1){t=s}}if(!t){let e=i.tail;while(e){const i=e;e=e.previous;if(i){const e=this.mirror.getId(Mm.parentNode(i.value));const n=r(i.value);if(n===-1)continue;else if(e!==-1){t=i;break}else{const e=i.value;const r=Mm.parentNode(e);if(r&&r.nodeType===Node.DOCUMENT_FRAGMENT_NODE){const e=Mm.host(r);const n=this.mirror.getId(e);if(n!==-1){t=i;break}}}}}}if(!t){while(i.head){i.removeNode(i.head.value)}break}s=t.previous;i.removeNode(t.value);n(t.value)}const o={texts:this.texts.map((t=>{const e=t.node;const i=Mm.parentNode(e);if(i&&i.tagName==="TEXTAREA"){this.genTextAreaValueMutation(i)}return{id:this.mirror.getId(e),value:t.value}})).filter((t=>!e.has(t.id))).filter((t=>this.mirror.has(t.id))),attributes:this.attributes.map((t=>{const{attributes:e}=t;if(typeof e.style==="string"){const i=JSON.stringify(t.styleDiff);const r=JSON.stringify(t._unchangedStyles);if(i.length<e.style.length){if((i+r).split("var(").length===e.style.split("var(").length){e.style=t.styleDiff}}}return{id:this.mirror.getId(t.node),attributes:e}})).filter((t=>!e.has(t.id))).filter((t=>this.mirror.has(t.id))),removes:this.removes,adds:t};if(!o.texts.length&&!o.attributes.length&&!o.removes.length&&!o.adds.length){return}this.texts=[];this.attributes=[];this.attributeMap=new WeakMap;this.removes=[];this.addedSet=new Set;this.movedSet=new Set;this.droppedSet=new Set;this.removesSubTreeCache=new Set;this.movedMap={};this.mutationCb(o)}));ms(this,"genTextAreaValueMutation",(t=>{let e=this.attributeMap.get(t);if(!e){e={node:t,attributes:{},styleDiff:{},_unchangedStyles:{}};this.attributes.push(e);this.attributeMap.set(t,e)}e.attributes.value=Array.from(Mm.childNodes(t),(t=>Mm.textContent(t)||"")).join("")}));ms(this,"processMutation",(t=>{if(Pm(t.target,this.mirror,this.slimDOMOptions)){return}switch(t.type){case"characterData":{const e=Mm.textContent(t.target);if(!Fm(t.target,this.blockClass,this.blockSelector,false)&&e!==t.oldValue){this.texts.push({value:Lo(t.target,this.maskTextClass,this.maskTextSelector,true)&&e?this.maskTextFn?this.maskTextFn(e,Dm(t.target)):e.replace(/[\S]/g,"*"):e,node:t.target})}break}case"attributes":{const e=t.target;let i=t.attributeName;let r=t.target.getAttribute(i);if(i==="value"){const t=ao(e);r=io({element:e,maskInputOptions:this.maskInputOptions,tagName:e.tagName,type:t,value:r,maskInputFn:this.maskInputFn})}if(Fm(t.target,this.blockClass,this.blockSelector,false)||r===t.oldValue){return}let n=this.attributeMap.get(t.target);if(e.tagName==="IFRAME"&&i==="src"&&!this.keepIframeSrcFn(r)){if(!e.contentDocument){i="rr_src"}else{return}}if(!n){n={node:t.target,attributes:{},styleDiff:{},_unchangedStyles:{}};this.attributes.push(n);this.attributeMap.set(t.target,n)}if(i==="type"&&e.tagName==="INPUT"&&(t.oldValue||"").toLowerCase()==="password"){e.setAttribute("data-rr-is-password","true")}if(!No(e.tagName,i)){n.attributes[i]=jo(this.doc,ro(e.tagName),ro(i),r);if(i==="style"){if(!this.unattachedDoc){try{this.unattachedDoc=document.implementation.createHTMLDocument()}catch(t){this.unattachedDoc=this.doc}}const i=this.unattachedDoc.createElement("span");if(t.oldValue){i.setAttribute("style",t.oldValue)}for(const t of Array.from(e.style)){const r=e.style.getPropertyValue(t);const s=e.style.getPropertyPriority(t);if(r!==i.style.getPropertyValue(t)||s!==i.style.getPropertyPriority(t)){if(s===""){n.styleDiff[t]=r}else{n.styleDiff[t]=[r,s]}}else{n._unchangedStyles[t]=[r,s]}}for(const t of Array.from(i.style)){if(e.style.getPropertyValue(t)===""){n.styleDiff[t]=false}}}else if(i==="open"&&e.tagName==="DIALOG"){if(e.matches("dialog:modal")){n.attributes["rr_open_mode"]="modal"}else{n.attributes["rr_open_mode"]="non-modal"}}}break}case"childList":{if(Fm(t.target,this.blockClass,this.blockSelector,true))return;if(t.target.tagName==="TEXTAREA"){this.genTextAreaValueMutation(t.target);return}t.addedNodes.forEach((e=>this.genAdds(e,t.target)));t.removedNodes.forEach((e=>{const i=this.mirror.getId(e);const r=Hs(t.target)?this.mirror.getId(Mm.host(t.target)):this.mirror.getId(t.target);if(Fm(t.target,this.blockClass,this.blockSelector,false)||Pm(e,this.mirror,this.slimDOMOptions)||!Lm(e,this.mirror)){return}if(this.addedSet.has(e)){pg(this.addedSet,e);this.droppedSet.add(e)}else if(this.addedSet.has(t.target)&&i===-1);else if(Bm(t.target,this.mirror));else if(this.movedSet.has(e)&&this.movedMap[dg(i,r)]){pg(this.movedSet,e)}else{this.removes.push({parentId:r,id:i,isShadow:Hs(t.target)&&Ys(t.target)?true:void 0});mg(e,this.removesSubTreeCache)}this.mapRemoves.push(e)}));break}}}));ms(this,"genAdds",((t,e)=>{if(this.processedNodeManager.inOtherBuffer(t,this))return;if(this.addedSet.has(t)||this.movedSet.has(t))return;if(this.mirror.hasNode(t)){if(Pm(t,this.mirror,this.slimDOMOptions)){return}this.movedSet.add(t);let i=null;if(e&&this.mirror.hasNode(e)){i=this.mirror.getId(e)}if(i&&i!==-1){this.movedMap[dg(this.mirror.getId(t),i)]=true}}else{this.addedSet.add(t);this.droppedSet.delete(t)}if(!Fm(t,this.blockClass,this.blockSelector,false)){Mm.childNodes(t).forEach((t=>this.genAdds(t)));if(Zm(t)){Mm.childNodes(Mm.shadowRoot(t)).forEach((e=>{this.processedNodeManager.add(e,this);this.genAdds(e,t)}))}}}))}init(t){["mutationCb","blockClass","blockSelector","maskTextClass","maskTextSelector","inlineStylesheet","maskInputOptions","maskTextFn","maskInputFn","keepIframeSrcFn","recordCanvas","inlineImages","slimDOMOptions","dataURLOptions","doc","mirror","iframeManager","stylesheetManager","shadowDomManager","canvasManager","processedNodeManager"].forEach((e=>{this[e]=t[e]}))}freeze(){this.frozen=true;this.canvasManager.freeze()}unfreeze(){this.frozen=false;this.canvasManager.unfreeze();this.emit()}isFrozen(){return this.frozen}lock(){this.locked=true;this.canvasManager.lock()}unlock(){this.locked=false;this.canvasManager.unlock();this.emit()}reset(){this.shadowDomManager.reset();this.canvasManager.reset()}}function pg(t,e){t.delete(e);Mm.childNodes(e).forEach((e=>pg(t,e)))}function mg(t,e){const i=[t];while(i.length){const t=i.pop();if(e.has(t))continue;e.add(t);Mm.childNodes(t).forEach((t=>i.push(t)))}return}function gg(t,e,i){if(t.size===0)return false;return yg(t,e)}function yg(t,e,i){const r=Mm.parentNode(e);if(!r)return false;return t.has(r)}function bg(t,e){if(t.size===0)return false;return wg(t,e)}function wg(t,e){const i=Mm.parentNode(e);if(!i){return false}if(t.has(i)){return true}return wg(t,i)}let Sg;function kg(t){Sg=t}function Cg(){Sg=void 0}const Mg=t=>{if(!Sg){return t}const e=(...e)=>{try{return t(...e)}catch(t){if(Sg&&Sg(t)===true){return}throw t}};return e};const Ig=[];function Og(t){try{if("composedPath"in t){const e=t.composedPath();if(e.length){return e[0]}}else if("path"in t&&t.path.length){return t.path[0]}}catch{}return t&&t.target}function Ag(t,e){const i=new vg;Ig.push(i);i.init(t);const r=new(Cm())(Mg(i.processMutations.bind(i)));r.observe(e,{attributes:true,attributeOldValue:true,characterData:true,characterDataOldValue:true,childList:true,subtree:true});return r}function Tg({mousemoveCb:t,sampling:e,doc:i,mirror:r}){if(e.mousemove===false){return()=>{}}const n=typeof e.mousemove==="number"?e.mousemove:50;const s=typeof e.mousemoveCallback==="number"?e.mousemoveCallback:500;let o=[];let a;const u=Tm(Mg((e=>{const i=Date.now()-a;t(o.map((t=>{t.timeOffset-=i;return t})),e);o=[];a=null})),s);const f=Mg(Tm(Mg((t=>{const e=Og(t);const{clientX:i,clientY:n}=Um(t)?t.changedTouches[0]:t;if(!a){a=Rm()}o.push({x:i,y:n,id:r.getId(e),timeOffset:Rm()-a});u(typeof DragEvent!=="undefined"&&t instanceof DragEvent?ng.Drag:t instanceof MouseEvent?ng.MouseMove:ng.TouchMove)})),n,{trailing:false}));const l=[Im("mousemove",f,i),Im("touchmove",f,i),Im("drag",f,i)];return Mg((()=>{l.forEach((t=>t()))}))}function _g({mouseInteractionCb:t,doc:e,mirror:i,blockClass:r,blockSelector:n,sampling:s}){if(s.mouseInteraction===false){return()=>{}}const o=s.mouseInteraction===true||s.mouseInteraction===void 0?{}:s.mouseInteraction;const a=[];let u=null;const f=e=>s=>{const o=Og(s);if(Fm(o,r,n,true)){return}let a=null;let f=e;if("pointerType"in s){switch(s.pointerType){case"mouse":a=og.Mouse;break;case"touch":a=og.Touch;break;case"pen":a=og.Pen;break}if(a===og.Touch){if(sg[e]===sg.MouseDown){f="TouchStart"}else if(sg[e]===sg.MouseUp){f="TouchEnd"}}}else if(Um(s)){a=og.Touch}if(a!==null){u=a;if(f.startsWith("Touch")&&a===og.Touch||f.startsWith("Mouse")&&a===og.Mouse){a=null}}else if(sg[e]===sg.Click){a=u;u=null}const l=Um(s)?s.changedTouches[0]:s;if(!l){return}const c=i.getId(o);const{clientX:h,clientY:d}=l;Mg(t)({type:sg[f],id:c,x:h,y:d,...a!==null&&{pointerType:a}})};Object.keys(sg).filter((t=>Number.isNaN(Number(t))&&!t.endsWith("_Departed")&&o[t]!==false)).forEach((t=>{let i=ro(t);const r=f(t);if(window.PointerEvent){switch(sg[t]){case sg.MouseDown:case sg.MouseUp:i=i.replace("mouse","pointer");break;case sg.TouchStart:case sg.TouchEnd:return}}a.push(Im(i,r,e))}));return Mg((()=>{a.forEach((t=>t()))}))}function Eg({scrollCb:t,doc:e,mirror:i,blockClass:r,blockSelector:n,sampling:s}){const o=Mg(Tm(Mg((s=>{const o=Og(s);if(!o||Fm(o,r,n,true)){return}const a=i.getId(o);if(o===e&&e.defaultView){const i=xm(e.defaultView);t({id:a,x:i.left,y:i.top})}else{t({id:a,x:o.scrollLeft,y:o.scrollTop})}})),s.scroll||100));return Im("scroll",o,e)}function Rg({viewportResizeCb:t},{win:e}){let i=-1;let r=-1;const n=Mg(Tm(Mg((()=>{const e=jm();const n=Nm();if(i!==e||r!==n){t({width:Number(n),height:Number(e)});i=e;r=n}})),200));return Im("resize",n,e)}const xg=["INPUT","TEXTAREA","SELECT"];const jg=new WeakMap;function Ng({inputCb:t,doc:e,mirror:i,blockClass:r,blockSelector:n,ignoreClass:s,ignoreSelector:o,maskInputOptions:a,maskInputFn:u,sampling:f,userTriggeredOnInput:l}){function c(t){let i=Og(t);const f=t.isTrusted;const c=i&&i.tagName;if(i&&c==="OPTION"){i=Mm.parentElement(i)}if(!i||!c||xg.indexOf(c)<0||Fm(i,r,n,true)){return}if(i.classList.contains(s)||o&&i.matches(o)){return}let d=i.value;let v=false;const p=ao(i)||"";if(p==="radio"||p==="checkbox"){v=i.checked}else if(a[c.toLowerCase()]||a[p]){d=io({element:i,maskInputOptions:a,tagName:c,type:p,value:d,maskInputFn:u})}h(i,l?{text:d,isChecked:v,userTriggered:f}:{text:d,isChecked:v});const m=i.name;if(p==="radio"&&m&&v){e.querySelectorAll(`input[type="radio"][name="${m}"]`).forEach((t=>{if(t!==i){const e=t.value;h(t,l?{text:e,isChecked:!v,userTriggered:false}:{text:e,isChecked:!v})}}))}}function h(e,r){const n=jg.get(e);if(!n||n.text!==r.text||n.isChecked!==r.isChecked){jg.set(e,r);const n=i.getId(e);Mg(t)({...r,id:n})}}const d=f.input==="last"?["change"]:["input","change"];const v=d.map((t=>Im(t,Mg(c),e)));const p=e.defaultView;if(!p){return()=>{v.forEach((t=>t()))}}const m=p.Object.getOwnPropertyDescriptor(p.HTMLInputElement.prototype,"value");const g=[[p.HTMLInputElement.prototype,"value"],[p.HTMLInputElement.prototype,"checked"],[p.HTMLSelectElement.prototype,"value"],[p.HTMLTextAreaElement.prototype,"value"],[p.HTMLSelectElement.prototype,"selectedIndex"],[p.HTMLOptionElement.prototype,"selected"]];if(m&&m.set){v.push(...g.map((t=>_m(t[0],t[1],{set(){Mg(c)({target:this,isTrusted:false})}},false,p))))}return Mg((()=>{v.forEach((t=>t()))}))}function Dg(t){const e=[];function i(t,e){if(Yg("CSSGroupingRule")&&t.parentRule instanceof CSSGroupingRule||Yg("CSSMediaRule")&&t.parentRule instanceof CSSMediaRule||Yg("CSSSupportsRule")&&t.parentRule instanceof CSSSupportsRule||Yg("CSSConditionRule")&&t.parentRule instanceof CSSConditionRule){const i=Array.from(t.parentRule.cssRules);const r=i.indexOf(t);e.unshift(r)}else if(t.parentStyleSheet){const i=Array.from(t.parentStyleSheet.cssRules);const r=i.indexOf(t);e.unshift(r)}return e}return i(t,e)}function Fg(t,e,i){let r,n;if(!t)return{};if(t.ownerNode)r=e.getId(t.ownerNode);else n=i.getId(t);return{styleId:n,id:r}}function Lg({styleSheetRuleCb:t,mirror:e,stylesheetManager:i},{win:r}){if(!r.CSSStyleSheet||!r.CSSStyleSheet.prototype){return()=>{}}const n=r.CSSStyleSheet.prototype.insertRule;r.CSSStyleSheet.prototype.insertRule=new Proxy(n,{apply:Mg(((r,n,s)=>{const[o,a]=s;const{id:u,styleId:f}=Fg(n,e,i.styleMirror);if(u&&u!==-1||f&&f!==-1){t({id:u,styleId:f,adds:[{rule:o,index:a}]})}return r.apply(n,s)}))});r.CSSStyleSheet.prototype.addRule=function(t,e,i=this.cssRules.length){const n=`${t} { ${e} }`;return r.CSSStyleSheet.prototype.insertRule.apply(this,[n,i])};const s=r.CSSStyleSheet.prototype.deleteRule;r.CSSStyleSheet.prototype.deleteRule=new Proxy(s,{apply:Mg(((r,n,s)=>{const[o]=s;const{id:a,styleId:u}=Fg(n,e,i.styleMirror);if(a&&a!==-1||u&&u!==-1){t({id:a,styleId:u,removes:[{index:o}]})}return r.apply(n,s)}))});r.CSSStyleSheet.prototype.removeRule=function(t){return r.CSSStyleSheet.prototype.deleteRule.apply(this,[t])};let o;if(r.CSSStyleSheet.prototype.replace){o=r.CSSStyleSheet.prototype.replace;r.CSSStyleSheet.prototype.replace=new Proxy(o,{apply:Mg(((r,n,s)=>{const[o]=s;const{id:a,styleId:u}=Fg(n,e,i.styleMirror);if(a&&a!==-1||u&&u!==-1){t({id:a,styleId:u,replace:o})}return r.apply(n,s)}))})}let a;if(r.CSSStyleSheet.prototype.replaceSync){a=r.CSSStyleSheet.prototype.replaceSync;r.CSSStyleSheet.prototype.replaceSync=new Proxy(a,{apply:Mg(((r,n,s)=>{const[o]=s;const{id:a,styleId:u}=Fg(n,e,i.styleMirror);if(a&&a!==-1||u&&u!==-1){t({id:a,styleId:u,replaceSync:o})}return r.apply(n,s)}))})}const u={};if(Zg("CSSGroupingRule")){u.CSSGroupingRule=r.CSSGroupingRule}else{if(Zg("CSSMediaRule")){u.CSSMediaRule=r.CSSMediaRule}if(Zg("CSSConditionRule")){u.CSSConditionRule=r.CSSConditionRule}if(Zg("CSSSupportsRule")){u.CSSSupportsRule=r.CSSSupportsRule}}const f={};Object.entries(u).forEach((([r,n])=>{f[r]={insertRule:n.prototype.insertRule,deleteRule:n.prototype.deleteRule};n.prototype.insertRule=new Proxy(f[r].insertRule,{apply:Mg(((r,n,s)=>{const[o,a]=s;const{id:u,styleId:f}=Fg(n.parentStyleSheet,e,i.styleMirror);if(u&&u!==-1||f&&f!==-1){t({id:u,styleId:f,adds:[{rule:o,index:[...Dg(n),a||0]}]})}return r.apply(n,s)}))});n.prototype.deleteRule=new Proxy(f[r].deleteRule,{apply:Mg(((r,n,s)=>{const[o]=s;const{id:a,styleId:u}=Fg(n.parentStyleSheet,e,i.styleMirror);if(a&&a!==-1||u&&u!==-1){t({id:a,styleId:u,removes:[{index:[...Dg(n),o]}]})}return r.apply(n,s)}))})}));return Mg((()=>{r.CSSStyleSheet.prototype.insertRule=n;r.CSSStyleSheet.prototype.deleteRule=s;o&&(r.CSSStyleSheet.prototype.replace=o);a&&(r.CSSStyleSheet.prototype.replaceSync=a);Object.entries(u).forEach((([t,e])=>{e.prototype.insertRule=f[t].insertRule;e.prototype.deleteRule=f[t].deleteRule}))}))}function Pg({mirror:t,stylesheetManager:e},i){var r,n,s;let o=null;if(i.nodeName==="#document")o=t.getId(i);else o=t.getId(Mm.host(i));const a=i.nodeName==="#document"?(r=i.defaultView)==null?void 0:r.Document:(s=(n=i.ownerDocument)==null?void 0:n.defaultView)==null?void 0:s.ShadowRoot;const u=(a==null?void 0:a.prototype)?Object.getOwnPropertyDescriptor(a==null?void 0:a.prototype,"adoptedStyleSheets"):void 0;if(o===null||o===-1||!a||!u)return()=>{};Object.defineProperty(i,"adoptedStyleSheets",{configurable:u.configurable,enumerable:u.enumerable,get(){var t;return(t=u.get)==null?void 0:t.call(this)},set(t){var i;const r=(i=u.set)==null?void 0:i.call(this,t);if(o!==null&&o!==-1){try{e.adoptStyleSheets(t,o)}catch(t){}}return r}});return Mg((()=>{Object.defineProperty(i,"adoptedStyleSheets",{configurable:u.configurable,enumerable:u.enumerable,get:u.get,set:u.set})}))}function Bg({styleDeclarationCb:t,mirror:e,ignoreCSSAttributes:i,stylesheetManager:r},{win:n}){const s=n.CSSStyleDeclaration.prototype.setProperty;n.CSSStyleDeclaration.prototype.setProperty=new Proxy(s,{apply:Mg(((n,o,a)=>{var u;const[f,l,c]=a;if(i.has(f)){return s.apply(o,[f,l,c])}const{id:h,styleId:d}=Fg((u=o.parentRule)==null?void 0:u.parentStyleSheet,e,r.styleMirror);if(h&&h!==-1||d&&d!==-1){t({id:h,styleId:d,set:{property:f,value:l,priority:c},index:Dg(o.parentRule)})}return n.apply(o,a)}))});const o=n.CSSStyleDeclaration.prototype.removeProperty;n.CSSStyleDeclaration.prototype.removeProperty=new Proxy(o,{apply:Mg(((n,s,a)=>{var u;const[f]=a;if(i.has(f)){return o.apply(s,[f])}const{id:l,styleId:c}=Fg((u=s.parentRule)==null?void 0:u.parentStyleSheet,e,r.styleMirror);if(l&&l!==-1||c&&c!==-1){t({id:l,styleId:c,remove:{property:f},index:Dg(s.parentRule)})}return n.apply(s,a)}))});return Mg((()=>{n.CSSStyleDeclaration.prototype.setProperty=s;n.CSSStyleDeclaration.prototype.removeProperty=o}))}function Ug({mediaInteractionCb:t,blockClass:e,blockSelector:i,mirror:r,sampling:n,doc:s}){const o=Mg((s=>Tm(Mg((n=>{const o=Og(n);if(!o||Fm(o,e,i,true)){return}const{currentTime:a,volume:u,muted:f,playbackRate:l,loop:c}=o;t({type:s,id:r.getId(o),currentTime:a,volume:u,muted:f,playbackRate:l,loop:c})})),n.media||500)));const a=[Im("play",o(ug.Play),s),Im("pause",o(ug.Pause),s),Im("seeked",o(ug.Seeked),s),Im("volumechange",o(ug.VolumeChange),s),Im("ratechange",o(ug.RateChange),s)];return Mg((()=>{a.forEach((t=>t()))}))}function Gg({fontCb:t,doc:e}){const i=e.defaultView;if(!i){return()=>{}}const r=[];const n=new WeakMap;const s=i.FontFace;i.FontFace=function t(e,i,r){const o=new s(e,i,r);n.set(o,{family:e,buffer:typeof i!=="string",descriptors:r,fontSource:typeof i==="string"?i:JSON.stringify(Array.from(new Uint8Array(i)))});return o};const o=Em(e.fonts,"add",(function(e){return function(i){setTimeout(Mg((()=>{const e=n.get(i);if(e){t(e);n.delete(i)}})),0);return e.apply(this,[i])}}));r.push((()=>{i.FontFace=s}));r.push(o);return Mg((()=>{r.forEach((t=>t()))}))}function Wg(t){const{doc:e,mirror:i,blockClass:r,blockSelector:n,selectionCb:s}=t;let o=true;const a=Mg((()=>{const t=e.getSelection();if(!t||o&&(t==null?void 0:t.isCollapsed))return;o=t.isCollapsed||false;const a=[];const u=t.rangeCount||0;for(let e=0;e<u;e++){const s=t.getRangeAt(e);const{startContainer:o,startOffset:u,endContainer:f,endOffset:l}=s;const c=Fm(o,r,n,true)||Fm(f,r,n,true);if(c)continue;a.push({start:i.getId(o),startOffset:u,end:i.getId(f),endOffset:l})}s({ranges:a})}));a();return Im("selectionchange",a)}function zg({doc:t,customElementCb:e}){const i=t.defaultView;if(!i||!i.customElements)return()=>{};const r=Em(i.customElements,"define",(function(t){return function(i,r,n){try{e({define:{name:i}})}catch(t){console.warn(`Custom element callback failed for ${i}`)}return t.apply(this,[i,r,n])}}));return r}function Vg(t,e){const{mutationCb:i,mousemoveCb:r,mouseInteractionCb:n,scrollCb:s,viewportResizeCb:o,inputCb:a,mediaInteractionCb:u,styleSheetRuleCb:f,styleDeclarationCb:l,canvasMutationCb:c,fontCb:h,selectionCb:d,customElementCb:v}=t;t.mutationCb=(...t)=>{if(e.mutation){e.mutation(...t)}i(...t)};t.mousemoveCb=(...t)=>{if(e.mousemove){e.mousemove(...t)}r(...t)};t.mouseInteractionCb=(...t)=>{if(e.mouseInteraction){e.mouseInteraction(...t)}n(...t)};t.scrollCb=(...t)=>{if(e.scroll){e.scroll(...t)}s(...t)};t.viewportResizeCb=(...t)=>{if(e.viewportResize){e.viewportResize(...t)}o(...t)};t.inputCb=(...t)=>{if(e.input){e.input(...t)}a(...t)};t.mediaInteractionCb=(...t)=>{if(e.mediaInteaction){e.mediaInteaction(...t)}u(...t)};t.styleSheetRuleCb=(...t)=>{if(e.styleSheetRule){e.styleSheetRule(...t)}f(...t)};t.styleDeclarationCb=(...t)=>{if(e.styleDeclaration){e.styleDeclaration(...t)}l(...t)};t.canvasMutationCb=(...t)=>{if(e.canvasMutation){e.canvasMutation(...t)}c(...t)};t.fontCb=(...t)=>{if(e.font){e.font(...t)}h(...t)};t.selectionCb=(...t)=>{if(e.selection){e.selection(...t)}d(...t)};t.customElementCb=(...t)=>{if(e.customElement){e.customElement(...t)}v(...t)}}function Hg(t,e={}){const i=t.doc.defaultView;if(!i){return()=>{}}Vg(t,e);let r;if(t.recordDOM){r=Ag(t,t.doc)}const n=Tg(t);const s=_g(t);const o=Eg(t);const a=Rg(t,{win:i});const u=Ng(t);const f=Ug(t);let l=()=>{};let c=()=>{};let h=()=>{};let d=()=>{};if(t.recordDOM){l=Lg(t,{win:i});c=Pg(t,t.doc);h=Bg(t,{win:i});if(t.collectFonts){d=Gg(t)}}const v=Wg(t);const p=zg(t);const m=[];for(const e of t.plugins){m.push(e.observer(e.callback,i,e.options))}return Mg((()=>{Ig.forEach((t=>t.reset()));r==null?void 0:r.disconnect();n();s();o();a();u();f();l();c();h();d();v();p();m.forEach((t=>t()))}))}function Yg(t){return typeof window[t]!=="undefined"}function Zg(t){return Boolean(typeof window[t]!=="undefined"&&window[t].prototype&&"insertRule"in window[t].prototype&&"deleteRule"in window[t].prototype)}class Xg{constructor(t){ms(this,"iframeIdToRemoteIdMap",new WeakMap);ms(this,"iframeRemoteIdToIdMap",new WeakMap);this.generateIdFn=t}getId(t,e,i,r){const n=i||this.getIdToRemoteIdMap(t);const s=r||this.getRemoteIdToIdMap(t);let o=n.get(e);if(!o){o=this.generateIdFn();n.set(e,o);s.set(o,e)}return o}getIds(t,e){const i=this.getIdToRemoteIdMap(t);const r=this.getRemoteIdToIdMap(t);return e.map((e=>this.getId(t,e,i,r)))}getRemoteId(t,e,i){const r=i||this.getRemoteIdToIdMap(t);if(typeof e!=="number")return e;const n=r.get(e);if(!n)return-1;return n}getRemoteIds(t,e){const i=this.getRemoteIdToIdMap(t);return e.map((e=>this.getRemoteId(t,e,i)))}reset(t){if(!t){this.iframeIdToRemoteIdMap=new WeakMap;this.iframeRemoteIdToIdMap=new WeakMap;return}this.iframeIdToRemoteIdMap.delete(t);this.iframeRemoteIdToIdMap.delete(t)}getIdToRemoteIdMap(t){let e=this.iframeIdToRemoteIdMap.get(t);if(!e){e=new Map;this.iframeIdToRemoteIdMap.set(t,e)}return e}getRemoteIdToIdMap(t){let e=this.iframeRemoteIdToIdMap.get(t);if(!e){e=new Map;this.iframeRemoteIdToIdMap.set(t,e)}return e}}class $g{constructor(t){ms(this,"iframes",new WeakMap);ms(this,"crossOriginIframeMap",new WeakMap);ms(this,"crossOriginIframeMirror",new Xg(ko));ms(this,"crossOriginIframeStyleMirror");ms(this,"crossOriginIframeRootIdMap",new WeakMap);ms(this,"mirror");ms(this,"mutationCb");ms(this,"wrappedEmit");ms(this,"loadListener");ms(this,"stylesheetManager");ms(this,"recordCrossOriginIframes");this.mutationCb=t.mutationCb;this.wrappedEmit=t.wrappedEmit;this.stylesheetManager=t.stylesheetManager;this.recordCrossOriginIframes=t.recordCrossOriginIframes;this.crossOriginIframeStyleMirror=new Xg(this.stylesheetManager.styleMirror.generateId.bind(this.stylesheetManager.styleMirror));this.mirror=t.mirror;if(this.recordCrossOriginIframes){window.addEventListener("message",this.handleMessage.bind(this))}}addIframe(t){this.iframes.set(t,true);if(t.contentWindow)this.crossOriginIframeMap.set(t.contentWindow,t)}addLoadListener(t){this.loadListener=t}attachIframe(t,e){var i,r;this.mutationCb({adds:[{parentId:this.mirror.getId(t),nextId:null,node:e}],removes:[],texts:[],attributes:[],isAttachIframe:true});if(this.recordCrossOriginIframes)(i=t.contentWindow)==null?void 0:i.addEventListener("message",this.handleMessage.bind(this));(r=this.loadListener)==null?void 0:r.call(this,t);if(t.contentDocument&&t.contentDocument.adoptedStyleSheets&&t.contentDocument.adoptedStyleSheets.length>0)this.stylesheetManager.adoptStyleSheets(t.contentDocument.adoptedStyleSheets,this.mirror.getId(t.contentDocument))}handleMessage(t){const e=t;if(e.data.type!=="rrweb"||e.origin!==e.data.origin)return;const i=t.source;if(!i)return;const r=this.crossOriginIframeMap.get(t.source);if(!r)return;const n=this.transformCrossOriginEvent(r,e.data.event);if(n)this.wrappedEmit(n,e.data.isCheckout)}transformCrossOriginEvent(t,e){var i;switch(e.type){case rg.FullSnapshot:{this.crossOriginIframeMirror.reset(t);this.crossOriginIframeStyleMirror.reset(t);this.replaceIdOnNode(e.data.node,t);const i=e.data.node.id;this.crossOriginIframeRootIdMap.set(t,i);this.patchRootIdOnNode(e.data.node,i);return{timestamp:e.timestamp,type:rg.IncrementalSnapshot,data:{source:ng.Mutation,adds:[{parentId:this.mirror.getId(t),nextId:null,node:e.data.node}],removes:[],texts:[],attributes:[],isAttachIframe:true}}}case rg.Meta:case rg.Load:case rg.DomContentLoaded:{return false}case rg.Plugin:{return e}case rg.Custom:{this.replaceIds(e.data.payload,t,["id","parentId","previousId","nextId"]);return e}case rg.IncrementalSnapshot:{switch(e.data.source){case ng.Mutation:{e.data.adds.forEach((e=>{this.replaceIds(e,t,["parentId","nextId","previousId"]);this.replaceIdOnNode(e.node,t);const i=this.crossOriginIframeRootIdMap.get(t);i&&this.patchRootIdOnNode(e.node,i)}));e.data.removes.forEach((e=>{this.replaceIds(e,t,["parentId","id"])}));e.data.attributes.forEach((e=>{this.replaceIds(e,t,["id"])}));e.data.texts.forEach((e=>{this.replaceIds(e,t,["id"])}));return e}case ng.Drag:case ng.TouchMove:case ng.MouseMove:{e.data.positions.forEach((e=>{this.replaceIds(e,t,["id"])}));return e}case ng.ViewportResize:{return false}case ng.MediaInteraction:case ng.MouseInteraction:case ng.Scroll:case ng.CanvasMutation:case ng.Input:{this.replaceIds(e.data,t,["id"]);return e}case ng.StyleSheetRule:case ng.StyleDeclaration:{this.replaceIds(e.data,t,["id"]);this.replaceStyleIds(e.data,t,["styleId"]);return e}case ng.Font:{return e}case ng.Selection:{e.data.ranges.forEach((e=>{this.replaceIds(e,t,["start","end"])}));return e}case ng.AdoptedStyleSheet:{this.replaceIds(e.data,t,["id"]);this.replaceStyleIds(e.data,t,["styleIds"]);(i=e.data.styles)==null?void 0:i.forEach((e=>{this.replaceStyleIds(e,t,["styleId"])}));return e}}}}return false}replace(t,e,i,r){for(const n of r){if(!Array.isArray(e[n])&&typeof e[n]!=="number")continue;if(Array.isArray(e[n])){e[n]=t.getIds(i,e[n])}else{e[n]=t.getId(i,e[n])}}return e}replaceIds(t,e,i){return this.replace(this.crossOriginIframeMirror,t,e,i)}replaceStyleIds(t,e,i){return this.replace(this.crossOriginIframeStyleMirror,t,e,i)}replaceIdOnNode(t,e){this.replaceIds(t,e,["id","rootId"]);if("childNodes"in t){t.childNodes.forEach((t=>{this.replaceIdOnNode(t,e)}))}}patchRootIdOnNode(t,e){if(t.type!==lg.Document&&!t.rootId)t.rootId=e;if("childNodes"in t){t.childNodes.forEach((t=>{this.patchRootIdOnNode(t,e)}))}}}class Jg{constructor(t){ms(this,"shadowDoms",new WeakSet);ms(this,"mutationCb");ms(this,"scrollCb");ms(this,"bypassOptions");ms(this,"mirror");ms(this,"restoreHandlers",[]);this.mutationCb=t.mutationCb;this.scrollCb=t.scrollCb;this.bypassOptions=t.bypassOptions;this.mirror=t.mirror;this.init()}init(){this.reset();this.patchAttachShadow(Element,document)}addShadowRoot(t,e){if(!Ys(t))return;if(this.shadowDoms.has(t))return;this.shadowDoms.add(t);const i=Ag({...this.bypassOptions,doc:e,mutationCb:this.mutationCb,mirror:this.mirror,shadowDomManager:this},t);this.restoreHandlers.push((()=>i.disconnect()));this.restoreHandlers.push(Eg({...this.bypassOptions,scrollCb:this.scrollCb,doc:t,mirror:this.mirror}));setTimeout((()=>{if(t.adoptedStyleSheets&&t.adoptedStyleSheets.length>0)this.bypassOptions.stylesheetManager.adoptStyleSheets(t.adoptedStyleSheets,this.mirror.getId(Mm.host(t)));this.restoreHandlers.push(Pg({mirror:this.mirror,stylesheetManager:this.bypassOptions.stylesheetManager},t))}),0)}observeAttachShadow(t){if(!t.contentWindow||!t.contentDocument)return;this.patchAttachShadow(t.contentWindow.Element,t.contentDocument)}patchAttachShadow(t,e){const i=this;this.restoreHandlers.push(Em(t.prototype,"attachShadow",(function(t){return function(r){const n=t.call(this,r);const s=Mm.shadowRoot(this);if(s&&eg(this))i.addShadowRoot(s,e);return n}})))}reset(){this.restoreHandlers.forEach((t=>{try{t()}catch(t){}}));this.restoreHandlers=[];this.shadowDoms=new WeakSet}}var Kg="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";var qg=typeof Uint8Array==="undefined"?[]:new Uint8Array(256);for(var Qg=0;Qg<Kg.length;Qg++){qg[Kg.charCodeAt(Qg)]=Qg}var ty=function(t){var e=new Uint8Array(t),i,r=e.length,n="";for(i=0;i<r;i+=3){n+=Kg[e[i]>>2];n+=Kg[(e[i]&3)<<4|e[i+1]>>4];n+=Kg[(e[i+1]&15)<<2|e[i+2]>>6];n+=Kg[e[i+2]&63]}if(r%3===2){n=n.substring(0,n.length-1)+"="}else if(r%3===1){n=n.substring(0,n.length-2)+"=="}return n};var ey=function(t){var e=t.length*.75,i=t.length,r,n=0,s,o,a,u;if(t[t.length-1]==="="){e--;if(t[t.length-2]==="="){e--}}var f=new ArrayBuffer(e),l=new Uint8Array(f);for(r=0;r<i;r+=4){s=qg[t.charCodeAt(r)];o=qg[t.charCodeAt(r+1)];a=qg[t.charCodeAt(r+2)];u=qg[t.charCodeAt(r+3)];l[n++]=s<<2|o>>4;l[n++]=(o&15)<<4|a>>2;l[n++]=(a&3)<<6|u&63}return f};const iy=new Map;function ry(t,e){let i=iy.get(t);if(!i){i=new Map;iy.set(t,i)}if(!i.has(e)){i.set(e,[])}return i.get(e)}const ny=(t,e,i)=>{if(!t||!(ay(t,e)||typeof t==="object"))return;const r=t.constructor.name;const n=ry(i,r);let s=n.indexOf(t);if(s===-1){s=n.length;n.push(t)}return s};function sy(t,e,i){if(t instanceof Array){return t.map((t=>sy(t,e,i)))}else if(t===null){return t}else if(t instanceof Float32Array||t instanceof Float64Array||t instanceof Int32Array||t instanceof Uint32Array||t instanceof Uint8Array||t instanceof Uint16Array||t instanceof Int16Array||t instanceof Int8Array||t instanceof Uint8ClampedArray){const e=t.constructor.name;return{rr_type:e,args:[Object.values(t)]}}else if(t instanceof ArrayBuffer){const e=t.constructor.name;const i=ty(t);return{rr_type:e,base64:i}}else if(t instanceof DataView){const r=t.constructor.name;return{rr_type:r,args:[sy(t.buffer,e,i),t.byteOffset,t.byteLength]}}else if(t instanceof HTMLImageElement){const e=t.constructor.name;const{src:i}=t;return{rr_type:e,src:i}}else if(t instanceof HTMLCanvasElement){const e="HTMLImageElement";const i=t.toDataURL();return{rr_type:e,src:i}}else if(t instanceof ImageData){const r=t.constructor.name;return{rr_type:r,args:[sy(t.data,e,i),t.width,t.height]}}else if(ay(t,e)||typeof t==="object"){const r=t.constructor.name;const n=ny(t,e,i);return{rr_type:r,index:n}}return t}const oy=(t,e,i)=>t.map((t=>sy(t,e,i)));const ay=(t,e)=>{const i=["WebGLActiveInfo","WebGLBuffer","WebGLFramebuffer","WebGLProgram","WebGLRenderbuffer","WebGLShader","WebGLShaderPrecisionFormat","WebGLTexture","WebGLUniformLocation","WebGLVertexArrayObject","WebGLVertexArrayObjectOES"];const r=i.filter((t=>typeof e[t]==="function"));return Boolean(r.find((i=>t instanceof e[i])))};function uy(t,e,i,r){const n=[];const s=Object.getOwnPropertyNames(e.CanvasRenderingContext2D.prototype);for(const o of s){try{if(typeof e.CanvasRenderingContext2D.prototype[o]!=="function"){continue}const s=Em(e.CanvasRenderingContext2D.prototype,o,(function(n){return function(...s){if(!Fm(this.canvas,i,r,true)){setTimeout((()=>{const i=oy(s,e,this);t(this.canvas,{type:ag["2D"],property:o,args:i})}),0)}return n.apply(this,s)}}));n.push(s)}catch{const i=_m(e.CanvasRenderingContext2D.prototype,o,{set(e){t(this.canvas,{type:ag["2D"],property:o,args:[e],setter:true})}});n.push(i)}}return()=>{n.forEach((t=>t()))}}function fy(t){return t==="experimental-webgl"?"webgl":t}function ly(t,e,i,r){const n=[];try{const s=Em(t.HTMLCanvasElement.prototype,"getContext",(function(t){return function(n,...s){if(!Fm(this,e,i,true)){const t=fy(n);if(!("__context"in this))this.__context=t;if(r&&["webgl","webgl2"].includes(t)){if(s[0]&&typeof s[0]==="object"){const t=s[0];if(!t.preserveDrawingBuffer){t.preserveDrawingBuffer=true}}else{s.splice(0,1,{preserveDrawingBuffer:true})}}}return t.apply(this,[n,...s])}}));n.push(s)}catch{console.error("failed to patch HTMLCanvasElement.prototype.getContext")}return()=>{n.forEach((t=>t()))}}function cy(t,e,i,r,n,s){const o=[];const a=Object.getOwnPropertyNames(t);for(const u of a){if(["isContextLost","canvas","drawingBufferWidth","drawingBufferHeight"].includes(u)){continue}try{if(typeof t[u]!=="function"){continue}const a=Em(t,u,(function(t){return function(...o){const a=t.apply(this,o);ny(a,s,this);if("tagName"in this.canvas&&!Fm(this.canvas,r,n,true)){const t=oy(o,s,this);const r={type:e,property:u,args:t};i(this.canvas,r)}return a}}));o.push(a)}catch{const r=_m(t,u,{set(t){i(this.canvas,{type:e,property:u,args:[t],setter:true})}});o.push(r)}}return o}function hy(t,e,i,r){const n=[];n.push(...cy(e.WebGLRenderingContext.prototype,ag.WebGL,t,i,r,e));if(typeof e.WebGL2RenderingContext!=="undefined"){n.push(...cy(e.WebGL2RenderingContext.prototype,ag.WebGL2,t,i,r,e))}return()=>{n.forEach((t=>t()))}}const dy="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";const vy=t=>Uint8Array.from(atob(t),(t=>t.charCodeAt(0)));const py=typeof window!=="undefined"&&window.Blob&&new Blob([vy(dy)],{type:"text/javascript;charset=utf-8"});function my(t){let e;try{e=py&&(window.URL||window.webkitURL).createObjectURL(py);if(!e)throw"";const i=new Worker(e,{name:t==null?void 0:t.name});i.addEventListener("error",(()=>{(window.URL||window.webkitURL).revokeObjectURL(e)}));return i}catch(e){return new Worker("data:text/javascript;base64,"+dy,{name:t==null?void 0:t.name})}finally{e&&(window.URL||window.webkitURL).revokeObjectURL(e)}}class gy{constructor(t){ms(this,"pendingCanvasMutations",new Map);ms(this,"rafStamps",{latestId:0,invokeId:null});ms(this,"mirror");ms(this,"mutationCb");ms(this,"resetObservers");ms(this,"frozen",false);ms(this,"locked",false);ms(this,"processMutation",((t,e)=>{const i=this.rafStamps.invokeId&&this.rafStamps.latestId!==this.rafStamps.invokeId;if(i||!this.rafStamps.invokeId)this.rafStamps.invokeId=this.rafStamps.latestId;if(!this.pendingCanvasMutations.has(t)){this.pendingCanvasMutations.set(t,[])}this.pendingCanvasMutations.get(t).push(e)}));const{sampling:e="all",win:i,blockClass:r,blockSelector:n,recordCanvas:s,dataURLOptions:o}=t;this.mutationCb=t.mutationCb;this.mirror=t.mirror;if(s&&e==="all")this.initCanvasMutationObserver(i,r,n);if(s&&typeof e==="number")this.initCanvasFPSObserver(e,i,r,n,{dataURLOptions:o})}reset(){this.pendingCanvasMutations.clear();this.resetObservers&&this.resetObservers()}freeze(){this.frozen=true}unfreeze(){this.frozen=false}lock(){this.locked=true}unlock(){this.locked=false}initCanvasFPSObserver(t,e,i,r,n){const s=ly(e,i,r,true);const o=new Map;const a=new my;a.onmessage=t=>{const{id:e}=t.data;o.set(e,false);if(!("base64"in t.data))return;const{base64:i,type:r,width:n,height:s}=t.data;this.mutationCb({id:e,type:ag["2D"],commands:[{property:"clearRect",args:[0,0,n,s]},{property:"drawImage",args:[{rr_type:"ImageBitmap",args:[{rr_type:"Blob",data:[{rr_type:"ArrayBuffer",base64:i}],type:r}]},0,0]}]})};const u=1e3/t;let f=0;let l;const c=()=>{const t=[];e.document.querySelectorAll("canvas").forEach((e=>{if(!Fm(e,i,r,true)){t.push(e)}}));return t};const h=t=>{if(f&&t-f<u){l=requestAnimationFrame(h);return}f=t;c().forEach((async t=>{var e;const i=this.mirror.getId(t);if(o.get(i))return;if(t.width===0||t.height===0)return;o.set(i,true);if(["webgl","webgl2"].includes(t.__context)){const i=t.getContext(t.__context);if(((e=i==null?void 0:i.getContextAttributes())==null?void 0:e.preserveDrawingBuffer)===false){i.clear(i.COLOR_BUFFER_BIT)}}const r=await createImageBitmap(t);a.postMessage({id:i,bitmap:r,width:t.width,height:t.height,dataURLOptions:n.dataURLOptions},[r])}));l=requestAnimationFrame(h)};l=requestAnimationFrame(h);this.resetObservers=()=>{s();cancelAnimationFrame(l)}}initCanvasMutationObserver(t,e,i){this.startRAFTimestamping();this.startPendingCanvasMutationFlusher();const r=ly(t,e,i,false);const n=uy(this.processMutation.bind(this),t,e,i);const s=hy(this.processMutation.bind(this),t,e,i);this.resetObservers=()=>{r();n();s()}}startPendingCanvasMutationFlusher(){requestAnimationFrame((()=>this.flushPendingCanvasMutations()))}startRAFTimestamping(){const t=e=>{this.rafStamps.latestId=e;requestAnimationFrame(t)};requestAnimationFrame(t)}flushPendingCanvasMutations(){this.pendingCanvasMutations.forEach(((t,e)=>{const i=this.mirror.getId(e);this.flushPendingCanvasMutationFor(e,i)}));requestAnimationFrame((()=>this.flushPendingCanvasMutations()))}flushPendingCanvasMutationFor(t,e){if(this.frozen||this.locked){return}const i=this.pendingCanvasMutations.get(t);if(!i||e===-1)return;const r=i.map((t=>{const{type:e,...i}=t;return i}));const{type:n}=i[0];this.mutationCb({id:e,type:n,commands:r});this.pendingCanvasMutations.delete(t)}}class yy{constructor(t){ms(this,"trackedLinkElements",new WeakSet);ms(this,"mutationCb");ms(this,"adoptedStyleSheetCb");ms(this,"styleMirror",new Km);this.mutationCb=t.mutationCb;this.adoptedStyleSheetCb=t.adoptedStyleSheetCb}attachLinkElement(t,e){if("_cssText"in e.attributes)this.mutationCb({adds:[],removes:[],texts:[],attributes:[{id:e.id,attributes:e.attributes}]});this.trackLinkElement(t)}trackLinkElement(t){if(this.trackedLinkElements.has(t))return;this.trackedLinkElements.add(t);this.trackStylesheetInLinkElement(t)}adoptStyleSheets(t,e){if(t.length===0)return;const i={id:e,styleIds:[]};const r=[];for(const e of t){let t;if(!this.styleMirror.has(e)){t=this.styleMirror.add(e);r.push({styleId:t,rules:Array.from(e.rules||CSSRule,((t,i)=>({rule:Js(t,e.href),index:i})))})}else t=this.styleMirror.getId(e);i.styleIds.push(t)}if(r.length>0)i.styles=r;this.adoptedStyleSheetCb(i)}reset(){this.styleMirror.reset();this.trackedLinkElements=new WeakSet}trackStylesheetInLinkElement(t){}}class by{constructor(){ms(this,"nodeMap",new WeakMap);ms(this,"active",false)}inOtherBuffer(t,e){const i=this.nodeMap.get(t);return i&&Array.from(i).some((t=>t!==e))}add(t,e){if(!this.active){this.active=true;requestAnimationFrame((()=>{this.nodeMap=new WeakMap;this.active=false}))}this.nodeMap.set(t,(this.nodeMap.get(t)||new Set).add(e))}destroy(){}}let wy;let Sy;let ky;let Cy=false;try{if(Array.from([1],(t=>t*2))[0]!==2){const t=document.createElement("iframe");document.body.appendChild(t);Array.from=((gs=t.contentWindow)==null?void 0:gs.Array.from)||Array.from;document.body.removeChild(t)}}catch(tw){console.debug("Unable to override Array.from",tw)}const My=eo();function Iy(t={}){const{emit:e,checkoutEveryNms:i,checkoutEveryNth:r,blockClass:n="rr-block",blockSelector:s=null,ignoreClass:o="rr-ignore",ignoreSelector:a=null,maskTextClass:u="rr-mask",maskTextSelector:f=null,inlineStylesheet:l=true,maskAllInputs:c,maskInputOptions:h,slimDOMOptions:d,maskInputFn:v,maskTextFn:p,hooks:m,packFn:g,sampling:y={},dataURLOptions:b={},mousemoveWait:w,recordDOM:S=true,recordCanvas:k=false,recordCrossOriginIframes:C=false,recordAfter:M=(t.recordAfter==="DOMContentLoaded"?t.recordAfter:"load"),userTriggeredOnInput:I=false,collectFonts:O=false,inlineImages:A=false,plugins:T,keepIframeSrcFn:_=()=>false,ignoreCSSAttributes:E=new Set([]),errorHandler:R}=t;kg(R);const x=C?window.parent===window:true;let j=false;if(!x){try{if(window.parent.document){j=false}}catch(t){j=true}}if(x&&!e){throw new Error("emit function is required")}if(!x&&!j){return()=>{}}if(w!==void 0&&y.mousemove===void 0){y.mousemove=w}My.reset();const N=c===true?{color:true,date:true,"datetime-local":true,email:true,month:true,number:true,range:true,search:true,tel:true,text:true,time:true,url:true,week:true,textarea:true,select:true,password:true}:h!==void 0?h:{password:true};const D=d===true||d==="all"?{script:true,comment:true,headFavicon:true,headWhitespace:true,headMetaSocial:true,headMetaRobots:true,headMetaHttpEquiv:true,headMetaVerification:true,headMetaAuthorship:d==="all",headMetaDescKeywords:d==="all",headTitleMutations:d==="all"}:d?d:{};Gm();let F;let L=0;const P=t=>{for(const e of T||[]){if(e.eventProcessor){t=e.eventProcessor(t)}}if(g&&!j){t=g(t)}return t};wy=(t,n)=>{var s;const o=t;o.timestamp=Rm();if(((s=Ig[0])==null?void 0:s.isFrozen())&&o.type!==rg.FullSnapshot&&!(o.type===rg.IncrementalSnapshot&&o.data.source===ng.Mutation)){Ig.forEach((t=>t.unfreeze()))}if(x){e==null?void 0:e(P(o),n)}else if(j){const t={type:"rrweb",event:P(o),origin:window.location.origin,isCheckout:n};window.parent.postMessage(t,"*")}if(o.type===rg.FullSnapshot){F=o;L=0}else if(o.type===rg.IncrementalSnapshot){if(o.data.source===ng.Mutation&&o.data.isAttachIframe){return}L++;const t=r&&L>=r;const e=i&&o.timestamp-F.timestamp>i;if(t||e){Sy(true)}}};const B=t=>{wy({type:rg.IncrementalSnapshot,data:{source:ng.Mutation,...t}})};const U=t=>wy({type:rg.IncrementalSnapshot,data:{source:ng.Scroll,...t}});const G=t=>wy({type:rg.IncrementalSnapshot,data:{source:ng.CanvasMutation,...t}});const W=t=>wy({type:rg.IncrementalSnapshot,data:{source:ng.AdoptedStyleSheet,...t}});const z=new yy({mutationCb:B,adoptedStyleSheetCb:W});const V=new $g({mirror:My,mutationCb:B,stylesheetManager:z,recordCrossOriginIframes:C,wrappedEmit:wy});for(const t of T||[]){if(t.getMirror)t.getMirror({nodeMirror:My,crossOriginIframeMirror:V.crossOriginIframeMirror,crossOriginIframeStyleMirror:V.crossOriginIframeStyleMirror})}const H=new by;ky=new gy({recordCanvas:k,mutationCb:G,win:window,blockClass:n,blockSelector:s,mirror:My,sampling:y.canvas,dataURLOptions:b});const Y=new Jg({mutationCb:B,scrollCb:U,bypassOptions:{blockClass:n,blockSelector:s,maskTextClass:u,maskTextSelector:f,inlineStylesheet:l,maskInputOptions:N,dataURLOptions:b,maskTextFn:p,maskInputFn:v,recordCanvas:k,inlineImages:A,sampling:y,slimDOMOptions:D,iframeManager:V,stylesheetManager:z,canvasManager:ky,keepIframeSrcFn:_,processedNodeManager:H},mirror:My});Sy=(t=false)=>{if(!S){return}wy({type:rg.Meta,data:{href:window.location.href,width:Nm(),height:jm()}},t);z.reset();Y.init();Ig.forEach((t=>t.lock()));const e=Zo(document,{mirror:My,blockClass:n,blockSelector:s,maskTextClass:u,maskTextSelector:f,inlineStylesheet:l,maskAllInputs:N,maskTextFn:p,maskInputFn:v,slimDOM:D,dataURLOptions:b,recordCanvas:k,inlineImages:A,onSerialize:t=>{if(Vm(t,My)){V.addIframe(t)}if(Hm(t,My)){z.trackLinkElement(t)}if(Zm(t)){Y.addShadowRoot(Mm.shadowRoot(t),document)}},onIframeLoad:(t,e)=>{V.attachIframe(t,e);Y.observeAttachShadow(t)},onStylesheetLoad:(t,e)=>{z.attachLinkElement(t,e)},keepIframeSrcFn:_});if(!e){return console.warn("Failed to snapshot the document")}wy({type:rg.FullSnapshot,data:{node:e,initialOffset:xm(window)}},t);Ig.forEach((t=>t.unlock()));if(document.adoptedStyleSheets&&document.adoptedStyleSheets.length>0)z.adoptStyleSheets(document.adoptedStyleSheets,My.getId(document))};try{const t=[];const e=t=>{var e;return Mg(Hg)({mutationCb:B,mousemoveCb:(t,e)=>wy({type:rg.IncrementalSnapshot,data:{source:e,positions:t}}),mouseInteractionCb:t=>wy({type:rg.IncrementalSnapshot,data:{source:ng.MouseInteraction,...t}}),scrollCb:U,viewportResizeCb:t=>wy({type:rg.IncrementalSnapshot,data:{source:ng.ViewportResize,...t}}),inputCb:t=>wy({type:rg.IncrementalSnapshot,data:{source:ng.Input,...t}}),mediaInteractionCb:t=>wy({type:rg.IncrementalSnapshot,data:{source:ng.MediaInteraction,...t}}),styleSheetRuleCb:t=>wy({type:rg.IncrementalSnapshot,data:{source:ng.StyleSheetRule,...t}}),styleDeclarationCb:t=>wy({type:rg.IncrementalSnapshot,data:{source:ng.StyleDeclaration,...t}}),canvasMutationCb:G,fontCb:t=>wy({type:rg.IncrementalSnapshot,data:{source:ng.Font,...t}}),selectionCb:t=>{wy({type:rg.IncrementalSnapshot,data:{source:ng.Selection,...t}})},customElementCb:t=>{wy({type:rg.IncrementalSnapshot,data:{source:ng.CustomElement,...t}})},blockClass:n,ignoreClass:o,ignoreSelector:a,maskTextClass:u,maskTextSelector:f,maskInputOptions:N,inlineStylesheet:l,sampling:y,recordDOM:S,recordCanvas:k,inlineImages:A,userTriggeredOnInput:I,collectFonts:O,doc:t,maskInputFn:v,maskTextFn:p,keepIframeSrcFn:_,blockSelector:s,slimDOMOptions:D,dataURLOptions:b,mirror:My,iframeManager:V,stylesheetManager:z,shadowDomManager:Y,processedNodeManager:H,canvasManager:ky,ignoreCSSAttributes:E,plugins:((e=T==null?void 0:T.filter((t=>t.observer)))==null?void 0:e.map((t=>({observer:t.observer,options:t.options,callback:e=>wy({type:rg.Plugin,data:{plugin:t.name,payload:e}})}))))||[]},m)};V.addLoadListener((i=>{try{t.push(e(i.contentDocument))}catch(t){console.warn(t)}}));const i=()=>{Sy();t.push(e(document));Cy=true};if(document.readyState==="interactive"||document.readyState==="complete"){i()}else{t.push(Im("DOMContentLoaded",(()=>{wy({type:rg.DomContentLoaded,data:{}});if(M==="DOMContentLoaded")i()})));t.push(Im("load",(()=>{wy({type:rg.Load,data:{}});if(M==="load")i()}),window))}return()=>{t.forEach((t=>t()));H.destroy();Cy=false;Cg()}}catch(t){console.warn(t)}}Iy.addCustomEvent=(t,e)=>{if(!Cy){throw new Error("please add custom event after start recording")}wy({type:rg.Custom,data:{tag:t,payload:e}})};Iy.freezePage=()=>{Ig.forEach((t=>t.freeze()))};Iy.takeFullSnapshot=t=>{if(!Cy){throw new Error("please take full snapshot after start recording")}Sy(t)};Iy.mirror=My;function Oy(t){return{all:t=t||new Map,on:function(e,i){var r=t.get(e);r?r.push(i):t.set(e,[i])},off:function(e,i){var r=t.get(e);r&&(i?r.splice(r.indexOf(i)>>>0,1):t.set(e,[]))},emit:function(e,i){var r=t.get(e);r&&r.slice().map((function(t){t(i)})),(r=t.get("*"))&&r.slice().map((function(t){t(e,i)}))}}}const Ay=Object.freeze(Object.defineProperty({__proto__:null,default:Oy},Symbol.toStringTag,{value:"Module"}));function Ty(t=window,e=document){if("scrollBehavior"in e.documentElement.style&&t.__forceSmoothScrollPolyfill__!==true){return}const i=t.HTMLElement||t.Element;const r=468;const n={scroll:t.scroll||t.scrollTo,scrollBy:t.scrollBy,elementScroll:i.prototype.scroll||u,scrollIntoView:i.prototype.scrollIntoView};const s=t.performance&&t.performance.now?t.performance.now.bind(t.performance):Date.now;function o(t){const e=["MSIE ","Trident/","Edge/"];return new RegExp(e.join("|")).test(t)}const a=o(t.navigator.userAgent)?1:0;function u(t,e){this.scrollLeft=t;this.scrollTop=e}function f(t){return.5*(1-Math.cos(Math.PI*t))}function l(t){if(t===null||typeof t!=="object"||t.behavior===void 0||t.behavior==="auto"||t.behavior==="instant"){return true}if(typeof t==="object"&&t.behavior==="smooth"){return false}throw new TypeError("behavior member of ScrollOptions "+t.behavior+" is not a valid value for enumeration ScrollBehavior.")}function c(t,e){if(e==="Y"){return t.clientHeight+a<t.scrollHeight}if(e==="X"){return t.clientWidth+a<t.scrollWidth}}function h(e,i){const r=t.getComputedStyle(e,null)["overflow"+i];return r==="auto"||r==="scroll"}function d(t){const e=c(t,"Y")&&h(t,"Y");const i=c(t,"X")&&h(t,"X");return e||i}function v(t){while(t!==e.body&&d(t)===false){t=t.parentNode||t.host}return t}function p(e){const i=s();let n;let o;let a;let u=(i-e.startTime)/r;u=u>1?1:u;n=f(u);o=e.startX+(e.x-e.startX)*n;a=e.startY+(e.y-e.startY)*n;e.method.call(e.scrollable,o,a);if(o!==e.x||a!==e.y){t.requestAnimationFrame(p.bind(t,e))}}function m(i,r,o){let a;let f;let l;let c;const h=s();if(i===e.body){a=t;f=t.scrollX||t.pageXOffset;l=t.scrollY||t.pageYOffset;c=n.scroll}else{a=i;f=i.scrollLeft;l=i.scrollTop;c=u}p({scrollable:a,method:c,startTime:h,startX:f,startY:l,x:r,y:o})}t.scroll=t.scrollTo=function(){if(arguments[0]===void 0){return}if(l(arguments[0])===true){n.scroll.call(t,arguments[0].left!==void 0?arguments[0].left:typeof arguments[0]!=="object"?arguments[0]:t.scrollX||t.pageXOffset,arguments[0].top!==void 0?arguments[0].top:arguments[1]!==void 0?arguments[1]:t.scrollY||t.pageYOffset);return}m.call(t,e.body,arguments[0].left!==void 0?~~arguments[0].left:t.scrollX||t.pageXOffset,arguments[0].top!==void 0?~~arguments[0].top:t.scrollY||t.pageYOffset)};t.scrollBy=function(){if(arguments[0]===void 0){return}if(l(arguments[0])){n.scrollBy.call(t,arguments[0].left!==void 0?arguments[0].left:typeof arguments[0]!=="object"?arguments[0]:0,arguments[0].top!==void 0?arguments[0].top:arguments[1]!==void 0?arguments[1]:0);return}m.call(t,e.body,~~arguments[0].left+(t.scrollX||t.pageXOffset),~~arguments[0].top+(t.scrollY||t.pageYOffset))};i.prototype.scroll=i.prototype.scrollTo=function(){if(arguments[0]===void 0){return}if(l(arguments[0])===true){if(typeof arguments[0]==="number"&&arguments[1]===void 0){throw new SyntaxError("Value could not be converted")}n.elementScroll.call(this,arguments[0].left!==void 0?~~arguments[0].left:typeof arguments[0]!=="object"?~~arguments[0]:this.scrollLeft,arguments[0].top!==void 0?~~arguments[0].top:arguments[1]!==void 0?~~arguments[1]:this.scrollTop);return}const t=arguments[0].left;const e=arguments[0].top;m.call(this,this,typeof t==="undefined"?this.scrollLeft:~~t,typeof e==="undefined"?this.scrollTop:~~e)};i.prototype.scrollBy=function(){if(arguments[0]===void 0){return}if(l(arguments[0])===true){n.elementScroll.call(this,arguments[0].left!==void 0?~~arguments[0].left+this.scrollLeft:~~arguments[0]+this.scrollLeft,arguments[0].top!==void 0?~~arguments[0].top+this.scrollTop:~~arguments[1]+this.scrollTop);return}this.scroll({left:~~arguments[0].left+this.scrollLeft,top:~~arguments[0].top+this.scrollTop,behavior:arguments[0].behavior})};i.prototype.scrollIntoView=function(){if(l(arguments[0])===true){n.scrollIntoView.call(this,arguments[0]===void 0?true:arguments[0]);return}const i=v(this);const r=i.getBoundingClientRect();const s=this.getBoundingClientRect();if(i!==e.body){m.call(this,i,i.scrollLeft+s.left-r.left,i.scrollTop+s.top-r.top);if(t.getComputedStyle(i).position!=="fixed"){t.scrollBy({left:r.left,top:r.top,behavior:"smooth"})}}else{t.scrollBy({left:s.left,top:s.top,behavior:"smooth"})}}}class _y{constructor(t=[],e){ms(this,"timeOffset",0);ms(this,"speed");ms(this,"actions");ms(this,"raf",null);ms(this,"lastTimestamp");this.actions=t;this.speed=e.speed}addAction(t){const e=this.raf===true;if(!this.actions.length||this.actions[this.actions.length-1].delay<=t.delay){this.actions.push(t)}else{const e=this.findActionIndex(t);this.actions.splice(e,0,t)}if(e){this.raf=requestAnimationFrame(this.rafCheck.bind(this))}}start(){this.timeOffset=0;this.lastTimestamp=performance.now();this.raf=requestAnimationFrame(this.rafCheck.bind(this))}rafCheck(){const t=performance.now();this.timeOffset+=(t-this.lastTimestamp)*this.speed;this.lastTimestamp=t;while(this.actions.length){const t=this.actions[0];if(this.timeOffset>=t.delay){this.actions.shift();t.doAction()}else{break}}if(this.actions.length>0){this.raf=requestAnimationFrame(this.rafCheck.bind(this))}else{this.raf=true}}clear(){if(this.raf){if(this.raf!==true){cancelAnimationFrame(this.raf)}this.raf=null}this.actions.length=0}setSpeed(t){this.speed=t}isActive(){return this.raf!==null}findActionIndex(t){let e=0;let i=this.actions.length-1;while(e<=i){const r=Math.floor((e+i)/2);if(this.actions[r].delay<t.delay){e=r+1}else if(this.actions[r].delay>t.delay){i=r-1}else{return r+1}}return e}}function Ey(t,e){if(t.type===rg.IncrementalSnapshot&&t.data.source===ng.MouseMove&&t.data.positions&&t.data.positions.length){const i=t.data.positions[0].timeOffset;const r=t.timestamp+i;t.delay=r-e;return r-e}t.delay=t.timestamp-e;return t.delay}
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */function Ry(t,e){var i="function"==typeof Symbol&&t[Symbol.iterator];if(!i)return t;var r,n,s=i.call(t),o=[];try{for(;(void 0===e||e-- >0)&&!(r=s.next()).done;)o.push(r.value)}catch(t){n={error:t}}finally{try{r&&!r.done&&(i=s.return)&&i.call(s)}finally{if(n)throw n.error}}return o}var xy;!function(t){t[t.NotStarted=0]="NotStarted",t[t.Running=1]="Running",t[t.Stopped=2]="Stopped"}(xy||(xy={}));var jy={type:"xstate.init"};function Ny(t){return void 0===t?[]:[].concat(t)}function Dy(t){return{type:"xstate.assign",assignment:t}}function Fy(t,e){return"string"==typeof(t="string"==typeof t&&e&&e[t]?e[t]:t)?{type:t}:"function"==typeof t?{type:t.name,exec:t}:t}function Ly(t){return function(e){return t===e}}function Py(t){return"string"==typeof t?{type:t}:t}function By(t,e){return{value:t,context:e,actions:[],changed:false,matches:Ly(t)}}function Uy(t,e,i){var r=e,n=false;return[t.filter((function(t){if("xstate.assign"===t.type){n=true;var e=Object.assign({},r);return"function"==typeof t.assignment?e=t.assignment(r,i):Object.keys(t.assignment).forEach((function(n){e[n]="function"==typeof t.assignment[n]?t.assignment[n](r,i):t.assignment[n]})),r=e,false}return true})),r,n]}function Gy(t,e){void 0===e&&(e={});var i=Ry(Uy(Ny(t.states[t.initial].entry).map((function(t){return Fy(t,e.actions)})),t.context,jy),2),r=i[0],n=i[1],s={config:t,_options:e,initialState:{value:t.initial,actions:r,context:n,matches:Ly(t.initial)},transition:function(e,i){var r,n,o="string"==typeof e?{value:e,context:t.context}:e,a=o.value,u=o.context,f=Py(i),l=t.states[a];if(l.on){var c=Ny(l.on[f.type]);try{for(var h=function(t){var e="function"==typeof Symbol&&Symbol.iterator,i=e&&t[e],r=0;if(i)return i.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}(c),d=h.next();!d.done;d=h.next()){var v=d.value;if(void 0===v)return By(a,u);var p="string"==typeof v?{target:v}:v,m=p.target,g=p.actions,y=void 0===g?[]:g,b=p.cond,w=void 0===b?function(){return true}:b,S=void 0===m,k=null!=m?m:a,C=t.states[k];if(w(u,f)){var M=Ry(Uy((S?Ny(y):[].concat(l.exit,y,C.entry).filter((function(t){return t}))).map((function(t){return Fy(t,s._options.actions)})),u,f),3),I=M[0],O=M[1],A=M[2],T=null!=m?m:a;return{value:T,context:O,actions:I,changed:m!==a||I.length>0||A,matches:Ly(T)}}}}catch(t){r={error:t}}finally{try{d&&!d.done&&(n=h.return)&&n.call(h)}finally{if(r)throw r.error}}}return By(a,u)}};return s}var Wy=function(t,e){return t.actions.forEach((function(i){var r=i.exec;return r&&r(t.context,e)}))};function zy(t){var e=t.initialState,i=xy.NotStarted,r=new Set,n={_machine:t,send:function(n){i===xy.Running&&(e=t.transition(e,n),Wy(e,Py(n)),r.forEach((function(t){return t(e)})))},subscribe:function(t){return r.add(t),t(e),{unsubscribe:function(){return r.delete(t)}}},start:function(r){if(r){var s="object"==typeof r?r:{context:t.config.context,value:r};e={value:s.value,actions:[],context:s.context,matches:Ly(s.value)}}return i=xy.Running,Wy(e,jy),n},stop:function(){return i=xy.Stopped,r.clear(),n},get state(){return e},get status(){return i}};return n}function Vy(t,e){for(let i=t.length-1;i>=0;i--){const r=t[i];if(r.type===rg.Meta){if(r.timestamp<=e){return t.slice(i)}}}return t}function Hy(t,{getCastFn:e,applyEventsSynchronously:i,emitter:r}){const n=Gy({id:"player",context:t,initial:"paused",states:{playing:{on:{PAUSE:{target:"paused",actions:["pause"]},CAST_EVENT:{target:"playing",actions:"castEvent"},END:{target:"paused",actions:["resetLastPlayedEvent","pause"]},ADD_EVENT:{target:"playing",actions:["addEvent"]}}},paused:{on:{PLAY:{target:"playing",actions:["recordTimeOffset","play"]},CAST_EVENT:{target:"paused",actions:"castEvent"},TO_LIVE:{target:"live",actions:["startLive"]},ADD_EVENT:{target:"paused",actions:["addEvent"]}}},live:{on:{ADD_EVENT:{target:"live",actions:["addEvent"]},CAST_EVENT:{target:"live",actions:["castEvent"]}}}}},{actions:{castEvent:Dy({lastPlayedEvent:(t,e)=>{if(e.type==="CAST_EVENT"){return e.payload.event}return t.lastPlayedEvent}}),recordTimeOffset:Dy(((t,e)=>{let i=t.timeOffset;if("payload"in e&&"timeOffset"in e.payload){i=e.payload.timeOffset}return{...t,timeOffset:i,baselineTime:t.events[0].timestamp+i}})),play(t){var n;const{timer:s,events:o,baselineTime:a,lastPlayedEvent:u}=t;s.clear();for(const t of o){Ey(t,a)}const f=Vy(o,a);let l=u==null?void 0:u.timestamp;if((u==null?void 0:u.type)===rg.IncrementalSnapshot&&u.data.source===ng.MouseMove){l=u.timestamp+((n=u.data.positions[0])==null?void 0:n.timeOffset)}if(a<(l||0)){r.emit(fg.PlayBack)}const c=new Array;for(const t of f){if(l&&l<a&&(t.timestamp<=l||t===u)){continue}if(t.timestamp<a){c.push(t)}else{const i=e(t,false);s.addAction({doAction:()=>{i()},delay:t.delay})}}i(c);r.emit(fg.Flush);s.start()},pause(t){t.timer.clear()},resetLastPlayedEvent:Dy((t=>({...t,lastPlayedEvent:null}))),startLive:Dy({baselineTime:(t,e)=>{t.timer.start();if(e.type==="TO_LIVE"&&e.payload.baselineTime){return e.payload.baselineTime}return Date.now()}}),addEvent:Dy(((t,i)=>{const{baselineTime:r,timer:n,events:s}=t;if(i.type==="ADD_EVENT"){const{event:t}=i.payload;Ey(t,r);let o=s.length-1;if(!s[o]||s[o].timestamp<=t.timestamp){s.push(t)}else{let e=-1;let i=0;while(i<=o){const e=Math.floor((i+o)/2);if(s[e].timestamp<=t.timestamp){i=e+1}else{o=e-1}}if(e===-1){e=i}s.splice(e,0,t)}const a=t.timestamp<r;const u=e(t,a);if(a){u()}else if(n.isActive()){n.addAction({doAction:()=>{u()},delay:t.delay})}}return{...t,events:s}}))}});return zy(n)}function Yy(t){const e=Gy({id:"speed",context:t,initial:"normal",states:{normal:{on:{FAST_FORWARD:{target:"skipping",actions:["recordSpeed","setSpeed"]},SET_SPEED:{target:"normal",actions:["setSpeed"]}}},skipping:{on:{BACK_TO_NORMAL:{target:"normal",actions:["restoreSpeed"]},SET_SPEED:{target:"normal",actions:["setSpeed"]}}}}},{actions:{setSpeed:(t,e)=>{if("payload"in e){t.timer.setSpeed(e.payload.speed)}},recordSpeed:Dy({normalSpeed:t=>t.timer.speed}),restoreSpeed:t=>{t.timer.setSpeed(t.normalSpeed)}}});return zy(e)}const Zy=t=>[`.${t} { background: currentColor }`,"noscript { display: none !important; }"];const Xy=new Map;function $y(t,e){let i=Xy.get(t);if(!i){i=new Map;Xy.set(t,i)}if(!i.has(e)){i.set(e,[])}return i.get(e)}function Jy(t,e,i){return async r=>{if(r&&typeof r==="object"&&"rr_type"in r){if(i)i.isUnchanged=false;if(r.rr_type==="ImageBitmap"&&"args"in r){const n=await Jy(t,e,i)(r.args);return await createImageBitmap.apply(null,n)}else if("index"in r){if(i||e===null)return r;const{rr_type:t,index:n}=r;return $y(e,t)[n]}else if("args"in r){const{rr_type:n,args:s}=r;const o=window[n];return new o(...await Promise.all(s.map(Jy(t,e,i))))}else if("base64"in r){return ey(r.base64)}else if("src"in r){const e=t.get(r.src);if(e){return e}else{const e=new Image;e.src=r.src;t.set(r.src,e);return e}}else if("data"in r&&r.rr_type==="Blob"){const n=await Promise.all(r.data.map(Jy(t,e,i)));const s=new Blob(n,{type:r.type});return s}}else if(Array.isArray(r)){const n=await Promise.all(r.map(Jy(t,e,i)));return n}return r}}function Ky(t,e){try{if(e===ag.WebGL){return t.getContext("webgl")||t.getContext("experimental-webgl")}return t.getContext("webgl2")}catch(t){return null}}const qy=["WebGLActiveInfo","WebGLBuffer","WebGLFramebuffer","WebGLProgram","WebGLRenderbuffer","WebGLShader","WebGLShaderPrecisionFormat","WebGLTexture","WebGLUniformLocation","WebGLVertexArrayObject"];function Qy(t,e){if(!(e==null?void 0:e.constructor))return;const{name:i}=e.constructor;if(!qy.includes(i))return;const r=$y(t,i);if(!r.includes(e))r.push(e)}async function tb({mutation:t,target:e,type:i,imageMap:r,errorHandler:n}){try{const n=Ky(e,i);if(!n)return;if(t.setter){n[t.property]=t.args[0];return}const s=n[t.property];const o=await Promise.all(t.args.map(Jy(r,n)));const a=s.apply(n,o);Qy(n,a)}catch(e){n(t,e)}}async function eb({event:t,mutations:e,target:i,imageMap:r,errorHandler:n}){const s=i.getContext("2d");if(!s){n(e[0],new Error("Canvas context is null"));return}const o=e.map((async t=>Promise.all(t.args.map(Jy(r,s)))));const a=await Promise.all(o);a.forEach(((i,o)=>{const a=e[o];try{if(a.setter){s[a.property]=a.args[0];return}const e=s[a.property];if(a.property==="drawImage"&&typeof a.args[0]==="string"){r.get(t);e.apply(s,a.args)}else{e.apply(s,i)}}catch(t){n(a,t)}return}))}async function ib({event:t,mutation:e,target:i,imageMap:r,canvasEventMap:n,errorHandler:s}){try{const o=n.get(t)||e;const a="commands"in o?o.commands:[o];if([ag.WebGL,ag.WebGL2].includes(e.type)){for(let t=0;t<a.length;t++){const n=a[t];await tb({mutation:n,type:e.type,target:i,imageMap:r,errorHandler:s})}return}await eb({event:t,mutations:a,target:i,imageMap:r,errorHandler:s})}catch(t){s(e,t)}}class rb{constructor(t){ms(this,"mediaMap",new Map);ms(this,"warn");ms(this,"service");ms(this,"speedService");ms(this,"emitter");ms(this,"getCurrentTime");ms(this,"metadataCallbackMap",new Map);this.warn=t.warn;this.service=t.service;this.speedService=t.speedService;this.emitter=t.emitter;this.getCurrentTime=t.getCurrentTime;this.emitter.on(fg.Start,this.start.bind(this));this.emitter.on(fg.SkipStart,this.start.bind(this));this.emitter.on(fg.Pause,this.pause.bind(this));this.emitter.on(fg.Finish,this.pause.bind(this));this.speedService.subscribe((()=>{this.syncAllMediaElements()}))}syncAllMediaElements(t={pause:false}){this.mediaMap.forEach(((e,i)=>{this.syncTargetWithState(i);if(t.pause){i.pause()}}))}start(){this.syncAllMediaElements()}pause(){this.syncAllMediaElements({pause:true})}seekTo({time:t,target:e,mediaState:i}){if(i.isPlaying){const r=t-i.lastInteractionTimeOffset;const n=r/1e3*i.playbackRate;const s="duration"in e&&e.duration;if(Number.isNaN(s)){this.waitForMetadata(e);return}let o=i.currentTimeAtLastInteraction+n;if(e.loop&&s!==false){o=o%s}e.currentTime=o}else{e.pause();e.currentTime=i.currentTimeAtLastInteraction}}waitForMetadata(t){if(this.metadataCallbackMap.has(t))return;if(!("addEventListener"in t))return;const e=()=>{this.metadataCallbackMap.delete(t);const e=this.mediaMap.get(t);if(!e)return;this.seekTo({time:this.getCurrentTime(),target:t,mediaState:e})};this.metadataCallbackMap.set(t,e);t.addEventListener("loadedmetadata",e,{once:true})}getMediaStateFromMutation({target:t,timeOffset:e,mutation:i}){const r=this.mediaMap.get(t);const{type:n,playbackRate:s,currentTime:o,muted:a,volume:u,loop:f}=i;const l=n===ug.Play||n!==ug.Pause&&((r==null?void 0:r.isPlaying)||t.getAttribute("autoplay")!==null);const c={isPlaying:l,currentTimeAtLastInteraction:o??(r==null?void 0:r.currentTimeAtLastInteraction)??0,lastInteractionTimeOffset:e,playbackRate:s??(r==null?void 0:r.playbackRate)??1,volume:u??(r==null?void 0:r.volume)??1,muted:a??(r==null?void 0:r.muted)??t.getAttribute("muted")===null,loop:f??(r==null?void 0:r.loop)??t.getAttribute("loop")===null};return c}syncTargetWithState(t){const e=this.mediaMap.get(t);if(!e)return;const{muted:i,loop:r,volume:n,isPlaying:s}=e;const o=this.service.state.matches("paused");const a=e.playbackRate*this.speedService.state.context.timer.speed;try{this.seekTo({time:this.getCurrentTime(),target:t,mediaState:e});if(t.volume!==n){t.volume=n}t.muted=i;t.loop=r;if(t.playbackRate!==a){t.playbackRate=a}if(s&&!o){void t.play()}else{t.pause()}}catch(t){this.warn(`Failed to replay media interactions: ${t.message||t}`)}}addMediaElements(t,e,i){if(!["AUDIO","VIDEO"].includes(t.nodeName))return;const r=t;const n=i.getMeta(r);if(!n||!("attributes"in n))return;const s=this.service.state.matches("paused");const o=n.attributes;let a=false;if(o.rr_mediaState){a=o.rr_mediaState==="played"}else{a=r.getAttribute("autoplay")!==null}if(a&&s)r.pause();let u=1;if(typeof o.rr_mediaPlaybackRate==="number"){u=o.rr_mediaPlaybackRate}let f=false;if(typeof o.rr_mediaMuted==="boolean"){f=o.rr_mediaMuted}else{f=r.getAttribute("muted")!==null}let l=false;if(typeof o.rr_mediaLoop==="boolean"){l=o.rr_mediaLoop}else{l=r.getAttribute("loop")!==null}let c=1;if(typeof o.rr_mediaVolume==="number"){c=o.rr_mediaVolume}let h=0;if(typeof o.rr_mediaCurrentTime==="number"){h=o.rr_mediaCurrentTime}this.mediaMap.set(r,{isPlaying:a,currentTimeAtLastInteraction:h,lastInteractionTimeOffset:e,playbackRate:u,volume:c,muted:f,loop:l});this.syncTargetWithState(r)}mediaMutation({target:t,timeOffset:e,mutation:i}){this.mediaMap.set(t,this.getMediaStateFromMutation({target:t,timeOffset:e,mutation:i}));this.syncTargetWithState(t)}isSupportedMediaElement(t){return["AUDIO","VIDEO"].includes(t.nodeName)}reset(){this.mediaMap.clear()}}function nb(t,e){if(t.nodeName!=="DIALOG"||t instanceof pp)return;const i=t;const r=i.open;const n=r&&i.matches("dialog:modal");const s=i.getAttribute("rr_open_mode");const o=typeof(e==null?void 0:e.attributes.open)==="string"||typeof i.getAttribute("open")==="string";const a=s==="modal";const u=s==="non-modal";const f=n&&u||!n&&a;if(r&&!f)return;if(!i.isConnected){console.warn("dialog is not attached to the dom",i);return}if(r)i.close();if(!o)return;if(a)i.showModal();else i.show()}function sb(t,e){if(t.nodeName!=="DIALOG"||t instanceof pp)return;const i=t;if(!i.isConnected){console.warn("dialog is not attached to the dom",i);return}if(e.attributes.open===null){i.removeAttribute("open");i.removeAttribute("rr_open_mode")}}const ob=5*1e3;const ab=Oy||Ay;const ub="[replayer]";const fb={duration:500,lineCap:"round",lineWidth:3,strokeStyle:"red"};function lb(t){return t.type==rg.IncrementalSnapshot&&(t.data.source==ng.TouchMove||t.data.source==ng.MouseInteraction&&t.data.type==sg.TouchStart)}class cb{constructor(t,e){ms(this,"wrapper");ms(this,"iframe");ms(this,"service");ms(this,"speedService");ms(this,"config");ms(this,"usingVirtualDom",false);ms(this,"virtualDom",new Up);ms(this,"mouse");ms(this,"mouseTail",null);ms(this,"tailPositions",[]);ms(this,"emitter",ab());ms(this,"nextUserInteractionEvent");ms(this,"legacy_missingNodeRetryMap",{});ms(this,"cache",ql());ms(this,"imageMap",new Map);ms(this,"canvasEventMap",new Map);ms(this,"mirror",eo());ms(this,"styleMirror",new Km);ms(this,"mediaManager");ms(this,"firstFullSnapshot",null);ms(this,"newDocumentQueue",[]);ms(this,"mousePos",null);ms(this,"touchActive",null);ms(this,"lastMouseDownEvent",null);ms(this,"lastHoveredRootNode");ms(this,"lastSelectionData",null);ms(this,"constructedStyleMutations",[]);ms(this,"adoptedStyleSheets",[]);ms(this,"handleResize",(t=>{this.iframe.style.display="inherit";for(const e of[this.mouseTail,this.iframe]){if(!e){continue}e.setAttribute("width",String(t.width));e.setAttribute("height",String(t.height))}}));ms(this,"applyEventsSynchronously",(t=>{for(const e of t){switch(e.type){case rg.DomContentLoaded:case rg.Load:case rg.Custom:continue}const t=this.getCastFn(e,true);t()}}));ms(this,"getCastFn",((t,e=false)=>{let i;switch(t.type){case rg.DomContentLoaded:case rg.Load:break;case rg.Custom:i=()=>{this.emitter.emit(fg.CustomEvent,t)};break;case rg.Meta:i=()=>this.emitter.emit(fg.Resize,{width:t.data.width,height:t.data.height});break;case rg.FullSnapshot:i=()=>{var i;if(this.firstFullSnapshot){if(this.firstFullSnapshot===t){this.firstFullSnapshot=true;return}}else{this.firstFullSnapshot=true}this.mediaManager.reset();this.styleMirror.reset();this.rebuildFullSnapshot(t,e);(i=this.iframe.contentWindow)==null?void 0:i.scrollTo(t.data.initialOffset)};break;case rg.IncrementalSnapshot:i=()=>{this.applyIncremental(t,e);if(e){return}if(t===this.nextUserInteractionEvent){this.nextUserInteractionEvent=null;this.backToNormal()}if(this.config.skipInactive&&!this.nextUserInteractionEvent){for(const e of this.service.state.context.events){if(e.timestamp<=t.timestamp){continue}if(this.isUserInteraction(e)){if(e.delay-t.delay>this.config.inactivePeriodThreshold*this.speedService.state.context.timer.speed){this.nextUserInteractionEvent=e}break}}if(this.nextUserInteractionEvent){const e=this.nextUserInteractionEvent.delay-t.delay;const i={speed:Math.min(Math.round(e/ob),this.config.maxSpeed)};this.speedService.send({type:"FAST_FORWARD",payload:i});this.emitter.emit(fg.SkipStart,i)}}};break}const r=()=>{if(i){i()}for(const i of this.config.plugins||[]){if(i.handler)i.handler(t,e,{replayer:this})}this.service.send({type:"CAST_EVENT",payload:{event:t}});const r=this.service.state.context.events.length-1;if(!this.config.liveMode&&t===this.service.state.context.events[r]){const e=()=>{if(r<this.service.state.context.events.length-1){return}this.backToNormal();this.service.send("END");this.emitter.emit(fg.Finish)};let i=50;if(t.type===rg.IncrementalSnapshot&&t.data.source===ng.MouseMove&&t.data.positions.length){i+=Math.max(0,-t.data.positions[0].timeOffset)}setTimeout(e,i)}this.emitter.emit(fg.EventCast,t)};return r}));if(!(e==null?void 0:e.liveMode)&&t.length<2){throw new Error("Replayer need at least 2 events.")}const i={speed:1,maxSpeed:360,root:document.body,loadTimeout:0,skipInactive:false,inactivePeriodThreshold:10*1e3,showWarning:true,showDebug:false,blockClass:"rr-block",liveMode:false,insertStyleRules:[],triggerFocus:true,UNSAFE_replayCanvas:false,pauseAnimation:true,mouseTail:fb,useVirtualDom:true,logger:console};this.config=Object.assign({},i,e);this.handleResize=this.handleResize.bind(this);this.getCastFn=this.getCastFn.bind(this);this.applyEventsSynchronously=this.applyEventsSynchronously.bind(this);this.emitter.on(fg.Resize,this.handleResize);this.setupDom();for(const t of this.config.plugins||[]){if(t.getMirror)t.getMirror({nodeMirror:this.mirror})}this.emitter.on(fg.Flush,(()=>{if(this.usingVirtualDom){const t={mirror:this.mirror,applyCanvas:(t,e,i)=>{void ib({event:t,mutation:e,target:i,imageMap:this.imageMap,canvasEventMap:this.canvasEventMap,errorHandler:this.warnCanvasMutationFailed.bind(this)})},applyInput:this.applyInput.bind(this),applyScroll:this.applyScroll.bind(this),applyStyleSheetMutation:(t,e)=>{if(t.source===ng.StyleSheetRule)this.applyStyleSheetRule(t,e);else if(t.source===ng.StyleDeclaration)this.applyStyleDeclaration(t,e)},afterAppend:(t,e)=>{for(const i of this.config.plugins||[]){if(i.onBuild)i.onBuild(t,{id:e,replayer:this})}}};if(this.iframe.contentDocument)try{xp(this.iframe.contentDocument,this.virtualDom,t,this.virtualDom.mirror)}catch(t){console.warn(t)}this.virtualDom.destroyTree();this.usingVirtualDom=false;if(Object.keys(this.legacy_missingNodeRetryMap).length){for(const e in this.legacy_missingNodeRetryMap){try{const i=this.legacy_missingNodeRetryMap[e];const r=Lp(i.node,this.mirror,this.virtualDom.mirror);xp(r,i.node,t,this.virtualDom.mirror);i.node=r}catch(t){this.warn(t)}}}this.constructedStyleMutations.forEach((t=>{this.applyStyleSheetMutation(t)}));this.constructedStyleMutations=[];this.adoptedStyleSheets.forEach((t=>{this.applyAdoptedStyleSheet(t)}));this.adoptedStyleSheets=[]}if(this.mousePos){this.moveAndHover(this.mousePos.x,this.mousePos.y,this.mousePos.id,true,this.mousePos.debugData);this.mousePos=null}if(this.touchActive===true){this.mouse.classList.add("touch-active")}else if(this.touchActive===false){this.mouse.classList.remove("touch-active")}this.touchActive=null;if(this.lastMouseDownEvent){const[t,e]=this.lastMouseDownEvent;t.dispatchEvent(e)}this.lastMouseDownEvent=null;if(this.lastSelectionData){this.applySelection(this.lastSelectionData);this.lastSelectionData=null}}));this.emitter.on(fg.PlayBack,(()=>{this.firstFullSnapshot=null;this.mirror.reset();this.styleMirror.reset();this.mediaManager.reset()}));const r=new _y([],{speed:this.config.speed});this.service=Hy({events:t.map((t=>{if(e&&e.unpackFn){return e.unpackFn(t)}return t})).sort(((t,e)=>t.timestamp-e.timestamp)),timer:r,timeOffset:0,baselineTime:0,lastPlayedEvent:null},{getCastFn:this.getCastFn,applyEventsSynchronously:this.applyEventsSynchronously,emitter:this.emitter});this.service.start();this.service.subscribe((t=>{this.emitter.emit(fg.StateChange,{player:t})}));this.speedService=Yy({normalSpeed:-1,timer:r});this.speedService.start();this.speedService.subscribe((t=>{this.emitter.emit(fg.StateChange,{speed:t})}));this.mediaManager=new rb({warn:this.warn.bind(this),service:this.service,speedService:this.speedService,emitter:this.emitter,getCurrentTime:this.getCurrentTime.bind(this)});const n=this.service.state.context.events.find((t=>t.type===rg.Meta));const s=this.service.state.context.events.find((t=>t.type===rg.FullSnapshot));if(n){const{width:t,height:e}=n.data;setTimeout((()=>{this.emitter.emit(fg.Resize,{width:t,height:e})}),0)}if(s){setTimeout((()=>{var t;if(this.firstFullSnapshot){return}this.firstFullSnapshot=s;this.rebuildFullSnapshot(s);(t=this.iframe.contentWindow)==null?void 0:t.scrollTo(s.data.initialOffset)}),1)}if(this.service.state.context.events.find(lb)){this.mouse.classList.add("touch-device")}}get timer(){return this.service.state.context.timer}on(t,e){this.emitter.on(t,e);return this}off(t,e){this.emitter.off(t,e);return this}setConfig(t){Object.keys(t).forEach((e=>{this.config[e]=t[e]}));if(!this.config.skipInactive){this.backToNormal()}if(typeof t.speed!=="undefined"){this.speedService.send({type:"SET_SPEED",payload:{speed:t.speed}})}if(typeof t.mouseTail!=="undefined"){if(t.mouseTail===false){if(this.mouseTail){this.mouseTail.style.display="none"}}else{if(!this.mouseTail){this.mouseTail=document.createElement("canvas");this.mouseTail.width=Number.parseFloat(this.iframe.width);this.mouseTail.height=Number.parseFloat(this.iframe.height);this.mouseTail.classList.add("replayer-mouse-tail");this.wrapper.insertBefore(this.mouseTail,this.iframe)}this.mouseTail.style.display="inherit"}}}getMetaData(){const t=this.service.state.context.events[0];const e=this.service.state.context.events[this.service.state.context.events.length-1];return{startTime:t.timestamp,endTime:e.timestamp,totalTime:e.timestamp-t.timestamp}}getCurrentTime(){return this.timer.timeOffset+this.getTimeOffset()}getTimeOffset(){const{baselineTime:t,events:e}=this.service.state.context;return t-e[0].timestamp}getMirror(){return this.mirror}play(t=0){var e,i;if(this.service.state.matches("paused")){this.service.send({type:"PLAY",payload:{timeOffset:t}})}else{this.service.send({type:"PAUSE"});this.service.send({type:"PLAY",payload:{timeOffset:t}})}(i=(e=this.iframe.contentDocument)==null?void 0:e.getElementsByTagName("html")[0])==null?void 0:i.classList.remove("rrweb-paused");this.emitter.emit(fg.Start)}pause(t){var e,i;if(t===void 0&&this.service.state.matches("playing")){this.service.send({type:"PAUSE"})}if(typeof t==="number"){this.play(t);this.service.send({type:"PAUSE"})}(i=(e=this.iframe.contentDocument)==null?void 0:e.getElementsByTagName("html")[0])==null?void 0:i.classList.add("rrweb-paused");this.emitter.emit(fg.Pause)}resume(t=0){this.warn(`The 'resume' was deprecated in 1.0. Please use 'play' method which has the same interface.`);this.play(t);this.emitter.emit(fg.Resume)}destroy(){this.pause();this.mirror.reset();this.styleMirror.reset();this.mediaManager.reset();this.config.root.removeChild(this.wrapper);this.emitter.emit(fg.Destroy)}startLive(t){this.service.send({type:"TO_LIVE",payload:{baselineTime:t}})}addEvent(t){const e=this.config.unpackFn?this.config.unpackFn(t):t;if(lb(e)){this.mouse.classList.add("touch-device")}void Promise.resolve().then((()=>this.service.send({type:"ADD_EVENT",payload:{event:e}})))}enableInteract(){this.iframe.setAttribute("scrolling","auto");this.iframe.style.pointerEvents="auto"}disableInteract(){this.iframe.setAttribute("scrolling","no");this.iframe.style.pointerEvents="none"}resetCache(){this.cache=ql()}setupDom(){this.wrapper=document.createElement("div");this.wrapper.classList.add("replayer-wrapper");this.config.root.appendChild(this.wrapper);this.mouse=document.createElement("div");this.mouse.classList.add("replayer-mouse");this.wrapper.appendChild(this.mouse);if(this.config.mouseTail!==false){this.mouseTail=document.createElement("canvas");this.mouseTail.classList.add("replayer-mouse-tail");this.mouseTail.style.display="inherit";this.wrapper.appendChild(this.mouseTail)}this.iframe=document.createElement("iframe");const t=["allow-same-origin"];if(this.config.UNSAFE_replayCanvas){t.push("allow-scripts")}this.iframe.style.display="none";this.iframe.setAttribute("sandbox",t.join(" "));this.disableInteract();this.wrapper.appendChild(this.iframe);if(this.iframe.contentWindow&&this.iframe.contentDocument){Ty(this.iframe.contentWindow,this.iframe.contentDocument);Gm(this.iframe.contentWindow)}}rebuildFullSnapshot(t,e=false){if(!this.iframe.contentDocument){return this.warn("Looks like your replayer has been destroyed.")}if(Object.keys(this.legacy_missingNodeRetryMap).length){this.warn("Found unresolved missing node map",this.legacy_missingNodeRetryMap)}this.legacy_missingNodeRetryMap={};const i=[];const r=new Set;const n=(e,n)=>{if(e.nodeName==="DIALOG")r.add(e);this.collectIframeAndAttachDocument(i,e);if(this.mediaManager.isSupportedMediaElement(e)){const{events:i}=this.service.state.context;this.mediaManager.addMediaElements(e,t.timestamp-i[0].timestamp,this.mirror)}for(const t of this.config.plugins||[]){if(t.onBuild)t.onBuild(e,{id:n,replayer:this})}};if(this.usingVirtualDom){this.virtualDom.destroyTree();this.usingVirtualDom=false}this.mirror.reset();sc(t.data.node,{doc:this.iframe.contentDocument,afterAppend:n,cache:this.cache,mirror:this.mirror});n(this.iframe.contentDocument,t.data.node.id);for(const{mutationInQueue:t,builtNode:e}of i){this.attachDocumentToIframe(t,e);this.newDocumentQueue=this.newDocumentQueue.filter((e=>e!==t))}const{documentElement:s,head:o}=this.iframe.contentDocument;this.insertStyleRules(s,o);r.forEach((t=>nb(t)));if(!this.service.state.matches("playing")){this.iframe.contentDocument.getElementsByTagName("html")[0].classList.add("rrweb-paused")}this.emitter.emit(fg.FullsnapshotRebuilded,t);if(!e){this.waitForStylesheetLoad()}if(this.config.UNSAFE_replayCanvas){void this.preloadAllImages()}}insertStyleRules(t,e){var i;const r=Zy(this.config.blockClass).concat(this.config.insertStyleRules);if(this.config.pauseAnimation){r.push("html.rrweb-paused *, html.rrweb-paused *:before, html.rrweb-paused *:after { animation-play-state: paused !important; }")}if(!r.length){return}if(this.usingVirtualDom){const i=this.virtualDom.createElement("style");this.virtualDom.mirror.add(i,im(i,this.virtualDom.unserializedId));t.insertBefore(i,e);i.rules.push({source:ng.StyleSheetRule,adds:r.map(((t,e)=>({rule:t,index:e})))})}else{const n=document.createElement("style");t.insertBefore(n,e);for(let t=0;t<r.length;t++){(i=n.sheet)==null?void 0:i.insertRule(r[t],t)}}}attachDocumentToIframe(t,e){const i=this.usingVirtualDom?this.virtualDom.mirror:this.mirror;const r=[];const n=new Set;const s=(t,s)=>{if(t.nodeName==="DIALOG")n.add(t);this.collectIframeAndAttachDocument(r,t);const o=i.getMeta(t);if((o==null?void 0:o.type)===lg.Element&&(o==null?void 0:o.tagName.toUpperCase())==="HTML"){const{documentElement:t,head:i}=e.contentDocument;this.insertStyleRules(t,i)}if(this.usingVirtualDom)return;for(const e of this.config.plugins||[]){if(e.onBuild)e.onBuild(t,{id:s,replayer:this})}};ic(t.node,{doc:e.contentDocument,mirror:i,hackCss:true,skipChild:false,afterAppend:s,cache:this.cache});s(e.contentDocument,t.node.id);for(const{mutationInQueue:t,builtNode:e}of r){this.attachDocumentToIframe(t,e);this.newDocumentQueue=this.newDocumentQueue.filter((e=>e!==t))}n.forEach((t=>nb(t)))}collectIframeAndAttachDocument(t,e){if(Vm(e,this.mirror)){const i=this.newDocumentQueue.find((t=>t.parentId===this.mirror.getId(e)));if(i){t.push({mutationInQueue:i,builtNode:e})}}}waitForStylesheetLoad(){var t;const e=(t=this.iframe.contentDocument)==null?void 0:t.head;if(e){const t=new Set;let i;let r=this.service.state;const n=()=>{r=this.service.state};this.emitter.on(fg.Start,n);this.emitter.on(fg.Pause,n);const s=()=>{this.emitter.off(fg.Start,n);this.emitter.off(fg.Pause,n)};e.querySelectorAll('link[rel="stylesheet"]').forEach((e=>{if(!e.sheet){t.add(e);e.addEventListener("load",(()=>{t.delete(e);if(t.size===0&&i!==-1){if(r.matches("playing")){this.play(this.getCurrentTime())}this.emitter.emit(fg.LoadStylesheetEnd);if(i){clearTimeout(i)}s()}}))}}));if(t.size>0){this.service.send({type:"PAUSE"});this.emitter.emit(fg.LoadStylesheetStart);i=setTimeout((()=>{if(r.matches("playing")){this.play(this.getCurrentTime())}i=-1;s()}),this.config.loadTimeout)}}}async preloadAllImages(){const t=[];for(const e of this.service.state.context.events){if(e.type===rg.IncrementalSnapshot&&e.data.source===ng.CanvasMutation){t.push(this.deserializeAndPreloadCanvasEvents(e.data,e));const i="commands"in e.data?e.data.commands:[e.data];i.forEach((t=>{this.preloadImages(t,e)}))}}return Promise.all(t)}preloadImages(t,e){if(t.property==="drawImage"&&typeof t.args[0]==="string"&&!this.imageMap.has(e)){const t=document.createElement("canvas");const e=t.getContext("2d");const i=e==null?void 0:e.createImageData(t.width,t.height);e==null?void 0:e.putImageData(i,0,0)}}async deserializeAndPreloadCanvasEvents(t,e){if(!this.canvasEventMap.has(e)){const i={isUnchanged:true};if("commands"in t){const r=await Promise.all(t.commands.map((async t=>{const e=await Promise.all(t.args.map(Jy(this.imageMap,null,i)));return{...t,args:e}})));if(i.isUnchanged===false)this.canvasEventMap.set(e,{...t,commands:r})}else{const r=await Promise.all(t.args.map(Jy(this.imageMap,null,i)));if(i.isUnchanged===false)this.canvasEventMap.set(e,{...t,args:r})}}}applyIncremental(t,e){var i,r,n;const{data:s}=t;switch(s.source){case ng.Mutation:{try{this.applyMutation(s,e)}catch(t){this.warn(`Exception in mutation ${t.message||t}`,s)}break}case ng.Drag:case ng.TouchMove:case ng.MouseMove:if(e){const t=s.positions[s.positions.length-1];this.mousePos={x:t.x,y:t.y,id:t.id,debugData:s}}else{s.positions.forEach((i=>{const r={doAction:()=>{this.moveAndHover(i.x,i.y,i.id,e,s)},delay:i.timeOffset+t.timestamp-this.service.state.context.baselineTime};this.timer.addAction(r)}));this.timer.addAction({doAction(){},delay:t.delay-((i=s.positions[0])==null?void 0:i.timeOffset)})}break;case ng.MouseInteraction:{if(s.id===-1){break}const t=new Event(ro(sg[s.type]));const i=this.mirror.getNode(s.id);if(!i){return this.debugNodeNotFound(s,s.id)}this.emitter.emit(fg.MouseInteraction,{type:s.type,target:i});const{triggerFocus:r}=this.config;switch(s.type){case sg.Blur:if("blur"in i){i.blur()}break;case sg.Focus:if(r&&i.focus){i.focus({preventScroll:true})}break;case sg.Click:case sg.TouchStart:case sg.TouchEnd:case sg.MouseDown:case sg.MouseUp:if(e){if(s.type===sg.TouchStart){this.touchActive=true}else if(s.type===sg.TouchEnd){this.touchActive=false}if(s.type===sg.MouseDown){this.lastMouseDownEvent=[i,t]}else if(s.type===sg.MouseUp){this.lastMouseDownEvent=null}this.mousePos={x:s.x||0,y:s.y||0,id:s.id,debugData:s}}else{if(s.type===sg.TouchStart){this.tailPositions.length=0}this.moveAndHover(s.x||0,s.y||0,s.id,e,s);if(s.type===sg.Click){this.mouse.classList.remove("active");this.mouse.classList.add("active")}else if(s.type===sg.TouchStart){this.mouse.classList.add("touch-active")}else if(s.type===sg.TouchEnd){this.mouse.classList.remove("touch-active")}else{i.dispatchEvent(t)}}break;case sg.TouchCancel:if(e){this.touchActive=false}else{this.mouse.classList.remove("touch-active")}break;default:i.dispatchEvent(t)}break}case ng.Scroll:{if(s.id===-1){break}if(this.usingVirtualDom){const t=this.virtualDom.mirror.getNode(s.id);if(!t){return this.debugNodeNotFound(s,s.id)}t.scrollData=s;break}this.applyScroll(s,e);break}case ng.ViewportResize:this.emitter.emit(fg.Resize,{width:s.width,height:s.height});break;case ng.Input:{if(s.id===-1){break}if(this.usingVirtualDom){const t=this.virtualDom.mirror.getNode(s.id);if(!t){return this.debugNodeNotFound(s,s.id)}t.inputData=s;break}this.applyInput(s);break}case ng.MediaInteraction:{const e=this.usingVirtualDom?this.virtualDom.mirror.getNode(s.id):this.mirror.getNode(s.id);if(!e){return this.debugNodeNotFound(s,s.id)}const i=e;const{events:r}=this.service.state.context;this.mediaManager.mediaMutation({target:i,timeOffset:t.timestamp-r[0].timestamp,mutation:s});break}case ng.StyleSheetRule:case ng.StyleDeclaration:{if(this.usingVirtualDom){if(s.styleId)this.constructedStyleMutations.push(s);else if(s.id)(r=this.virtualDom.mirror.getNode(s.id))==null?void 0:r.rules.push(s)}else this.applyStyleSheetMutation(s);break}case ng.CanvasMutation:{if(!this.config.UNSAFE_replayCanvas){return}if(this.usingVirtualDom){const e=this.virtualDom.mirror.getNode(s.id);if(!e){return this.debugNodeNotFound(s,s.id)}e.canvasMutations.push({event:t,mutation:s})}else{const e=this.mirror.getNode(s.id);if(!e){return this.debugNodeNotFound(s,s.id)}void ib({event:t,mutation:s,target:e,imageMap:this.imageMap,canvasEventMap:this.canvasEventMap,errorHandler:this.warnCanvasMutationFailed.bind(this)})}break}case ng.Font:{try{const t=new FontFace(s.family,s.buffer?new Uint8Array(JSON.parse(s.fontSource)):s.fontSource,s.descriptors);(n=this.iframe.contentDocument)==null?void 0:n.fonts.add(t)}catch(t){this.warn(t)}break}case ng.Selection:{if(e){this.lastSelectionData=s;break}this.applySelection(s);break}case ng.AdoptedStyleSheet:{if(this.usingVirtualDom)this.adoptedStyleSheets.push(s);else this.applyAdoptedStyleSheet(s);break}}}applyMutation(t,e){if(this.config.useVirtualDom&&!this.usingVirtualDom&&e){this.usingVirtualDom=true;Qp(this.iframe.contentDocument,this.mirror,this.virtualDom);if(Object.keys(this.legacy_missingNodeRetryMap).length){for(const t in this.legacy_missingNodeRetryMap){try{const e=this.legacy_missingNodeRetryMap[t];const i=qp(e.node,this.virtualDom,this.mirror);if(i)e.node=i}catch(t){this.warn(t)}}}}const i=this.usingVirtualDom?this.virtualDom.mirror:this.mirror;t.removes=t.removes.filter((e=>{if(!i.getNode(e.id)){this.warnNodeNotFound(t,e.id);return false}return true}));t.removes.forEach((e=>{var r;const n=i.getNode(e.id);if(!n){return}let s=i.getNode(e.parentId);if(!s){return this.warnNodeNotFound(t,e.parentId)}if(e.isShadow&&Zm(s)){s=s.shadowRoot}i.removeNodeFromMap(n);if(s)try{s.removeChild(n);if(this.usingVirtualDom&&n.nodeName==="#text"&&s.nodeName==="STYLE"&&((r=s.rules)==null?void 0:r.length)>0)s.rules=[]}catch(e){if(e instanceof DOMException){this.warn("parent could not remove child in mutation",s,n,t)}else{throw e}}}));const r={...this.legacy_missingNodeRetryMap};const n=[];const s=t=>{let e=null;if(t.nextId){e=i.getNode(t.nextId)}if(t.nextId!==null&&t.nextId!==void 0&&t.nextId!==-1&&!e){return true}return false};const o=t=>{var e,o;if(!this.iframe.contentDocument){return this.warn("Looks like your replayer has been destroyed.")}let a=i.getNode(t.parentId);if(!a){if(t.node.type===lg.Document){return this.newDocumentQueue.push(t)}return n.push(t)}if(t.node.isShadow){if(!Zm(a)){a.attachShadow({mode:"open"});a=a.shadowRoot}else a=a.shadowRoot}let u=null;let f=null;if(t.previousId){u=i.getNode(t.previousId)}if(t.nextId){f=i.getNode(t.nextId)}if(s(t)){return n.push(t)}if(t.node.rootId&&!i.getNode(t.node.rootId)){return}const l=t.node.rootId?i.getNode(t.node.rootId):this.usingVirtualDom?this.virtualDom:this.iframe.contentDocument;if(Vm(a,i)){this.attachDocumentToIframe(t,a);return}const c=(t,e)=>{if(this.usingVirtualDom)return;nb(t);for(const i of this.config.plugins||[]){if(i.onBuild)i.onBuild(t,{id:e,replayer:this})}};const h=ic(t.node,{doc:l,mirror:i,skipChild:true,hackCss:true,cache:this.cache,afterAppend:c});if(t.previousId===-1||t.nextId===-1){r[t.node.id]={node:h,mutation:t};return}const d=i.getMeta(a);if(d&&d.type===lg.Element&&t.node.type===lg.Text){const t=Array.isArray(a.childNodes)?a.childNodes:Array.from(a.childNodes);if(d.tagName==="textarea"){for(const e of t){if(e.nodeType===a.TEXT_NODE){a.removeChild(e)}}}else if(d.tagName==="style"&&t.length===1){for(const e of t){if(e.nodeType===a.TEXT_NODE&&!i.hasNode(e)){h.textContent=e.textContent;a.removeChild(e)}}}}else if((d==null?void 0:d.type)===lg.Document){const i=a;if(t.node.type===lg.DocumentType&&((e=i.childNodes[0])==null?void 0:e.nodeType)===Node.DOCUMENT_TYPE_NODE)i.removeChild(i.childNodes[0]);if(h.nodeName==="HTML"&&i.documentElement)i.removeChild(i.documentElement)}if(u&&u.nextSibling&&u.nextSibling.parentNode){a.insertBefore(h,u.nextSibling)}else if(f&&f.parentNode){a.contains(f)?a.insertBefore(h,f):a.insertBefore(h,null)}else{a.appendChild(h)}c(h,t.node.id);if(this.usingVirtualDom&&h.nodeName==="#text"&&a.nodeName==="STYLE"&&((o=a.rules)==null?void 0:o.length)>0)a.rules=[];if(Vm(h,this.mirror)){const t=this.mirror.getId(h);const e=this.newDocumentQueue.find((e=>e.parentId===t));if(e){this.attachDocumentToIframe(e,h);this.newDocumentQueue=this.newDocumentQueue.filter((t=>t!==e))}}if(t.previousId||t.nextId){this.legacy_resolveMissingNode(r,a,h,t)}};t.adds.forEach((t=>{o(t)}));const a=Date.now();while(n.length){const t=Wm(n);n.length=0;if(Date.now()-a>500){this.warn("Timeout in the loop, please check the resolve tree data:",t);break}for(const e of t){const t=i.getNode(e.value.parentId);if(!t){this.debug("Drop resolve tree since there is no parent for the root node.",e)}else{zm(e,(t=>{o(t)}))}}}if(Object.keys(r).length){Object.assign(this.legacy_missingNodeRetryMap,r)}Jm(t.texts).forEach((e=>{var r;const n=i.getNode(e.id);if(!n){if(t.removes.find((t=>t.id===e.id))){return}return this.warnNodeNotFound(t,e.id)}const s=n.parentElement;if(e.value&&s&&s.tagName==="STYLE"){n.textContent=Kl(e.value,this.cache)}else{n.textContent=e.value}if(this.usingVirtualDom){const t=n.parentNode;if(((r=t==null?void 0:t.rules)==null?void 0:r.length)>0)t.rules=[]}}));t.attributes.forEach((e=>{var r;const n=i.getNode(e.id);if(!n){if(t.removes.find((t=>t.id===e.id))){return}return this.warnNodeNotFound(t,e.id)}for(const t in e.attributes){if(typeof t==="string"){const s=e.attributes[t];if(s===null){n.removeAttribute(t);if(t==="open")sb(n,e)}else if(typeof s==="string"){try{if(t==="_cssText"&&(n.nodeName==="LINK"||n.nodeName==="STYLE")){try{const t=i.getMeta(n);Object.assign(t.attributes,e.attributes);const r=ic(t,{doc:n.ownerDocument,mirror:i,skipChild:true,hackCss:true,cache:this.cache});const s=n.nextSibling;const o=n.parentNode;if(r&&o){o.removeChild(n);o.insertBefore(r,s);i.replace(e.id,r);break}}catch(t){}}if(t==="value"&&n.nodeName==="TEXTAREA"){const t=n;t.childNodes.forEach((e=>t.removeChild(e)));const e=(r=n.ownerDocument)==null?void 0:r.createTextNode(s);if(e){t.appendChild(e)}}else{n.setAttribute(t,s)}if(t==="rr_open_mode"&&n.nodeName==="DIALOG"){nb(n,e)}}catch(t){this.warn("An error occurred may due to the checkout feature.",t)}}else if(t==="style"){const t=s;const e=n;for(const i in t){if(t[i]===false){e.style.removeProperty(i)}else if(t[i]instanceof Array){const r=t[i];e.style.setProperty(i,r[0],r[1])}else{const r=t[i];e.style.setProperty(i,r)}}}}}}))}applyScroll(t,e){var i,r;const n=this.mirror.getNode(t.id);if(!n){return this.debugNodeNotFound(t,t.id)}const s=this.mirror.getMeta(n);if(n===this.iframe.contentDocument){(i=this.iframe.contentWindow)==null?void 0:i.scrollTo({top:t.y,left:t.x,behavior:e?"auto":"smooth"})}else if((s==null?void 0:s.type)===lg.Document){(r=n.defaultView)==null?void 0:r.scrollTo({top:t.y,left:t.x,behavior:e?"auto":"smooth"})}else{try{n.scrollTo({top:t.y,left:t.x,behavior:e?"auto":"smooth"})}catch(t){}}}applyInput(t){const e=this.mirror.getNode(t.id);if(!e){return this.debugNodeNotFound(t,t.id)}try{e.checked=t.isChecked;e.value=t.text}catch(t){}}applySelection(t){try{const e=new Set;const i=t.ranges.map((({start:t,startOffset:i,end:r,endOffset:n})=>{const s=this.mirror.getNode(t);const o=this.mirror.getNode(r);if(!s||!o)return;const a=new Range;a.setStart(s,i);a.setEnd(o,n);const u=s.ownerDocument;const f=u==null?void 0:u.getSelection();f&&e.add(f);return{range:a,selection:f}}));e.forEach((t=>t.removeAllRanges()));i.forEach((t=>{var e;return t&&((e=t.selection)==null?void 0:e.addRange(t.range))}))}catch(t){}}applyStyleSheetMutation(t){var e;let i=null;if(t.styleId)i=this.styleMirror.getStyle(t.styleId);else if(t.id)i=((e=this.mirror.getNode(t.id))==null?void 0:e.sheet)||null;if(!i)return;if(t.source===ng.StyleSheetRule)this.applyStyleSheetRule(t,i);else if(t.source===ng.StyleDeclaration)this.applyStyleDeclaration(t,i)}applyStyleSheetRule(t,e){var i,r,n,s;(i=t.adds)==null?void 0:i.forEach((({rule:t,index:i})=>{try{if(Array.isArray(i)){const{positions:r,index:n}=$m(i);const s=Xm(e.cssRules,r);s.insertRule(t,n)}else{const r=i===void 0?void 0:Math.min(i,e.cssRules.length);e==null?void 0:e.insertRule(t,r)}}catch(t){}}));(r=t.removes)==null?void 0:r.forEach((({index:t})=>{try{if(Array.isArray(t)){const{positions:i,index:r}=$m(t);const n=Xm(e.cssRules,i);n.deleteRule(r||0)}else{e==null?void 0:e.deleteRule(t)}}catch(t){}}));if(t.replace)try{void((n=e.replace)==null?void 0:n.call(e,t.replace))}catch(t){}if(t.replaceSync)try{(s=e.replaceSync)==null?void 0:s.call(e,t.replaceSync)}catch(t){}}applyStyleDeclaration(t,e){if(t.set){const i=Xm(e.rules,t.index);i.style.setProperty(t.set.property,t.set.value,t.set.priority)}if(t.remove){const i=Xm(e.rules,t.index);i.style.removeProperty(t.remove.property)}}applyAdoptedStyleSheet(t){var e;const i=this.mirror.getNode(t.id);if(!i)return;(e=t.styles)==null?void 0:e.forEach((t=>{var e;let r=null;let n=null;if(Zm(i))n=((e=i.ownerDocument)==null?void 0:e.defaultView)||null;else if(i.nodeName==="#document")n=i.defaultView;if(!n)return;try{r=new n.CSSStyleSheet;this.styleMirror.add(r,t.styleId);this.applyStyleSheetRule({source:ng.StyleSheetRule,adds:t.rules},r)}catch(t){}}));const r=10;let n=0;const s=(t,e)=>{const i=e.map((t=>this.styleMirror.getStyle(t))).filter((t=>t!==null));if(Zm(t))t.shadowRoot.adoptedStyleSheets=i;else if(t.nodeName==="#document")t.adoptedStyleSheets=i;if(i.length!==e.length&&n<r){setTimeout((()=>s(t,e)),0+100*n);n++}};s(i,t.styleIds)}legacy_resolveMissingNode(t,e,i,r){const{previousId:n,nextId:s}=r;const o=n&&t[n];const a=s&&t[s];if(o){const{node:r,mutation:n}=o;e.insertBefore(r,i);delete t[n.node.id];delete this.legacy_missingNodeRetryMap[n.node.id];if(n.previousId||n.nextId){this.legacy_resolveMissingNode(t,e,r,n)}}if(a){const{node:r,mutation:n}=a;e.insertBefore(r,i.nextSibling);delete t[n.node.id];delete this.legacy_missingNodeRetryMap[n.node.id];if(n.previousId||n.nextId){this.legacy_resolveMissingNode(t,e,r,n)}}}moveAndHover(t,e,i,r,n){const s=this.mirror.getNode(i);if(!s){return this.debugNodeNotFound(n,i)}const o=Ym(s,this.iframe);const a=t*o.absoluteScale+o.x;const u=e*o.absoluteScale+o.y;this.mouse.style.left=`${a}px`;this.mouse.style.top=`${u}px`;if(!r){this.drawMouseTail({x:a,y:u})}this.hoverElements(s)}drawMouseTail(t){if(!this.mouseTail){return}const{lineCap:e,lineWidth:i,strokeStyle:r,duration:n}=this.config.mouseTail===true?fb:Object.assign({},fb,this.config.mouseTail);const s=()=>{if(!this.mouseTail){return}const t=this.mouseTail.getContext("2d");if(!t||!this.tailPositions.length){return}t.clearRect(0,0,this.mouseTail.width,this.mouseTail.height);t.beginPath();t.lineWidth=i;t.lineCap=e;t.strokeStyle=r;t.moveTo(this.tailPositions[0].x,this.tailPositions[0].y);this.tailPositions.forEach((e=>t.lineTo(e.x,e.y)));t.stroke()};this.tailPositions.push(t);s();setTimeout((()=>{this.tailPositions=this.tailPositions.filter((e=>e!==t));s()}),n/this.speedService.state.context.timer.speed)}hoverElements(t){var e;(e=this.lastHoveredRootNode||this.iframe.contentDocument)==null?void 0:e.querySelectorAll(".\\:hover").forEach((t=>{t.classList.remove(":hover")}));this.lastHoveredRootNode=t.getRootNode();let i=t;while(i){if(i.classList){i.classList.add(":hover")}i=i.parentElement}}isUserInteraction(t){if(t.type!==rg.IncrementalSnapshot){return false}return t.data.source>ng.Mutation&&t.data.source<=ng.Input}backToNormal(){this.nextUserInteractionEvent=null;if(this.speedService.state.matches("normal")){return}this.speedService.send({type:"BACK_TO_NORMAL"});this.emitter.emit(fg.SkipEnd,{speed:this.speedService.state.context.normalSpeed})}warnNodeNotFound(t,e){this.warn(`Node with id '${e}' not found. `,t)}warnCanvasMutationFailed(t,e){this.warn(`Has error on canvas update`,e,"canvas mutation:",t)}debugNodeNotFound(t,e){this.debug(`Node with id '${e}' not found. `,t)}warn(...t){if(!this.config.showWarning){return}this.config.logger.warn(ub,...t)}debug(...t){if(!this.config.showDebug){return}this.config.logger.log(ub,...t)}}const{addCustomEvent:hb}=Iy;const{freezePage:db}=Iy;const{takeFullSnapshot:vb}=Iy;var pb=Object.freeze({__proto__:null,EventType:rg,IncrementalSource:ng,MouseInteractions:sg,Replayer:cb,ReplayerEvents:fg,addCustomEvent:hb,canvasMutation:ib,freezePage:db,get mirror(){return Am},record:Iy,takeFullSnapshot:vb,utils:ig});var mb=i(pb);var gb={};var yb;function bb(){if(yb)return gb;yb=1;Object.defineProperty(gb,"__esModule",{value:true});gb.RecorderEvents=void 0;var t=Gi();class e{#I=new t.EventBuffer(Infinity);cycleTimestamp=Date.now();hasSnapshot=false;hasMeta=false;hasError=false;constructor(t=true){this.inlinedAllStylesheets=t}add(t){this.#I.add(t)}get events(){return this.#I.get()}get payloadBytesEstimation(){return this.#I.byteSize()}}gb.RecorderEvents=e;return gb}var wb;function Sb(){if(wb)return ds;wb=1;Object.defineProperty(ds,"__esModule",{value:true});ds.Recorder=void 0;var t=mb;var e=dt();var i=ve();var r=bb();var n=bt();var s=hs();var o=R();var a=Ut();var u=B();var f=st();var l=Bi();var c=$i();var h=Q();var d=ut();class v{#I;#O;#A;#T=false;#_=(0,d.single)((()=>(0,h.warn)(47)));constructor(t){this.parent=t;this.shouldFix=this.parent.agentRef.init.session_replay.fix_stylesheets;this.#I=new r.RecorderEvents(this.shouldFix);this.#O=new r.RecorderEvents(this.shouldFix);this.#A=[new r.RecorderEvents(this.shouldFix)];this.recording=false;this.currentBufferTarget=this.#I;this.hasSeenSnapshot=false;this.lastMeta=false;this.stopRecording=()=>{}}getEvents(){if(this.#A[0]?.events.length){return{...this.#A[0],events:this.#A[0].events,payloadBytesEstimation:this.#A[0].payloadBytesEstimation,type:"preloaded"}}return{events:[...this.#O.events,...this.#I.events].filter((t=>t)),type:"standard",cycleTimestamp:Math.min(this.#O.cycleTimestamp,this.#I.cycleTimestamp),payloadBytesEstimation:this.#O.payloadBytesEstimation+this.#I.payloadBytesEstimation,hasError:this.#O.hasError||this.#I.hasError,hasMeta:this.#O.hasMeta||this.#I.hasMeta,hasSnapshot:this.#O.hasSnapshot||this.#I.hasSnapshot,inlinedAllStylesheets:!!this.#O.events.length&&this.#O.inlinedAllStylesheets||this.#I.inlinedAllStylesheets}}clearBuffer(){if(this.#A[0]?.events.length)this.#A.shift();else if(this.parent.mode===n.MODE.ERROR)this.#O=this.#I;else this.#O=new r.RecorderEvents(this.shouldFix);this.#I=new r.RecorderEvents(this.shouldFix)}startRecording(){this.recording=true;const{block_class:e,ignore_class:r,mask_text_class:n,block_selector:s,mask_input_options:o,mask_text_selector:a,mask_all_inputs:u,inline_images:l,collect_fonts:c}=this.parent.agentRef.init.session_replay;const h=(0,t.record)({emit:this.audit.bind(this),blockClass:e,ignoreClass:r,maskTextClass:n,blockSelector:s,maskInputOptions:o,maskTextSelector:a,maskTextFn:f.customMasker,maskAllInputs:u,maskInputFn:f.customMasker,inlineStylesheet:true,inlineImages:l,collectFonts:c,checkoutEveryNms:i.CHECKOUT_MS[this.parent.mode],recordAfter:"DOMContentLoaded"});this.stopRecording=()=>{this.recording=false;this.notified=false;this.parent.ee.emit(i.SR_EVENT_EMITTER_TYPES.REPLAY_RUNNING,[false,this.parent.mode]);h?.()}}audit(t,e){const r=this.parent.agentRef.init.session_replay.fix_stylesheets?s.stylesheetEvaluator.evaluate():0;const n="SessionReplay/Payload/Missing-Inline-Css/";if(!this.shouldFix){if(r>0){this.currentBufferTarget.inlinedAllStylesheets=false;this.#_();(0,o.handle)(a.SUPPORTABILITY_METRIC_CHANNEL,[n+"Skipped",r],undefined,u.FEATURE_NAMES.metrics,this.parent.ee)}return this.store(t,e)}if(!r&&this.#T&&t.type===i.RRWEB_EVENT_TYPES.Meta)this.#T=false;if(r>0){s.stylesheetEvaluator.fix().then((t=>{if(t>0){this.currentBufferTarget.inlinedAllStylesheets=false;this.shouldFix=false}(0,o.handle)(a.SUPPORTABILITY_METRIC_CHANNEL,[n+"Failed",t],undefined,u.FEATURE_NAMES.metrics,this.parent.ee);(0,o.handle)(a.SUPPORTABILITY_METRIC_CHANNEL,[n+"Fixed",r-t],undefined,u.FEATURE_NAMES.metrics,this.parent.ee);this.takeFullSnapshot()}));if(t.type===i.RRWEB_EVENT_TYPES.FullSnapshot||t.type===i.RRWEB_EVENT_TYPES.Meta)this.#T=true}if(!this.#T)this.store(t,e)}store(t,s){if(!t)return;if(this.parent.agentRef.runtime?.session?.isAfterSessionExpiry(t.timestamp)){(0,o.handle)(a.SUPPORTABILITY_METRIC_CHANNEL,["Session/Expired/SessionReplay/Seen"],undefined,u.FEATURE_NAMES.metrics,this.ee);return}if(!(this.parent instanceof c.AggregateBase)&&this.#A.length)this.currentBufferTarget=this.#A[this.#A.length-1];else this.currentBufferTarget=this.#I;if(this.parent.blocked)return;if(!this.notified){this.parent.ee.emit(i.SR_EVENT_EMITTER_TYPES.REPLAY_RUNNING,[true,this.parent.mode]);this.notified=true}if(this.parent.timeKeeper?.ready&&!t.__newrelic){t.__newrelic=(0,f.buildNRMetaNode)(t.timestamp,this.parent.timeKeeper);t.timestamp=this.parent.timeKeeper.correctAbsoluteTimestamp(t.timestamp)}t.__serialized=(0,e.stringify)(t);const h=t.__serialized.length;const d=this.getPayloadSize(h);if(this.parent.mode===n.MODE.ERROR&&s&&t.type===i.RRWEB_EVENT_TYPES.Meta){this.clearBuffer()}if(t.type===i.RRWEB_EVENT_TYPES.Meta){this.currentBufferTarget.hasMeta=true}if(t.type===i.RRWEB_EVENT_TYPES.FullSnapshot){this.currentBufferTarget.hasSnapshot=true;this.hasSeenSnapshot=true}this.currentBufferTarget.add(t);if((t.type===i.RRWEB_EVENT_TYPES.FullSnapshot&&this.currentBufferTarget.hasMeta||d>l.IDEAL_PAYLOAD_SIZE)&&this.parent.mode===n.MODE.FULL){if(this.parent instanceof c.AggregateBase){this.parent.agentRef.runtime.harvester.triggerHarvestFor(this.parent)}else{this.#A.push(new r.RecorderEvents(this.shouldFix))}}}takeFullSnapshot(){try{if(!this.recording)return;t.record.takeFullSnapshot()}catch(t){}}clearTimestamps(){this.currentBufferTarget.cycleTimestamp=undefined}getPayloadSize(t=0){return this.estimateCompression(this.currentBufferTarget.payloadBytesEstimation+t)+i.QUERY_PARAM_PADDING}estimateCompression(t){if(!!this.parent.gzipper&&!!this.parent.u8)return t*i.AVG_COMPRESSION;return t}}ds.Recorder=v;return ds}var kb={};var Cb=function(t,e,i,r,n){var s=new Worker(kb[e]||(kb[e]=URL.createObjectURL(new Blob([t+';addEventListener("error",function(e){e=e.error;postMessage({$e$:[e.message,e.code,e.stack]})})'],{type:"text/javascript"}))));s.onmessage=function(t){var e=t.data,i=e.S;if(i){var r=new Error(i[0]);r["code"]=i[1];r.stack=i[2];n(r,null)}else n(null,e)};s.postMessage(i,r);return s};var Mb=Uint8Array,Ib=Uint16Array,Ob=Int32Array;var Ab=new Mb([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]);var Tb=new Mb([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]);var _b=new Mb([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]);var Eb=function(t,e){var i=new Ib(31);for(var r=0;r<31;++r){i[r]=e+=1<<t[r-1]}var n=new Ob(i[30]);for(var r=1;r<30;++r){for(var s=i[r];s<i[r+1];++s){n[s]=s-i[r]<<5|r}}return{b:i,r:n}};var Rb=Eb(Ab,2),xb=Rb.b,jb=Rb.r;xb[28]=258,jb[258]=28;var Nb=Eb(Tb,0),Db=Nb.b,Fb=Nb.r;var Lb=new Ib(32768);for(var Pb=0;Pb<32768;++Pb){var Bb=(Pb&43690)>>1|(Pb&21845)<<1;Bb=(Bb&52428)>>2|(Bb&13107)<<2;Bb=(Bb&61680)>>4|(Bb&3855)<<4;Lb[Pb]=((Bb&65280)>>8|(Bb&255)<<8)>>1}var Ub=function(t,e,i){var r=t.length;var n=0;var s=new Ib(e);for(;n<r;++n){if(t[n])++s[t[n]-1]}var o=new Ib(e);for(n=1;n<e;++n){o[n]=o[n-1]+s[n-1]<<1}var a;if(i){a=new Ib(1<<e);var u=15-e;for(n=0;n<r;++n){if(t[n]){var f=n<<4|t[n];var l=e-t[n];var c=o[t[n]-1]++<<l;for(var h=c|(1<<l)-1;c<=h;++c){a[Lb[c]>>u]=f}}}}else{a=new Ib(r);for(n=0;n<r;++n){if(t[n]){a[n]=Lb[o[t[n]-1]++]>>15-t[n]}}}return a};var Gb=new Mb(288);for(var Pb=0;Pb<144;++Pb)Gb[Pb]=8;for(var Pb=144;Pb<256;++Pb)Gb[Pb]=9;for(var Pb=256;Pb<280;++Pb)Gb[Pb]=7;for(var Pb=280;Pb<288;++Pb)Gb[Pb]=8;var Wb=new Mb(32);for(var Pb=0;Pb<32;++Pb)Wb[Pb]=5;var zb=Ub(Gb,9,0),Vb=Ub(Gb,9,1);var Hb=Ub(Wb,5,0),Yb=Ub(Wb,5,1);var Zb=function(t){var e=t[0];for(var i=1;i<t.length;++i){if(t[i]>e)e=t[i]}return e};var Xb=function(t,e,i){var r=e/8|0;return(t[r]|t[r+1]<<8)>>(e&7)&i};var $b=function(t,e){var i=e/8|0;return(t[i]|t[i+1]<<8|t[i+2]<<16)>>(e&7)};var Jb=function(t){return(t+7)/8|0};var Kb=function(t,e,i){if(e==null||e<0)e=0;if(i==null||i>t.length)i=t.length;return new Mb(t.subarray(e,i))};var qb={UnexpectedEOF:0,InvalidBlockType:1,InvalidLengthLiteral:2,InvalidDistance:3,StreamFinished:4,NoStreamHandler:5,InvalidHeader:6,NoCallback:7,InvalidUTF8:8,ExtraFieldTooLong:9,InvalidDate:10,FilenameTooLong:11,StreamFinishing:12,InvalidZipData:13,UnknownCompressionMethod:14};var Qb=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"];var tw=function(t,e,i){var r=new Error(e||Qb[t]);r.code=t;if(Error.captureStackTrace)Error.captureStackTrace(r,tw);if(!i)throw r;return r};var ew=function(t,e,i,r){var n=t.length,s=r?r.length:0;if(!n||e.f&&!e.l)return i||new Mb(0);var o=!i;var a=o||e.i!=2;var u=e.i;if(o)i=new Mb(n*3);var f=function(t){var e=i.length;if(t>e){var r=new Mb(Math.max(e*2,t));r.set(i);i=r}};var l=e.f||0,c=e.p||0,h=e.b||0,d=e.l,v=e.d,p=e.m,m=e.n;var g=n*8;do{if(!d){l=Xb(t,c,1);var y=Xb(t,c+1,3);c+=3;if(!y){var b=Jb(c)+4,w=t[b-4]|t[b-3]<<8,S=b+w;if(S>n){if(u)tw(0);break}if(a)f(h+w);i.set(t.subarray(b,S),h);e.b=h+=w,e.p=c=S*8,e.f=l;continue}else if(y==1)d=Vb,v=Yb,p=9,m=5;else if(y==2){var k=Xb(t,c,31)+257,C=Xb(t,c+10,15)+4;var M=k+Xb(t,c+5,31)+1;c+=14;var I=new Mb(M);var O=new Mb(19);for(var A=0;A<C;++A){O[_b[A]]=Xb(t,c+A*3,7)}c+=C*3;var T=Zb(O),_=(1<<T)-1;var E=Ub(O,T,1);for(var A=0;A<M;){var R=E[Xb(t,c,_)];c+=R&15;var b=R>>4;if(b<16){I[A++]=b}else{var x=0,j=0;if(b==16)j=3+Xb(t,c,3),c+=2,x=I[A-1];else if(b==17)j=3+Xb(t,c,7),c+=3;else if(b==18)j=11+Xb(t,c,127),c+=7;while(j--)I[A++]=x}}var N=I.subarray(0,k),D=I.subarray(k);p=Zb(N);m=Zb(D);d=Ub(N,p,1);v=Ub(D,m,1)}else tw(1);if(c>g){if(u)tw(0);break}}if(a)f(h+131072);var F=(1<<p)-1,L=(1<<m)-1;var P=c;for(;;P=c){var x=d[$b(t,c)&F],B=x>>4;c+=x&15;if(c>g){if(u)tw(0);break}if(!x)tw(2);if(B<256)i[h++]=B;else if(B==256){P=c,d=null;break}else{var U=B-254;if(B>264){var A=B-257,G=Ab[A];U=Xb(t,c,(1<<G)-1)+xb[A];c+=G}var W=v[$b(t,c)&L],z=W>>4;if(!W)tw(3);c+=W&15;var D=Db[z];if(z>3){var G=Tb[z];D+=$b(t,c)&(1<<G)-1,c+=G}if(c>g){if(u)tw(0);break}if(a)f(h+131072);var V=h+U;if(h<D){var H=s-D,Y=Math.min(D,V);if(H+h<0)tw(3);for(;h<Y;++h)i[h]=r[H+h]}for(;h<V;++h)i[h]=i[h-D]}}e.l=d,e.p=P,e.b=h,e.f=l;if(d)l=1,e.m=p,e.d=v,e.n=m}while(!l);return h!=i.length&&o?Kb(i,0,h):i.subarray(0,h)};var iw=function(t,e,i){i<<=e&7;var r=e/8|0;t[r]|=i;t[r+1]|=i>>8};var rw=function(t,e,i){i<<=e&7;var r=e/8|0;t[r]|=i;t[r+1]|=i>>8;t[r+2]|=i>>16};var nw=function(t,e){var i=[];for(var r=0;r<t.length;++r){if(t[r])i.push({s:r,f:t[r]})}var n=i.length;var s=i.slice();if(!n)return{t:cw,l:0};if(n==1){var o=new Mb(i[0].s+1);o[i[0].s]=1;return{t:o,l:1}}i.sort((function(t,e){return t.f-e.f}));i.push({s:-1,f:25001});var a=i[0],u=i[1],f=0,l=1,c=2;i[0]={s:-1,f:a.f+u.f,l:a,r:u};while(l!=n-1){a=i[i[f].f<i[c].f?f++:c++];u=i[f!=l&&i[f].f<i[c].f?f++:c++];i[l++]={s:-1,f:a.f+u.f,l:a,r:u}}var h=s[0].s;for(var r=1;r<n;++r){if(s[r].s>h)h=s[r].s}var d=new Ib(h+1);var v=sw(i[l-1],d,0);if(v>e){var r=0,p=0;var m=v-e,g=1<<m;s.sort((function(t,e){return d[e.s]-d[t.s]||t.f-e.f}));for(;r<n;++r){var y=s[r].s;if(d[y]>e){p+=g-(1<<v-d[y]);d[y]=e}else break}p>>=m;while(p>0){var b=s[r].s;if(d[b]<e)p-=1<<e-d[b]++-1;else++r}for(;r>=0&&p;--r){var w=s[r].s;if(d[w]==e){--d[w];++p}}v=e}return{t:new Mb(d),l:v}};var sw=function(t,e,i){return t.s==-1?Math.max(sw(t.l,e,i+1),sw(t.r,e,i+1)):e[t.s]=i};var ow=function(t){var e=t.length;while(e&&!t[--e]);var i=new Ib(++e);var r=0,n=t[0],s=1;var o=function(t){i[r++]=t};for(var a=1;a<=e;++a){if(t[a]==n&&a!=e)++s;else{if(!n&&s>2){for(;s>138;s-=138)o(32754);if(s>2){o(s>10?s-11<<5|28690:s-3<<5|12305);s=0}}else if(s>3){o(n),--s;for(;s>6;s-=6)o(8304);if(s>2)o(s-3<<5|8208),s=0}while(s--)o(n);s=1;n=t[a]}}return{c:i.subarray(0,r),n:e}};var aw=function(t,e){var i=0;for(var r=0;r<e.length;++r)i+=t[r]*e[r];return i};var uw=function(t,e,i){var r=i.length;var n=Jb(e+2);t[n]=r&255;t[n+1]=r>>8;t[n+2]=t[n]^255;t[n+3]=t[n+1]^255;for(var s=0;s<r;++s)t[n+s+4]=i[s];return(n+4+r)*8};var fw=function(t,e,i,r,n,s,o,a,u,f,l){iw(e,l++,i);++n[256];var c=nw(n,15),h=c.t,d=c.l;var v=nw(s,15),p=v.t,m=v.l;var g=ow(h),y=g.c,b=g.n;var w=ow(p),S=w.c,k=w.n;var C=new Ib(19);for(var M=0;M<y.length;++M)++C[y[M]&31];for(var M=0;M<S.length;++M)++C[S[M]&31];var I=nw(C,7),O=I.t,A=I.l;var T=19;for(;T>4&&!O[_b[T-1]];--T);var _=f+5<<3;var E=aw(n,Gb)+aw(s,Wb)+o;var R=aw(n,h)+aw(s,p)+o+14+3*T+aw(C,O)+2*C[16]+3*C[17]+7*C[18];if(u>=0&&_<=E&&_<=R)return uw(e,l,t.subarray(u,u+f));var x,j,N,D;iw(e,l,1+(R<E)),l+=2;if(R<E){x=Ub(h,d,0),j=h,N=Ub(p,m,0),D=p;var F=Ub(O,A,0);iw(e,l,b-257);iw(e,l+5,k-1);iw(e,l+10,T-4);l+=14;for(var M=0;M<T;++M)iw(e,l+3*M,O[_b[M]]);l+=3*T;var L=[y,S];for(var P=0;P<2;++P){var B=L[P];for(var M=0;M<B.length;++M){var U=B[M]&31;iw(e,l,F[U]),l+=O[U];if(U>15)iw(e,l,B[M]>>5&127),l+=B[M]>>12}}}else{x=zb,j=Gb,N=Hb,D=Wb}for(var M=0;M<a;++M){var G=r[M];if(G>255){var U=G>>18&31;rw(e,l,x[U+257]),l+=j[U+257];if(U>7)iw(e,l,G>>23&31),l+=Ab[U];var W=G&31;rw(e,l,N[W]),l+=D[W];if(W>3)rw(e,l,G>>5&8191),l+=Tb[W]}else{rw(e,l,x[G]),l+=j[G]}}rw(e,l,x[256]);return l+j[256]};var lw=new Ob([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]);var cw=new Mb(0);var hw=function(t,e,i,r,n,s){var o=s.z||t.length;var a=new Mb(r+o+5*(1+Math.ceil(o/7e3))+n);var u=a.subarray(r,a.length-n);var f=s.l;var l=(s.r||0)&7;if(e){if(l)u[0]=s.r>>3;var c=lw[e-1];var h=c>>13,d=c&8191;var v=(1<<i)-1;var p=s.p||new Ib(32768),m=s.h||new Ib(v+1);var g=Math.ceil(i/3),y=2*g;var b=function(e){return(t[e]^t[e+1]<<g^t[e+2]<<y)&v};var w=new Ob(25e3);var S=new Ib(288),k=new Ib(32);var C=0,M=0,I=s.i||0,O=0,A=s.w||0,T=0;for(;I+2<o;++I){var _=b(I);var E=I&32767,R=m[_];p[E]=R;m[_]=E;if(A<=I){var x=o-I;if((C>7e3||O>24576)&&(x>423||!f)){l=fw(t,u,0,w,S,k,M,O,T,I-T,l);O=C=M=0,T=I;for(var j=0;j<286;++j)S[j]=0;for(var j=0;j<30;++j)k[j]=0}var N=2,D=0,F=d,L=E-R&32767;if(x>2&&_==b(I-L)){var P=Math.min(h,x)-1;var B=Math.min(32767,I);var U=Math.min(258,x);while(L<=B&&--F&&E!=R){if(t[I+N]==t[I+N-L]){var G=0;for(;G<U&&t[I+G]==t[I+G-L];++G);if(G>N){N=G,D=L;if(G>P)break;var W=Math.min(L,G-2);var z=0;for(var j=0;j<W;++j){var V=I-L+j&32767;var H=p[V];var Y=V-H&32767;if(Y>z)z=Y,R=V}}}E=R,R=p[E];L+=E-R&32767}}if(D){w[O++]=268435456|jb[N]<<18|Fb[D];var Z=jb[N]&31,X=Fb[D]&31;M+=Ab[Z]+Tb[X];++S[257+Z];++k[X];A=I+N;++C}else{w[O++]=t[I];++S[t[I]]}}}for(I=Math.max(I,A);I<o;++I){w[O++]=t[I];++S[t[I]]}l=fw(t,u,f,w,S,k,M,O,T,I-T,l);if(!f){s.r=l&7|u[l/8|0]<<3;l-=7;s.h=m,s.p=p,s.i=I,s.w=A}}else{for(var I=s.w||0;I<o+f;I+=65535){var $=I+65535;if($>=o){u[l/8|0]=f;$=o}l=uw(u,l+1,t.subarray(I,$))}s.i=o}return Kb(a,0,r+Jb(l)+n)};var dw=function(){var t=new Int32Array(256);for(var e=0;e<256;++e){var i=e,r=9;while(--r)i=(i&1&&-306674912)^i>>>1;t[e]=i}return t}();var vw=function(){var t=-1;return{p:function(e){var i=t;for(var r=0;r<e.length;++r)i=dw[i&255^e[r]]^i>>>8;t=i},d:function(){return~t}}};var pw=function(){var t=1,e=0;return{p:function(i){var r=t,n=e;var s=i.length|0;for(var o=0;o!=s;){var a=Math.min(o+2655,s);for(;o<a;++o)n+=r+=i[o];r=(r&65535)+15*(r>>16),n=(n&65535)+15*(n>>16)}t=r,e=n},d:function(){t%=65521,e%=65521;return(t&255)<<24|(t&65280)<<8|(e&255)<<8|e>>8}}};var mw=function(t,e,i,r,n){if(!n){n={l:1};if(e.dictionary){var s=e.dictionary.subarray(-32768);var o=new Mb(s.length+t.length);o.set(s);o.set(t,s.length);t=o;n.w=s.length}}return hw(t,e.level==null?6:e.level,e.mem==null?n.l?Math.ceil(Math.max(8,Math.min(13,Math.log(t.length)))*1.5):20:12+e.mem,i,r,n)};var gw=function(t,e){var i={};for(var r in t)i[r]=t[r];for(var r in e)i[r]=e[r];return i};var yw=function(t,e,i){var r=t();var n=t.toString();var s=n.slice(n.indexOf("[")+1,n.lastIndexOf("]")).replace(/\s+/g,"").split(",");for(var o=0;o<r.length;++o){var a=r[o],u=s[o];if(typeof a=="function"){e+=";"+u+"=";var f=a.toString();if(a.prototype){if(f.indexOf("[native code]")!=-1){var l=f.indexOf(" ",8)+1;e+=f.slice(l,f.indexOf("(",l))}else{e+=f;for(var c in a.prototype)e+=";"+u+".prototype."+c+"="+a.prototype[c].toString()}}else e+=f}else i[u]=a}return e};var bw=[];var ww=function(t){var e=[];for(var i in t){if(t[i].buffer){e.push((t[i]=new t[i].constructor(t[i])).buffer)}}return e};var Sw=function(t,e,i,r){if(!bw[i]){var n="",s={},o=t.length-1;for(var a=0;a<o;++a)n=yw(t[a],n,s);bw[i]={c:yw(t[o],n,s),e:s}}var u=gw({},bw[i].e);return Cb(bw[i].c+";onmessage=function(e){for(var k in e.data)self[k]=e.data[k];onmessage="+e.toString()+"}",i,u,ww(u),r)};var kw=function(){return[Mb,Ib,Ob,Ab,Tb,_b,xb,Db,Vb,Yb,Lb,Qb,Ub,Zb,Xb,$b,Jb,Kb,tw,ew,Kw,Tw,_w]};var Cw=function(){return[Mb,Ib,Ob,Ab,Tb,_b,jb,Fb,zb,Gb,Hb,Wb,Lb,lw,cw,Ub,iw,rw,nw,sw,ow,aw,uw,fw,Jb,Kb,hw,mw,Zw,Tw]};var Mw=function(){return[Lw,Uw,Fw,vw,dw]};var Iw=function(){return[Pw,Bw]};var Ow=function(){return[Gw,Fw,pw]};var Aw=function(){return[Ww]};var Tw=function(t){return postMessage(t,[t.buffer])};var _w=function(t){return t&&{out:t.size&&new Mb(t.size),dictionary:t.dictionary}};var Ew=function(t,e,i,r,n,s){var o=Sw(i,r,n,(function(t,e){o.terminate();s(t,e)}));o.postMessage([t,e],e.consume?[t.buffer]:[]);return function(){o.terminate()}};var Rw=function(t){t.ondata=function(t,e){return postMessage([t,e],[t.buffer])};return function(e){if(e.data.length){t.push(e.data[0],e.data[1]);postMessage([e.data[0].length])}else t.flush()}};var xw=function(t,e,i,r,n,s,o){var a;var u=Sw(t,r,n,(function(t,i){if(t)u.terminate(),e.ondata.call(e,t);else if(!Array.isArray(i))o(i);else if(i.length==1){e.queuedSize-=i[0];if(e.ondrain)e.ondrain(i[0])}else{if(i[1])u.terminate();e.ondata.call(e,t,i[0],i[1])}}));u.postMessage(i);e.queuedSize=0;e.push=function(t,i){if(!e.ondata)tw(5);if(a)e.ondata(tw(4,0,1),null,!!i);e.queuedSize+=t.length;u.postMessage([t,a=i],[t.buffer])};e.terminate=function(){u.terminate()};if(s){e.flush=function(){u.postMessage([])}}};var jw=function(t,e){return t[e]|t[e+1]<<8};var Nw=function(t,e){return(t[e]|t[e+1]<<8|t[e+2]<<16|t[e+3]<<24)>>>0};var Dw=function(t,e){return Nw(t,e)+Nw(t,e+4)*4294967296};var Fw=function(t,e,i){for(;i;++e)t[e]=i,i>>>=8};var Lw=function(t,e){var i=e.filename;t[0]=31,t[1]=139,t[2]=8,t[8]=e.level<2?4:e.level==9?2:0,t[9]=3;if(e.mtime!=0)Fw(t,4,Math.floor(new Date(e.mtime||Date.now())/1e3));if(i){t[3]=8;for(var r=0;r<=i.length;++r)t[r+10]=i.charCodeAt(r)}};var Pw=function(t){if(t[0]!=31||t[1]!=139||t[2]!=8)tw(6,"invalid gzip data");var e=t[3];var i=10;if(e&4)i+=(t[10]|t[11]<<8)+2;for(var r=(e>>3&1)+(e>>4&1);r>0;r-=!t[i++]);return i+(e&2)};var Bw=function(t){var e=t.length;return(t[e-4]|t[e-3]<<8|t[e-2]<<16|t[e-1]<<24)>>>0};var Uw=function(t){return 10+(t.filename?t.filename.length+1:0)};var Gw=function(t,e){var i=e.level,r=i==0?0:i<6?1:i==9?3:2;t[0]=120,t[1]=r<<6|(e.dictionary&&32);t[1]|=31-(t[0]<<8|t[1])%31;if(e.dictionary){var n=pw();n.p(e.dictionary);Fw(t,2,n.d())}};var Ww=function(t,e){if((t[0]&15)!=8||t[0]>>4>7||(t[0]<<8|t[1])%31)tw(6,"invalid zlib data");if((t[1]>>5&1)==+!e)tw(6,"invalid zlib data: "+(t[1]&32?"need":"unexpected")+" dictionary");return(t[1]>>3&4)+2};function zw(t,e){if(typeof t=="function")e=t,t={};this.ondata=e;return t}var Vw=function(){function t(t,e){if(typeof t=="function")e=t,t={};this.ondata=e;this.o=t||{};this.s={l:0,i:32768,w:32768,z:32768};this.b=new Mb(98304);if(this.o.dictionary){var i=this.o.dictionary.subarray(-32768);this.b.set(i,32768-i.length);this.s.i=32768-i.length}}t.prototype.p=function(t,e){this.ondata(mw(t,this.o,0,0,this.s),e)};t.prototype.push=function(t,e){if(!this.ondata)tw(5);if(this.s.l)tw(4);var i=t.length+this.s.z;if(i>this.b.length){if(i>2*this.b.length-32768){var r=new Mb(i&-32768);r.set(this.b.subarray(0,this.s.z));this.b=r}var n=this.b.length-this.s.z;this.b.set(t.subarray(0,n),this.s.z);this.s.z=this.b.length;this.p(this.b,false);this.b.set(this.b.subarray(-32768));this.b.set(t.subarray(n),32768);this.s.z=t.length-n+32768;this.s.i=32766,this.s.w=32768}else{this.b.set(t,this.s.z);this.s.z+=t.length}this.s.l=e&1;if(this.s.z>this.s.w+8191||e){this.p(this.b,e||false);this.s.w=this.s.i,this.s.i-=2}};t.prototype.flush=function(){if(!this.ondata)tw(5);if(this.s.l)tw(4);this.p(this.b,false);this.s.w=this.s.i,this.s.i-=2};return t}();var Hw=function(){function t(t,e){xw([Cw,function(){return[Rw,Vw]}],this,zw.call(this,t,e),(function(t){var e=new Vw(t.data);onmessage=Rw(e)}),6,1)}return t}();function Yw(t,e,i){if(!i)i=e,e={};if(typeof i!="function")tw(7);return Ew(t,e,[Cw],(function(t){return Tw(Zw(t.data[0],t.data[1]))}),0,i)}function Zw(t,e){return mw(t,e||{},0,0)}var Xw=function(){function t(t,e){if(typeof t=="function")e=t,t={};this.ondata=e;var i=t&&t.dictionary&&t.dictionary.subarray(-32768);this.s={i:0,b:i?i.length:0};this.o=new Mb(32768);this.p=new Mb(0);if(i)this.o.set(i)}t.prototype.e=function(t){if(!this.ondata)tw(5);if(this.d)tw(4);if(!this.p.length)this.p=t;else if(t.length){var e=new Mb(this.p.length+t.length);e.set(this.p),e.set(t,this.p.length),this.p=e}};t.prototype.c=function(t){this.s.i=+(this.d=t||false);var e=this.s.b;var i=ew(this.p,this.s,this.o);this.ondata(Kb(i,e,this.s.b),this.d);this.o=Kb(i,this.s.b-32768),this.s.b=this.o.length;this.p=Kb(this.p,this.s.p/8|0),this.s.p&=7};t.prototype.push=function(t,e){this.e(t),this.c(e)};return t}();var $w=function(){function t(t,e){xw([kw,function(){return[Rw,Xw]}],this,zw.call(this,t,e),(function(t){var e=new Xw(t.data);onmessage=Rw(e)}),7,0)}return t}();function Jw(t,e,i){if(!i)i=e,e={};if(typeof i!="function")tw(7);return Ew(t,e,[kw],(function(t){return Tw(Kw(t.data[0],_w(t.data[1])))}),1,i)}function Kw(t,e){return ew(t,{i:2},e&&e.out,e&&e.dictionary)}var qw=function(){function t(t,e){this.c=vw();this.l=0;this.v=1;Vw.call(this,t,e)}t.prototype.push=function(t,e){this.c.p(t);this.l+=t.length;Vw.prototype.push.call(this,t,e)};t.prototype.p=function(t,e){var i=mw(t,this.o,this.v&&Uw(this.o),e&&8,this.s);if(this.v)Lw(i,this.o),this.v=0;if(e)Fw(i,i.length-8,this.c.d()),Fw(i,i.length-4,this.l);this.ondata(i,e)};t.prototype.flush=function(){Vw.prototype.flush.call(this)};return t}();var Qw=function(){function t(t,e){xw([Cw,Mw,function(){return[Rw,Vw,qw]}],this,zw.call(this,t,e),(function(t){var e=new qw(t.data);onmessage=Rw(e)}),8,1)}return t}();function tS(t,e,i){if(!i)i=e,e={};if(typeof i!="function")tw(7);return Ew(t,e,[Cw,Mw,function(){return[eS]}],(function(t){return Tw(eS(t.data[0],t.data[1]))}),2,i)}function eS(t,e){if(!e)e={};var i=vw(),r=t.length;i.p(t);var n=mw(t,e,Uw(e),8),s=n.length;return Lw(n,e),Fw(n,s-8,i.d()),Fw(n,s-4,r),n}var iS=function(){function t(t,e){this.v=1;this.r=0;Xw.call(this,t,e)}t.prototype.push=function(t,e){Xw.prototype.e.call(this,t);this.r+=t.length;if(this.v){var i=this.p.subarray(this.v-1);var r=i.length>3?Pw(i):4;if(r>i.length){if(!e)return}else if(this.v>1&&this.onmember){this.onmember(this.r-i.length)}this.p=i.subarray(r),this.v=0}Xw.prototype.c.call(this,e);if(this.s.f&&!this.s.l&&!e){this.v=Jb(this.s.p)+9;this.s={i:0};this.o=new Mb(0);this.push(new Mb(0),e)}};return t}();var rS=function(){function t(t,e){var i=this;xw([kw,Iw,function(){return[Rw,Xw,iS]}],this,zw.call(this,t,e),(function(t){var e=new iS(t.data);e.onmember=function(t){return postMessage(t)};onmessage=Rw(e)}),9,0,(function(t){return i.onmember&&i.onmember(t)}))}return t}();function nS(t,e,i){if(!i)i=e,e={};if(typeof i!="function")tw(7);return Ew(t,e,[kw,Iw,function(){return[sS]}],(function(t){return Tw(sS(t.data[0],t.data[1]))}),3,i)}function sS(t,e){var i=Pw(t);if(i+8>t.length)tw(6,"invalid gzip data");return ew(t.subarray(i,-8),{i:2},e&&e.out||new Mb(Bw(t)),e&&e.dictionary)}var oS=function(){function t(t,e){this.c=pw();this.v=1;Vw.call(this,t,e)}t.prototype.push=function(t,e){this.c.p(t);Vw.prototype.push.call(this,t,e)};t.prototype.p=function(t,e){var i=mw(t,this.o,this.v&&(this.o.dictionary?6:2),e&&4,this.s);if(this.v)Gw(i,this.o),this.v=0;if(e)Fw(i,i.length-4,this.c.d());this.ondata(i,e)};t.prototype.flush=function(){Vw.prototype.flush.call(this)};return t}();var aS=function(){function t(t,e){xw([Cw,Ow,function(){return[Rw,Vw,oS]}],this,zw.call(this,t,e),(function(t){var e=new oS(t.data);onmessage=Rw(e)}),10,1)}return t}();function uS(t,e,i){if(!i)i=e,e={};if(typeof i!="function")tw(7);return Ew(t,e,[Cw,Ow,function(){return[fS]}],(function(t){return Tw(fS(t.data[0],t.data[1]))}),4,i)}function fS(t,e){if(!e)e={};var i=pw();i.p(t);var r=mw(t,e,e.dictionary?6:2,4);return Gw(r,e),Fw(r,r.length-4,i.d()),r}var lS=function(){function t(t,e){Xw.call(this,t,e);this.v=t&&t.dictionary?2:1}t.prototype.push=function(t,e){Xw.prototype.e.call(this,t);if(this.v){if(this.p.length<6&&!e)return;this.p=this.p.subarray(Ww(this.p,this.v-1)),this.v=0}if(e){if(this.p.length<4)tw(6,"invalid zlib data");this.p=this.p.subarray(0,-4)}Xw.prototype.c.call(this,e)};return t}();var cS=function(){function t(t,e){xw([kw,Aw,function(){return[Rw,Xw,lS]}],this,zw.call(this,t,e),(function(t){var e=new lS(t.data);onmessage=Rw(e)}),11,0)}return t}();function hS(t,e,i){if(!i)i=e,e={};if(typeof i!="function")tw(7);return Ew(t,e,[kw,Aw,function(){return[dS]}],(function(t){return Tw(dS(t.data[0],_w(t.data[1])))}),5,i)}function dS(t,e){return ew(t.subarray(Ww(t,e&&e.dictionary),-4),{i:2},e&&e.out,e&&e.dictionary)}var vS=function(){function t(t,e){this.o=zw.call(this,t,e)||{};this.G=iS;this.I=Xw;this.Z=lS}t.prototype.i=function(){var t=this;this.s.ondata=function(e,i){t.ondata(e,i)}};t.prototype.push=function(t,e){if(!this.ondata)tw(5);if(!this.s){if(this.p&&this.p.length){var i=new Mb(this.p.length+t.length);i.set(this.p),i.set(t,this.p.length)}else this.p=t;if(this.p.length>2){this.s=this.p[0]==31&&this.p[1]==139&&this.p[2]==8?new this.G(this.o):(this.p[0]&15)!=8||this.p[0]>>4>7||(this.p[0]<<8|this.p[1])%31?new this.I(this.o):new this.Z(this.o);this.i();this.s.push(this.p,e);this.p=null}}else this.s.push(t,e)};return t}();var pS=function(){function t(t,e){vS.call(this,t,e);this.queuedSize=0;this.G=rS;this.I=$w;this.Z=cS}t.prototype.i=function(){var t=this;this.s.ondata=function(e,i,r){t.ondata(e,i,r)};this.s.ondrain=function(e){t.queuedSize-=e;if(t.ondrain)t.ondrain(e)}};t.prototype.push=function(t,e){this.queuedSize+=t.length;vS.prototype.push.call(this,t,e)};return t}();function mS(t,e,i){if(!i)i=e,e={};if(typeof i!="function")tw(7);return t[0]==31&&t[1]==139&&t[2]==8?nS(t,e,i):(t[0]&15)!=8||t[0]>>4>7||(t[0]<<8|t[1])%31?Jw(t,e,i):hS(t,e,i)}function gS(t,e){return t[0]==31&&t[1]==139&&t[2]==8?sS(t,e):(t[0]&15)!=8||t[0]>>4>7||(t[0]<<8|t[1])%31?Kw(t,e):dS(t,e)}var yS=function(t,e,i,r){for(var n in t){var s=t[n],o=e+n,a=r;if(Array.isArray(s))a=gw(r,s[1]),s=s[0];if(s instanceof Mb)i[o]=[s,a];else{i[o+="/"]=[new Mb(0),a];yS(s,o,i,r)}}};var bS=typeof TextEncoder!="undefined"&&new TextEncoder;var wS=typeof TextDecoder!="undefined"&&new TextDecoder;var SS=0;try{wS.decode(cw,{stream:true});SS=1}catch(jy){}var kS=function(t){for(var e="",i=0;;){var r=t[i++];var n=(r>127)+(r>223)+(r>239);if(i+n>t.length)return{s:e,r:Kb(t,i-1)};if(!n)e+=String.fromCharCode(r);else if(n==3){r=((r&15)<<18|(t[i++]&63)<<12|(t[i++]&63)<<6|t[i++]&63)-65536,e+=String.fromCharCode(55296|r>>10,56320|r&1023)}else if(n&1)e+=String.fromCharCode((r&31)<<6|t[i++]&63);else e+=String.fromCharCode((r&15)<<12|(t[i++]&63)<<6|t[i++]&63)}};var CS=function(){function t(t){this.ondata=t;if(SS)this.t=new TextDecoder;else this.p=cw}t.prototype.push=function(t,e){if(!this.ondata)tw(5);e=!!e;if(this.t){this.ondata(this.t.decode(t,{stream:true}),e);if(e){if(this.t.decode().length)tw(8);this.t=null}return}if(!this.p)tw(4);var i=new Mb(this.p.length+t.length);i.set(this.p);i.set(t,this.p.length);var r=kS(i),n=r.s,s=r.r;if(e){if(s.length)tw(8);this.p=null}else this.p=s;this.ondata(n,e)};return t}();var MS=function(){function t(t){this.ondata=t}t.prototype.push=function(t,e){if(!this.ondata)tw(5);if(this.d)tw(4);this.ondata(IS(t),this.d=e||false)};return t}();function IS(t,e){if(e){var i=new Mb(t.length);for(var r=0;r<t.length;++r)i[r]=t.charCodeAt(r);return i}if(bS)return bS.encode(t);var n=t.length;var s=new Mb(t.length+(t.length>>1));var o=0;var a=function(t){s[o++]=t};for(var r=0;r<n;++r){if(o+5>s.length){var u=new Mb(o+8+(n-r<<1));u.set(s);s=u}var f=t.charCodeAt(r);if(f<128||e)a(f);else if(f<2048)a(192|f>>6),a(128|f&63);else if(f>55295&&f<57344)f=65536+(f&1023<<10)|t.charCodeAt(++r)&1023,a(240|f>>18),a(128|f>>12&63),a(128|f>>6&63),a(128|f&63);else a(224|f>>12),a(128|f>>6&63),a(128|f&63)}return Kb(s,0,o)}function OS(t,e){if(e){var i="";for(var r=0;r<t.length;r+=16384)i+=String.fromCharCode.apply(null,t.subarray(r,r+16384));return i}else if(wS){return wS.decode(t)}else{var n=kS(t),s=n.s,i=n.r;if(i.length)tw(8);return s}}var AS=function(t){return t==1?3:t<6?2:t==9?1:0};var TS=function(t,e){return e+30+jw(t,e+26)+jw(t,e+28)};var _S=function(t,e,i){var r=jw(t,e+28),n=OS(t.subarray(e+46,e+46+r),!(jw(t,e+8)&2048)),s=e+46+r,o=Nw(t,e+20);var a=i&&o==4294967295?ES(t,s):[o,Nw(t,e+24),Nw(t,e+42)],u=a[0],f=a[1],l=a[2];return[jw(t,e+10),u,f,n,s+jw(t,e+30)+jw(t,e+32),l]};var ES=function(t,e){for(;jw(t,e)!=1;e+=4+jw(t,e+2));return[Dw(t,e+12),Dw(t,e+4),Dw(t,e+20)]};var RS=function(t){var e=0;if(t){for(var i in t){var r=t[i].length;if(r>65535)tw(9);e+=r+4}}return e};var xS=function(t,e,i,r,n,s,o,a){var u=r.length,f=i.extra,l=a&&a.length;var c=RS(f);Fw(t,e,o!=null?33639248:67324752),e+=4;if(o!=null)t[e++]=20,t[e++]=i.os;t[e]=20,e+=2;t[e++]=i.flag<<1|(s<0&&8),t[e++]=n&&8;t[e++]=i.compression&255,t[e++]=i.compression>>8;var h=new Date(i.mtime==null?Date.now():i.mtime),d=h.getFullYear()-1980;if(d<0||d>119)tw(10);Fw(t,e,d<<25|h.getMonth()+1<<21|h.getDate()<<16|h.getHours()<<11|h.getMinutes()<<5|h.getSeconds()>>1),e+=4;if(s!=-1){Fw(t,e,i.crc);Fw(t,e+4,s<0?-s-2:s);Fw(t,e+8,i.size)}Fw(t,e+12,u);Fw(t,e+14,c),e+=16;if(o!=null){Fw(t,e,l);Fw(t,e+6,i.attrs);Fw(t,e+10,o),e+=14}t.set(r,e);e+=u;if(c){for(var v in f){var p=f[v],m=p.length;Fw(t,e,+v);Fw(t,e+2,m);t.set(p,e+4),e+=4+m}}if(l)t.set(a,e),e+=l;return e};var jS=function(t,e,i,r,n){Fw(t,e,101010256);Fw(t,e+8,i);Fw(t,e+10,i);Fw(t,e+12,r);Fw(t,e+16,n)};var NS=function(){function t(t){this.filename=t;this.c=vw();this.size=0;this.compression=0}t.prototype.process=function(t,e){this.ondata(null,t,e)};t.prototype.push=function(t,e){if(!this.ondata)tw(5);this.c.p(t);this.size+=t.length;if(e)this.crc=this.c.d();this.process(t,e||false)};return t}();var DS=function(){function t(t,e){var i=this;if(!e)e={};NS.call(this,t);this.d=new Vw(e,(function(t,e){i.ondata(null,t,e)}));this.compression=8;this.flag=AS(e.level)}t.prototype.process=function(t,e){try{this.d.push(t,e)}catch(t){this.ondata(t,null,e)}};t.prototype.push=function(t,e){NS.prototype.push.call(this,t,e)};return t}();var FS=function(){function t(t,e){var i=this;if(!e)e={};NS.call(this,t);this.d=new Hw(e,(function(t,e,r){i.ondata(t,e,r)}));this.compression=8;this.flag=AS(e.level);this.terminate=this.d.terminate}t.prototype.process=function(t,e){this.d.push(t,e)};t.prototype.push=function(t,e){NS.prototype.push.call(this,t,e)};return t}();var LS=function(){function t(t){this.ondata=t;this.u=[];this.d=1}t.prototype.add=function(t){var e=this;if(!this.ondata)tw(5);if(this.d&2)this.ondata(tw(4+(this.d&1)*8,0,1),null,false);else{var i=IS(t.filename),r=i.length;var n=t.comment,s=n&&IS(n);var o=r!=t.filename.length||s&&n.length!=s.length;var a=r+RS(t.extra)+30;if(r>65535)this.ondata(tw(11,0,1),null,false);var u=new Mb(a);xS(u,0,t,i,o,-1);var f=[u];var l=function(){for(var t=0,i=f;t<i.length;t++){var r=i[t];e.ondata(null,r,false)}f=[]};var c=this.d;this.d=0;var h=this.u.length;var d=gw(t,{f:i,u:o,o:s,t:function(){if(t.terminate)t.terminate()},r:function(){l();if(c){var t=e.u[h+1];if(t)t.r();else e.d=1}c=1}});var v=0;t.ondata=function(i,r,n){if(i){e.ondata(i,r,n);e.terminate()}else{v+=r.length;f.push(r);if(n){var s=new Mb(16);Fw(s,0,134695760);Fw(s,4,t.crc);Fw(s,8,v);Fw(s,12,t.size);f.push(s);d.c=v,d.b=a+v+16,d.crc=t.crc,d.size=t.size;if(c)d.r();c=1}else if(c)l()}};this.u.push(d)}};t.prototype.end=function(){var t=this;if(this.d&2){this.ondata(tw(4+(this.d&1)*8,0,1),null,true);return}if(this.d)this.e();else this.u.push({r:function(){if(!(t.d&1))return;t.u.splice(-1,1);t.e()},t:function(){}});this.d=3};t.prototype.e=function(){var t=0,e=0,i=0;for(var r=0,n=this.u;r<n.length;r++){var s=n[r];i+=46+s.f.length+RS(s.extra)+(s.o?s.o.length:0)}var o=new Mb(i+22);for(var a=0,u=this.u;a<u.length;a++){var s=u[a];xS(o,t,s,s.f,s.u,-s.c-2,e,s.o);t+=46+s.f.length+RS(s.extra)+(s.o?s.o.length:0),e+=s.b}jS(o,t,this.u.length,i,e);this.ondata(null,o,true);this.d=2};t.prototype.terminate=function(){for(var t=0,e=this.u;t<e.length;t++){var i=e[t];i.t()}this.d=2};return t}();function PS(t,e,i){if(!i)i=e,e={};if(typeof i!="function")tw(7);var r={};yS(t,"",r,e);var n=Object.keys(r);var s=n.length,o=0,a=0;var u=s,f=new Array(s);var l=[];var c=function(){for(var t=0;t<l.length;++t)l[t]()};var h=function(t,e){VS((function(){i(t,e)}))};VS((function(){h=i}));var d=function(){var t=new Mb(a+22),e=o,i=a-o;a=0;for(var r=0;r<u;++r){var n=f[r];try{var s=n.c.length;xS(t,a,n,n.f,n.u,s);var l=30+n.f.length+RS(n.extra);var c=a+l;t.set(n.c,c);xS(t,o,n,n.f,n.u,s,a,n.m),o+=16+l+(n.m?n.m.length:0),a=c+s}catch(t){return h(t,null)}}jS(t,o,f.length,i,e);h(null,t)};if(!s)d();var v=function(t){var e=n[t];var i=r[e],u=i[0],v=i[1];var p=vw(),m=u.length;p.p(u);var g=IS(e),y=g.length;var b=v.comment,w=b&&IS(b),S=w&&w.length;var k=RS(v.extra);var C=v.level==0?0:8;var M=function(i,r){if(i){c();h(i,null)}else{var n=r.length;f[t]=gw(v,{size:m,crc:p.d(),c:r,f:g,m:w,u:y!=e.length||w&&b.length!=S,compression:C});o+=30+y+k+n;a+=76+2*(y+k)+(S||0)+n;if(! --s)d()}};if(y>65535)M(tw(11,0,1),null);if(!C)M(null,u);else if(m<16e4){try{M(null,Zw(u,v))}catch(t){M(t,null)}}else l.push(Yw(u,v,M))};for(var p=0;p<u;++p){v(p)}return c}function BS(t,e){if(!e)e={};var i={};var r=[];yS(t,"",i,e);var n=0;var s=0;for(var o in i){var a=i[o],u=a[0],f=a[1];var l=f.level==0?0:8;var c=IS(o),h=c.length;var d=f.comment,v=d&&IS(d),p=v&&v.length;var m=RS(f.extra);if(h>65535)tw(11);var g=l?Zw(u,f):u,y=g.length;var b=vw();b.p(u);r.push(gw(f,{size:u.length,crc:b.d(),c:g,f:c,m:v,u:h!=o.length||v&&d.length!=p,o:n,compression:l}));n+=30+h+m+y;s+=76+2*(h+m)+(p||0)+y}var w=new Mb(s+22),S=n,k=s-n;for(var C=0;C<r.length;++C){var c=r[C];xS(w,c.o,c,c.f,c.u,c.c.length);var M=30+c.f.length+RS(c.extra);w.set(c.c,c.o+M);xS(w,n,c,c.f,c.u,c.c.length,c.o,c.m),n+=16+M+(c.m?c.m.length:0)}jS(w,n,r.length,k,S);return w}var US=function(){function t(){}t.prototype.push=function(t,e){this.ondata(null,t,e)};t.compression=0;return t}();var GS=function(){function t(){var t=this;this.i=new Xw((function(e,i){t.ondata(null,e,i)}))}t.prototype.push=function(t,e){try{this.i.push(t,e)}catch(t){this.ondata(t,null,e)}};t.compression=8;return t}();var WS=function(){function t(t,e){var i=this;if(e<32e4){this.i=new Xw((function(t,e){i.ondata(null,t,e)}))}else{this.i=new $w((function(t,e,r){i.ondata(t,e,r)}));this.terminate=this.i.terminate}}t.prototype.push=function(t,e){if(this.i.terminate)t=Kb(t,0);this.i.push(t,e)};t.compression=8;return t}();var zS=function(){function t(t){this.onfile=t;this.k=[];this.o={0:US};this.p=cw}t.prototype.push=function(t,e){var i=this;if(!this.onfile)tw(5);if(!this.p)tw(4);if(this.c>0){var r=Math.min(this.c,t.length);var n=t.subarray(0,r);this.c-=r;if(this.d)this.d.push(n,!this.c);else this.k[0].push(n);t=t.subarray(r);if(t.length)return this.push(t,e)}else{var s=0,o=0,a=void 0,u=void 0;if(!this.p.length)u=t;else if(!t.length)u=this.p;else{u=new Mb(this.p.length+t.length);u.set(this.p),u.set(t,this.p.length)}var f=u.length,l=this.c,c=l&&this.d;var h=function(){var t;var e=Nw(u,o);if(e==67324752){s=1,a=o;d.d=null;d.c=0;var r=jw(u,o+6),n=jw(u,o+8),c=r&2048,h=r&8,v=jw(u,o+26),p=jw(u,o+28);if(f>o+30+v+p){var m=[];d.k.unshift(m);s=2;var g=Nw(u,o+18),y=Nw(u,o+22);var b=OS(u.subarray(o+30,o+=30+v),!c);if(g==4294967295){t=h?[-2]:ES(u,o),g=t[0],y=t[1]}else if(h)g=-1;o+=p;d.c=g;var w;var S={name:b,compression:n,start:function(){if(!S.ondata)tw(5);if(!g)S.ondata(null,cw,true);else{var t=i.o[n];if(!t)S.ondata(tw(14,"unknown compression type "+n,1),null,false);w=g<0?new t(b):new t(b,g,y);w.ondata=function(t,e,i){S.ondata(t,e,i)};for(var e=0,r=m;e<r.length;e++){var s=r[e];w.push(s,false)}if(i.k[0]==m&&i.c)i.d=w;else w.push(cw,true)}},terminate:function(){if(w&&w.terminate)w.terminate()}};if(g>=0)S.size=g,S.originalSize=y;d.onfile(S)}return"break"}else if(l){if(e==134695760){a=o+=12+(l==-2&&8),s=3,d.c=0;return"break"}else if(e==33639248){a=o-=4,s=3,d.c=0;return"break"}}};var d=this;for(;o<f-4;++o){var v=h();if(v==="break")break}this.p=cw;if(l<0){var p=s?u.subarray(0,a-12-(l==-2&&8)-(Nw(u,a-16)==134695760&&4)):u.subarray(0,o);if(c)c.push(p,!!s);else this.k[+(s==2)].push(p)}if(s&2)return this.push(u.subarray(o),e);this.p=u.subarray(o)}if(e){if(this.c)tw(13);this.p=null}};t.prototype.register=function(t){this.o[t.compression]=t};return t}();var VS=typeof queueMicrotask=="function"?queueMicrotask:typeof setTimeout=="function"?setTimeout:function(t){t()};function HS(t,e,i){if(!i)i=e,e={};if(typeof i!="function")tw(7);var r=[];var n=function(){for(var t=0;t<r.length;++t)r[t]()};var s={};var o=function(t,e){VS((function(){i(t,e)}))};VS((function(){o=i}));var a=t.length-22;for(;Nw(t,a)!=101010256;--a){if(!a||t.length-a>65558){o(tw(13,0,1),null);return n}}var u=jw(t,a+8);if(u){var f=u;var l=Nw(t,a+16);var c=l==4294967295||f==65535;if(c){var h=Nw(t,a-12);c=Nw(t,h)==101075792;if(c){f=u=Nw(t,h+32);l=Nw(t,h+48)}}var d=e&&e.filter;var v=function(e){var i=_S(t,l,c),a=i[0],f=i[1],h=i[2],v=i[3],p=i[4],m=i[5],g=TS(t,m);l=p;var y=function(t,e){if(t){n();o(t,null)}else{if(e)s[v]=e;if(! --u)o(null,s)}};if(!d||d({name:v,size:f,originalSize:h,compression:a})){if(!a)y(null,Kb(t,g,g+f));else if(a==8){var b=t.subarray(g,g+f);if(h<524288||f>.8*h){try{y(null,Kw(b,{out:new Mb(h)}))}catch(t){y(t,null)}}else r.push(Jw(b,{size:h},y))}else y(tw(14,"unknown compression type "+a,1),null)}else y(null,null)};for(var p=0;p<f;++p){v(p)}}else o(null,{});return n}function YS(t,e){var i={};var r=t.length-22;for(;Nw(t,r)!=101010256;--r){if(!r||t.length-r>65558)tw(13)}var n=jw(t,r+8);if(!n)return{};var s=Nw(t,r+16);var o=s==4294967295||n==65535;if(o){var a=Nw(t,r-12);o=Nw(t,a)==101075792;if(o){n=Nw(t,a+32);s=Nw(t,a+48)}}var u=e&&e.filter;for(var f=0;f<n;++f){var l=_S(t,s,o),c=l[0],h=l[1],d=l[2],v=l[3],p=l[4],m=l[5],g=TS(t,m);s=p;if(!u||u({name:v,size:h,originalSize:d,compression:c})){if(!c)i[v]=Kb(t,g,g+h);else if(c==8)i[v]=Kw(t.subarray(g,g+h),{out:new Mb(d)});else tw(14,"unknown compression type "+c)}}return i}var ZS=Object.freeze({__proto__:null,AsyncCompress:Qw,AsyncDecompress:pS,AsyncDeflate:Hw,AsyncGunzip:rS,AsyncGzip:Qw,AsyncInflate:$w,AsyncUnzipInflate:WS,AsyncUnzlib:cS,AsyncZipDeflate:FS,AsyncZlib:aS,Compress:qw,DecodeUTF8:CS,Decompress:vS,Deflate:Vw,EncodeUTF8:MS,FlateErrorCode:qb,Gunzip:iS,Gzip:qw,Inflate:Xw,Unzip:zS,UnzipInflate:GS,UnzipPassThrough:US,Unzlib:lS,Zip:LS,ZipDeflate:DS,ZipPassThrough:NS,Zlib:oS,compress:tS,compressSync:eS,decompress:mS,decompressSync:gS,deflate:Yw,deflateSync:Zw,gunzip:nS,gunzipSync:sS,gzip:tS,gzipSync:eS,inflate:Jw,inflateSync:Kw,strFromU8:OS,strToU8:IS,unzip:HS,unzipSync:YS,unzlib:hS,unzlibSync:dS,zip:PS,zipSync:BS,zlib:uS,zlibSync:fS});var XS=i(ZS);var $S;function JS(){if($S)return os;$S=1;Object.defineProperty(os,"__esModule",{value:true});os.Aggregate=void 0;var t=F();var e=ve();var i=$i();var r=fs();var n=Ai();var s=Q();var o=d();var a=qe();var u=bt();var f=dt();var l=hs();var h=c();var v=st();var p=Bi();var m=Mi();var g=rt();function y(t){if("function"!=typeof WeakMap)return null;var e=new WeakMap,i=new WeakMap;return(y=function(t){return t?i:e})(t)}function b(t,e){if(t&&t.__esModule)return t;if(null===t||"object"!=typeof t&&"function"!=typeof t)return{default:t};var i=y(e);if(i&&i.has(t))return i.get(t);var r={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in t)if("default"!==s&&{}.hasOwnProperty.call(t,s)){var o=n?Object.getOwnPropertyDescriptor(t,s):null;o&&(o.get||o.set)?Object.defineProperty(r,s,o):r[s]=t[s]}return r.default=t,i&&i.set(t,r),r}class w extends i.AggregateBase{static featureName=e.FEATURE_NAME;mode=u.MODE.OFF;constructor(i,n){super(i,e.FEATURE_NAME);this.initialized=false;this.blocked=false;this.gzipper=undefined;this.u8=undefined;this.entitled=false;this.timeKeeper=undefined;this.recorder=n?.recorder;this.errorNoticed=n?.errorNoticed||false;this.harvestOpts.raw=true;this.isSessionTrackingEnabled=(0,g.canEnableSessionTracking)(i.init)&&!!i.runtime.session;this.reportSupportabilityMetric("Config/SessionReplay/Enabled");this.ee.on(u.SESSION_EVENTS.RESET,(()=>{this.abort(e.ABORT_REASONS.RESET)}));this.ee.on(u.SESSION_EVENTS.PAUSE,(()=>{this.recorder?.stopRecording()}));this.ee.on(u.SESSION_EVENTS.RESUME,(()=>{if(!this.recorder)return;this.mode=i.runtime.session.state.sessionReplayMode;if(!this.initialized||this.mode===u.MODE.OFF)return;this.recorder?.startRecording()}));this.ee.on(u.SESSION_EVENTS.UPDATE,((t,i)=>{if(!this.recorder||!this.initialized||this.blocked||t!==u.SESSION_EVENT_TYPES.CROSS_TAB)return;if(this.mode!==u.MODE.OFF&&i.sessionReplayMode===u.MODE.OFF)this.abort(e.ABORT_REASONS.CROSS_TAB);this.mode=i.sessionReplayMode}));(0,t.registerHandler)(e.SR_EVENT_EMITTER_TYPES.PAUSE,(()=>{this.forceStop(this.mode===u.MODE.FULL)}),this.featureName,this.ee);(0,t.registerHandler)(e.SR_EVENT_EMITTER_TYPES.ERROR_DURING_REPLAY,(t=>{this.handleError(t)}),this.featureName,this.ee);const{error_sampling_rate:s,sampling_rate:o,autoStart:a,block_selector:f,mask_text_selector:l,mask_all_inputs:c,inline_images:h,collect_fonts:d}=i.init.session_replay;this.waitForFlags(["srs","sr"]).then((([t,i])=>{this.entitled=!!i;if(!this.entitled){this.deregisterDrain();if(this.recorder?.recording){this.abort(e.ABORT_REASONS.ENTITLEMENTS);this.reportSupportabilityMetric("SessionReplay/EnabledNotEntitled/Detected")}return}this.drain();this.initializeRecording(t)})).then((()=>{if(this.mode===u.MODE.OFF){this.recorder?.stopRecording();while(this.recorder?.getEvents().events.length)this.recorder?.clearBuffer?.()}r.sharedChannel.onReplayReady(this.mode)}));if(!a)this.reportSupportabilityMetric("Config/SessionReplay/AutoStart/Modified");if(d===true)this.reportSupportabilityMetric("Config/SessionReplay/CollectFonts/Modified");if(h===true)this.reportSupportabilityMetric("Config/SessionReplay/InlineImages/Modifed");if(c!==true)this.reportSupportabilityMetric("Config/SessionReplay/MaskAllInputs/Modified");if(f!=="[data-nr-block]")this.reportSupportabilityMetric("Config/SessionReplay/BlockSelector/Modified");if(l!=="*")this.reportSupportabilityMetric("Config/SessionReplay/MaskTextSelector/Modified");this.reportSupportabilityMetric("Config/SessionReplay/SamplingRate/Value",o);this.reportSupportabilityMetric("Config/SessionReplay/ErrorSamplingRate/Value",s)}replayIsActive(){return Boolean(this.recorder&&this.mode===u.MODE.FULL&&!this.blocked&&this.entitled)}handleError(t){if(this.recorder)this.recorder.currentBufferTarget.hasError=true;if(this.mode===u.MODE.ERROR&&o.globalScope?.document.visibilityState==="visible"){this.switchToFull()}}switchToFull(){if(!this.entitled||this.blocked)return;this.mode=u.MODE.FULL;if(this.recorder&&this.initialized){if(!this.recorder.recording)this.recorder.startRecording();this.syncWithSessionManager({sessionReplayMode:this.mode})}else{this.initializeRecording(u.MODE.FULL,true)}}async initializeRecording(t,i){this.initialized=true;if(!this.entitled)return;const{session:r,timeKeeper:n}=this.agentRef.runtime;this.timeKeeper=n;if(this.recorder?.parent.trigger===e.TRIGGERS.API&&this.recorder?.recording){this.mode=u.MODE.FULL}else if(!r.isNew&&!i){this.mode=r.state.sessionReplayMode}else{this.mode=t}if(this.mode===u.MODE.OFF)return;if(!this.recorder){try{const{Recorder:t}=await Promise.resolve().then((()=>b(Sb())));this.recorder=new t(this);this.recorder.currentBufferTarget.hasError=this.errorNoticed}catch(t){return this.abort(e.ABORT_REASONS.IMPORT)}}else{this.recorder.parent=this}if(this.mode===u.MODE.ERROR&&this.errorNoticed)this.mode=u.MODE.FULL;if(this.mode===u.MODE.FULL&&this.recorder?.getEvents().type==="preloaded"){this.prepUtils().then((()=>this.agentRef.runtime.harvester.triggerHarvestFor(this)))}await this.prepUtils();if(!this.recorder.recording)this.recorder.startRecording();this.syncWithSessionManager({sessionReplayMode:this.mode})}async prepUtils(){try{const{gzipSync:t,strToU8:e}=await Promise.resolve().then((()=>b(XS)));this.gzipper=t;this.u8=e}catch(t){}}makeHarvestPayload(t){const i={targetApp:undefined,payload:undefined};if(this.mode!==u.MODE.FULL||this.blocked)return;if(!this.recorder||!this.timeKeeper?.ready||!this.recorder.hasSeenSnapshot)return;const r=this.recorder.getEvents();if(!r.events.length)return;const n=this.getHarvestContents(r);if(!n.body.length){this.recorder.clearBuffer();return[i]}this.reportSupportabilityMetric("SessionReplay/Harvest/Attempts");let s=0;if(!!this.gzipper&&!!this.u8){n.body=this.gzipper(this.u8("[".concat(n.body.map((({__serialized:t,...e})=>{if(e.__newrelic&&t)return t;const i={...e};if(!i.__newrelic){i.__newrelic=(0,v.buildNRMetaNode)(e.timestamp,this.timeKeeper);i.timestamp=this.timeKeeper.correctAbsoluteTimestamp(e.timestamp)}return(0,f.stringify)(i)})).join(","),"]")));s=n.body.length}else{n.body=n.body.map((({__serialized:t,...e})=>{if(e.__newrelic)return e;const i={...e};i.__newrelic=(0,v.buildNRMetaNode)(e.timestamp,this.timeKeeper);i.timestamp=this.timeKeeper.correctAbsoluteTimestamp(e.timestamp);return i}));s=(0,f.stringify)(n.body).length}if(s>p.MAX_PAYLOAD_SIZE){this.abort(e.ABORT_REASONS.TOO_BIG,s);return[i]}if(!this.agentRef.runtime.session.state.sessionReplaySentFirstChunk)this.syncWithSessionManager({sessionReplaySentFirstChunk:true});this.recorder.clearBuffer();if(r.type==="preloaded")this.agentRef.runtime.harvester.triggerHarvestFor(this);i.payload=n;return[i]}getCorrectedTimestamp(t){if(!t?.timestamp)return;if(t.__newrelic)return t.timestamp;return this.timeKeeper.correctAbsoluteTimestamp(t.timestamp)}getHarvestContents(t){t??=this.recorder.getEvents();let i=t.events;const r=this.agentRef.runtime;const s=this.agentRef.info.jsAttributes?.["enduser.id"];const o=i?.[0]?.type===e.RRWEB_EVENT_TYPES.FullSnapshot;if(o&&!!this.recorder.lastMeta){t.hasMeta=true;i.unshift(this.recorder.lastMeta);this.recorder.lastMeta=undefined}const u=i[i.length-1]?.type===e.RRWEB_EVENT_TYPES.Meta;if(u){this.recorder.lastMeta=i[i.length-1];i=i.slice(0,i.length-1);t.hasMeta=!!i.find((t=>t.type===e.RRWEB_EVENT_TYPES.Meta))}const f=(0,h.now)();const c=this.getCorrectedTimestamp(i[0]);const d=this.getCorrectedTimestamp(i[i.length-1]);const v=c||Math.floor(this.timeKeeper.correctAbsoluteTimestamp(t.cycleTimestamp));const p=d||Math.floor(this.timeKeeper.correctRelativeTimestamp(f));const g=r.appMetadata?.agents?.[0]||{};return{qs:{browser_monitoring_key:this.agentRef.info.licenseKey,type:"SessionReplay",app_id:this.agentRef.info.applicationID,protocol_version:"0",timestamp:v,attributes:(0,n.obj)({...!!this.gzipper&&!!this.u8&&{content_encoding:"gzip"},...g.entityGuid&&{entityGuid:g.entityGuid},harvestId:[r.session?.state.value,r.ptid,r.harvestCount].filter((t=>t)).join("_"),"replay.firstTimestamp":v,"replay.lastTimestamp":p,"replay.nodes":i.length,"session.durationMs":r.session.getDuration(),agentVersion:r.version,session:r.session.state.value,rst:f,hasMeta:t.hasMeta||false,hasSnapshot:t.hasSnapshot||false,hasError:t.hasError||false,isFirstChunk:r.session.state.sessionReplaySentFirstChunk===false,decompressedBytes:t.payloadBytesEstimation,invalidStylesheetsDetected:l.stylesheetEvaluator.invalidStylesheetsDetected,inlinedAllStylesheets:t.inlinedAllStylesheets,"rrweb.version":a.RRWEB_VERSION,"payload.type":t.type,...s&&{"enduser.id":this.obfuscator.obfuscateString(s)},currentUrl:this.obfuscator.obfuscateString((0,m.cleanURL)(""+location))},e.QUERY_PARAM_PADDING).substring(1)},body:i}}postHarvestCleanup(t){if(t.status===429){this.abort(e.ABORT_REASONS.TOO_MANY)}}forceStop(t){if(t)this.agentRef.runtime.harvester.triggerHarvestFor(this);this.mode=u.MODE.OFF;this.recorder?.stopRecording?.();this.syncWithSessionManager({sessionReplayMode:this.mode})}abort(t={},e){(0,s.warn)(33,t.message);this.reportSupportabilityMetric("SessionReplay/Abort/".concat(t.sm),e);this.blocked=true;this.mode=u.MODE.OFF;this.recorder?.stopRecording?.();this.syncWithSessionManager({sessionReplayMode:this.mode});this.recorder?.clearTimestamps?.();while(this.recorder?.getEvents().events.length)this.recorder?.clearBuffer?.()}syncWithSessionManager(t={}){if(this.isSessionTrackingEnabled){this.agentRef.runtime.session.write(t)}}}os.Aggregate=w;return os}var KS={};var qS={};var QS;function tk(){if(QS)return qS;QS=1;Object.defineProperty(qS,"__esModule",{value:true});qS.START=qS.RESOURCE=qS.PUSH_STATE=qS.MAX_NODES_PER_HARVEST=qS.FN_START=qS.FN_END=qS.FEATURE_NAME=qS.END=qS.BST_RESOURCE=void 0;var t=B();qS.FEATURE_NAME=t.FEATURE_NAMES.sessionTrace;qS.BST_RESOURCE="bstResource";qS.RESOURCE="resource";const e=qS.START="-start";const i=qS.END="-end";qS.FN_START="fn"+e;qS.FN_END="fn"+i;qS.PUSH_STATE="pushState";qS.MAX_NODES_PER_HARVEST=1e3;return qS}var ek={};var ik={};var rk;function nk(){if(rk)return ik;rk=1;Object.defineProperty(ik,"__esModule",{value:true});ik.parseUrl=e;var t=d();function e(e){if((e||"").indexOf("data:")===0){return{protocol:"data"}}try{const i=new URL(e,location.href);const r={port:i.port,hostname:i.hostname,pathname:i.pathname,search:i.search,protocol:i.protocol.slice(0,i.protocol.indexOf(":")),sameOrigin:i.protocol===t.globalScope?.location?.protocol&&i.host===t.globalScope?.location?.host};if(!r.port||r.port===""){if(i.protocol==="http:")r.port="80";if(i.protocol==="https:")r.port="443"}if(!r.pathname||r.pathname===""){r.pathname="/"}else if(!r.pathname.startsWith("/")){r.pathname="/".concat(r.pathname)}return r}catch(t){return{}}}return ik}var sk={};var ok;function ak(){if(ok)return sk;ok=1;Object.defineProperty(sk,"__esModule",{value:true});sk.TraceNode=void 0;class t{constructor(t,e,i,r,n){this.n=t;this.s=e;this.e=i;this.o=r;this.t=n}}sk.TraceNode=t;return sk}var uk;function fk(){if(uk)return ek;uk=1;Object.defineProperty(ek,"__esModule",{value:true});ek.TraceStorage=void 0;var t=d();var e=bt();var i=c();var r=nk();var n=rs();var s=tk();var o=ak();const a=30*1e3;const u=typeof t.globalScope.PerformanceObserver==="function";const f={global:{mouseup:true,mousedown:true,mousemove:true},window:{load:true,pagehide:true},xhrOriginMissing:{ignoreAll:true}};const l={typing:[1e3,2e3],scrolling:[100,1e3],mousing:[1e3,2e3],touching:[1e3,2e3]};class h{nodeCount=0;trace={};earliestTimeStamp=Infinity;latestTimeStamp=0;prevStoredEvents=new Set;#E;constructor(t){this.parent=t}isAfterSessionExpiry(t){return this.parent.agentRef.runtime?.session?.isAfterSessionExpiry((this.parent.timeKeeper?.ready&&this.parent.timeKeeper.convertRelativeTimestamp(t))??undefined)}storeSTN(t){if(this.parent.blocked)return;if(this.nodeCount>=s.MAX_NODES_PER_HARVEST){if(this.parent.mode!==e.MODE.ERROR)return;const t=this.trimSTNs(a);if(t===0)return}if(this.isAfterSessionExpiry(t.s)){this.parent.reportSupportabilityMetric("Session/Expired/SessionTrace/Seen");return}if(this.trace[t.n])this.trace[t.n].push(t);else this.trace[t.n]=[t];if(t.s<this.earliestTimeStamp)this.earliestTimeStamp=t.s;if(t.s>this.latestTimeStamp)this.latestTimeStamp=t.s;this.nodeCount++}trimSTNs(t){let e=0;const r=Math.max((0,i.now)()-t,0);Object.keys(this.trace).forEach((t=>{const i=this.trace[t];let n=i.findIndex((t=>r<=t.e));if(n===0)return;else if(n<0){n=i.length;delete this.trace[t]}else i.splice(0,n);this.nodeCount-=n;e+=n}));return e}takeSTNs(){if(!u){this.storeResources(t.globalScope.performance?.getEntriesByType?.("resource"))}const e=Object.entries(this.trace).flatMap((([t,e])=>{if(!(t in l))return e;const i=this.smearEvtsByOrigin(t);const r=e.sort(((t,e)=>t.s-e.s)).reduce(i,{});return Object.values(r).flat()}),this);const i=this.earliestTimeStamp;const r=this.latestTimeStamp;return{stns:e,earliestTimeStamp:i,latestTimeStamp:r}}smearEvtsByOrigin(t){const e=l[t][0];const i=l[t][1];const r={};return(s,o)=>{let a=s[o.o];if(!a)a=s[o.o]=[];const u=r[o.o];if(t==="scrolling"&&!n(o)){r[o.o]=null;o.n="scroll";a.push(o)}else if(u&&o.s-u.s<i&&u.e>o.s-e){u.e=o.e}else{r[o.o]=o;a.push(o)}return s};function n(t){const e=4;return!!(t&&typeof t.e==="number"&&typeof t.s==="number"&&t.e-t.s<e)}}processPVT(t,e,i){this.storeTiming({[t]:e})}storeTiming(t,e=false){if(!t)return;for(let i in t){let r=t[i];const n=i.toLowerCase();if(n.indexOf("size")>=0||n.indexOf("status")>=0)continue;if(!(typeof r==="number"&&r>=0))continue;r=Math.round(r);if(this.parent.timeKeeper&&this.parent.timeKeeper.ready&&e){r=this.parent.timeKeeper.convertAbsoluteTimestamp(Math.floor(this.parent.timeKeeper.correctAbsoluteTimestamp(r)))}this.storeSTN(new o.TraceNode(i,r,r,"document","timing"))}}storeEvent(t,e,i,r){if(this.shouldIgnoreEvent(t,e))return;if(this.prevStoredEvents.has(t))return;this.prevStoredEvents.add(t);const s=new o.TraceNode(this.evtName(t.type),i,r,undefined,"event");try{s.o=(0,n.eventOrigin)(t.target,e,this.parent.ee)}catch(t){s.o=(0,n.eventOrigin)(null,e,this.parent.ee)}this.storeSTN(s)}shouldIgnoreEvent(t,e){if(t.type in f.global)return true;const i=(0,n.eventOrigin)(t.target,e,this.parent.ee);if(!!f[i]&&f[i].ignoreAll)return true;return!!(!!f[i]&&t.type in f[i])}evtName(t){switch(t){case"keydown":case"keyup":case"keypress":return"typing";case"mousemove":case"mouseenter":case"mouseleave":case"mouseover":case"mouseout":return"mousing";case"scroll":return"scrolling";case"touchstart":case"touchmove":case"touchend":case"touchcancel":case"touchenter":case"touchleave":return"touching";default:return t}}storeHist(t,e,i){this.storeSTN(new o.TraceNode("history.pushState",i,i,t,e))}#R=0;storeResources(t){if(!t||t.length===0)return;t.forEach((t=>{if((t.fetchStart|0)<=this.#R)return;const{initiatorType:e,fetchStart:i,responseEnd:n,entryType:s}=t;const{protocol:a,hostname:u,port:f,pathname:l}=(0,r.parseUrl)(t.name);const c=new o.TraceNode(e,i|0,n|0,"".concat(a,"://").concat(u,":").concat(f).concat(l),s);this.storeSTN(c)}));this.#R=t[t.length-1].fetchStart|0}storeErrorAgg(t,e,i,r){if(t!=="err")return;this.storeSTN(new o.TraceNode("error",r.time,r.time,i.message,i.stackHash))}storeXhrAgg(t,e,i,r){if(t!=="xhr")return;this.storeSTN(new o.TraceNode("Ajax",r.time,r.time+r.duration,"".concat(i.status," ").concat(i.method,": ").concat(i.host).concat(i.pathname),"ajax"))}isEmpty(){return this.nodeCount===0}save(){this.#E=this.trace}get(){return[{targetApp:this.parent.agentRef.runtime.entityManager.get(),data:this.takeSTNs()}]}clear(){this.trace={};this.nodeCount=0;this.prevStoredEvents.clear();this.earliestTimeStamp=Infinity;this.latestTimeStamp=0}reloadSave(){Object.values(this.#E).forEach((t=>t.forEach((t=>this.storeSTN(t)))))}clearSave(){this.#E=undefined}}ek.TraceStorage=h;return ek}var lk;function ck(){if(lk)return KS;lk=1;Object.defineProperty(KS,"__esModule",{value:true});KS.Aggregate=void 0;var t=F();var e=tk();var i=$i();var r=fk();var n=Ai();var s=d();var o=bt();var a=Tr();var u=Mi();const f=30*1e3;const l=5e3;class c extends i.AggregateBase{static featureName=e.FEATURE_NAME;constructor(t){super(t,e.FEATURE_NAME);this.harvestOpts.raw=true;this.entitled=undefined;this.everHarvested=false;this.harvesting=false;this.events=new r.TraceStorage(this);this.waitForFlags(["sts","st"]).then((([t,e])=>this.initialize(t,e)))}initialize(e,i,r){this.entitled??=i;if(!this.entitled)this.blocked=true;if(this.blocked)return this.deregisterDrain();if(!this.initialized){this.initialized=true;this.ptid=this.agentRef.runtime.ptid;this.sessionId=this.agentRef.runtime.session?.state.value;this.ee.on(o.SESSION_EVENTS.RESET,(()=>{if(this.blocked)return;this.abort(1)}));this.ee.on(o.SESSION_EVENTS.UPDATE,((t,e)=>{if(this.blocked)return;if(this.mode!==o.MODE.FULL&&(e.sessionReplayMode===o.MODE.FULL||e.sessionTraceMode===o.MODE.FULL))this.switchToFull();if(this.sessionId!==e.value||t==="cross-tab"&&e.sessionTraceMode===o.MODE.OFF)this.abort(2)}));if(typeof PerformanceNavigationTiming!=="undefined"){this.events.storeTiming(s.globalScope.performance?.getEntriesByType?.("navigation")[0])}else{this.events.storeTiming(s.globalScope.performance?.timing,true)}}if(!this.agentRef.runtime.session.isNew&&!r)this.mode=this.agentRef.runtime.session.state.sessionTraceMode;else this.mode=e;if(this.mode===o.MODE.OFF)return this.deregisterDrain();this.timeKeeper??=this.agentRef.runtime.timeKeeper;(0,t.registerHandler)("bst",((...t)=>this.events.storeEvent(...t)),this.featureName,this.ee);(0,t.registerHandler)("bstResource",((...t)=>this.events.storeResources(...t)),this.featureName,this.ee);(0,t.registerHandler)("bstHist",((...t)=>this.events.storeHist(...t)),this.featureName,this.ee);(0,t.registerHandler)("bstXhrAgg",((...t)=>this.events.storeXhrAgg(...t)),this.featureName,this.ee);(0,t.registerHandler)("bstApi",((...t)=>this.events.storeSTN(...t)),this.featureName,this.ee);(0,t.registerHandler)("trace-jserror",((...t)=>this.events.storeErrorAgg(...t)),this.featureName,this.ee);(0,t.registerHandler)("pvtAdded",((...t)=>this.events.processPVT(...t)),this.featureName,this.ee);if(this.mode!==o.MODE.FULL){(0,t.registerHandler)("trace-jserror",(()=>{if(this.mode===o.MODE.ERROR)this.switchToFull()}),this.featureName,this.ee)}this.agentRef.runtime.session.write({sessionTraceMode:this.mode});this.drain()}preHarvestChecks(){if(this.mode!==o.MODE.FULL)return;if(!this.timeKeeper?.ready)return;if(!this.agentRef.runtime.session)return;if(this.sessionId!==this.agentRef.runtime.session.state.value||this.ptid!==this.agentRef.runtime.ptid){this.abort(3);return}return true}serializer({stns:t}){if(!t.length)return;this.everHarvested=true;return(0,a.applyFnToProps)(t,this.obfuscator.obfuscateString.bind(this.obfuscator),"string")}queryStringsBuilder({stns:t,earliestTimeStamp:e,latestTimeStamp:i}){const r=!this.agentRef.runtime.session.state.traceHarvestStarted;if(r)this.agentRef.runtime.session.write({traceHarvestStarted:true});const s=this.agentRef.runtime.session.state.sessionReplayMode===1;const o=this.agentRef.info.jsAttributes["enduser.id"];const a=this.agentRef.runtime.appMetadata.agents?.[0]?.entityGuid;return{browser_monitoring_key:this.agentRef.info.licenseKey,type:"BrowserSessionChunk",app_id:this.agentRef.info.applicationID,protocol_version:"0",timestamp:Math.floor(this.timeKeeper.correctRelativeTimestamp(e)),attributes:(0,n.obj)({...a&&{entityGuid:a},harvestId:"".concat(this.agentRef.runtime.session.state.value,"_").concat(this.agentRef.runtime.ptid,"_").concat(this.agentRef.runtime.harvestCount),"trace.firstTimestamp":Math.floor(this.timeKeeper.correctRelativeTimestamp(e)),"trace.lastTimestamp":Math.floor(this.timeKeeper.correctRelativeTimestamp(i)),"trace.nodes":t.length,"trace.originTimestamp":this.timeKeeper.correctedOriginTime,agentVersion:this.agentRef.runtime.version,...r&&{firstSessionHarvest:r},...s&&{hasReplay:s},ptid:"".concat(this.ptid),session:"".concat(this.sessionId),...o&&{"enduser.id":this.obfuscator.obfuscateString(o)},currentUrl:this.obfuscator.obfuscateString((0,u.cleanURL)(""+location))},l).substring(1)}}switchToFull(){if(this.mode===o.MODE.FULL||!this.entitled||this.blocked)return;const t=this.mode;this.mode=o.MODE.FULL;this.agentRef.runtime.session.write({sessionTraceMode:this.mode});if(t===o.MODE.OFF||!this.initialized)return this.initialize(this.mode,this.entitled);if(this.initialized){this.events.trimSTNs(f);this.agentRef.runtime.harvester.triggerHarvestFor(this)}}abort(){this.blocked=true;this.mode=o.MODE.OFF;this.agentRef.runtime.session.write({sessionTraceMode:this.mode});this.events.clear()}}KS.Aggregate=c;return KS}var hk={};var dk={};var vk={};var pk;function mk(){if(pk)return vk;pk=1;Object.defineProperty(vk,"__esModule",{value:true});vk.InteractionNode=i;var t=128;var e=0;function i(t,i,r,n){Object.defineProperty(this,"interaction",{value:t,writable:true});this.parent=i;this.id=++e;this.type=r;this.children=[];this.end=null;this.jsEnd=this.start=n;this.jsTime=0;this.attrs={};this.cancelled=false}var r=i.prototype;r.child=function e(r,n,s,o){var a=this.interaction;if(a.end||a.nodes>=t)return null;a.onNodeAdded(this);var u=new i(a,this,r,n);u.attrs.name=s;a.nodes++;if(!o){a.remaining++}return u};r.callback=function t(e,i){var r=this;r.jsTime+=e;if(i>r.jsEnd){r.jsEnd=i;r.interaction.lastCb=i}};r.cancel=function t(){this.cancelled=true;var e=this.interaction;e.remaining--};r.finish=function t(e){var i=this;if(i.end)return;i.end=e;let r=i.parent;while(r?.cancelled)r=r.parent;if(r)r.children.push(i);i.parent=null;var n=this.interaction;n.remaining--;n.lastFinish=e;n.checkFinish()};return vk}var gk;function yk(){if(gk)return dk;gk=1;Object.defineProperty(dk,"__esModule",{value:true});dk.Interaction=o;var t=d();var e=p();var i=mk();var r=(0,e.gosNREUMOriginals)().o.ST;var n=(0,e.gosNREUMOriginals)().o.CT;var s={};function o(e,r,n,o,a,u){this.agentRef=u;s[u.agentIdentifier]=0;this.id=++s[u.agentIdentifier];this.eventName=e;this.nodes=0;this.remaining=0;this.finishTimer=null;this.checkingFinish=false;this.lastCb=this.lastFinish=r;this.handlers=[];this.onFinished=a;this.done=false;var f=this.root=new i.InteractionNode(this,null,"interaction",r);var l=f.attrs;l.trigger=e;l.initialPageURL=t.initialLocation;l.oldRoute=o;l.newURL=l.oldURL=n;l.custom={};l.store={}}var a=o.prototype;a.checkFinish=function t(){var e=this;if(e.remaining>0){e._resetFinishCheck();return}if(e.checkingFinish){return}if(e.root.end!==null)return;e._resetFinishCheck();e.checkingFinish=true;e.finishTimer=r((()=>{e.checkingFinish=false;e.finishTimer=r((()=>{e.finishTimer=null;if(e.remaining<=0)e.finish()}),1)}),0)};a.setNewURL=function t(e){this.root.attrs.newURL=e};a.setNewRoute=function t(e){this.root.attrs.newRoute=e};a.onNodeAdded=function t(){this._resetFinishCheck()};a._resetFinishCheck=function t(){if(this.finishTimer){n(this.finishTimer);this.finishTimer=null;this.checkingFinish=false}};a.finish=function t(){var e=this;var i=e.root;if(i.end!==null)return;var r=Math.max(e.lastCb,e.lastFinish);var n=i.attrs;var s=n.custom;if(this.onFinished){this.onFinished(this)}Object.entries(e.agentRef.info.jsAttributes||{}).forEach((([t,e])=>{if(!(t in s))s[t]=e}));i.end=r;e.agentRef.ee.emit("interaction",[this])};return dk}var bk={};var wk;function Sk(){if(wk)return bk;wk=1;Object.defineProperty(bk,"__esModule",{value:true});bk.Serializer=void 0;var t=Mi();var e=nr();class i{constructor(t){this.obfuscator=t.runtime.obfuscator;this.info=t.info;this.firstTimestamp=undefined}serializeMultiple(t,i,r){var n=(0,e.getAddStringContext)(this.obfuscator);var s="bel.7";t.forEach((t=>{s+=";"+this.serializeInteraction(t.root,i,r,t.routeChange,n,this.info)}));this.firstTimestamp=undefined;return s}serializeSingle(t,i,r,n){var s=(0,e.getAddStringContext)(this.obfuscator);var o="bel.7;"+this.serializeInteraction(t,i,r,n,s,this.info);this.firstTimestamp=undefined;return o}serializeInteraction(i,r,n,s,o,a){r=r||0;var u=i.attrs.trigger==="initialPageLoad";var f={interaction:1,ajax:2,customTracer:4};var l=true;const c=(i,h)=>{if(i.type==="customEnd")return h.push([3,(0,e.numeric)(i.end-this.firstTimestamp)]);var d=i.type;var v=f[d];var p=i.start;var m=i.children.length;var g=0;var y=a.atts;var b=u&&n.length&&v===1;var w=[];var S=i.attrs;var k=S.metrics;var C=S.params;var M=a.queueTime;var I=a.applicationTime;if(typeof this.firstTimestamp==="undefined"){p+=r;this.firstTimestamp=p}else{p-=this.firstTimestamp}var O=[(0,e.numeric)(p),(0,e.numeric)(i.end-i.start),(0,e.numeric)(i.jsEnd-i.end),(0,e.numeric)(i.jsTime)];switch(v){case 1:O[2]=(0,e.numeric)(i.jsEnd-this.firstTimestamp);O.push(o(S.trigger),o((0,t.cleanURL)(S.initialPageURL,l)),o((0,t.cleanURL)(S.oldURL,l)),o((0,t.cleanURL)(S.newURL,l)),o(S.customName),u?"":s?1:2,(0,e.nullable)(u&&M,e.numeric,true)+(0,e.nullable)(u&&I,e.numeric,true)+(0,e.nullable)(S.oldRoute,o,true)+(0,e.nullable)(S.newRoute,o,true)+o(S.id),o(i.id),(0,e.nullable)(S.firstPaint,e.numeric,true)+(0,e.nullable)(S.firstContentfulPaint,e.numeric,false));var A=(0,e.addCustomAttributes)(S.custom,o);w=w.concat(A);g=A.length;if(y){m++;w.push("a,"+o(y))}break;case 2:O.push(o(C.method),(0,e.numeric)(C.status),o(C.host),o(C.pathname),(0,e.numeric)(k.txSize),(0,e.numeric)(k.rxSize),S.isFetch?1:S.isJSONP?2:"",o(i.id),(0,e.nullable)(i.dt&&i.dt.spanId,o,true)+(0,e.nullable)(i.dt&&i.dt.traceId,o,true)+(0,e.nullable)(i.dt&&i.dt.timestamp,e.numeric,false));if(Object.keys(C?.gql||{}).length){var T=(0,e.addCustomAttributes)(C.gql,o);w=w.concat(T);g=T.length}break;case 4:var _=S.tracedTime;O.push(o(S.name),(0,e.nullable)(_,e.numeric,true)+o(i.id));break}for(var E=0;E<i.children.length;E++){c(i.children[E],w)}O.unshift((0,e.numeric)(v),(0,e.numeric)(m+=g));h.push(O);if(m){h.push(w.join(";"))}if(b){var R=",";var x="b";var j=0;Object.values(n.slice(1,21)||{}).forEach((t=>{if(t!==undefined){x+=R+(0,e.numeric)(t-j);R=",";j=t}else{x+=R+"!";R=""}}));h.push(x)}else if(v===1){h.push("")}return h};return c(i,[]).join(";")}}bk.Serializer=i;return bk}var kk={};var Ck;function Mk(){if(Ck)return kk;Ck=1;Object.defineProperty(kk,"__esModule",{value:true});kk.originalSetTimeout=kk.START=kk.SPA_NODE=kk.REMAINING=kk.MAX_TIMER_BUDGET=kk.JS_TIME=kk.JSONP_NODE=kk.JSONP_END=kk.INTERACTION_EVENTS=kk.INTERACTION_API=kk.INTERACTION=kk.FN_START=kk.FN_END=kk.FETCH_START=kk.FETCH_DONE=kk.FETCH_BODY=kk.FETCH=kk.FEATURE_NAME=kk.END=kk.CB_START=kk.CB_END=kk.BODY=void 0;var t=p();var e=B();kk.FEATURE_NAME=e.FEATURE_NAMES.spa;kk.INTERACTION_EVENTS=["click","submit","keypress","keydown","keyup","change"];kk.MAX_TIMER_BUDGET=999;kk.FN_START="fn-start";kk.FN_END="fn-end";kk.CB_START="cb-start";kk.INTERACTION_API="api-ixn-";kk.REMAINING="remaining";kk.INTERACTION="interaction";kk.SPA_NODE="spaNode";kk.JSONP_NODE="jsonpNode";kk.FETCH_START="fetch-start";kk.FETCH_DONE="fetch-done";kk.FETCH_BODY="fetch-body-";kk.JSONP_END="jsonp-end";kk.originalSetTimeout=(0,t.gosNREUMOriginals)().o.ST;kk.START="-start";const i=kk.END="-end";kk.BODY="-body";kk.CB_END="cb"+i;kk.JS_TIME="jsTime";kk.FETCH="fetch";return kk}var Ik;function Ok(){if(Ik)return hk;Ik=1;Object.defineProperty(hk,"__esModule",{value:true});hk.Aggregate=void 0;var t=F();var e=nk();var i=ee();var r=cn();var n=I();var s=yk();var o=X();var a=Sk();var u=_();var f=S(Mk());var l=B();var c=$i();var h=En();var v=jn();var p=A();var m=d();var g=R();var y=Ut();var b=Q();function w(t){if("function"!=typeof WeakMap)return null;var e=new WeakMap,i=new WeakMap;return(w=function(t){return t?i:e})(t)}function S(t,e){if(t&&t.__esModule)return t;if(null===t||"object"!=typeof t&&"function"!=typeof t)return{default:t};var i=w(e);if(i&&i.has(t))return i.get(t);var r={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in t)if("default"!==s&&{}.hasOwnProperty.call(t,s)){var o=n?Object.getOwnPropertyDescriptor(t,s):null;o&&(o.get||o.set)?Object.defineProperty(r,s,o):r[s]=t[s]}return r.default=t,i&&i.set(t,r),r}const{FEATURE_NAME:k,INTERACTION_EVENTS:C,MAX_TIMER_BUDGET:M,FN_START:O,FN_END:T,CB_START:E,INTERACTION_API:x,REMAINING:j,INTERACTION:N,SPA_NODE:D,JSONP_NODE:L,FETCH_START:P,FETCH_DONE:U,FETCH_BODY:G,JSONP_END:W,originalSetTimeout:z}=f;class V extends c.AggregateBase{static featureName=k;constructor(r){super(r,k);const f=this.state={initialPageURL:m.initialLocation,lastSeenUrl:m.initialLocation,lastSeenRouteName:null,timerMap:{},timerBudget:M,currentNode:null,prevNode:null,nodeOnLastHashUpdate:null,initialPageLoad:null,pageLoaded:false,childTime:0,depth:0,disableSpaFix:(r.init.feature_flags||[]).indexOf("disable-spa-fix")>-1};this.spaSerializerClass=new a.Serializer(r);const c=this;const d=u.ee.get(r.agentIdentifier);const w=d.get("mutation");const S=d.get("promise");const I=d.get("history");const A=d.get("events");const _=d.get("timer");const R=d.get("fetch");const F=d.get("jsonp");const B=d.get("xhr");const V=d.get("tracer");let H;this.waitForFlags(["spa"]).then((([t])=>{if(t){H=r.runtime.harvester;this.drain()}else{this.blocked=true;this.deregisterDrain()}}));if(r.init.spa.enabled!==true)return;f.initialPageLoad=new s.Interaction("initialPageLoad",0,f.lastSeenUrl,f.lastSeenRouteName,et,r);f.initialPageLoad.save=true;if(r.runtime.session?.isNew)f.initialPageLoad.root.attrs.custom.isFirstOfSession=true;f.prevInteraction=f.initialPageLoad;f.currentNode=f.initialPageLoad.root;f.initialPageLoad[j]++;(0,t.registerHandler)(O,Z,this.featureName,d);(0,t.registerHandler)(E,Z,this.featureName,S);var Y={getCurrentNode:Q,setCurrentNode:tt};(0,t.registerHandler)("spa-register",(function(t){if(typeof t==="function"){t(Y)}}),l.FEATURE_NAMES.spa,d);function Z(){f.depth++;this.prevNode=f.currentNode;this.ct=f.childTime;f.childTime=0;f.timerBudget=M}(0,t.registerHandler)(T,X,this.featureName,d);(0,t.registerHandler)("cb-end",X,this.featureName,S);function X(){f.depth--;var t=this.jsTime||0;var e=t-f.childTime;f.childTime=this.ct+t;if(f.currentNode){f.currentNode.callback(e,this[T]);if(this.isTraced){f.currentNode.attrs.tracedTime=e}}this.jsTime=f.currentNode?0:e;tt(this.prevNode);this.prevNode=null;f.timerBudget=M}(0,t.registerHandler)(O,(function(t,e){var i=t[0];var n=i.type;var o=i["__nrNode:".concat(p.bundleId)];if(!f.pageLoaded&&(n==="load"&&e===window||m.loadedAsDeferredBrowserScript)){f.pageLoaded=true;this.prevNode=f.currentNode=null;if(f.initialPageLoad){o=f.initialPageLoad.root;f.initialPageLoad[j]=0;z((function(){C.push("popstate")}))}}if(o){tt(o)}else if(n==="hashchange"){tt(f.nodeOnLastHashUpdate);f.nodeOnLastHashUpdate=null}else if(e instanceof XMLHttpRequest){tt(d.context(e).spaNode)}else if(!f.currentNode&&C.indexOf(n)!==-1){var a=new s.Interaction(n,this[O],f.lastSeenUrl,f.lastSeenRouteName,et,r);f.prevInteraction=a;tt(a.root);if(n==="click"){var u=it(i.target);if(u){f.currentNode.attrs.custom.actionText=u}}}i["__nrNode:".concat(p.bundleId)]=f.currentNode}),this.featureName,A);(0,t.registerHandler)("setTimeout-end",(function t(e,i,r){if(!f.currentNode||f.timerBudget-this.timerDuration<0)return;if(e&&!(e[0]instanceof Function))return;f.currentNode[N][j]++;this.timerId=r;f.timerMap[r]=f.currentNode;this.timerBudget=f.timerBudget-50}),this.featureName,_);(0,t.registerHandler)("clearTimeout-start",(function t(e){var i=e[0];var r=f.timerMap[i];if(r){var n=r[N];n[j]--;n.checkFinish();delete f.timerMap[i]}}),this.featureName,_);(0,t.registerHandler)(O,(function(){f.timerBudget=this.timerBudget||M;var t=this.timerId;var e=f.timerMap[t];tt(e);delete f.timerMap[t];if(e)e[N][j]--}),this.featureName,_);(0,t.registerHandler)(O,(function(){tt(this[D])}),this.featureName,B);(0,t.registerHandler)("new-xhr",(function(){if(!f.disableSpaFix&&!f.currentNode&&f.prevInteraction&&!f.prevInteraction.ignored){const t=f.prevInteraction;f.currentNode=t.root;t.root.end=null}if(f.currentNode){this[D]=f.currentNode.child("ajax",null,null,true)}}),this.featureName,B);(0,t.registerHandler)("send-xhr-start",(function(){var t=this[D];if(t&&!this.sent){this.sent=true;t.dt=this.dt;if(t.dt?.timestamp){t.dt.timestamp=r.runtime.timeKeeper.correctAbsoluteTimestamp(t.dt.timestamp)}t.jsEnd=t.start=this.startTime;t[N][j]++}}),this.featureName,B);(0,t.registerHandler)("xhr-resolved",(function(){var t=this[D];if(t){if(!(0,i.shouldCollectEvent)(this.params)){t.cancel();return}var e=t.attrs;e.params=this.params;e.metrics=this.metrics;t.finish(this.endTime);if(!!this.currentNode&&!!this.currentNode.interaction)this.currentNode.interaction.checkFinish()}}),this.featureName,d);(0,t.registerHandler)("new-jsonp",(function(t){if(f.currentNode){var e=this[L]=f.currentNode.child("ajax",this[P]);e.start=this["new-jsonp"];this.url=t;this.status=null}}),this.featureName,F);(0,t.registerHandler)("cb-start",(function(t){var e=this[L];if(e){tt(e);this.status=200}}),this.featureName,F);(0,t.registerHandler)("jsonp-error",(function(){var t=this[L];if(t){tt(t);this.status=0}}),this.featureName,F);(0,t.registerHandler)(W,(function(){var t=this[L];if(t){if(this.status===null){t.cancel();return}var i=t.attrs;var r=i.params={};var n=(0,e.parseUrl)(this.url);r.method="GET";r.pathname=n.pathname;r.host=n.hostname+":"+n.port;r.status=this.status;i.metrics={txSize:0,rxSize:0};i.isJSONP=true;t.jsEnd=this[W];t.jsTime=this[E]?this[W]-this[E]:0;t.finish(t.jsEnd)}}),this.featureName,F);(0,t.registerHandler)(P,(function(t,e){if(t){if(!f.disableSpaFix&&!f.currentNode&&f.prevInteraction&&!f.prevInteraction.ignored){const t=f.prevInteraction;f.currentNode=t.root;t.root.end=null}if(f.currentNode){this[D]=f.currentNode.child("ajax",this[P]);if(e&&this[D]){this[D].dt=e;if(this[D].dt?.timestamp){this[D].dt.timestamp=r.runtime.timeKeeper.correctAbsoluteTimestamp(this[D].dt.timestamp)}}}}}),this.featureName,R);(0,t.registerHandler)(G+"start",(function(t){if(f.currentNode){this[D]=f.currentNode;f.currentNode[N][j]++}}),this.featureName,R);(0,t.registerHandler)(G+"end",(function(t,e,i){var r=this[D];if(r)r[N][j]--}),this.featureName,R);(0,t.registerHandler)(U,(function(t,e){var r=this[D];if(r){if(t||!(0,i.shouldCollectEvent)(this.params)){r.cancel();return}var n=r.attrs;n.params=this.params;n.metrics={txSize:this.txSize,rxSize:this.rxSize};n.isFetch=true;r.finish(this[U])}}),this.featureName,R);(0,t.registerHandler)("newURL",(function(t,e){if(f.currentNode){f.currentNode[N].setNewURL(t)}else if(f.prevInteraction&&!f.prevInteraction.ignored){const e=f.prevInteraction;e.setNewURL(t);e.root.end=null;tt(e.root)}if(f.currentNode){if(f.lastSeenUrl!==t){f.currentNode[N].routeChange=true}if(e){f.nodeOnLastHashUpdate=f.currentNode}}f.lastSeenUrl=t}),this.featureName,I);F.on("dom-start",(function(t){if(!f.currentNode)return;var e=t[0];var i=e&&e.nodeName==="SCRIPT"&&e.src!=="";var r=f.currentNode.interaction;if(i){r[j]++;e.addEventListener("load",n,(0,o.eventListenerOpts)(false));e.addEventListener("error",s,(0,o.eventListenerOpts)(false))}function n(){r[j]--;r.checkFinish()}function s(){r[j]--;r.checkFinish()}}));(0,t.registerHandler)(O,(function(){tt(f.prevNode)}),this.featureName,w);(0,t.registerHandler)("resolve-start",q,this.featureName,S);(0,t.registerHandler)("executor-err",q,this.featureName,S);(0,t.registerHandler)("propagate",K,this.featureName,S);(0,t.registerHandler)(E,(function(){var t=this.getCtx?this.getCtx():this;tt(t[D])}),this.featureName,S);(0,t.registerHandler)(x+"get",(function(t){var e;if(f?.currentNode?.[N])e=this.ixn=f.currentNode[N];else if(f?.prevNode?.end===null&&f?.prevNode?.[N]?.root?.[N]?.eventName!=="initialPageLoad")e=this.ixn=f.prevNode[N];else e=this.ixn=new s.Interaction("api",t,f.lastSeenUrl,f.lastSeenRouteName,et,r);if(!f.currentNode){e.checkFinish();if(f.depth)tt(e.root)}}),this.featureName,d);(0,t.registerHandler)(x+"actionText",(function(t,e){var i=this.ixn.root.attrs.custom;if(e)i.actionText=e}),this.featureName,d);(0,t.registerHandler)(x+"setName",(function(t,e,i){var r=this.ixn.root.attrs;if(e)r.customName=e;if(i)r.trigger=i}),this.featureName,d);(0,t.registerHandler)(x+"setAttribute",(function(t,e,i){this.ixn.root.attrs.custom[e]=i}),this.featureName,d);(0,t.registerHandler)(x+"end",(function(t){var e=this.ixn;var i=J(e);tt(null);i.child("customEnd",t)?.finish(t);e.finish()}),this.featureName,d);(0,t.registerHandler)(x+"ignore",(function(t){this.ixn.ignored=true}),this.featureName,d);(0,t.registerHandler)(x+"save",(function(t){this.ixn.save=true}),this.featureName,d);(0,t.registerHandler)(x+"tracer",(function(t,e,i){var r=this.ixn;var n=J(r);var s=d.context(i);if(!e){s.inc=++r[j];return s[D]=n}s[D]=n.child("customTracer",t,e)}),this.featureName,d);(0,t.registerHandler)(O,$,this.featureName,V);(0,t.registerHandler)("no-"+O,$,this.featureName,V);function $(t,e,i){var r=this[D];if(!r)return;var n=r[N];var s=this.inc;this.isTraced=true;if(s){n[j]--}else if(r){r.finish(t)}i?tt(r):n.checkFinish()}(0,t.registerHandler)(x+"getContext",(function(t,e){var i=this.ixn.root.attrs.store;setTimeout((function(){e(i)}),0)}),this.featureName,d);(0,t.registerHandler)(x+"onEnd",(function(t,e){this.ixn.handlers.push(e)}),this.featureName,d);(0,t.registerHandler)("api-routeName",(function(t,e){f.lastSeenRouteName=e;if(f.currentNode)f.currentNode[N].setNewRoute(e)}),this.featureName,d);function J(t){return f.currentNode&&f.currentNode[N]===t?f.currentNode:t.root}function K(t,e){if(e||!this[D])this[D]=f.currentNode}function q(){if(!this.resolved){this.resolved=true;this[D]=f.currentNode}}function Q(){return f.currentNode}function tt(t){if(!f.pageLoaded&&!t&&f.initialPageLoad)t=f.initialPageLoad.root;if(f.currentNode){f.currentNode[N].checkFinish()}f.prevNode=f.currentNode;f.currentNode=t&&!t[N].root.end?t:null}function et(t){if(t===f.initialPageLoad)f.initialPageLoad=null;var e=t.root;var i=e.attrs;f.currentNode=e;Object.values(t.handlers||{}).forEach((function(t){t(i.store)}));tt(null)}d.on("spa-jserror",(function(t,e,i,r){if(!f.currentNode)return;i._interactionId=f.currentNode.interaction.id;if(f.currentNode.type&&f.currentNode.type!=="interaction"){i._interactionNodeId=f.currentNode.id}}));(0,t.registerHandler)("function-err",(function(t,e,i){if(!f.currentNode)return;i.__newrelic??={};i.__newrelic[r.agentIdentifier]={interactionId:f.currentNode.interaction.id};if(f.currentNode.type&&f.currentNode.type!=="interaction"){i.__newrelic[r.agentIdentifier].interactionNodeId=f.currentNode.id}}),this.featureName,d);d.on("interaction",rt);function it(t){var e=t.tagName.toLowerCase();var i=["a","button","input"];var r=i.indexOf(e)!==-1;if(r){return t.title||t.value||t.innerText}}function rt(t){if(t.ignored||!t.save&&!t.routeChange){d.emit("interactionDone",[t,false]);return}if(f.prevInteraction===t){f.prevInteraction=null}t.root.attrs.id=(0,n.generateUuid)();if(t.root.attrs.trigger==="initialPageLoad"){t.root.attrs.firstPaint=v.firstPaint.current.value;t.root.attrs.firstContentfulPaint=h.firstContentfulPaint.current.value}d.emit("interactionDone",[t,true]);c.events.add(t);let e;if(t.root?.attrs?.trigger==="initialPageLoad")e="InitialPageLoad";else if(t.routeChange)e="RouteChange";else e="Custom";(0,g.handle)(y.SUPPORTABILITY_METRIC_CHANNEL,["Spa/Interaction/".concat(e,"/Duration/Ms"),Math.max((t.root?.end||0)-(t.root?.start||0),0)],undefined,l.FEATURE_NAMES.metrics,d);if(!H){(0,b.warn)(19);return}H.triggerHarvestFor(c)}}serializer(t){return this.spaSerializerClass.serializeMultiple(t,0,r.navTimingValues)}}hk.Aggregate=V;return hk}var Ak={};var Tk={};var _k;function Ek(){if(_k)return Tk;_k=1;Object.defineProperty(Tk,"__esModule",{value:true});Tk.NODE_TYPE=Tk.IPL_TRIGGER_NAME=Tk.INTERACTION_TYPE=Tk.INTERACTION_TRIGGERS=Tk.INTERACTION_STATUS=Tk.FEATURE_NAME=Tk.API_TRIGGER_NAME=void 0;var t=B();Tk.INTERACTION_TRIGGERS=["click","keydown","submit","popstate"];Tk.API_TRIGGER_NAME="api";Tk.IPL_TRIGGER_NAME="initialPageLoad";Tk.FEATURE_NAME=t.FEATURE_NAMES.softNav;Tk.INTERACTION_TYPE={INITIAL_PAGE_LOAD:"",ROUTE_CHANGE:1,UNSPECIFIED:2};Tk.NODE_TYPE={INTERACTION:1,AJAX:2,CUSTOM_END:3,CUSTOM_TRACER:4};Tk.INTERACTION_STATUS={IP:"in progress",FIN:"finished",CAN:"cancelled"};return Tk}var Rk={};var xk={};var jk;function Nk(){if(jk)return xk;jk=1;Object.defineProperty(xk,"__esModule",{value:true});xk.BelNode=void 0;let t=0;class e{belType;children=[];start;end;callbackEnd=0;callbackDuration=0;nodeId=++t;constructor(t){this.obfuscator=t.runtime.obfuscator;this.info=t.info}addChild(t){this.children.push(t)}serialize(){}}xk.BelNode=e;return xk}var Dk;function Fk(){if(Dk)return Rk;Dk=1;Object.defineProperty(Rk,"__esModule",{value:true});Rk.AjaxNode=void 0;var t=nr();var e=Ek();var i=Nk();class r extends i.BelNode{constructor(t,i){super(t);this.belType=e.NODE_TYPE.AJAX;this.method=i.method;this.status=i.status;this.domain=i.domain;this.path=i.path;this.txSize=i.requestSize;this.rxSize=i.responseSize;this.requestedWith=i.type==="fetch"?1:"";this.spanId=i.spanId;this.traceId=i.traceId;this.spanTimestamp=i.spanTimestamp;this.gql=i.gql;this.start=i.startTime;this.end=i.endTime}serialize(e){const i=(0,t.getAddStringContext)(this.obfuscator);const r=[];const n=[(0,t.numeric)(this.belType),0,(0,t.numeric)(this.start-e),(0,t.numeric)(this.end-this.start),(0,t.numeric)(this.callbackEnd),(0,t.numeric)(this.callbackDuration),i(this.method),(0,t.numeric)(this.status),i(this.domain),i(this.path),(0,t.numeric)(this.txSize),(0,t.numeric)(this.rxSize),this.requestedWith,i(this.nodeId),(0,t.nullable)(this.spanId,i,true)+(0,t.nullable)(this.traceId,i,true)+(0,t.nullable)(this.spanTimestamp,t.numeric)];let s=[];if(typeof this.gql==="object")s=(0,t.addCustomAttributes)(this.gql,i);this.children.forEach((t=>s.push(t.serialize())));n[1]=(0,t.numeric)(s.length);r.push(n);if(s.length)r.push(s.join(";"));return r.join(";")}}Rk.AjaxNode=r;return Rk}var Lk={};var Pk={};var Bk;function Uk(){if(Bk)return Pk;Bk=1;Object.defineProperty(Pk,"__esModule",{value:true});Pk.Interaction=void 0;var t=d();var e=I();var i=nr();var r=c();var n=Mi();var s=Ek();var o=Nk();class a extends o.BelNode{id=(0,e.generateUuid)();initialPageURL=t.initialLocation;customName;customAttributes={};customDataByApi={};queueTime;appTime;newRoute;status=s.INTERACTION_STATUS.IP;domTimestamp=0;historyTimestamp=0;createdByApi=false;keepOpenUntilEndApi=false;onDone=[];cancellationTimer;constructor(e,i,r,n,o){super(e);this.belType=s.NODE_TYPE.INTERACTION;this.trigger=i;this.start=r;this.oldRoute=n;this.eventSubscription=new Map([["finished",[]],["cancelled",[]]]);this.forceSave=this.forceIgnore=false;if(this.trigger===s.API_TRIGGER_NAME)this.createdByApi=true;this.newURL=this.oldURL=o||t.globalScope?.location.href}updateDom(t){this.domTimestamp=t||(0,r.now)()}updateHistory(e,i){this.newURL=i||""+t.globalScope?.location;this.historyTimestamp=e||(0,r.now)()}seenHistoryAndDomChange(){return this.historyTimestamp>0&&this.domTimestamp>this.historyTimestamp}on(t,e){if(!this.eventSubscription.has(t))throw new Error("Cannot subscribe to non pre-defined events.");if(typeof e!=="function")throw new Error("Must supply function as callback.");this.eventSubscription.get(t).push(e)}done(t){if(this.keepOpenUntilEndApi&&t===undefined)return false;if(this.status!==s.INTERACTION_STATUS.IP)return true;this.onDone.forEach((t=>t(this.customDataByApi)));if(this.forceIgnore)this.#x();else if(this.seenHistoryAndDomChange())this.#j(t);else if(this.forceSave)this.#j(t||performance.now());else this.#x();return true}#j(t=0){clearTimeout(this.cancellationTimer);this.end=Math.max(this.domTimestamp,this.historyTimestamp,t);this.customAttributes={...this.info.jsAttributes,...this.customAttributes};this.status=s.INTERACTION_STATUS.FIN;const e=this.eventSubscription.get("finished");e.forEach((t=>t()))}#x(){clearTimeout(this.cancellationTimer);this.status=s.INTERACTION_STATUS.CAN;const t=this.eventSubscription.get("cancelled");t.forEach((t=>t()))}isActiveDuring(t){if(this.status===s.INTERACTION_STATUS.IP)return this.start<=t;return this.status===s.INTERACTION_STATUS.FIN&&this.start<=t&&this.end>t}get firstPaint(){}get firstContentfulPaint(){}get navTiming(){}serialize(t){const e=t===undefined;const r=(0,i.getAddStringContext)(this.obfuscator);const o=[];let a;if(this.trigger===s.IPL_TRIGGER_NAME)a=s.INTERACTION_TYPE.INITIAL_PAGE_LOAD;else if(this.newURL!==this.oldURL)a=s.INTERACTION_TYPE.ROUTE_CHANGE;else a=s.INTERACTION_TYPE.UNSPECIFIED;const u=[(0,i.numeric)(this.belType),0,(0,i.numeric)(this.start-(e?0:t)),(0,i.numeric)(this.end-this.start),(0,i.numeric)(this.callbackEnd),(0,i.numeric)(this.callbackDuration),r(this.trigger),r((0,n.cleanURL)(this.initialPageURL,true)),r((0,n.cleanURL)(this.oldURL,true)),r((0,n.cleanURL)(this.newURL,true)),r(this.customName),a,(0,i.nullable)(this.queueTime,i.numeric,true)+(0,i.nullable)(this.appTime,i.numeric,true)+(0,i.nullable)(this.oldRoute,r,true)+(0,i.nullable)(this.newRoute,r,true)+r(this.id),r(this.nodeId),(0,i.nullable)(this.firstPaint,i.numeric,true)+(0,i.nullable)(this.firstContentfulPaint,i.numeric)];const f=(0,i.addCustomAttributes)(this.customAttributes||{},r);if(this.info.atts)f.push("a,"+r(this.info.atts));this.children.forEach((i=>f.push(i.serialize(e?this.start:t))));u[1]=(0,i.numeric)(f.length);o.push(u);if(f.length)o.push(f.join(";"));if(this.navTiming)o.push(this.navTiming);else o.push("");return o.join(";")}}Pk.Interaction=a;return Pk}var Gk;function Wk(){if(Gk)return Lk;Gk=1;Object.defineProperty(Lk,"__esModule",{value:true});Lk.InitialPageLoadInteraction=void 0;var t=cn();var e=Uk();var i=nr();var r=jn();var n=En();var s=Ek();class o extends e.Interaction{constructor(t){super(t,s.IPL_TRIGGER_NAME,0,null);this.queueTime=t.info.queueTime;this.appTime=t.info.applicationTime}get firstPaint(){return r.firstPaint.current.value}get firstContentfulPaint(){return n.firstContentfulPaint.current.value}get navTiming(){if(!t.navTimingValues.length)return;let e=",";let r="b";let n=0;t.navTimingValues.slice(1,21).forEach((t=>{if(t!==undefined){r+=e+(0,i.numeric)(t-n);e=",";n=t}else{r+=e+"!";e=""}}));return r}}Lk.InitialPageLoadInteraction=o;return Lk}var zk;function Vk(){if(zk)return Ak;zk=1;Object.defineProperty(Ak,"__esModule",{value:true});Ak.Aggregate=void 0;var t=R();var e=F();var i=ut();var r=Fn();var n=B();var s=$i();var o=Ek();var a=Fk();var u=Wk();var f=Uk();class l extends s.AggregateBase{static featureName=o.FEATURE_NAME;constructor(t,{domObserver:i}){super(t,o.FEATURE_NAME);this.interactionsToHarvest=this.events;this.domObserver=i;this.initialPageLoadInteraction=new u.InitialPageLoadInteraction(t);this.initialPageLoadInteraction.onDone.push((()=>{if(t.runtime.session?.isNew)this.initialPageLoadInteraction.customAttributes.isFirstOfSession=true;this.initialPageLoadInteraction.forceSave=true;const e=this.initialPageLoadInteraction;this.interactionsToHarvest.add(e);this.initialPageLoadInteraction=null}));r.timeToFirstByte.subscribe((({attrs:t})=>{const e=t.navigationEntry.loadEventEnd;this.initialPageLoadInteraction.done(e);this.reportSupportabilityMetric("SoftNav/Interaction/InitialPageLoad/Duration/Ms",Math.round(e))}));this.latestRouteSetByApi=null;this.interactionInProgress=null;this.latestHistoryUrl=null;this.harvestOpts.beforeUnload=()=>this.interactionInProgress?.done();this.waitForFlags(["spa"]).then((([e])=>{if(e){this.drain();setTimeout((()=>t.runtime.harvester.triggerHarvestFor(this)),0)}else{this.blocked=true;this.deregisterDrain()}}));(0,e.registerHandler)("newUIEvent",(t=>this.startUIInteraction(t.type,Math.floor(t.timeStamp),t.target)),this.featureName,this.ee);(0,e.registerHandler)("newURL",((t,e)=>{this.latestHistoryUrl=e;this.interactionInProgress?.updateHistory(t,e)}),this.featureName,this.ee);(0,e.registerHandler)("newDom",(t=>{this.interactionInProgress?.updateDom(t);if(this.interactionInProgress?.seenHistoryAndDomChange())this.interactionInProgress.done()}),this.featureName,this.ee);this.#N();(0,e.registerHandler)("ajax",this.#D.bind(this),this.featureName,this.ee);(0,e.registerHandler)("jserror",this.#F.bind(this),this.featureName,this.ee)}serializer(t){let e;const i=[];for(const r of t){i.push(r.serialize(e));if(e===undefined)e=Math.floor(r.start)}return"bel.7;".concat(i.join(";"))}startUIInteraction(t,e,i){if(this.interactionInProgress?.createdByApi)return;if(this.interactionInProgress?.done()===false)return;const r=t===o.INTERACTION_TRIGGERS[3]?this.latestHistoryUrl:undefined;this.interactionInProgress=new f.Interaction(this.agentRef,t,e,this.latestRouteSetByApi,r);if(t===o.INTERACTION_TRIGGERS[0]){const t=c(i);if(t)this.interactionInProgress.customAttributes.actionText=t}this.interactionInProgress.cancellationTimer=setTimeout((()=>{this.interactionInProgress.done();this.reportSupportabilityMetric("SoftNav/Interaction/TimeOut")}),3e4);this.setClosureHandlers()}setClosureHandlers(){this.interactionInProgress.on("finished",(()=>{const t=this.interactionInProgress;this.interactionsToHarvest.add(this.interactionInProgress);this.interactionInProgress=null;this.domObserver.disconnect();this.reportSupportabilityMetric("SoftNav/Interaction/".concat(t.newURL!==t.oldURL?"RouteChange":"Custom","/Duration/Ms"),Math.round(t.end-t.start))}));this.interactionInProgress.on("cancelled",(()=>{this.interactionInProgress=null;this.domObserver.disconnect()}))}getInteractionFor(t){if(this.interactionInProgress?.isActiveDuring(t))return this.interactionInProgress;let e;const[{data:i}]=this.interactionsToHarvest.get();for(let r=i.length-1;r>=0;r--){const n=i[r];if(n.isActiveDuring(t)){if(n.trigger!==o.IPL_TRIGGER_NAME)return n;else e=n}}if(e)return e;if(this.initialPageLoadInteraction?.isActiveDuring(t))return this.initialPageLoadInteraction}#D(e){const i=this.getInteractionFor(e.startTime);if(!i){(0,t.handle)("returnAjax",[e],undefined,n.FEATURE_NAMES.ajax,this.ee)}else{if(i.status===o.INTERACTION_STATUS.FIN)r(this.agentRef,e,i);else{i.on("finished",(()=>r(this.agentRef,e,i)));i.on("cancelled",(()=>(0,t.handle)("returnAjax",[e],undefined,n.FEATURE_NAMES.ajax,this.ee)))}}function r(t,e,i){const r=new a.AjaxNode(t,e);i.addChild(r)}}#F(e,r){const s=this.getInteractionFor(r);if(!s)return;e.browserInteractionId=s.id;if(s.status===o.INTERACTION_STATUS.FIN){e._softNavFinished=true;e._softNavAttributes=s.customAttributes}else{s.on("finished",(0,i.single)((()=>(0,t.handle)("softNavFlush",[s.id,true,s.customAttributes],undefined,n.FEATURE_NAMES.jserrors,this.ee))));s.on("cancelled",(0,i.single)((()=>(0,t.handle)("softNavFlush",[s.id,false,undefined],undefined,n.FEATURE_NAMES.jserrors,this.ee))))}}#N(){const t="api-ixn-";const i=this;(0,e.registerHandler)(t+"get",(function(t,{waitForEnd:e}={}){this.associatedInteraction=i.getInteractionFor(t);if(this.associatedInteraction?.trigger===o.IPL_TRIGGER_NAME)this.associatedInteraction=null;if(!this.associatedInteraction){this.associatedInteraction=i.interactionInProgress=new f.Interaction(i.agentRef,o.API_TRIGGER_NAME,t,i.latestRouteSetByApi);i.domObserver.observe(document.body,{attributes:true,childList:true,subtree:true,characterData:true});i.setClosureHandlers()}if(e===true)this.associatedInteraction.keepOpenUntilEndApi=true}),i.featureName,i.ee);(0,e.registerHandler)(t+"end",(function(t){this.associatedInteraction.done(t)}),i.featureName,i.ee);(0,e.registerHandler)(t+"save",(function(){this.associatedInteraction.forceSave=true}),i.featureName,i.ee);(0,e.registerHandler)(t+"ignore",(function(){this.associatedInteraction.forceIgnore=true}),i.featureName,i.ee);(0,e.registerHandler)(t+"getContext",(function(t,e){if(typeof e!=="function")return;setTimeout((()=>e(this.associatedInteraction.customDataByApi)),0)}),i.featureName,i.ee);(0,e.registerHandler)(t+"onEnd",(function(t,e){if(typeof e!=="function")return;this.associatedInteraction.onDone.push(e)}),i.featureName,i.ee);(0,e.registerHandler)(t+"actionText",(function(t,e){if(e)this.associatedInteraction.customAttributes.actionText=e}),i.featureName,i.ee);(0,e.registerHandler)(t+"setName",(function(t,e,i){if(e)this.associatedInteraction.customName=e;if(i)this.associatedInteraction.trigger=i}),i.featureName,i.ee);(0,e.registerHandler)(t+"setAttribute",(function(t,e,i){this.associatedInteraction.customAttributes[e]=i}),i.featureName,i.ee);(0,e.registerHandler)(t+"routeName",(function(t,e){i.latestRouteSetByApi=e;if(i.interactionInProgress)i.interactionInProgress.newRoute=e}),i.featureName,i.ee)}}Ak.Aggregate=l;function c(t){const e=t.tagName.toLowerCase();const i=["a","button","input"];if(i.includes(e)){return t.title||t.value||t.innerText}}return Ak}var Hk;function Yk(){if(Hk)return Kt;Hk=1;Object.defineProperty(Kt,"__esModule",{value:true});Kt.lazyFeatureLoader=r;var t=B();function e(t){if("function"!=typeof WeakMap)return null;var i=new WeakMap,r=new WeakMap;return(e=function(t){return t?r:i})(t)}function i(t,i){if(t&&t.__esModule)return t;if(null===t||"object"!=typeof t&&"function"!=typeof t)return{default:t};var r=e(i);if(r&&r.has(t))return r.get(t);var n={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in t)if("default"!==o&&{}.hasOwnProperty.call(t,o)){var a=s?Object.getOwnPropertyDescriptor(t,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=t[o]}return n.default=t,r&&r.set(t,n),n}function r(e,r){if(r==="aggregate"){switch(e){case t.FEATURE_NAMES.ajax:return Promise.resolve().then((()=>i(or())));case t.FEATURE_NAMES.jserrors:return Promise.resolve().then((()=>i(jr())));case t.FEATURE_NAMES.genericEvents:return Promise.resolve().then((()=>i(Xr())));case t.FEATURE_NAMES.logging:return Promise.resolve().then((()=>i(tn())));case t.FEATURE_NAMES.metrics:return Promise.resolve().then((()=>i(an())));case t.FEATURE_NAMES.pageViewEvent:return Promise.resolve().then((()=>i(Gn())));case t.FEATURE_NAMES.pageViewTiming:return Promise.resolve().then((()=>i(ss())));case t.FEATURE_NAMES.sessionReplay:return Promise.resolve().then((()=>i(JS())));case t.FEATURE_NAMES.sessionTrace:return Promise.resolve().then((()=>i(ck())));case t.FEATURE_NAMES.spa:return Promise.resolve().then((()=>i(Ok())));case t.FEATURE_NAMES.softNav:return Promise.resolve().then((()=>i(Vk())));default:throw new Error("Attempted to load unsupported agent feature: ".concat(e," ").concat(r))}}}return Kt}var Zk;function Xk(){if(Zk)return x;Zk=1;Object.defineProperty(x,"__esModule",{value:true});x.InstrumentBase=void 0;var t=G();var e=V();var i=J();var r=d();var n=Q();var s=B();var o=st();var a=rt();var u=ut();function f(t){if("function"!=typeof WeakMap)return null;var e=new WeakMap,i=new WeakMap;return(f=function(t){return t?i:e})(t)}function l(t,e){if(t&&t.__esModule)return t;if(null===t||"object"!=typeof t&&"function"!=typeof t)return{default:t};var i=f(e);if(i&&i.has(t))return i.get(t);var r={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in t)if("default"!==s&&{}.hasOwnProperty.call(t,s)){var o=n?Object.getOwnPropertyDescriptor(t,s):null;o&&(o.get||o.set)?Object.defineProperty(r,s,o):r[s]=t[s]}return r.default=t,i&&i.set(t,r),r}class c extends e.FeatureBase{constructor(e,i,r=true){super(e.agentIdentifier,i);this.auto=r;this.abortHandler=undefined;this.featAggregate=undefined;this.onAggregateImported=undefined;if(e.init[this.featureName].autoStart===false)this.auto=false;if(this.auto)(0,t.registerDrain)(e.agentIdentifier,i);else{this.ee.on("manual-start-all",(0,u.single)((()=>{(0,t.registerDrain)(e.agentIdentifier,this.featureName);this.auto=true;this.importAggregator(e)})))}}importAggregator(e,o={}){if(this.featAggregate||!this.auto)return;let u;this.onAggregateImported=new Promise((t=>{u=t}));const f=async()=>{let i;try{if((0,a.canEnableSessionTracking)(e.init)){const{setupAgentSession:t}=await Promise.resolve().then((()=>l(Jt())));i=t(e)}}catch(t){(0,n.warn)(20,t);this.ee.emit("internal-error",[t]);if(this.featureName===s.FEATURE_NAMES.sessionReplay)this.abortHandler?.()}try{if(!this.#L(this.featureName,i,e.init)){(0,t.drain)(this.agentIdentifier,this.featureName);u(false);return}const{lazyFeatureLoader:r}=await Promise.resolve().then((()=>l(Yk())));const{Aggregate:n}=await r(this.featureName,"aggregate");this.featAggregate=new n(e,o);e.runtime.harvester.initializedAggregates.push(this.featAggregate);u(true)}catch(e){(0,n.warn)(34,e);this.abortHandler?.();(0,t.drain)(this.agentIdentifier,this.featureName,true);u(false);if(this.ee)this.ee.abort()}};if(!r.isBrowserScope)f();else(0,i.onWindowLoad)((()=>f()),true)}#L(t,e,i){switch(t){case s.FEATURE_NAMES.sessionReplay:return(0,o.hasReplayPrerequisite)(i)&&!!e;case s.FEATURE_NAMES.sessionTrace:return!!e;default:return true}}}x.InstrumentBase=c;return x}var $k;function Jk(){if($k)return n;$k=1;Object.defineProperty(n,"__esModule",{value:true});n.PageViewEvent=n.Instrument=void 0;var t=R();var e=Xk();var i=s(vn());function r(t){if("function"!=typeof WeakMap)return null;var e=new WeakMap,i=new WeakMap;return(r=function(t){return t?i:e})(t)}function s(t,e){if(t&&t.__esModule)return t;if(null===t||"object"!=typeof t&&"function"!=typeof t)return{default:t};var i=r(e);if(i&&i.has(t))return i.get(t);var n={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in t)if("default"!==o&&{}.hasOwnProperty.call(t,o)){var a=s?Object.getOwnPropertyDescriptor(t,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=t[o]}return n.default=t,i&&i.set(t,n),n}class o extends e.InstrumentBase{static featureName=i.FEATURE_NAME;constructor(e,r=true){super(e,i.FEATURE_NAME,r);this.ee.on("api-send-rum",((e,i)=>(0,t.handle)("send-rum",[e,i],undefined,this.featureName,this.ee)));this.importAggregator(e)}}n.Instrument=o;n.PageViewEvent=o;return n}var Kk={};var qk;function Qk(){if(qk)return Kk;qk=1;Object.defineProperty(Kk,"__esModule",{value:true});Kk.getEnabledFeatures=i;var t=B();const e=Object.values(t.FEATURE_NAMES);function i(t){const i={};e.forEach((e=>{i[e]=!!t[e]?.enabled}));return i}return Kk}var tC={};var eC;function iC(){if(eC)return tC;eC=1;Object.defineProperty(tC,"__esModule",{value:true});tC.MicroAgentBase=void 0;var t=Q();var e=I();class i{agentIdentifier;constructor(){this.agentIdentifier=(0,e.generateRandomHexString)(16)}#P(e,...r){if(this[e]===i.prototype[e])(0,t.warn)(35,e);else return this[e](...r)}addPageAction(t,e){return this.#P("addPageAction",t,e)}register(t){return this.#P("register",t)}recordCustomEvent(t,e){return this.#P("recordCustomEvent",t,e)}setPageViewName(t,e){return this.#P("setPageViewName",t,e)}setCustomAttribute(t,e,i){return this.#P("setCustomAttribute",t,e,i)}noticeError(t,e){return this.#P("noticeError",t,e)}setUserId(t){return this.#P("setUserId",t)}setApplicationVersion(t){return this.#P("setApplicationVersion",t)}setErrorHandler(t){return this.#P("setErrorHandler",t)}addRelease(t,e){return this.#P("addRelease",t,e)}log(t,e){return this.#P("log",t,e)}}tC.MicroAgentBase=i;return tC}var rC;function nC(){if(rC)return r;rC=1;Object.defineProperty(r,"__esModule",{value:true});r.MicroAgent=void 0;var t=Jk();var e=Qk();var i=ui();var n=p();var s=B();var o=Q();var a=iC();function u(t){if("function"!=typeof WeakMap)return null;var e=new WeakMap,i=new WeakMap;return(u=function(t){return t?i:e})(t)}function f(t,e){if(t&&t.__esModule)return t;if(null===t||"object"!=typeof t&&"function"!=typeof t)return{default:t};var i=u(e);if(i&&i.has(t))return i.get(t);var r={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in t)if("default"!==s&&{}.hasOwnProperty.call(t,s)){var o=n?Object.getOwnPropertyDescriptor(t,s):null;o&&(o.get||o.set)?Object.defineProperty(r,s,o):r[s]=t[s]}return r.default=t,i&&i.set(t,r),r}const l=[s.FEATURE_NAMES.jserrors,s.FEATURE_NAMES.genericEvents,s.FEATURE_NAMES.metrics,s.FEATURE_NAMES.logging];class c extends a.MicroAgentBase{constructor(r){super();this.features={};(0,n.setNREUMInitializedAgent)(this.agentIdentifier,this);(0,i.configure)(this,{...r,runtime:{isolatedBacklog:true}},r.loaderType||"micro-agent");this.start=i=>{try{if(i===undefined||Array.isArray(i)&&i.length===0)i=l;else if(typeof i==="string")i=[i];if(i.some((t=>!l.includes(t))))(0,o.warn)(37,l);const r=(0,e.getEnabledFeatures)(this.init);try{this.features.page_view_event=new t.Instrument(this)}catch(t){(0,o.warn)(24,t)}this.features.page_view_event.onAggregateImported.then((()=>{l.forEach((t=>{if(r[t]&&i.includes(t)){Promise.resolve().then((()=>f(Yk()))).then((({lazyFeatureLoader:e})=>e(t,"aggregate"))).then((({Aggregate:e})=>{this.features[t]=new e(this);this.runtime.harvester.initializedAggregates.push(this.features[t])})).catch((t=>(0,o.warn)(25,t)))}}))}));return true}catch(t){(0,o.warn)(26,t);return false}};this.start(l.filter((t=>!!this.init[t].autoStart)))}get config(){return{info:this.info,init:this.init,loader_config:this.loader_config,runtime:this.runtime}}get api(){return this}}r.MicroAgent=c;return r}var sC=nC();var oC=e(sC);var aC=t({__proto__:null,default:oC},[sC]);export{aC as m};
//# sourceMappingURL=p-DUfjYV1O.js.map