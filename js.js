(self.webpackChunkportal = self.webpackChunkportal || []).push([["node_modules_porsche-customer_navigate-header_dist_esm_phn-header_3_entry_js"], {
    52030: (ln, xe, O) => {
        O.r(xe),
        O.d(xe, {
            phn_header: () => zt,
            phn_level_0: () => on,
            phn_wrapper: () => an
        });
        var f = O(56207)
          , y = O(21733)
          , I = O(53182)
          , a = O(27849)
          , Ee = O(6968)
          , M = O(56359)
          , Z = O(52425)
          , A = O(30470)
          , Xe = O(98997);
        const Ye = {
            dev: {
                featureOverrideEnabled: {
                    enabled: !0
                },
                navi_drawer_cdn: {
                    enabled: !0
                },
                electricownership: {
                    enabled: !0
                }
            },
            local: {
                featureOverrideEnabled: {
                    enabled: !0
                },
                navi_drawer_cdn: {
                    enabled: !0
                },
                electricownership: {
                    enabled: !0
                }
            },
            preview: {
                featureOverrideEnabled: {
                    enabled: !0
                },
                navi_drawer_cdn: {
                    enabled: !0
                },
                electricownership: {
                    enabled: !0
                }
            },
            test: {
                featureOverrideEnabled: {
                    enabled: !0
                },
                navi_drawer_cdn: {
                    enabled: !0
                },
                navi_3: {
                    enabled: !0
                },
                pcom_search: {
                    enabled: !0
                },
                electricownership: {
                    enabled: !0
                }
            },
            production: {
                featureOverrideEnabled: {
                    enabled: !1
                },
                navi_3: {
                    enabled: !0
                },
                content_V4: {
                    enabled: !0
                },
                dealer_search_china: {
                    enabled: !0
                },
                mock_shop_content: {
                    enabled: !1
                },
                shop: {
                    enabled: !1
                },
                pcom_search: {
                    enabled: !1
                },
                shop_wishlist: {
                    enabled: !1
                },
                navi_drawer_cdn: {
                    enabled: !1
                },
                mpi_integration: {
                    enabled: !1
                }
            }
        }
          , V = (0,
        a.c)({
            features: {}
        })
          , ee = new class tt {
            constructor(e=(0,
            a.g)(a.D), t=Ye) {
                this.featureEnvMap = t,
                this.features = {},
                this.env = "local",
                this.isABTestingToggleActive = () => this.isFeatureEnabled("ab", window.location?.search ?? ""),
                this.isNewRelicEnabled = () => this.isFeatureEnabled("newrelic", window.location?.search ?? ""),
                this.isTestToggleEnabled = () => this.isFeatureEnabled("test_toggle", window.location?.search ?? ""),
                this.isElectricOwnershipEnabled = () => this.isFeatureEnabled("electricownership", window.location?.search ?? ""),
                this.env = e,
                this.features = this.featureEnvMap[e],
                V.state.features = this.featureEnvMap[e]
            }
            loadRemoteToggles() {
                var e = this;
                return (0,
                f.A)(function*() {
                    try {
                        const t = (0,
                        a.a)(e.env)
                          , {locale: n} = a.n.state;
                        let o = t.BFF_ENDPOINT;
                        if (("en-CN" === n || "zh-CN" === n) && (o = t.BFF_CN_ENDPOINT),
                        "local" === e.env)
                            return;
                        const r = yield fetch(`${o}/toggles?env=${e.env}&fromAppConfig=true`);
                        if (!r.ok) {
                            const d = yield r.text();
                            throw new Error(d)
                        }
                        const s = yield r.json();
                        e.features = s
                    } catch (t) {
                        throw new a.N(`Load Feature Toggles failed with error: ${t}`,a.b.GENERAL)
                    }
                })()
            }
            stringToBooleanStrict(e) {
                return "true" === e || "false" !== e && null
            }
            getUrlFeatureToggleOverride(e, t) {
                const n = t.split("?")[1]?.split("&").find(o => o.includes(e))?.split("=")[1];
                return (0,
                a.i)(n) ? this.stringToBooleanStrict(n || "") : null
            }
            isFeatureEnabled(e, t) {
                if (this.features = V.state.features,
                void 0 === this.features || this.env === a.E.PRODUCTION && this.isFeatureNotReadyForProd(e))
                    return !1;
                if (this.features.featureOverrideEnabled?.enabled) {
                    const n = [this.getUrlFeatureToggleOverride(e, t)];
                    for (const o of n)
                        if (null !== o)
                            return o
                }
                return void 0 !== this.features[e] && this.features[e].enabled
            }
            setEnvironment(e) {
                this.env = (0,
                a.g)(e),
                this.features = this.featureEnvMap[e]
            }
            setFeatures(e) {
                var t = this;
                return (0,
                f.A)(function*() {
                    t.env = (0,
                    a.g)(e);
                    try {
                        if ("test" === e)
                            return void (t.features = t.featureEnvMap[e]);
                        yield t.loadRemoteToggles()
                    } catch (n) {
                        I.l.error("INIT_APP_CONFIG", n),
                        t.features = t.featureEnvMap[e],
                        V.state.features = t.featureEnvMap[e]
                    }
                })()
            }
            isFeatureNotReadyForProd(e) {
                return [].includes(e)
            }
        }
        ;
        function te() {
            return (te = (0,
            f.A)(function*(i, e) {
                const t = (0,
                a.a)(a.n.state.env)
                  , n = {
                    method: "GET",
                    headers: {
                        "X-Trace-Id": crypto.randomUUID(),
                        Authorization: `${i}`,
                        Accept: "application/json",
                        Env: a.n.state.env
                    }
                };
                let o;
                try {
                    o = yield fetch((0,
                    a.d)(`${t?.BFF_ENDPOINT}/user-information?locale=${e}`), n)
                } catch (r) {
                    const s = `Get User information request failed in Frontend with error: ${JSON.stringify(r.message)}`;
                    throw new a.N(s,a.b.GENERAL)
                }
                if (o.status === Ee.S.unauthorized)
                    throw new a.N("AccessToken has expired",a.b.UNAUTHORIZED);
                if (!o.ok)
                    throw new a.N(`Get User information request failed in Frontend with error: ${o.status} ${o.statusText}`,a.b.GENERAL);
                return o.json()
            })).apply(this, arguments)
        }
        function L(i, e) {
            var t = {};
            for (var n in i)
                Object.prototype.hasOwnProperty.call(i, n) && e.indexOf(n) < 0 && (t[n] = i[n]);
            if (null != i && "function" == typeof Object.getOwnPropertySymbols) {
                var o = 0;
                for (n = Object.getOwnPropertySymbols(i); o < n.length; o++)
                    e.indexOf(n[o]) < 0 && Object.prototype.propertyIsEnumerable.call(i, n[o]) && (t[n[o]] = i[n[o]])
            }
            return t
        }
        "function" == typeof SuppressedError && SuppressedError;
        var D = typeof globalThis < "u" ? globalThis : typeof window < "u" ? window : typeof global < "u" ? global : typeof self < "u" ? self : {};
        function ne(i) {
            return i && i.__esModule && Object.prototype.hasOwnProperty.call(i, "default") ? i.default : i
        }
        function ie(i, e) {
            return i(e = {
                exports: {}
            }, e.exports),
            e.exports
        }
        var P = ie(function(i, e) {
            Object.defineProperty(e, "__esModule", {
                value: !0
            });
            var t = function() {
                function n() {
                    var o = this;
                    this.locked = new Map,
                    this.addToLocked = function(r, s) {
                        var d = o.locked.get(r);
                        void 0 === d ? o.locked.set(r, void 0 === s ? [] : [s]) : void 0 !== s && (d.unshift(s),
                        o.locked.set(r, d))
                    }
                    ,
                    this.isLocked = function(r) {
                        return o.locked.has(r)
                    }
                    ,
                    this.lock = function(r) {
                        return new Promise(function(s, d) {
                            o.isLocked(r) ? o.addToLocked(r, s) : (o.addToLocked(r),
                            s())
                        }
                        )
                    }
                    ,
                    this.unlock = function(r) {
                        var s = o.locked.get(r);
                        if (void 0 !== s && 0 !== s.length) {
                            var d = s.pop();
                            o.locked.set(r, s),
                            void 0 !== d && setTimeout(d, 0)
                        } else
                            o.locked.delete(r)
                    }
                }
                return n.getInstance = function() {
                    return void 0 === n.instance && (n.instance = new n),
                    n.instance
                }
                ,
                n
            }();
            e.default = function() {
                return t.getInstance()
            }
        });
        ne(P);
        var it = ne(ie(function(i, e) {
            var t = D && D.__awaiter || function(l, c, p, u) {
                return new (p || (p = Promise))(function(m, b) {
                    function w(k) {
                        try {
                            _(u.next(k))
                        } catch (v) {
                            b(v)
                        }
                    }
                    function C(k) {
                        try {
                            _(u.throw(k))
                        } catch (v) {
                            b(v)
                        }
                    }
                    function _(k) {
                        k.done ? m(k.value) : new p(function(v) {
                            v(k.value)
                        }
                        ).then(w, C)
                    }
                    _((u = u.apply(l, c || [])).next())
                }
                )
            }
              , n = D && D.__generator || function(l, c) {
                var p, u, m, b, w = {
                    label: 0,
                    sent: function() {
                        if (1 & m[0])
                            throw m[1];
                        return m[1]
                    },
                    trys: [],
                    ops: []
                };
                return b = {
                    next: C(0),
                    throw: C(1),
                    return: C(2)
                },
                "function" == typeof Symbol && (b[Symbol.iterator] = function() {
                    return this
                }
                ),
                b;
                function C(_) {
                    return function(k) {
                        return function(v) {
                            if (p)
                                throw new TypeError("Generator is already executing.");
                            for (; w; )
                                try {
                                    if (p = 1,
                                    u && (m = 2 & v[0] ? u.return : v[0] ? u.throw || ((m = u.return) && m.call(u),
                                    0) : u.next) && !(m = m.call(u, v[1])).done)
                                        return m;
                                    switch (u = 0,
                                    m && (v = [2 & v[0], m.value]),
                                    v[0]) {
                                    case 0:
                                    case 1:
                                        m = v;
                                        break;
                                    case 4:
                                        return w.label++,
                                        {
                                            value: v[1],
                                            done: !1
                                        };
                                    case 5:
                                        w.label++,
                                        u = v[1],
                                        v = [0];
                                        continue;
                                    case 7:
                                        v = w.ops.pop(),
                                        w.trys.pop();
                                        continue;
                                    default:
                                        if (!((m = (m = w.trys).length > 0 && m[m.length - 1]) || 6 !== v[0] && 2 !== v[0])) {
                                            w = 0;
                                            continue
                                        }
                                        if (3 === v[0] && (!m || v[1] > m[0] && v[1] < m[3])) {
                                            w.label = v[1];
                                            break
                                        }
                                        if (6 === v[0] && w.label < m[1]) {
                                            w.label = m[1],
                                            m = v;
                                            break
                                        }
                                        if (m && w.label < m[2]) {
                                            w.label = m[2],
                                            w.ops.push(v);
                                            break
                                        }
                                        m[2] && w.ops.pop(),
                                        w.trys.pop();
                                        continue
                                    }
                                    v = c.call(l, w)
                                } catch (E) {
                                    v = [6, E],
                                    u = 0
                                } finally {
                                    p = m = 0
                                }
                            if (5 & v[0])
                                throw v[1];
                            return {
                                value: v[0] ? v[1] : void 0,
                                done: !0
                            }
                        }([_, k])
                    }
                }
            }
              , o = D;
            Object.defineProperty(e, "__esModule", {
                value: !0
            });
            var r = "browser-tabs-lock-key"
              , s = {
                key: function(l) {
                    return t(o, void 0, void 0, function() {
                        return n(this, function(c) {
                            throw new Error("Unsupported")
                        })
                    })
                },
                getItem: function(l) {
                    return t(o, void 0, void 0, function() {
                        return n(this, function(c) {
                            throw new Error("Unsupported")
                        })
                    })
                },
                clear: function() {
                    return t(o, void 0, void 0, function() {
                        return n(this, function(l) {
                            return [2, window.localStorage.clear()]
                        })
                    })
                },
                removeItem: function(l) {
                    return t(o, void 0, void 0, function() {
                        return n(this, function(c) {
                            throw new Error("Unsupported")
                        })
                    })
                },
                setItem: function(l, c) {
                    return t(o, void 0, void 0, function() {
                        return n(this, function(p) {
                            throw new Error("Unsupported")
                        })
                    })
                },
                keySync: function(l) {
                    return window.localStorage.key(l)
                },
                getItemSync: function(l) {
                    return window.localStorage.getItem(l)
                },
                clearSync: function() {
                    return window.localStorage.clear()
                },
                removeItemSync: function(l) {
                    return window.localStorage.removeItem(l)
                },
                setItemSync: function(l, c) {
                    return window.localStorage.setItem(l, c)
                }
            };
            function d(l) {
                return new Promise(function(c) {
                    return setTimeout(c, l)
                }
                )
            }
            function h(l) {
                for (var p = "", u = 0; u < l; u++)
                    p += "0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz"[Math.floor(61 * Math.random())];
                return p
            }
            var g = function() {
                function l(c) {
                    this.acquiredIatSet = new Set,
                    this.storageHandler = void 0,
                    this.id = Date.now().toString() + h(15),
                    this.acquireLock = this.acquireLock.bind(this),
                    this.releaseLock = this.releaseLock.bind(this),
                    this.releaseLock__private__ = this.releaseLock__private__.bind(this),
                    this.waitForSomethingToChange = this.waitForSomethingToChange.bind(this),
                    this.refreshLockWhileAcquired = this.refreshLockWhileAcquired.bind(this),
                    this.storageHandler = c,
                    void 0 === l.waiters && (l.waiters = [])
                }
                return l.prototype.acquireLock = function(c, p) {
                    return void 0 === p && (p = 5e3),
                    t(this, void 0, void 0, function() {
                        var u, m, b, w, C, _, k;
                        return n(this, function(v) {
                            switch (v.label) {
                            case 0:
                                u = Date.now() + h(4),
                                m = Date.now() + p,
                                b = r + "-" + c,
                                w = void 0 === this.storageHandler ? s : this.storageHandler,
                                v.label = 1;
                            case 1:
                                return Date.now() < m ? [4, d(30)] : [3, 8];
                            case 2:
                                return v.sent(),
                                null !== w.getItemSync(b) ? [3, 5] : (C = this.id + "-" + c + "-" + u,
                                [4, d(Math.floor(25 * Math.random()))]);
                            case 3:
                                return v.sent(),
                                w.setItemSync(b, JSON.stringify({
                                    id: this.id,
                                    iat: u,
                                    timeoutKey: C,
                                    timeAcquired: Date.now(),
                                    timeRefreshed: Date.now()
                                })),
                                [4, d(30)];
                            case 4:
                                return v.sent(),
                                null !== (_ = w.getItemSync(b)) && (k = JSON.parse(_)).id === this.id && k.iat === u ? (this.acquiredIatSet.add(u),
                                this.refreshLockWhileAcquired(b, u),
                                [2, !0]) : [3, 7];
                            case 5:
                                return l.lockCorrector(void 0 === this.storageHandler ? s : this.storageHandler),
                                [4, this.waitForSomethingToChange(m)];
                            case 6:
                                v.sent(),
                                v.label = 7;
                            case 7:
                                return u = Date.now() + h(4),
                                [3, 1];
                            case 8:
                                return [2, !1]
                            }
                        })
                    })
                }
                ,
                l.prototype.refreshLockWhileAcquired = function(c, p) {
                    return t(this, void 0, void 0, function() {
                        var u = this;
                        return n(this, function(m) {
                            return setTimeout(function() {
                                return t(u, void 0, void 0, function() {
                                    var b, w, C;
                                    return n(this, function(_) {
                                        switch (_.label) {
                                        case 0:
                                            return [4, P.default().lock(p)];
                                        case 1:
                                            return _.sent(),
                                            this.acquiredIatSet.has(p) ? null === (w = (b = void 0 === this.storageHandler ? s : this.storageHandler).getItemSync(c)) ? (P.default().unlock(p),
                                            [2]) : ((C = JSON.parse(w)).timeRefreshed = Date.now(),
                                            b.setItemSync(c, JSON.stringify(C)),
                                            P.default().unlock(p),
                                            this.refreshLockWhileAcquired(c, p),
                                            [2]) : (P.default().unlock(p),
                                            [2])
                                        }
                                    })
                                })
                            }, 1e3),
                            [2]
                        })
                    })
                }
                ,
                l.prototype.waitForSomethingToChange = function(c) {
                    return t(this, void 0, void 0, function() {
                        return n(this, function(p) {
                            switch (p.label) {
                            case 0:
                                return [4, new Promise(function(u) {
                                    var m = !1
                                      , b = Date.now()
                                      , w = !1;
                                    function C() {
                                        if (w || (window.removeEventListener("storage", C),
                                        l.removeFromWaiting(C),
                                        clearTimeout(_),
                                        w = !0),
                                        !m) {
                                            m = !0;
                                            var k = 50 - (Date.now() - b);
                                            k > 0 ? setTimeout(u, k) : u(null)
                                        }
                                    }
                                    window.addEventListener("storage", C),
                                    l.addToWaiting(C);
                                    var _ = setTimeout(C, Math.max(0, c - Date.now()))
                                }
                                )];
                            case 1:
                                return p.sent(),
                                [2]
                            }
                        })
                    })
                }
                ,
                l.addToWaiting = function(c) {
                    this.removeFromWaiting(c),
                    void 0 !== l.waiters && l.waiters.push(c)
                }
                ,
                l.removeFromWaiting = function(c) {
                    void 0 !== l.waiters && (l.waiters = l.waiters.filter(function(p) {
                        return p !== c
                    }))
                }
                ,
                l.notifyWaiters = function() {
                    void 0 !== l.waiters && l.waiters.slice().forEach(function(c) {
                        return c()
                    })
                }
                ,
                l.prototype.releaseLock = function(c) {
                    return t(this, void 0, void 0, function() {
                        return n(this, function(p) {
                            switch (p.label) {
                            case 0:
                                return [4, this.releaseLock__private__(c)];
                            case 1:
                                return [2, p.sent()]
                            }
                        })
                    })
                }
                ,
                l.prototype.releaseLock__private__ = function(c) {
                    return t(this, void 0, void 0, function() {
                        var p, u, m, b;
                        return n(this, function(w) {
                            switch (w.label) {
                            case 0:
                                return null === (m = (p = void 0 === this.storageHandler ? s : this.storageHandler).getItemSync(u = r + "-" + c)) ? [2] : (b = JSON.parse(m)).id !== this.id ? [3, 2] : [4, P.default().lock(b.iat)];
                            case 1:
                                w.sent(),
                                this.acquiredIatSet.delete(b.iat),
                                p.removeItemSync(u),
                                P.default().unlock(b.iat),
                                l.notifyWaiters(),
                                w.label = 2;
                            case 2:
                                return [2]
                            }
                        })
                    })
                }
                ,
                l.lockCorrector = function(c) {
                    for (var p = Date.now() - 5e3, u = c, m = [], b = 0; ; ) {
                        var w = u.keySync(b);
                        if (null === w)
                            break;
                        m.push(w),
                        b++
                    }
                    for (var C = !1, _ = 0; _ < m.length; _++) {
                        var k = m[_];
                        if (k.includes(r)) {
                            var v = u.getItemSync(k);
                            if (null !== v) {
                                var E = JSON.parse(v);
                                (void 0 === E.timeRefreshed && E.timeAcquired < p || void 0 !== E.timeRefreshed && E.timeRefreshed < p) && (u.removeItemSync(k),
                                C = !0)
                            }
                        }
                    }
                    C && l.notifyWaiters()
                }
                ,
                l.waiters = void 0,
                l
            }();
            e.default = g
        }));
        const ot = {
            timeoutInSeconds: 60
        }
          , Ie = {
            name: "auth0-spa-js",
            version: "2.1.3"
        }
          , Te = () => Date.now();
        class x extends Error {
            constructor(e, t) {
                super(t),
                this.error = e,
                this.error_description = t,
                Object.setPrototypeOf(this, x.prototype)
            }
            static fromPayload({error: e, error_description: t}) {
                return new x(e,t)
            }
        }
        class oe extends x {
            constructor(e, t, n, o=null) {
                super(e, t),
                this.state = n,
                this.appState = o,
                Object.setPrototypeOf(this, oe.prototype)
            }
        }
        class H extends x {
            constructor() {
                super("timeout", "Timeout"),
                Object.setPrototypeOf(this, H.prototype)
            }
        }
        class re extends H {
            constructor(e) {
                super(),
                this.popup = e,
                Object.setPrototypeOf(this, re.prototype)
            }
        }
        class ae extends x {
            constructor(e) {
                super("cancelled", "Popup closed"),
                this.popup = e,
                Object.setPrototypeOf(this, ae.prototype)
            }
        }
        class se extends x {
            constructor(e, t, n) {
                super(e, t),
                this.mfa_token = n,
                Object.setPrototypeOf(this, se.prototype)
            }
        }
        class X extends x {
            constructor(e, t) {
                super("missing_refresh_token", `Missing Refresh Token (audience: '${Oe(e, ["default"])}', scope: '${Oe(t)}')`),
                this.audience = e,
                this.scope = t,
                Object.setPrototypeOf(this, X.prototype)
            }
        }
        function Oe(i, e=[]) {
            return i && !e.includes(i) ? i : ""
        }
        const Y = () => window.crypto
          , le = () => {
            let e = "";
            return Array.from(Y().getRandomValues(new Uint8Array(43))).forEach(t => e += "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_~."[t % 66]),
            e
        }
          , Se = i => btoa(i)
          , ce = i => {
            var n, {clientId: e} = i, t = L(i, ["clientId"]);
            return new URLSearchParams((n = Object.assign({
                client_id: e
            }, t),
            Object.keys(n).filter(o => void 0 !== n[o]).reduce( (o, r) => Object.assign(Object.assign({}, o), {
                [r]: n[r]
            }), {}))).toString()
        }
          , Le = i => {
            return e = i.replace(/_/g, "/").replace(/-/g, "+"),
            decodeURIComponent(atob(e).split("").map(t => "%" + ("00" + t.charCodeAt(0).toString(16)).slice(-2)).join(""));
            var e
        }
          , rt = function() {
            var i = (0,
            f.A)(function*(e, t) {
                const n = yield fetch(e, t);
                return {
                    ok: n.ok,
                    json: yield n.json()
                }
            });
            return function(t, n) {
                return i.apply(this, arguments)
            }
        }()
          , at = function() {
            var i = (0,
            f.A)(function*(e, t, n) {
                const o = new AbortController;
                let r;
                return t.signal = o.signal,
                Promise.race([rt(e, t), new Promise( (s, d) => {
                    r = setTimeout( () => {
                        o.abort(),
                        d(new Error("Timeout when executing 'fetch'"))
                    }
                    , n)
                }
                )]).finally( () => {
                    clearTimeout(r)
                }
                )
            });
            return function(t, n, o) {
                return i.apply(this, arguments)
            }
        }()
          , st = function() {
            var i = (0,
            f.A)(function*(e, t, n, o, r, s, d) {
                return h = {
                    auth: {
                        audience: t,
                        scope: n
                    },
                    timeout: r,
                    fetchUrl: e,
                    fetchOptions: o,
                    useFormData: d
                },
                g = s,
                new Promise(function(l, c) {
                    const p = new MessageChannel;
                    p.port1.onmessage = function(u) {
                        u.data.error ? c(new Error(u.data.error)) : l(u.data),
                        p.port1.close()
                    }
                    ,
                    g.postMessage(h, [p.port2])
                }
                );
                var h, g
            });
            return function(t, n, o, r, s, d, h) {
                return i.apply(this, arguments)
            }
        }()
          , lt = function() {
            var i = (0,
            f.A)(function*(e, t, n, o, r, s, d=1e4) {
                return r ? st(e, t, n, o, d, r, s) : at(e, o, d)
            });
            return function(t, n, o, r, s, d) {
                return i.apply(this, arguments)
            }
        }();
        function de() {
            return de = (0,
            f.A)(function*(i, e) {
                var {baseUrl: t, timeout: n, audience: o, scope: r, auth0Client: s, useFormData: d} = i
                  , h = L(i, ["baseUrl", "timeout", "audience", "scope", "auth0Client", "useFormData"]);
                const g = d ? ce(h) : JSON.stringify(h);
                return yield(l = (0,
                f.A)(function*(c, p, u, m, b, w, C) {
                    let _, k = null;
                    for (let z = 0; z < 3; z++)
                        try {
                            _ = yield lt(c, u, m, b, w, C, p),
                            k = null;
                            break
                        } catch (sn) {
                            k = sn
                        }
                    if (k)
                        throw k;
                    const v = _.json
                      , {error: E, error_description: K} = v
                      , Q = L(v, ["error", "error_description"])
                      , {ok: N} = _;
                    if (!N) {
                        const z = K || `HTTP error. Unable to fetch ${c}`;
                        throw "mfa_required" === E ? new se(E,z,Q.mfa_token) : "missing_refresh_token" === E ? new X(u,m) : new x(E || "request_error",z)
                    }
                    return Q
                }),
                function(c, p, u, m, b, w, C) {
                    return l.apply(this, arguments)
                }
                )(`${t}/oauth/token`, n, o || "default", r, {
                    method: "POST",
                    body: g,
                    headers: {
                        "Content-Type": d ? "application/x-www-form-urlencoded" : "application/json",
                        "Auth0-Client": btoa(JSON.stringify(s || Ie))
                    }
                }, e, d);
                var l
            }),
            de.apply(this, arguments)
        }
        const J = (...i) => {
            return (e = i.filter(Boolean).join(" ").trim().split(/\s+/),
            Array.from(new Set(e))).join(" ");
            var e
        }
        ;
        class R {
            constructor(e, t="@@auth0spajs@@", n) {
                this.prefix = t,
                this.suffix = n,
                this.clientId = e.clientId,
                this.scope = e.scope,
                this.audience = e.audience
            }
            toKey() {
                return [this.prefix, this.clientId, this.audience, this.scope, this.suffix].filter(Boolean).join("::")
            }
            static fromKey(e) {
                const [t,n,o,r] = e.split("::");
                return new R({
                    clientId: n,
                    scope: r,
                    audience: o
                },t)
            }
            static fromCacheEntry(e) {
                const {scope: t, audience: n, client_id: o} = e;
                return new R({
                    scope: t,
                    audience: n,
                    clientId: o
                })
            }
        }
        class dt {
            set(e, t) {
                localStorage.setItem(e, JSON.stringify(t))
            }
            get(e) {
                const t = window.localStorage.getItem(e);
                if (t)
                    try {
                        return JSON.parse(t)
                    } catch {
                        return
                    }
            }
            remove(e) {
                localStorage.removeItem(e)
            }
            allKeys() {
                return Object.keys(window.localStorage).filter(e => e.startsWith("@@auth0spajs@@"))
            }
        }
        class Re {
            constructor() {
                this.enclosedCache = function() {
                    let e = {};
                    return {
                        set(t, n) {
                            e[t] = n
                        },
                        get(t) {
                            const n = e[t];
                            if (n)
                                return n
                        },
                        remove(t) {
                            delete e[t]
                        },
                        allKeys: () => Object.keys(e)
                    }
                }()
            }
        }
        class ht {
            constructor(e, t, n) {
                this.cache = e,
                this.keyManifest = t,
                this.nowProvider = n || Te
            }
            setIdToken(e, t, n) {
                var o = this;
                return (0,
                f.A)(function*() {
                    var r;
                    const s = o.getIdTokenCacheKey(e);
                    yield o.cache.set(s, {
                        id_token: t,
                        decodedToken: n
                    }),
                    yield null === (r = o.keyManifest) || void 0 === r ? void 0 : r.add(s)
                })()
            }
            getIdToken(e) {
                var t = this;
                return (0,
                f.A)(function*() {
                    const n = yield t.cache.get(t.getIdTokenCacheKey(e.clientId));
                    if (!n && e.scope && e.audience) {
                        const o = yield t.get(e);
                        return o && o.id_token && o.decodedToken ? {
                            id_token: o.id_token,
                            decodedToken: o.decodedToken
                        } : void 0
                    }
                    if (n)
                        return {
                            id_token: n.id_token,
                            decodedToken: n.decodedToken
                        }
                })()
            }
            get(e) {
                var t = this;
                return (0,
                f.A)(function*(n, o=0) {
                    var r;
                    let s = yield t.cache.get(n.toKey());
                    if (!s) {
                        const g = yield t.getCacheKeys();
                        if (!g)
                            return;
                        const l = t.matchExistingCacheKey(n, g);
                        l && (s = yield t.cache.get(l))
                    }
                    if (!s)
                        return;
                    const d = yield t.nowProvider()
                      , h = Math.floor(d / 1e3);
                    return s.expiresAt - o < h ? s.body.refresh_token ? (s.body = {
                        refresh_token: s.body.refresh_token
                    },
                    yield t.cache.set(n.toKey(), s),
                    s.body) : (yield t.cache.remove(n.toKey()),
                    void (yield null === (r = t.keyManifest) || void 0 === r ? void 0 : r.remove(n.toKey()))) : s.body
                }).apply(this, arguments)
            }
            set(e) {
                var t = this;
                return (0,
                f.A)(function*() {
                    var n;
                    const o = new R({
                        clientId: e.client_id,
                        scope: e.scope,
                        audience: e.audience
                    })
                      , r = yield t.wrapCacheEntry(e);
                    yield t.cache.set(o.toKey(), r),
                    yield null === (n = t.keyManifest) || void 0 === n ? void 0 : n.add(o.toKey())
                })()
            }
            clear(e) {
                var t = this;
                return (0,
                f.A)(function*() {
                    var n;
                    const o = yield t.getCacheKeys();
                    o && (yield o.filter(r => !e || r.includes(e)).reduce(function() {
                        var r = (0,
                        f.A)(function*(s, d) {
                            yield s,
                            yield t.cache.remove(d)
                        });
                        return function(s, d) {
                            return r.apply(this, arguments)
                        }
                    }(), Promise.resolve()),
                    yield null === (n = t.keyManifest) || void 0 === n ? void 0 : n.clear())
                })()
            }
            wrapCacheEntry(e) {
                var t = this;
                return (0,
                f.A)(function*() {
                    const n = yield t.nowProvider();
                    return {
                        body: e,
                        expiresAt: Math.floor(n / 1e3) + e.expires_in
                    }
                })()
            }
            getCacheKeys() {
                var e = this;
                return (0,
                f.A)(function*() {
                    var t;
                    return e.keyManifest ? null === (t = yield e.keyManifest.get()) || void 0 === t ? void 0 : t.keys : e.cache.allKeys ? e.cache.allKeys() : void 0
                })()
            }
            getIdTokenCacheKey(e) {
                return new R({
                    clientId: e
                },"@@auth0spajs@@","@@user@@").toKey()
            }
            matchExistingCacheKey(e, t) {
                return t.filter(n => {
                    var o;
                    const r = R.fromKey(n)
                      , s = new Set(r.scope && r.scope.split(" "))
                      , d = (null === (o = e.scope) || void 0 === o ? void 0 : o.split(" ")) || []
                      , h = r.scope && d.reduce( (g, l) => g && s.has(l), !0);
                    return "@@auth0spajs@@" === r.prefix && r.clientId === e.clientId && r.audience === e.audience && h
                }
                )[0]
            }
        }
        class pt {
            constructor(e, t, n) {
                this.storage = e,
                this.clientId = t,
                this.cookieDomain = n,
                this.storageKey = `a0.spajs.txs.${this.clientId}`
            }
            create(e) {
                this.storage.save(this.storageKey, e, {
                    daysUntilExpire: 1,
                    cookieDomain: this.cookieDomain
                })
            }
            get() {
                return this.storage.get(this.storageKey)
            }
            remove() {
                this.storage.remove(this.storageKey, {
                    cookieDomain: this.cookieDomain
                })
            }
        }
        const B = i => "number" == typeof i
          , ut = ["iss", "aud", "exp", "nbf", "iat", "jti", "azp", "nonce", "auth_time", "at_hash", "c_hash", "acr", "amr", "sub_jwk", "cnf", "sip_from_tag", "sip_date", "sip_callid", "sip_cseq_num", "sip_via_branch", "orig", "dest", "mky", "events", "toe", "txn", "rph", "sid", "vot", "vtm"];
        var $ = ie(function(i, e) {
            var t = D && D.__assign || function() {
                return t = Object.assign || function(h) {
                    for (var g, l = 1, c = arguments.length; l < c; l++)
                        for (var p in g = arguments[l])
                            Object.prototype.hasOwnProperty.call(g, p) && (h[p] = g[p]);
                    return h
                }
                ,
                t.apply(this, arguments)
            }
            ;
            function n(h, g) {
                if (!g)
                    return "";
                var l = "; " + h;
                return !0 === g ? l : l + "=" + g
            }
            function o(h, g, l) {
                return encodeURIComponent(h).replace(/%(23|24|26|2B|5E|60|7C)/g, decodeURIComponent).replace(/\(/g, "%28").replace(/\)/g, "%29") + "=" + encodeURIComponent(g).replace(/%(23|24|26|2B|3A|3C|3E|3D|2F|3F|40|5B|5D|5E|60|7B|7D|7C)/g, decodeURIComponent) + function(c) {
                    if ("number" == typeof c.expires) {
                        var p = new Date;
                        p.setMilliseconds(p.getMilliseconds() + 864e5 * c.expires),
                        c.expires = p
                    }
                    return n("Expires", c.expires ? c.expires.toUTCString() : "") + n("Domain", c.domain) + n("Path", c.path) + n("Secure", c.secure) + n("SameSite", c.sameSite)
                }(l)
            }
            function r(h) {
                for (var g = {}, l = h ? h.split("; ") : [], c = /(%[\dA-F]{2})+/gi, p = 0; p < l.length; p++) {
                    var u = l[p].split("=")
                      , m = u.slice(1).join("=");
                    '"' === m.charAt(0) && (m = m.slice(1, -1));
                    try {
                        g[u[0].replace(c, decodeURIComponent)] = m.replace(c, decodeURIComponent)
                    } catch {}
                }
                return g
            }
            function s() {
                return r(document.cookie)
            }
            function d(h, g, l) {
                document.cookie = o(h, g, t({
                    path: "/"
                }, l))
            }
            e.__esModule = !0,
            e.encode = o,
            e.parse = r,
            e.getAll = s,
            e.get = function(h) {
                return s()[h]
            }
            ,
            e.set = d,
            e.remove = function(h, g) {
                d(h, "", t(t({}, g), {
                    expires: -1
                }))
            }
        });
        ne($);
        var ft = $.get
          , Ae = $.set
          , Ne = $.remove;
        const j = {
            get(i) {
                const e = ft(i);
                if (void 0 !== e)
                    return JSON.parse(e)
            },
            save(i, e, t) {
                let n = {};
                "https:" === window.location.protocol && (n = {
                    secure: !0,
                    sameSite: "none"
                }),
                t?.daysUntilExpire && (n.expires = t.daysUntilExpire),
                t?.cookieDomain && (n.domain = t.cookieDomain),
                Ae(i, JSON.stringify(e), n)
            },
            remove(i, e) {
                let t = {};
                e?.cookieDomain && (t.domain = e.cookieDomain),
                Ne(i, t)
            }
        }
          , gt = {
            get: i => j.get(i) || j.get(`_legacy_${i}`),
            save(i, e, t) {
                let n = {};
                "https:" === window.location.protocol && (n = {
                    secure: !0
                }),
                t?.daysUntilExpire && (n.expires = t.daysUntilExpire),
                t?.cookieDomain && (n.domain = t.cookieDomain),
                Ae(`_legacy_${i}`, JSON.stringify(e), n),
                j.save(i, e, t)
            },
            remove(i, e) {
                let t = {};
                e?.cookieDomain && (t.domain = e.cookieDomain),
                Ne(i, t),
                j.remove(i, e),
                j.remove(`_legacy_${i}`, e)
            }
        }
          , vt = {
            get(i) {
                if (typeof sessionStorage > "u")
                    return;
                const e = sessionStorage.getItem(i);
                return null != e ? JSON.parse(e) : void 0
            },
            save(i, e) {
                sessionStorage.setItem(i, JSON.stringify(e))
            },
            remove(i) {
                sessionStorage.removeItem(i)
            }
        };
        var he, yt = function(i) {
            return he = he || function wt(i, e, t) {
                var n = void 0 === e ? null : e
                  , o = function(h, g) {
                    var l = atob(h);
                    if (g) {
                        for (var c = new Uint8Array(l.length), p = 0, u = l.length; p < u; ++p)
                            c[p] = l.charCodeAt(p);
                        return String.fromCharCode.apply(null, new Uint16Array(c.buffer))
                    }
                    return l
                }(i, void 0 !== t && t)
                  , r = o.indexOf("\n", 10) + 1
                  , s = o.substring(r) + (n ? "//# sourceMappingURL=" + n : "")
                  , d = new Blob([s],{
                    type: "application/javascript"
                });
                return URL.createObjectURL(d)
            }("Lyogcm9sbHVwLXBsdWdpbi13ZWItd29ya2VyLWxvYWRlciAqLwohZnVuY3Rpb24oKXsidXNlIHN0cmljdCI7Y2xhc3MgZSBleHRlbmRzIEVycm9ye2NvbnN0cnVjdG9yKHQscil7c3VwZXIociksdGhpcy5lcnJvcj10LHRoaXMuZXJyb3JfZGVzY3JpcHRpb249cixPYmplY3Quc2V0UHJvdG90eXBlT2YodGhpcyxlLnByb3RvdHlwZSl9c3RhdGljIGZyb21QYXlsb2FkKHtlcnJvcjp0LGVycm9yX2Rlc2NyaXB0aW9uOnJ9KXtyZXR1cm4gbmV3IGUodCxyKX19Y2xhc3MgdCBleHRlbmRzIGV7Y29uc3RydWN0b3IoZSxzKXtzdXBlcigibWlzc2luZ19yZWZyZXNoX3Rva2VuIixgTWlzc2luZyBSZWZyZXNoIFRva2VuIChhdWRpZW5jZTogJyR7cihlLFsiZGVmYXVsdCJdKX0nLCBzY29wZTogJyR7cihzKX0nKWApLHRoaXMuYXVkaWVuY2U9ZSx0aGlzLnNjb3BlPXMsT2JqZWN0LnNldFByb3RvdHlwZU9mKHRoaXMsdC5wcm90b3R5cGUpfX1mdW5jdGlvbiByKGUsdD1bXSl7cmV0dXJuIGUmJiF0LmluY2x1ZGVzKGUpP2U6IiJ9ImZ1bmN0aW9uIj09dHlwZW9mIFN1cHByZXNzZWRFcnJvciYmU3VwcHJlc3NlZEVycm9yO2NvbnN0IHM9ZT0+e3ZhcntjbGllbnRJZDp0fT1lLHI9ZnVuY3Rpb24oZSx0KXt2YXIgcj17fTtmb3IodmFyIHMgaW4gZSlPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwoZSxzKSYmdC5pbmRleE9mKHMpPDAmJihyW3NdPWVbc10pO2lmKG51bGwhPWUmJiJmdW5jdGlvbiI9PXR5cGVvZiBPYmplY3QuZ2V0T3duUHJvcGVydHlTeW1ib2xzKXt2YXIgbz0wO2ZvcihzPU9iamVjdC5nZXRPd25Qcm9wZXJ0eVN5bWJvbHMoZSk7bzxzLmxlbmd0aDtvKyspdC5pbmRleE9mKHNbb10pPDAmJk9iamVjdC5wcm90b3R5cGUucHJvcGVydHlJc0VudW1lcmFibGUuY2FsbChlLHNbb10pJiYocltzW29dXT1lW3Nbb11dKX1yZXR1cm4gcn0oZSxbImNsaWVudElkIl0pO3JldHVybiBuZXcgVVJMU2VhcmNoUGFyYW1zKChlPT5PYmplY3Qua2V5cyhlKS5maWx0ZXIoKHQ9PnZvaWQgMCE9PWVbdF0pKS5yZWR1Y2UoKCh0LHIpPT5PYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sdCkse1tyXTplW3JdfSkpLHt9KSkoT2JqZWN0LmFzc2lnbih7Y2xpZW50X2lkOnR9LHIpKSkudG9TdHJpbmcoKX07bGV0IG89e307Y29uc3Qgbj0oZSx0KT0+YCR7ZX18JHt0fWA7YWRkRXZlbnRMaXN0ZW5lcigibWVzc2FnZSIsKGFzeW5jKHtkYXRhOnt0aW1lb3V0OmUsYXV0aDpyLGZldGNoVXJsOmksZmV0Y2hPcHRpb25zOmMsdXNlRm9ybURhdGE6YX0scG9ydHM6W3BdfSk9PntsZXQgZjtjb25zdHthdWRpZW5jZTp1LHNjb3BlOmx9PXJ8fHt9O3RyeXtjb25zdCByPWE/KGU9Pntjb25zdCB0PW5ldyBVUkxTZWFyY2hQYXJhbXMoZSkscj17fTtyZXR1cm4gdC5mb3JFYWNoKCgoZSx0KT0+e3JbdF09ZX0pKSxyfSkoYy5ib2R5KTpKU09OLnBhcnNlKGMuYm9keSk7aWYoIXIucmVmcmVzaF90b2tlbiYmInJlZnJlc2hfdG9rZW4iPT09ci5ncmFudF90eXBlKXtjb25zdCBlPSgoZSx0KT0+b1tuKGUsdCldKSh1LGwpO2lmKCFlKXRocm93IG5ldyB0KHUsbCk7Yy5ib2R5PWE/cyhPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30scikse3JlZnJlc2hfdG9rZW46ZX0pKTpKU09OLnN0cmluZ2lmeShPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30scikse3JlZnJlc2hfdG9rZW46ZX0pKX1sZXQgaCxnOyJmdW5jdGlvbiI9PXR5cGVvZiBBYm9ydENvbnRyb2xsZXImJihoPW5ldyBBYm9ydENvbnRyb2xsZXIsYy5zaWduYWw9aC5zaWduYWwpO3RyeXtnPWF3YWl0IFByb21pc2UucmFjZShbKGQ9ZSxuZXcgUHJvbWlzZSgoZT0+c2V0VGltZW91dChlLGQpKSkpLGZldGNoKGksT2JqZWN0LmFzc2lnbih7fSxjKSldKX1jYXRjaChlKXtyZXR1cm4gdm9pZCBwLnBvc3RNZXNzYWdlKHtlcnJvcjplLm1lc3NhZ2V9KX1pZighZylyZXR1cm4gaCYmaC5hYm9ydCgpLHZvaWQgcC5wb3N0TWVzc2FnZSh7ZXJyb3I6IlRpbWVvdXQgd2hlbiBleGVjdXRpbmcgJ2ZldGNoJyJ9KTtmPWF3YWl0IGcuanNvbigpLGYucmVmcmVzaF90b2tlbj8oKChlLHQscik9PntvW24odCxyKV09ZX0pKGYucmVmcmVzaF90b2tlbix1LGwpLGRlbGV0ZSBmLnJlZnJlc2hfdG9rZW4pOigoZSx0KT0+e2RlbGV0ZSBvW24oZSx0KV19KSh1LGwpLHAucG9zdE1lc3NhZ2Uoe29rOmcub2ssanNvbjpmfSl9Y2F0Y2goZSl7cC5wb3N0TWVzc2FnZSh7b2s6ITEsanNvbjp7ZXJyb3I6ZS5lcnJvcixlcnJvcl9kZXNjcmlwdGlvbjplLm1lc3NhZ2V9fSl9dmFyIGR9KSl9KCk7Cgo=", null, !1),
            new Worker(he,i)
        };
        const pe = {};
        class bt {
            constructor(e, t) {
                this.cache = e,
                this.clientId = t,
                this.manifestKey = this.createManifestKeyFrom(this.clientId)
            }
            add(e) {
                var t = this;
                return (0,
                f.A)(function*() {
                    var n;
                    const o = new Set((null === (n = yield t.cache.get(t.manifestKey)) || void 0 === n ? void 0 : n.keys) || []);
                    o.add(e),
                    yield t.cache.set(t.manifestKey, {
                        keys: [...o]
                    })
                })()
            }
            remove(e) {
                var t = this;
                return (0,
                f.A)(function*() {
                    const n = yield t.cache.get(t.manifestKey);
                    if (n) {
                        const o = new Set(n.keys);
                        return o.delete(e),
                        o.size > 0 ? yield t.cache.set(t.manifestKey, {
                            keys: [...o]
                        }) : yield t.cache.remove(t.manifestKey)
                    }
                })()
            }
            get() {
                return this.cache.get(this.manifestKey)
            }
            clear() {
                return this.cache.remove(this.manifestKey)
            }
            createManifestKeyFrom(e) {
                return `@@auth0spajs@@::${e}`
            }
        }
        const Ct = {
            memory: () => (new Re).enclosedCache,
            localstorage: () => new dt
        }
          , Ue = i => Ct[i]
          , je = i => {
            const {openUrl: e, onRedirect: t} = i
              , n = L(i, ["openUrl", "onRedirect"]);
            return Object.assign(Object.assign({}, n), {
                openUrl: !1 === e || e ? e : t
            })
        }
          , ue = new it;
        class kt {
            constructor(e) {
                var t = this;
                let n, o;
                if (this.userCache = (new Re).enclosedCache,
                this.defaultOptions = {
                    authorizationParams: {
                        scope: "openid profile email"
                    },
                    useRefreshTokensFallback: !1,
                    useFormData: !0
                },
                this._releaseLockOnPageHide = (0,
                f.A)(function*() {
                    yield ue.releaseLock("auth0.lock.getTokenSilently"),
                    window.removeEventListener("pagehide", t._releaseLockOnPageHide)
                }),
                this.options = Object.assign(Object.assign(Object.assign({}, this.defaultOptions), e), {
                    authorizationParams: Object.assign(Object.assign({}, this.defaultOptions.authorizationParams), e.authorizationParams)
                }),
                typeof window < "u" && ( () => {
                    if (!Y())
                        throw new Error("For security reasons, `window.crypto` is required to run `auth0-spa-js`.");
                    if (void 0 === Y().subtle)
                        throw new Error("\n      auth0-spa-js must run on a secure origin. See https://github.com/auth0/auth0-spa-js/blob/main/FAQ.md#why-do-i-get-auth0-spa-js-must-run-on-a-secure-origin for more information.\n    ")
                }
                )(),
                e.cache && e.cacheLocation && console.warn("Both `cache` and `cacheLocation` options have been specified in the Auth0Client configuration; ignoring `cacheLocation` and using `cache`."),
                e.cache)
                    o = e.cache;
                else {
                    if (n = e.cacheLocation || "memory",
                    !Ue(n))
                        throw new Error(`Invalid cache location "${n}"`);
                    o = Ue(n)()
                }
                this.httpTimeoutMs = e.httpTimeoutInSeconds ? 1e3 * e.httpTimeoutInSeconds : 1e4,
                this.cookieStorage = !1 === e.legacySameSiteCookie ? j : gt,
                this.orgHintCookieName = `auth0.${this.options.clientId}.organization_hint`,
                this.isAuthenticatedCookieName = ( () => `auth0.${this.options.clientId}.is.authenticated`)(),
                this.sessionCheckExpiryDays = e.sessionCheckExpiryDays || 1;
                const r = e.useCookiesForTransactions ? this.cookieStorage : vt;
                var s, d, h;
                this.scope = J("openid", this.options.authorizationParams.scope, this.options.useRefreshTokens ? "offline_access" : ""),
                this.transactionManager = new pt(r,this.options.clientId,this.options.cookieDomain),
                this.nowProvider = this.options.nowProvider || Te,
                this.cacheManager = new ht(o,o.allKeys ? void 0 : new bt(o,this.options.clientId),this.nowProvider),
                this.domainUrl = /^https?:\/\//.test(s = this.options.domain) ? s : `https://${s}`,
                this.tokenIssuer = (h = this.domainUrl,
                (d = this.options.issuer) ? d.startsWith("https://") ? d : `https://${d}/` : `${h}/`),
                typeof window < "u" && window.Worker && this.options.useRefreshTokens && "memory" === n && (this.worker = this.options.workerUrl ? new Worker(this.options.workerUrl) : new yt)
            }
            _url(e) {
                const t = encodeURIComponent(btoa(JSON.stringify(this.options.auth0Client || Ie)));
                return `${this.domainUrl}${e}&auth0Client=${t}`
            }
            _authorizeUrl(e) {
                return this._url(`/authorize?${ce(e)}`)
            }
            _verifyIdToken(e, t, n) {
                var o = this;
                return (0,
                f.A)(function*() {
                    const r = yield o.nowProvider();
                    return (i => {
                        if (!i.id_token)
                            throw new Error("ID token is required but missing");
                        const e = (r => {
                            const s = r.split(".")
                              , [d,h,g] = s;
                            if (3 !== s.length || !d || !h || !g)
                                throw new Error("ID token could not be decoded");
                            const l = JSON.parse(Le(h))
                              , c = {
                                __raw: r
                            }
                              , p = {};
                            return Object.keys(l).forEach(u => {
                                c[u] = l[u],
                                ut.includes(u) || (p[u] = l[u])
                            }
                            ),
                            {
                                encoded: {
                                    header: d,
                                    payload: h,
                                    signature: g
                                },
                                header: JSON.parse(Le(d)),
                                claims: c,
                                user: p
                            }
                        }
                        )(i.id_token);
                        if (!e.claims.iss)
                            throw new Error("Issuer (iss) claim must be a string present in the ID token");
                        if (e.claims.iss !== i.iss)
                            throw new Error(`Issuer (iss) claim mismatch in the ID token; expected "${i.iss}", found "${e.claims.iss}"`);
                        if (!e.user.sub)
                            throw new Error("Subject (sub) claim must be a string present in the ID token");
                        if ("RS256" !== e.header.alg)
                            throw new Error(`Signature algorithm of "${e.header.alg}" is not supported. Expected the ID token to be signed with "RS256".`);
                        if (!e.claims.aud || "string" != typeof e.claims.aud && !Array.isArray(e.claims.aud))
                            throw new Error("Audience (aud) claim must be a string or array of strings present in the ID token");
                        if (Array.isArray(e.claims.aud)) {
                            if (!e.claims.aud.includes(i.aud))
                                throw new Error(`Audience (aud) claim mismatch in the ID token; expected "${i.aud}" but was not one of "${e.claims.aud.join(", ")}"`);
                            if (e.claims.aud.length > 1) {
                                if (!e.claims.azp)
                                    throw new Error("Authorized Party (azp) claim must be a string present in the ID token when Audience (aud) claim has multiple values");
                                if (e.claims.azp !== i.aud)
                                    throw new Error(`Authorized Party (azp) claim mismatch in the ID token; expected "${i.aud}", found "${e.claims.azp}"`)
                            }
                        } else if (e.claims.aud !== i.aud)
                            throw new Error(`Audience (aud) claim mismatch in the ID token; expected "${i.aud}" but found "${e.claims.aud}"`);
                        if (i.nonce) {
                            if (!e.claims.nonce)
                                throw new Error("Nonce (nonce) claim must be a string present in the ID token");
                            if (e.claims.nonce !== i.nonce)
                                throw new Error(`Nonce (nonce) claim mismatch in the ID token; expected "${i.nonce}", found "${e.claims.nonce}"`)
                        }
                        if (i.max_age && !B(e.claims.auth_time))
                            throw new Error("Authentication Time (auth_time) claim must be a number present in the ID token when Max Age (max_age) is specified");
                        if (null == e.claims.exp || !B(e.claims.exp))
                            throw new Error("Expiration Time (exp) claim must be a number present in the ID token");
                        if (!B(e.claims.iat))
                            throw new Error("Issued At (iat) claim must be a number present in the ID token");
                        const t = i.leeway || 60
                          , n = new Date(i.now || Date.now())
                          , o = new Date(0);
                        if (o.setUTCSeconds(e.claims.exp + t),
                        n > o)
                            throw new Error(`Expiration Time (exp) claim error in the ID token; current time (${n}) is after expiration time (${o})`);
                        if (null != e.claims.nbf && B(e.claims.nbf)) {
                            const r = new Date(0);
                            if (r.setUTCSeconds(e.claims.nbf - t),
                            n < r)
                                throw new Error(`Not Before time (nbf) claim in the ID token indicates that this token can't be used just yet. Current time (${n}) is before ${r}`)
                        }
                        if (null != e.claims.auth_time && B(e.claims.auth_time)) {
                            const r = new Date(0);
                            if (r.setUTCSeconds(parseInt(e.claims.auth_time) + i.max_age + t),
                            n > r)
                                throw new Error(`Authentication Time (auth_time) claim in the ID token indicates that too much time has passed since the last end-user authentication. Current time (${n}) is after last auth at ${r}`)
                        }
                        if (i.organization) {
                            const r = i.organization.trim();
                            if (r.startsWith("org_")) {
                                const s = r;
                                if (!e.claims.org_id)
                                    throw new Error("Organization ID (org_id) claim must be a string present in the ID token");
                                if (s !== e.claims.org_id)
                                    throw new Error(`Organization ID (org_id) claim mismatch in the ID token; expected "${s}", found "${e.claims.org_id}"`)
                            } else {
                                const s = r.toLowerCase();
                                if (!e.claims.org_name)
                                    throw new Error("Organization Name (org_name) claim must be a string present in the ID token");
                                if (s !== e.claims.org_name)
                                    throw new Error(`Organization Name (org_name) claim mismatch in the ID token; expected "${s}", found "${e.claims.org_name}"`)
                            }
                        }
                        return e
                    }
                    )({
                        iss: o.tokenIssuer,
                        aud: o.options.clientId,
                        id_token: e,
                        nonce: t,
                        organization: n,
                        leeway: o.options.leeway,
                        max_age: (s = o.options.authorizationParams.max_age,
                        "string" != typeof s ? s : parseInt(s, 10) || void 0),
                        now: r
                    });
                    var s
                })()
            }
            _processOrgHint(e) {
                e ? this.cookieStorage.save(this.orgHintCookieName, e, {
                    daysUntilExpire: this.sessionCheckExpiryDays,
                    cookieDomain: this.options.cookieDomain
                }) : this.cookieStorage.remove(this.orgHintCookieName, {
                    cookieDomain: this.options.cookieDomain
                })
            }
            _prepareAuthorizeUrl(e, t, n) {
                var o = this;
                return (0,
                f.A)(function*() {
                    const r = Se(le())
                      , s = Se(le())
                      , d = le()
                      , h = (c => {
                        const p = new Uint8Array(c);
                        return (u => {
                            const m = {
                                "+": "-",
                                "/": "_",
                                "=": ""
                            };
                            return u.replace(/[+/=]/g, b => m[b])
                        }
                        )(window.btoa(String.fromCharCode(...Array.from(p))))
                    }
                    )(yield(c = (0,
                    f.A)(function*(p) {
                        return yield Y().subtle.digest({
                            name: "SHA-256"
                        }, (new TextEncoder).encode(p))
                    }),
                    function(p) {
                        return c.apply(this, arguments)
                    }
                    )(d))
                      , g = ( (c, p, u, m, b, w, C, _) => Object.assign(Object.assign(Object.assign({
                        client_id: c.clientId
                    }, c.authorizationParams), u), {
                        scope: J(p, u.scope),
                        response_type: "code",
                        response_mode: _ || "query",
                        state: m,
                        nonce: b,
                        redirect_uri: C || c.authorizationParams.redirect_uri,
                        code_challenge: w,
                        code_challenge_method: "S256"
                    }))(o.options, o.scope, e, r, s, h, e.redirect_uri || o.options.authorizationParams.redirect_uri || n, t?.response_mode)
                      , l = o._authorizeUrl(g);
                    var c;
                    return {
                        nonce: s,
                        code_verifier: d,
                        scope: g.scope,
                        audience: g.audience || "default",
                        redirect_uri: g.redirect_uri,
                        state: r,
                        url: l
                    }
                })()
            }
            loginWithPopup(e, t) {
                var n = this;
                return (0,
                f.A)(function*() {
                    var o;
                    if (e = e || {},
                    !(t = t || {}).popup && (t.popup = ( () => {
                        const g = window.screenX + (window.innerWidth - 400) / 2
                          , l = window.screenY + (window.innerHeight - 600) / 2;
                        return window.open("", "auth0:authorize:popup", `left=${g},top=${l},width=400,height=600,resizable,scrollbars=yes,status=1`)
                    }
                    )(),
                    !t.popup))
                        throw new Error("Unable to open a popup for loginWithPopup - window.open returned `null`");
                    const r = yield n._prepareAuthorizeUrl(e.authorizationParams || {}, {
                        response_mode: "web_message"
                    }, window.location.origin);
                    t.popup.location.href = r.url;
                    const s = yield(h = Object.assign(Object.assign({}, t), {
                        timeoutInSeconds: t.timeoutInSeconds || n.options.authorizeTimeoutInSeconds || 60
                    }),
                    new Promise( (g, l) => {
                        let c;
                        const p = setInterval( () => {
                            h.popup && h.popup.closed && (clearInterval(p),
                            clearTimeout(u),
                            window.removeEventListener("message", c, !1),
                            l(new ae(h.popup)))
                        }
                        , 1e3)
                          , u = setTimeout( () => {
                            clearInterval(p),
                            l(new re(h.popup)),
                            window.removeEventListener("message", c, !1)
                        }
                        , 1e3 * (h.timeoutInSeconds || 60));
                        c = function(m) {
                            if (m.data && "authorization_response" === m.data.type) {
                                if (clearTimeout(u),
                                clearInterval(p),
                                window.removeEventListener("message", c, !1),
                                h.popup.close(),
                                m.data.response.error)
                                    return l(x.fromPayload(m.data.response));
                                g(m.data.response)
                            }
                        }
                        ,
                        window.addEventListener("message", c)
                    }
                    ));
                    var h;
                    if (r.state !== s.state)
                        throw new x("state_mismatch","Invalid state");
                    const d = (null === (o = e.authorizationParams) || void 0 === o ? void 0 : o.organization) || n.options.authorizationParams.organization;
                    yield n._requestToken({
                        audience: r.audience,
                        scope: r.scope,
                        code_verifier: r.code_verifier,
                        grant_type: "authorization_code",
                        code: s.code,
                        redirect_uri: r.redirect_uri
                    }, {
                        nonceIn: r.nonce,
                        organization: d
                    })
                })()
            }
            getUser() {
                var e = this;
                return (0,
                f.A)(function*() {
                    var t;
                    const n = yield e._getIdTokenFromCache();
                    return null === (t = n?.decodedToken) || void 0 === t ? void 0 : t.user
                })()
            }
            getIdTokenClaims() {
                var e = this;
                return (0,
                f.A)(function*() {
                    var t;
                    const n = yield e._getIdTokenFromCache();
                    return null === (t = n?.decodedToken) || void 0 === t ? void 0 : t.claims
                })()
            }
            loginWithRedirect() {
                var e = this;
                return (0,
                f.A)(function*(t={}) {
                    var n;
                    const o = je(t)
                      , {openUrl: r, fragment: s, appState: d} = o
                      , h = L(o, ["openUrl", "fragment", "appState"])
                      , g = (null === (n = h.authorizationParams) || void 0 === n ? void 0 : n.organization) || e.options.authorizationParams.organization
                      , l = yield e._prepareAuthorizeUrl(h.authorizationParams || {})
                      , {url: c} = l
                      , p = L(l, ["url"]);
                    e.transactionManager.create(Object.assign(Object.assign(Object.assign({}, p), {
                        appState: d
                    }), g && {
                        organization: g
                    }));
                    const u = s ? `${c}#${s}` : c;
                    r ? yield r(u) : window.location.assign(u)
                }).apply(this, arguments)
            }
            handleRedirectCallback() {
                var e = this;
                return (0,
                f.A)(function*(t=window.location.href) {
                    const n = t.split("?").slice(1);
                    if (0 === n.length)
                        throw new Error("There are no query params available for parsing.");
                    const {state: o, code: r, error: s, error_description: d} = (p => {
                        p.indexOf("#") > -1 && (p = p.substring(0, p.indexOf("#")));
                        const u = new URLSearchParams(p);
                        return {
                            state: u.get("state"),
                            code: u.get("code") || void 0,
                            error: u.get("error") || void 0,
                            error_description: u.get("error_description") || void 0
                        }
                    }
                    )(n.join(""))
                      , h = e.transactionManager.get();
                    if (!h)
                        throw new x("missing_transaction","Invalid state");
                    if (e.transactionManager.remove(),
                    s)
                        throw new oe(s,d || s,o,h.appState);
                    if (!h.code_verifier || h.state && h.state !== o)
                        throw new x("state_mismatch","Invalid state");
                    const g = h.organization
                      , l = h.nonce
                      , c = h.redirect_uri;
                    return yield e._requestToken(Object.assign({
                        audience: h.audience,
                        scope: h.scope,
                        code_verifier: h.code_verifier,
                        grant_type: "authorization_code",
                        code: r
                    }, c ? {
                        redirect_uri: c
                    } : {}), {
                        nonceIn: l,
                        organization: g
                    }),
                    {
                        appState: h.appState
                    }
                }).apply(this, arguments)
            }
            checkSession(e) {
                var t = this;
                return (0,
                f.A)(function*() {
                    if (!t.cookieStorage.get(t.isAuthenticatedCookieName)) {
                        if (!t.cookieStorage.get("auth0.is.authenticated"))
                            return;
                        t.cookieStorage.save(t.isAuthenticatedCookieName, !0, {
                            daysUntilExpire: t.sessionCheckExpiryDays,
                            cookieDomain: t.options.cookieDomain
                        }),
                        t.cookieStorage.remove("auth0.is.authenticated")
                    }
                    try {
                        yield t.getTokenSilently(e)
                    } catch {}
                })()
            }
            getTokenSilently() {
                var e = this;
                return (0,
                f.A)(function*(t={}) {
                    var n;
                    const o = Object.assign(Object.assign({
                        cacheMode: "on"
                    }, t), {
                        authorizationParams: Object.assign(Object.assign(Object.assign({}, e.options.authorizationParams), t.authorizationParams), {
                            scope: J(e.scope, null === (n = t.authorizationParams) || void 0 === n ? void 0 : n.scope)
                        })
                    })
                      , r = yield( (s, d) => {
                        let h = pe[d];
                        return h || (h = e._getTokenSilently(o).finally( () => {
                            delete pe[d],
                            h = null
                        }
                        ),
                        pe[d] = h),
                        h
                    }
                    )(0, `${e.options.clientId}::${o.authorizationParams.audience}::${o.authorizationParams.scope}`);
                    return t.detailedResponse ? r : r?.access_token
                }).apply(this, arguments)
            }
            _getTokenSilently(e) {
                var t = this;
                return (0,
                f.A)(function*() {
                    const {cacheMode: n} = e
                      , o = L(e, ["cacheMode"]);
                    if ("off" !== n) {
                        const r = yield t._getEntryFromCache({
                            scope: o.authorizationParams.scope,
                            audience: o.authorizationParams.audience || "default",
                            clientId: t.options.clientId
                        });
                        if (r)
                            return r
                    }
                    if ("cache-only" !== n) {
                        if (!(yield(r = (0,
                        f.A)(function*(s, d=3) {
                            for (let h = 0; h < d; h++)
                                if (yield s())
                                    return !0;
                            return !1
                        }),
                        function(s) {
                            return r.apply(this, arguments)
                        }
                        )( () => ue.acquireLock("auth0.lock.getTokenSilently", 5e3), 10)))
                            throw new H;
                        try {
                            if (window.addEventListener("pagehide", t._releaseLockOnPageHide),
                            "off" !== n) {
                                const l = yield t._getEntryFromCache({
                                    scope: o.authorizationParams.scope,
                                    audience: o.authorizationParams.audience || "default",
                                    clientId: t.options.clientId
                                });
                                if (l)
                                    return l
                            }
                            const r = t.options.useRefreshTokens ? yield t._getTokenUsingRefreshToken(o) : yield t._getTokenFromIFrame(o)
                              , {id_token: s, access_token: d, oauthTokenScope: h, expires_in: g} = r;
                            return Object.assign(Object.assign({
                                id_token: s,
                                access_token: d
                            }, h ? {
                                scope: h
                            } : null), {
                                expires_in: g
                            })
                        } finally {
                            yield ue.releaseLock("auth0.lock.getTokenSilently"),
                            window.removeEventListener("pagehide", t._releaseLockOnPageHide)
                        }
                    }
                    var r
                })()
            }
            getTokenWithPopup() {
                var e = this;
                return (0,
                f.A)(function*(t={}, n={}) {
                    var o;
                    const r = Object.assign(Object.assign({}, t), {
                        authorizationParams: Object.assign(Object.assign(Object.assign({}, e.options.authorizationParams), t.authorizationParams), {
                            scope: J(e.scope, null === (o = t.authorizationParams) || void 0 === o ? void 0 : o.scope)
                        })
                    });
                    return n = Object.assign(Object.assign({}, ot), n),
                    yield e.loginWithPopup(r, n),
                    (yield e.cacheManager.get(new R({
                        scope: r.authorizationParams.scope,
                        audience: r.authorizationParams.audience || "default",
                        clientId: e.options.clientId
                    }))).access_token
                }).apply(this, arguments)
            }
            isAuthenticated() {
                var e = this;
                return (0,
                f.A)(function*() {
                    return !!(yield e.getUser())
                })()
            }
            _buildLogoutUrl(e) {
                null !== e.clientId ? e.clientId = e.clientId || this.options.clientId : delete e.clientId;
                const t = e.logoutParams || {}
                  , {federated: n} = t
                  , o = L(t, ["federated"])
                  , r = n ? "&federated" : "";
                return this._url(`/v2/logout?${ce(Object.assign({
                    clientId: e.clientId
                }, o))}`) + r
            }
            logout() {
                var e = this;
                return (0,
                f.A)(function*(t={}) {
                    const n = je(t)
                      , {openUrl: o} = n
                      , r = L(n, ["openUrl"]);
                    null === t.clientId ? yield e.cacheManager.clear() : yield e.cacheManager.clear(t.clientId || e.options.clientId),
                    e.cookieStorage.remove(e.orgHintCookieName, {
                        cookieDomain: e.options.cookieDomain
                    }),
                    e.cookieStorage.remove(e.isAuthenticatedCookieName, {
                        cookieDomain: e.options.cookieDomain
                    }),
                    e.userCache.remove("@@user@@");
                    const s = e._buildLogoutUrl(r);
                    o ? yield o(s) : !1 !== o && window.location.assign(s)
                }).apply(this, arguments)
            }
            _getTokenFromIFrame(e) {
                var t = this;
                return (0,
                f.A)(function*() {
                    const n = Object.assign(Object.assign({}, e.authorizationParams), {
                        prompt: "none"
                    })
                      , o = t.cookieStorage.get(t.orgHintCookieName);
                    o && !n.organization && (n.organization = o);
                    const {url: r, state: s, nonce: d, code_verifier: h, redirect_uri: g, scope: l, audience: c} = yield t._prepareAuthorizeUrl(n, {
                        response_mode: "web_message"
                    }, window.location.origin);
                    try {
                        if (window.crossOriginIsolated)
                            throw new x("login_required","The application is running in a Cross-Origin Isolated context, silently retrieving a token without refresh token is not possible.");
                        const p = e.timeoutInSeconds || t.options.authorizeTimeoutInSeconds
                          , u = yield( (b, w, C=60) => new Promise( (_, k) => {
                            const v = window.document.createElement("iframe");
                            v.setAttribute("width", "0"),
                            v.setAttribute("height", "0"),
                            v.style.display = "none";
                            const E = () => {
                                window.document.body.contains(v) && (window.document.body.removeChild(v),
                                window.removeEventListener("message", K, !1))
                            }
                            ;
                            let K;
                            const Q = setTimeout( () => {
                                k(new H),
                                E()
                            }
                            , 1e3 * C);
                            K = function(N) {
                                if (N.origin != w || !N.data || "authorization_response" !== N.data.type)
                                    return;
                                const z = N.source;
                                z && z.close(),
                                N.data.response.error ? k(x.fromPayload(N.data.response)) : _(N.data.response),
                                clearTimeout(Q),
                                window.removeEventListener("message", K, !1),
                                setTimeout(E, 2e3)
                            }
                            ,
                            window.addEventListener("message", K, !1),
                            window.document.body.appendChild(v),
                            v.setAttribute("src", b)
                        }
                        ))(r, t.domainUrl, p);
                        if (s !== u.state)
                            throw new x("state_mismatch","Invalid state");
                        const m = yield t._requestToken(Object.assign(Object.assign({}, e.authorizationParams), {
                            code_verifier: h,
                            code: u.code,
                            grant_type: "authorization_code",
                            redirect_uri: g,
                            timeout: e.authorizationParams.timeout || t.httpTimeoutMs
                        }), {
                            nonceIn: d,
                            organization: n.organization
                        });
                        return Object.assign(Object.assign({}, m), {
                            scope: l,
                            oauthTokenScope: m.scope,
                            audience: c
                        })
                    } catch (p) {
                        throw "login_required" === p.error && t.logout({
                            openUrl: !1
                        }),
                        p
                    }
                })()
            }
            _getTokenUsingRefreshToken(e) {
                var t = this;
                return (0,
                f.A)(function*() {
                    const n = yield t.cacheManager.get(new R({
                        scope: e.authorizationParams.scope,
                        audience: e.authorizationParams.audience || "default",
                        clientId: t.options.clientId
                    }));
                    if (!(n && n.refresh_token || t.worker)) {
                        if (t.options.useRefreshTokensFallback)
                            return yield t._getTokenFromIFrame(e);
                        throw new X(e.authorizationParams.audience || "default",e.authorizationParams.scope)
                    }
                    const o = e.authorizationParams.redirect_uri || t.options.authorizationParams.redirect_uri || window.location.origin
                      , r = "number" == typeof e.timeoutInSeconds ? 1e3 * e.timeoutInSeconds : null;
                    try {
                        const s = yield t._requestToken(Object.assign(Object.assign(Object.assign({}, e.authorizationParams), {
                            grant_type: "refresh_token",
                            refresh_token: n && n.refresh_token,
                            redirect_uri: o
                        }), r && {
                            timeout: r
                        }));
                        return Object.assign(Object.assign({}, s), {
                            scope: e.authorizationParams.scope,
                            oauthTokenScope: s.scope,
                            audience: e.authorizationParams.audience || "default"
                        })
                    } catch (s) {
                        if ((s.message.indexOf("Missing Refresh Token") > -1 || s.message && s.message.indexOf("invalid refresh token") > -1) && t.options.useRefreshTokensFallback)
                            return yield t._getTokenFromIFrame(e);
                        throw s
                    }
                })()
            }
            _saveEntryInCache(e) {
                var t = this;
                return (0,
                f.A)(function*() {
                    const {id_token: n, decodedToken: o} = e
                      , r = L(e, ["id_token", "decodedToken"]);
                    t.userCache.set("@@user@@", {
                        id_token: n,
                        decodedToken: o
                    }),
                    yield t.cacheManager.setIdToken(t.options.clientId, e.id_token, e.decodedToken),
                    yield t.cacheManager.set(r)
                })()
            }
            _getIdTokenFromCache() {
                var e = this;
                return (0,
                f.A)(function*() {
                    const t = e.options.authorizationParams.audience || "default"
                      , n = yield e.cacheManager.getIdToken(new R({
                        clientId: e.options.clientId,
                        audience: t,
                        scope: e.scope
                    }))
                      , o = e.userCache.get("@@user@@");
                    return n && n.id_token === o?.id_token ? o : (e.userCache.set("@@user@@", n),
                    n)
                })()
            }
            _getEntryFromCache(e) {
                var t = this;
                return (0,
                f.A)(function*({scope: n, audience: o, clientId: r}) {
                    const s = yield t.cacheManager.get(new R({
                        scope: n,
                        audience: o,
                        clientId: r
                    }), 60);
                    if (s && s.access_token) {
                        const {access_token: d, oauthTokenScope: h, expires_in: g} = s
                          , l = yield t._getIdTokenFromCache();
                        return l && Object.assign(Object.assign({
                            id_token: l.id_token,
                            access_token: d
                        }, h ? {
                            scope: h
                        } : null), {
                            expires_in: g
                        })
                    }
                }).apply(this, arguments)
            }
            _requestToken(e, t) {
                var n = this;
                return (0,
                f.A)(function*() {
                    const {nonceIn: o, organization: r} = t || {}
                      , s = yield function ct(i, e) {
                        return de.apply(this, arguments)
                    }(Object.assign({
                        baseUrl: n.domainUrl,
                        client_id: n.options.clientId,
                        auth0Client: n.options.auth0Client,
                        useFormData: n.options.useFormData,
                        timeout: n.httpTimeoutMs
                    }, e), n.worker)
                      , d = yield n._verifyIdToken(s.id_token, o, r);
                    return yield n._saveEntryInCache(Object.assign(Object.assign(Object.assign(Object.assign({}, s), {
                        decodedToken: d,
                        scope: e.scope,
                        audience: e.audience || "default"
                    }), s.scope ? {
                        oauthTokenScope: s.scope
                    } : null), {
                        client_id: n.options.clientId
                    })),
                    n.cookieStorage.save(n.isAuthenticatedCookieName, !0, {
                        daysUntilExpire: n.sessionCheckExpiryDays,
                        cookieDomain: n.options.cookieDomain
                    }),
                    n._processOrgHint(r || d.claims.org_id),
                    Object.assign(Object.assign({}, s), {
                        decodedToken: d
                    })
                })()
            }
        }
        const T = new class xt {
            constructor() {
                this.initialBodyStyleValues = {
                    top: "",
                    position: "",
                    inlineSize: "",
                    overflowY: ""
                },
                this.initialDocumentScrollTop = 0
            }
            set version(e) {
                Object.assign(window, {
                    PHN_HEADER_VERSION: e
                })
            }
            get version() {
                return window.PHN_HEADER_VERSION
            }
            set drawerVersion(e) {
                Object.assign(window, {
                    PHN_DRAWER_VERSION: e
                })
            }
            get drawerVersion() {
                return window.PHN_DRAWER_VERSION
            }
            set navigationLoaded(e) {
                Object.assign(window, {
                    PHN_NAVIGATION_LOADED: e
                })
            }
            getBreakpoint() {
                const e = Math.max(window.document.documentElement.clientWidth || 0, window.innerWidth || 0)
                  , t = Object.keys(a.e).reverse().find(n => (0,
                a.h)(a.e, n) && e >= a.e[n]);
                return "string" == typeof t && (0,
                a.h)(a.e, t) ? a.e[t] : 0
            }
            get redirected() {
                return !0 === window.REDIRECTED || window.location.search.includes("cs_redirect=")
            }
            redirectToUrl(e) {
                window.location.assign(e)
            }
            cookieConsentGiven() {
                const e = window
                  , t = {
                    googleMaps: !1,
                    newRelic: !1
                };
                return Object.keys(a.f).forEach(o => {
                    const r = e?.usercentrics?.getConsents(e.GlobalConsent?.Processor?.[o] ?? a.f[o]);
                    r?.consentStatus && (t[o] = r.consentStatus)
                }
                ),
                t
            }
            maybeCheckMarketingConsent() {
                "function" == typeof window?.usercentrics?.getConsents ? (a.n.state.userConsent = this.cookieConsentGiven(),
                window.addEventListener("consents_changed_finished", () => {
                    a.n.state.userConsent = this.cookieConsentGiven()
                }
                )) : a.n.state.userConsent = a.j
            }
            getScrollbarWidth() {
                const e = window.document.createElement("div");
                e.style.overflow = "scroll",
                window.document.body.appendChild(e);
                const t = e.offsetWidth - e.clientWidth;
                return window.document.body.removeChild(e),
                Number.isNaN(t) ? "0px" : `${t}px`
            }
            fetchGoogleMapsApi(e) {
                return (0,
                f.A)(function*(t, n=`https://maps.googleapis.com/maps/api/js?key=${t}&loading=async&libraries=places`) {
                    if (void 0 !== window.google?.maps?.version || document.getElementById("googleMaps"))
                        return window.google;
                    const r = document.createElement("script");
                    return r.src = n,
                    r.id = "googleMaps",
                    document.head.appendChild(r),
                    new Promise( (s, d) => {
                        r.onerror = () => {
                            d(new a.N("Failed to load google maps script.",a.b.GENERAL))
                        }
                        ,
                        r.onload = () => {
                            s(window.google)
                        }
                    }
                    )
                }).apply(this, arguments)
            }
            getNavigatorLatLong() {
                return (0,
                f.A)(function*() {
                    return new Promise( (e, t) => {
                        window.navigator.geolocation.getCurrentPosition( ({coords: {latitude: n, longitude: o}}) => {
                            e({
                                latitude: n,
                                longitude: o
                            })
                        }
                        , n => t(new a.N(`geolocation.getCurrentPosition failed with ${n.code}: ${n.message}`,function Et(i) {
                            switch (i.code) {
                            case i.PERMISSION_DENIED:
                                return a.b.GEOLOCATION_DENIED;
                            case i.POSITION_UNAVAILABLE:
                            case i.TIMEOUT:
                                return a.b.RETRY;
                            default:
                                return a.b.GENERAL
                            }
                        }(n))))
                    }
                    )
                })()
            }
            getCookie(e) {
                return (0,
                a.d)(document.cookie.split("; ").find(t => t.startsWith(`${e}=`))?.split("=")[1])
            }
            setCookie(e, t) {
                document.cookie = `${e}=${t};domain=${a.C};expires=${new Date(Date.now() + 10 * a.M).toUTCString()};`
            }
            lockScroll() {
                this.initialBodyStyleValues = (0,
                a.s)(document.body.style, this.initialBodyStyleValues),
                this.initialDocumentScrollTop = document.documentElement.scrollTop,
                document.body.style.top = `-${document.documentElement.scrollTop}px`,
                document.body.style.position = "fixed",
                document.body.style.inlineSize = "100%",
                document.body.style.overflowY = "scroll"
            }
            unlockScroll() {
                Object.entries(this.initialBodyStyleValues).forEach( ([e,t]) => document.body.style.setProperty((0,
                a.k)(e), t)),
                document.documentElement.scrollTop = this.initialDocumentScrollTop
            }
            hasScrollbar() {
                return window.document.body.offsetHeight > window.innerHeight
            }
            waitForElement(e, t) {
                return (0,
                f.A)(function*() {
                    if (!(0,
                    a.i)(t))
                        return e;
                    const n = (0,
                    a.l)(e.querySelector(t));
                    return null !== n ? n : new Promise(o => {
                        const r = new MutationObserver( () => {
                            const s = (0,
                            a.l)(e.querySelector(t));
                            null !== s && (o(s),
                            r.disconnect())
                        }
                        );
                        r.observe(e, {
                            childList: !0,
                            subtree: !0
                        })
                    }
                    )
                })()
            }
        }
        ;
        var U = function(i) {
            return i.INIT = "INIT_AUTHENTICATION",
            i.TOKEN_RECEIVED = "TOKEN_RECEIVED",
            i.TOKEN_RECEIVED_ERROR = "TOKEN_RECEIVED_ERROR",
            i.PREPARE_LOGOUT = "PREPARE_LOGOUT",
            i.PREPARE_LOGIN_WITH_REDIRECT = "PREPARE_LOGIN_WITH_REDIRECT",
            i.PREPARE_GET_TOKEN_SILENTLY = "PREPARE_GET_TOKEN_SILENTLY",
            i.LEGACY_LOGOUT = "LEGACY_LOGOUT",
            i.LOGOUT_DEFAULT_PREVENTED = "LOGOUT_DEFAULT_PREVENTED",
            i.LOGIN_DEFAULT_PREVENTED = "LOGIN_DEFAULT_PREVENTED",
            i.MYPORSCHE_LOGIN = "MYPORSCHE_LOGIN",
            i.MYPORSCHE_LOGIN_DEFAULT_PREVENTED = "MYPORSCHE_LOGIN_DEFAULT_PREVENTED",
            i.UPDATED_USER_STATE = "UPDATED_USER_STATE",
            i.UPDATED_USER_STATE_ERROR = "UPDATED_USER_STATE_ERROR",
            i.UNKNOWN = "UNKNOWN",
            i
        }(U || {});
        function W(i) {
            window.dispatchEvent(new CustomEvent("PHN_AUTHENTICATION",{
                detail: i
            }))
        }
        function me(i) {
            window.PHN_NR_EVENT_QUEUE?.push(i)
        }
        var q = function(i) {
            return i.NAVIGATION_LOADED = "NAVIGATION_LOADED",
            i.NAVIGATION_DRAWER_LOADED = "NAVIGATION_DRAWER_LOADED",
            i.NAVIGATION_LOGOUT_SUCCESS = "NAVIGATION_LOGOUT_SUCCESS",
            i.NAVIGATION_LOGIN_SUCCESS = "NAVIGATION_LOGIN_SUCCESS",
            i.NAVIGATION_LOGIN = "NAVIGATION_LOGIN",
            i.NAVIGATION_LOGIN_ERROR = "NAVIGATION_LOGIN_ERROR",
            i.THIRD_PARTY_LOGIN = "NAVIGATION_THIRD_PARTY_LOGIN",
            i.NAVIGATION_FEATURES = "NAVIGATION_FEATURES",
            i.NAVIGATION_SERIES_IMG_PRELOAD = "NAVIGATION_SERIES_IMG_PRELOAD",
            i.NAVIGATION_SERIES_IMG_PRELOAD_ERROR = "NAVIGATION_SERIES_IMG_PRELOAD_ERROR",
            i
        }(q || {})
          , F = function(i) {
            return i.LOGIN = "login",
            i.LOGOUT = "logout",
            i
        }(F || {});
        function fe() {
            return fe = (0,
            f.A)(function*(i, e) {
                let n, o, t = a.p;
                try {
                    const r = yield function nt(i, e) {
                        return te.apply(this, arguments)
                    }(i, e);
                    if (null == r)
                        throw new a.N("Invalid user data object extracted from user profile response",a.b.NO_RESULTS);
                    I.f.state.savedSearchesCount = r.savedSearches,
                    I.f.state.savedVehiclesCount = r.savedVehicles,
                    a.n.state.unreadMessagesCount = r.unreadMessages;
                    const {userData: s} = r;
                    t = function Ot(i) {
                        if (!i)
                            return "";
                        const {firstName: e, lastName: t} = i;
                        return [e, t].filter(a.i).join(" ")
                    }(s) || a.L,
                    n = r.userData.userId ?? "",
                    o = r.userData.ciamId ?? "",
                    t && (a.G.getInstance().setUser(n, o, (0,
                    a.I)(t)),
                    a.G.getInstance().pushDataLayerEvent(a.m.IDS_LOAD)),
                    W({
                        eventType: U.UPDATED_USER_STATE,
                        state: t
                    })
                } catch (r) {
                    throw (!(r instanceof a.N) || r.type !== a.b.UNAUTHORIZED) && (t = a.L),
                    a.n.state.unreadMessagesCount = a.o,
                    a.n.state.loggedInState = t,
                    W({
                        eventType: U.UPDATED_USER_STATE_ERROR,
                        state: t,
                        error: r
                    }),
                    r
                }
                a.n.state.loggedInState = t,
                a.G.getInstance().setUser(n, o, (0,
                a.I)(t))
            }),
            fe.apply(this, arguments)
        }
        function ge() {
            const i = sessionStorage.getItem("phn-flow")?.replace("navi-", "");
            return i === F.LOGIN || i === F.LOGOUT ? i : null
        }
        function We() {
            sessionStorage.removeItem("phn-flow")
        }
        function Fe(i) {
            return ve.apply(this, arguments)
        }
        function ve() {
            return (ve = (0,
            f.A)(function*(i) {
                try {
                    W({
                        eventType: U.PREPARE_GET_TOKEN_SILENTLY
                    });
                    const t = yield function _t(i) {
                        return new kt({
                            clientId: i.IDENTITY_CLIENT_ID,
                            domain: i.IDENTITY_PROVIDER_URL,
                            authorizationParams: {
                                audience: i.IDENTITY_AUDIENCE,
                                scope: i.IDENTITY_USER_SCOPES
                            }
                        })
                    }(i).getTokenSilently({
                        authorizationParams: {
                            audience: i.IDENTITY_AUDIENCE,
                            scope: i.IDENTITY_USER_SCOPES
                        }
                    });
                    return function Tt(i) {
                        window.dispatchEvent(new CustomEvent("getToken",{
                            detail: {
                                token: i
                            }
                        }))
                    }(t),
                    W({
                        eventType: U.TOKEN_RECEIVED,
                        token: t
                    }),
                    ge() === F.LOGIN && (me({
                        type: q.NAVIGATION_LOGIN_SUCCESS
                    }),
                    We()),
                    t
                } catch (e) {
                    return ge() === F.LOGIN ? me({
                        type: q.NAVIGATION_LOGIN_ERROR,
                        options: e
                    }) : ge() === F.LOGOUT && me({
                        type: q.NAVIGATION_LOGOUT_SUCCESS
                    }),
                    We(),
                    W({
                        eventType: U.TOKEN_RECEIVED_ERROR,
                        error: e
                    }),
                    {
                        token: null,
                        error: JSON.stringify(e)
                    }
                }
            })).apply(this, arguments)
        }
        const S = (0,
        a.c)({
            routingKeyHistory: [],
            routingKeyPointer: -1,
            animatingPointer: -1,
            animatingHistory: [],
            breakpoint: T.getBreakpoint(),
            animatingBreakpoint: T.getBreakpoint(),
            initialRoutingKeyHistory: null,
            initialRoutingKeyPointer: null
        });
        class Ge {
            constructor(e) {
                this.delay = e,
                this.timer = null
            }
            set(e) {
                this.cancel(),
                this.timer = setTimeout( () => {
                    e(),
                    this.cancel()
                }
                , this.delay)
            }
            cancel() {
                null !== this.timer && (clearTimeout(this.timer),
                "function" == typeof this.timer.unref && this.timer.unref(),
                this.timer = null)
            }
        }
        function Rt(i) {
            (0,
            I.e)(i)
        }
        const At = () => {
            const i = (0,
            a.a)(a.n.state.env);
            return (0,
            y.h)(y.F, null, (0,
            y.h)("script", {
                type: "module",
                src: `${i.PHN_DRAWER_CDN_URL}/navigation-drawer.esm.js`
            }), (0,
            y.h)("script", {
                type: "nomodule",
                src: `${i.PHN_DRAWER_CDN_URL}/navigation-drawer.js`
            }))
        }
          , zt = ( () => {
            let i = class {
                constructor(e) {
                    (0,
                    y.r)(this, e),
                    this.navigationDidLoad = (0,
                    y.c)(this, "navigationDidLoad", 7),
                    this.locale = a.q,
                    this.env = "",
                    this.clientId = "",
                    this.pageName = "",
                    this.theme = a.r.light,
                    this.mode = a.t.navbar,
                    this.limitedWidth = "true",
                    this.app = a.A.default,
                    this.initialRoutingPath = "",
                    this.displayLogoOnly = !1,
                    this.numberOfShoppingItems = 0,
                    this.wishlistItemsNumber = 0,
                    this.shopInformation = '{ "wishlistItemsNumber": 0, "numberOfShoppingItems": 0, "subTotal": "0" }',
                    this.authOptions = "{}",
                    this.loadingContent = !1,
                    this.receivedContent = !1,
                    this.breakpoint = T.getBreakpoint(),
                    this.eventPreventedTimeout = new Ge(0),
                    this.loadedToggles = !1
                }
                localeWatchHandler() {
                    var e = this;
                    return (0,
                    f.A)(function*() {
                        a.n.state.locale = e.locale,
                        yield e.initNavContentStore(),
                        a.G.getInstance().setLocale(e.locale)
                    })()
                }
                pageNameWatchHandler() {
                    a.n.state.pageName = this.pageName,
                    a.G.getInstance().setPageName(this.pageName)
                }
                newEnvReceived() {
                    var e = this;
                    return (0,
                    f.A)(function*() {
                        yield e.initEnvironment(),
                        yield e.initNavContentStore(),
                        Fe((0,
                        a.a)(a.n.state.env))
                    })()
                }
                appWatcher(e) {
                    a.n.state.app = (0,
                    Z.s)(e)
                }
                watchShopVariable(e, t, n) {
                    const o = JSON.parse(e);
                    M.s.set(n, o)
                }
                initialRoutingPathWatcher(e) {
                    a.n.state.initialRoutingPath = e
                }
                themeWatcher(e) {
                    a.n.state.theme = e
                }
                modeWatcher(e) {
                    a.n.state.mode = e
                }
                auth0Watcher(e) {
                    this.setAuthConfig(JSON.parse(e), (0,
                    a.a)(a.n.state.env))
                }
                resizeHandler() {
                    this.breakpoint = T.getBreakpoint(),
                    S.state.breakpoint = T.getBreakpoint()
                }
                newAuthMessageReceived(e) {
                    var t = this;
                    return (0,
                    f.A)(function*() {
                        if ((0,
                        a.i)(e.detail?.token))
                            try {
                                yield function St(i, e) {
                                    return fe.apply(this, arguments)
                                }(e.detail.token, t.locale)
                            } catch (n) {
                                I.l.error("INIT_USER_DATA", n)
                            }
                    })()
                }
                handleUserCentricsInitalization() {
                    try {
                        this.checkMarketingConsent()
                    } catch (e) {
                        I.l.error("INIT_COOKIE_CONSENT", e)
                    }
                }
                navigationLoadedHandler({detail: e}) {
                    T.navigationLoaded = e,
                    e && this.navigationDidLoad.emit()
                }
                checkMarketingConsent() {
                    T.maybeCheckMarketingConsent()
                }
                initEnvironment() {
                    var e = this;
                    return (0,
                    f.A)(function*() {
                        a.n.state.env = (0,
                        a.g)(e.env),
                        a.n.state.app = (0,
                        Z.s)(e.app),
                        a.n.state.locale = e.locale,
                        a.n.state.pageName = e.pageName,
                        a.n.state.initialRoutingPath = e.initialRoutingPath,
                        a.G.getInstance().setEnvironment(a.n.state.env),
                        ee.setEnvironment(a.n.state.env),
                        a.n.state.theme = e.theme,
                        a.n.state.mode = e.mode;
                        const t = (0,
                        a.a)(a.n.state.env);
                        e.setAuthConfig(JSON.parse(e.authOptions), t),
                        Fe(t),
                        e.loadedToggles = !1,
                        yield ee.setFeatures(a.n.state.env),
                        V.state.features = ee.features,
                        e.loadedToggles = !0;
                        const n = JSON.parse(e.shopInformation);
                        (0,
                        M.l)({
                            shopInformation: n
                        })
                    })()
                }
                setAuthConfig(e, t) {
                    e.redirectUrl && (t.IDENTITY_REDIRECT_URI = e.redirectUrl),
                    e.logoutCallback && (t.IDENTITY_LOGOUT_CALLBACK = e.logoutCallback),
                    a.n.state.authOptions = e
                }
                initNavContentStore() {
                    var e = this;
                    return (0,
                    f.A)(function*() {
                        const t = e.receivedContent;
                        e.receivedContent = !1,
                        e.loadingContent = !0;
                        try {
                            const n = e.locale
                              , o = a.n.state.env
                              , r = yield(0,
                            Ee.f)(e.app);
                            e.locale === n && a.n.state.env === o && (Object.assign(a.u.state, r),
                            e.receivedContent = !0)
                        } catch (n) {
                            I.l.error("FETCH_CONTENT", n),
                            t && (e.receivedContent = !0)
                        }
                        e.loadingContent = !1
                    })()
                }
                componentWillLoad() {
                    var e = this;
                    (0,
                    I.i)(),
                    (0,
                    y.a)(Rt),
                    a.G.getInstance().setDefaultProperties(this.locale, this.pageName),
                    window.ncs = a.u,
                    this.initEnvironment().then((0,
                    f.A)(function*() {
                        return e.initNavContentStore()
                    }));
                    const t = document.createElement("style");
                    t.textContent = "\n     :root {\n        --isLTR: 1;\n\n        &:dir(rtl) {\n          --isLTR: -1;\n        }\n      }\n    ",
                    document.head.appendChild(t)
                }
                componentDidLoad() {
                    try {
                        this.checkMarketingConsent()
                    } catch (e) {
                        I.l.error("INIT_COOKIE_CONSENT", e)
                    }
                    !function Lt() {
                        setTimeout( () => {
                            W({
                                eventType: U.INIT,
                                state: a.p
                            })
                        }
                        ),
                        T.version = a.V,
                        a.G.getInstance().pushDataLayerEvent(a.m.GENERAL_LOAD)
                    }()
                }
                disconnectedCallback() {
                    this.eventPreventedTimeout.cancel()
                }
                hasLimitedWidth() {
                    return this.mode === a.t.hero && "true" === this.limitedWidth
                }
                render() {
                    const {shop: e} = a.u.state;
                    return this.loadedToggles ? (0,
                    y.h)("nav", {
                        class: this.hasLimitedWidth() ? "header limited-width" : "header"
                    }, (0,
                    y.h)(At, null), (0,
                    y.h)("phn-wrapper", {
                        theme: this.theme,
                        mode: this.mode,
                        locale: this.locale,
                        loadingContent: this.loadingContent,
                        receivedContent: this.receivedContent,
                        breakpoint: this.breakpoint,
                        app: (0,
                        Z.s)(this.app),
                        displayLogoOnly: this.displayLogoOnly,
                        limitedWidth: this.hasLimitedWidth()
                    }, (0,
                    M.i)(e, this.locale, (0,
                    Z.s)(this.app)) ? (0,
                    y.h)("div", {
                        slot: "contextual_drawer_header"
                    }, (0,
                    y.h)("slot", {
                        name: "contextual_drawer_header"
                    })) : null)) : (0,
                    y.h)("div", null)
                }
                static get assetsDirs() {
                    return ["auth"]
                }
                static get watchers() {
                    return {
                        locale: ["localeWatchHandler"],
                        pageName: ["pageNameWatchHandler"],
                        env: ["newEnvReceived"],
                        app: ["appWatcher"],
                        shopInformation: ["watchShopVariable"],
                        initialRoutingPath: ["initialRoutingPathWatcher"],
                        theme: ["themeWatcher"],
                        mode: ["modeWatcher"],
                        authOptions: ["auth0Watcher"]
                    }
                }
            }
            ;
            return i.style = "@keyframes translate-forward-in-animation{from{translate:0 12px}to{translate:0 0px}}@keyframes fade-in-animation{from{opacity:0;background-color:rgba(0, 0, 0, 0)}to{opacity:1;background-color:rgba(0, 0, 0, 0.6)}}@keyframes translate-in-from-left{from{translate:calc(var(--isLTR, 1) * -1000px)}to{translate:0px}}@keyframes slideDown{from{max-block-size:0}to{max-block-size:1000px}}@keyframes slideUp{from{max-block-size:1000px}to{max-block-size:0}}@keyframes slideLeft{from{max-inline-size:0}to{max-inline-size:1000px}}@keyframes slideRight{from{max-inline-size:1000px}to{max-inline-size:0}}@keyframes darken{from{background-color:rgba(0, 0, 0, 0)}to{background-color:rgba(0, 0, 0, 0.8)}}@keyframes lighten{from{background-color:rgba(0, 0, 0, 0.8)}to{background-color:rgba(0, 0, 0, 0)}}@keyframes fadeInFromBottom{0%{opacity:0;inset-block-start:36px}100%{opacity:1;inset-block-start:0px}}@keyframes fadeInFromTop{0%{opacity:0;inset-block-end:36px}100%{opacity:1;inset-block-end:0px}}@keyframes fadeOutToTop{0%{opacity:1;inset-block-end:0px}100%{opacity:0;inset-block-end:16px}}@keyframes slideLeftDoubleDrawer{from{inset-inline-end:-1000px}to{inset-inline-end:0px}}@keyframes slideRightDoubleDrawer{from{inset-inline-end:0px}to{inset-inline-end:-1000px}}@keyframes fadeIn{0%{opacity:0}100%{opacity:1}}@keyframes fadeOut{to{opacity:0}}.translate-forward-animation-in{opacity:1}@media (prefers-reduced-motion: no-preference){.translate-forward-animation-in{animation:translate-forward-in-animation 0.25s cubic-bezier(0.25, 0.1, 0.25, 1)}}@media (prefers-reduced-motion: no-preference){.translate-forward-animation-out,.translate-backward-animation-out{animation:fadeOut 0.25s cubic-bezier(0.25, 0.1, 0.25, 1)}}@media (prefers-reduced-motion: no-preference){.translate-backward-animation-in{animation:fadeIn 0.25s cubic-bezier(0.25, 0.1, 0.25, 1), translate-forward-in-animation 0.25s cubic-bezier(0.25, 0.1, 0.25, 1)}}@media (prefers-reduced-motion: no-preference){.mobile-transition-forward{animation:fadeIn 0.25s cubic-bezier(0.25, 0.1, 0.25, 1), translate-forward-in-animation 0.25s cubic-bezier(0.25, 0.1, 0.25, 1)}}.phn-flex-col{display:flex;flex-direction:column}.phn-flex-row{display:flex;flex-direction:row}.phn-align-center{align-items:center}.phn-justify-around{justify-content:space-around}.phn-justify-between{justify-content:space-between}.phn-flex-wrap{flex-wrap:wrap}.tooltip{position:relative;display:inline-block}.tooltip-text{visibility:hidden;inline-size:inherit;white-space:nowrap;background-color:#404044;border-radius:2px;font-size:13px;color:#fff;text-align:center;padding-block:0px;padding-inline:4px;position:absolute;z-index:100;inset-inline-start:50%;transform:translateX(calc(var(--isLTR, 1) * -50%))}.tooltip:hover .tooltip-text{visibility:visible}.tooltip-text.level0_icon{inset-block-end:-1.7em}.tooltip-text.search_icon{inset-block-end:-1.65em}.tooltip-text.shop_icon{inset-block-end:-1.5em}.tooltip-text.finder_icon{inset-block-end:-1.5em}.tooltip-text.logo{inset-block-end:-1.5em}.tooltip-text.my_porsche_icon{inset-block-end:-1.5em}:host{display:block;visibility:visible;position:static;z-index:500}:host .header{min-block-size:4.125rem}@media (min-width: 480px){:host .header{min-block-size:5rem}}@media (min-width: 760px){:host .header{min-block-size:4.5625rem}}@media (min-width: 1000px){:host .header{min-block-size:4.75rem}}@media (min-width: 1300px){:host .header{min-block-size:5.125rem}}@media (min-width: 1920px){:host .limited-width{max-inline-size:160rem;margin-block:0;margin-inline:auto}}",
            i
        }
        )();
        function we() {
            return we = (0,
            f.A)(function*(i, e, t=T.getBreakpoint() >= a.e.s) {
                if (null === i)
                    return null;
                const n = Array.isArray(i)
                  , o = n ? i?.[0] : i
                  , r = function() {
                    var g = (0,
                    f.A)(function*(l) {
                        const c = yield T.waitForElement(o, l);
                        return "function" == typeof c?.componentOnReady && (yield c.componentOnReady()),
                        c
                    });
                    return function(c) {
                        return g.apply(this, arguments)
                    }
                }()
                  , s = n ? i.slice(1).map(r) : [yield r()]
                  , d = new Promise(g => e.set( () => g(null)));
                let h;
                return Promise.race([Promise.all([...s, t && new Promise(g => {
                    h = function Pt(i) {
                        return () => i(null)
                    }(g),
                    window.addEventListener("finishedAnimation", h)
                }
                ).finally( () => window.removeEventListener("finishedAnimation", h))]), d])
            }),
            we.apply(this, arguments)
        }
        const ye = {}
          , G = {};
        window.matchMedia?.("(prefers-reduced-motion: no-preference)");
        function Ze(i) {
            const e = (0,
            a.l)(i.target);
            if (null === e)
                throw new Error(`${i.type} event target is null`);
            const t = e.dataset.phnAmId;
            null != t && Ve(t, e)
        }
        function Ve(i, e) {
            const {resolve: t, timeout: n} = ye[i];
            clearTimeout(n),
            delete ye[i],
            delete e.dataset.phnAmId,
            t()
        }
        const _e_initialize = function Ut() {
            (0,
            a.v)().addEventListener("transitionend", Ze),
            (0,
            a.v)().addEventListener("animationend", Ze)
        }
          , _e_isPlayingAny = function Ht() {
            return Object.values(G).some(i => void 0 !== i?.promise)
        }
          , He = (...i) => {
            if (_e_isPlayingAny())
                setTimeout( () => He(...i), 20);
            else {
                const e = [A.a.MAIN_MENU, ...i];
                S.state.routingKeyPointer = i.length,
                S.state.routingKeyHistory = e
            }
        }
          , Be = () => S.state.routingKeyPointer === A.R.LEVEL_ZERO.valueOf() && S.state.animatingPointer === A.R.LEVEL_ZERO.valueOf()
          , tn = () => {
            const i = {
                navContent: {
                    ...a.u.state
                },
                navStateContent: {
                    ...a.n.state
                },
                shopStoreContent: {
                    ...M.s.state
                },
                finderStoreContent: {
                    ...I.f.state
                },
                featureToggleStateStore: {
                    ...V.state
                }
            };
            return (0,
            y.h)("phn-nd-navigation-drawer-initiater", {
                ...i
            }, (0,
            y.h)("phn-nd-contextual-drawer", null, (0,
            y.h)("div", {
                slot: "contextual_drawer_header"
            }, (0,
            y.h)("slot", {
                name: "contextual_drawer_header"
            }))))
        }
          , on = ( () => {
            let i = class {
                constructor(e) {
                    (0,
                    y.r)(this, e),
                    this.navigationLoaded = (0,
                    y.c)(this, "navigationLoaded", 7),
                    this.loadingContent = !1,
                    this.receivedContent = !1,
                    this.theme = a.r.light,
                    this.mode = a.t.navbar,
                    this.app = a.A.default,
                    this.displayLogoOnly = !1,
                    this.limitedWidth = !1,
                    this.usingKeyboard = !1,
                    this.scrolledPastHeader = !1,
                    this.isNavigationLoaded = !1,
                    this.isLoading = !1,
                    this.isMyPorscheHoveredOn = !1,
                    this.drawerTimeout = new Ge(2e4)
                }
                loadingContentWatcher() {
                    this.setNavigationLoaded()
                }
                breakpointWatcher() {
                    this.updateRoutingKeyPointerOfMobileLevel1(),
                    this.setNavigationLoaded()
                }
                receivedContentWatcher() {
                    this.setNavigationLoaded()
                }
                scrollHandler() {
                    this.scrolledPastHeader = this.el.getBoundingClientRect().bottom <= 0
                }
                handleKeyDown(e) {
                    "Escape" === e.key && (this.usingKeyboard = !0,
                    a.n.state.usingKeyboard = !0,
                    (0,
                    I.c)(),
                    Be() || _e_isPlayingAny() || (S.state.routingKeyPointer = -1))
                }
                handleClick(e) {
                    this.usingKeyboard = (0,
                    a.y)(e),
                    a.n.state.usingKeyboard = (0,
                    a.y)(e)
                }
                handleOpenDrawer(e) {
                    var t = this;
                    return (0,
                    f.A)(function*() {
                        yield t.openDrawer(e.detail)
                    })()
                }
                componentWillLoad() {
                    this.setNavigationLoaded()
                }
                updateRoutingKeyPointerOfMobileLevel1() {
                    S.state.routingKeyPointer === A.R.LEVEL_ONE.valueOf() && (S.state.routingKeyPointer = (0,
                    a.z)(this.breakpoint) ? A.R.LEVEL_TWO : A.R.LEVEL_ONE)
                }
                setNavigationLoaded() {
                    this.isNavigationLoaded = !this.loadingContent,
                    this.navigationLoaded.emit(this.isNavigationLoaded)
                }
                openDrawer(e) {
                    var t = this;
                    return (0,
                    f.A)(function*() {
                        const {initialRoutingKey: n, isKeyboardClick: o} = e;
                        t.isLoading = !0,
                        t.usingKeyboard = o,
                        a.n.state.usingKeyboard = o,
                        t.openMenuDrawer(("" === n ? a.n.state.initialRoutingPath : n) ?? ""),
                        yield function Dt(i, e) {
                            return we.apply(this, arguments)
                        }([t.el, ".drawer", "phn-nd-backdrop"], t.drawerTimeout),
                        t.isLoading = !1
                    })()
                }
                openMenuDrawer(e) {
                    (0,
                    I.o)(e),
                    He(...function qt(i) {
                        const e = a.u.state.models ? A.a.MODELS : A.a.VEHICLE_PURCHASE
                          , t = (0,
                        a.x)(S.state.breakpoint) ? [] : [e];
                        try {
                            if (!(0,
                            a.i)(i))
                                return t;
                            const [n,...o] = i.split(">")
                              , r = a.u.state[function Bt(i) {
                                return i.replace(/_([a-zA-Z])/g, (e, t) => t.toUpperCase())
                            }(n)];
                            if (!r)
                                return t;
                            const s = function Xt(i, e) {
                                let t = i;
                                const n = [];
                                for (const o of e) {
                                    if (t = t.children.find(r => r.id === o),
                                    null == t)
                                        break;
                                    n.push(o)
                                }
                                return n
                            }(r, o);
                            return function Qt(i) {
                                const e = [i[0]];
                                for (let t = 1; t < i.length; t += 1)
                                    e.push(`${e[t - 1]}/${i[t]}`);
                                return e
                            }([n, ...s])
                        } catch {
                            return t
                        }
                    }(e))
                }
                disconnectedCallback() {
                    this.drawerTimeout.cancel()
                }
                getLogoSize() {
                    return this.breakpoint >= I.b.m ? "medium" : "small"
                }
                getLogo() {
                    const {shop: e, level0: t, crest: n} = a.u.state;
                    return (0,
                    y.h)("div", {
                        class: "level-0-content-logo tooltip"
                    }, (0,
                    y.h)("phn-logo", {
                        app: this.app,
                        theme: this.theme,
                        mode: this.mode,
                        size: this.getLogoSize(),
                        breakpoint: this.breakpoint
                    }), n && (0,
                    a.z)(this.breakpoint) ? (0,
                    y.h)("span", {
                        class: "tooltip-text logo"
                    }, (0,
                    Xe.g)(this.locale, this.app, e, t)) : null)
                }
                render() {
                    return (0,
                    y.h)(y.H, {
                        key: "22c3027eb2cc3f303b5dac0239eb542a738efbf2",
                        class: `${(0,
                        Z.c)(this.mode, this.theme)}-theme themed-background`
                    }, (0,
                    y.h)("header", {
                        key: "3afef6a7278e8a55a3d606be3c76530238a3992a",
                        class: this.limitedWidth ? "limited-width" : ""
                    }, !Be() || S.state.animatingPointer === A.R.LEVEL_ZERO.valueOf() && S.state.routingKeyPointer >= 0 ? (0,
                    y.h)("phn-backdrop", {
                        style: this.isLoading ? {
                            display: "none"
                        } : {}
                    }) : null, (0,
                    y.h)("div", {
                        key: "c57cc048368f2d900abbc99d0c400c4be8662a7a",
                        class: "level-0-content"
                    }, this.displayLogoOnly ? this.getLogo() : (0,
                    y.h)("div", {
                        class: "level-0-content-icons"
                    }, (0,
                    y.h)("phn-level-0-icons", {
                        breakpoint: this.breakpoint,
                        isLoading: this.isLoading,
                        isNavigationLoaded: this.isNavigationLoaded
                    }, this.getLogo())))), (0,
                    y.h)(tn, {
                        key: "78225c5ca1a8e72a2fe567d2ddff00f86ca4bfc9"
                    }))
                }
                get el() {
                    return (0,
                    y.g)(this)
                }
                static get watchers() {
                    return {
                        loadingContent: ["loadingContentWatcher"],
                        breakpoint: ["breakpointWatcher"],
                        receivedContent: ["receivedContentWatcher"]
                    }
                }
            }
            ;
            return i.style = "@keyframes translate-forward-in-animation{from{translate:0 12px}to{translate:0 0px}}@keyframes fade-in-animation{from{opacity:0;background-color:rgba(0, 0, 0, 0)}to{opacity:1;background-color:rgba(0, 0, 0, 0.6)}}@keyframes translate-in-from-left{from{translate:calc(var(--isLTR, 1) * -1000px)}to{translate:0px}}@keyframes slideDown{from{max-block-size:0}to{max-block-size:1000px}}@keyframes slideUp{from{max-block-size:1000px}to{max-block-size:0}}@keyframes slideLeft{from{max-inline-size:0}to{max-inline-size:1000px}}@keyframes slideRight{from{max-inline-size:1000px}to{max-inline-size:0}}@keyframes darken{from{background-color:rgba(0, 0, 0, 0)}to{background-color:rgba(0, 0, 0, 0.8)}}@keyframes lighten{from{background-color:rgba(0, 0, 0, 0.8)}to{background-color:rgba(0, 0, 0, 0)}}@keyframes fadeInFromBottom{0%{opacity:0;inset-block-start:36px}100%{opacity:1;inset-block-start:0px}}@keyframes fadeInFromTop{0%{opacity:0;inset-block-end:36px}100%{opacity:1;inset-block-end:0px}}@keyframes fadeOutToTop{0%{opacity:1;inset-block-end:0px}100%{opacity:0;inset-block-end:16px}}@keyframes slideLeftDoubleDrawer{from{inset-inline-end:-1000px}to{inset-inline-end:0px}}@keyframes slideRightDoubleDrawer{from{inset-inline-end:0px}to{inset-inline-end:-1000px}}@keyframes fadeIn{0%{opacity:0}100%{opacity:1}}@keyframes fadeOut{to{opacity:0}}.translate-forward-animation-in.sc-phn-level-0{opacity:1}@media (prefers-reduced-motion: no-preference){.translate-forward-animation-in.sc-phn-level-0{animation:translate-forward-in-animation 0.25s cubic-bezier(0.25, 0.1, 0.25, 1)}}@media (prefers-reduced-motion: no-preference){.translate-forward-animation-out.sc-phn-level-0,.translate-backward-animation-out.sc-phn-level-0{animation:fadeOut 0.25s cubic-bezier(0.25, 0.1, 0.25, 1)}}@media (prefers-reduced-motion: no-preference){.translate-backward-animation-in.sc-phn-level-0{animation:fadeIn 0.25s cubic-bezier(0.25, 0.1, 0.25, 1), translate-forward-in-animation 0.25s cubic-bezier(0.25, 0.1, 0.25, 1)}}@media (prefers-reduced-motion: no-preference){.mobile-transition-forward.sc-phn-level-0{animation:fadeIn 0.25s cubic-bezier(0.25, 0.1, 0.25, 1), translate-forward-in-animation 0.25s cubic-bezier(0.25, 0.1, 0.25, 1)}}.phn-flex-col.sc-phn-level-0{display:flex;flex-direction:column}.phn-flex-row.sc-phn-level-0{display:flex;flex-direction:row}.phn-align-center.sc-phn-level-0{align-items:center}.phn-justify-around.sc-phn-level-0{justify-content:space-around}.phn-justify-between.sc-phn-level-0{justify-content:space-between}.phn-flex-wrap.sc-phn-level-0{flex-wrap:wrap}.tooltip.sc-phn-level-0{position:relative;display:inline-block}.tooltip-text.sc-phn-level-0{visibility:hidden;inline-size:inherit;white-space:nowrap;background-color:#404044;border-radius:2px;font-size:13px;color:#fff;text-align:center;padding-block:0px;padding-inline:4px;position:absolute;z-index:100;inset-inline-start:50%;transform:translateX(calc(var(--isLTR, 1) * -50%))}.tooltip.sc-phn-level-0:hover .tooltip-text.sc-phn-level-0{visibility:visible}.tooltip-text.level0_icon.sc-phn-level-0{inset-block-end:-1.7em}.tooltip-text.search_icon.sc-phn-level-0{inset-block-end:-1.65em}.tooltip-text.shop_icon.sc-phn-level-0{inset-block-end:-1.5em}.tooltip-text.finder_icon.sc-phn-level-0{inset-block-end:-1.5em}.tooltip-text.logo.sc-phn-level-0{inset-block-end:-1.5em}.tooltip-text.my_porsche_icon.sc-phn-level-0{inset-block-end:-1.5em}.sc-phn-level-0-h{display:block}.hero_light-theme.themed-background.sc-phn-level-0-h{background:linear-gradient(to bottom, rgba(224, 224, 224, 0.9) 0%, rgba(224, 224, 224, 0.9) 20%, rgba(224, 224, 224, 0.852589) 26.67%, rgba(225, 225, 225, 0.768225) 33.33%, rgba(226, 226, 226, 0.668116) 40%, rgba(227, 227, 227, 0.557309) 46.67%, rgba(228, 228, 228, 0.442691) 53.33%, rgba(229, 229, 229, 0.331884) 60%, rgba(230, 230, 230, 0.231775) 66.67%, rgba(231, 231, 231, 0.147411) 73.33%, rgba(232, 232, 232, 0.0816599) 80%, rgba(232, 232, 232, 0.03551) 86.67%, rgba(232, 232, 232, 0.0086472) 93.33%, rgba(232, 232, 232, 0) 100%)}@media (min-width: 0px){.hero_light-theme.themed-background.sc-phn-level-0-h{block-size:72px}}@media (min-width: 760px){.hero_light-theme.themed-background.sc-phn-level-0-h{block-size:148px}}.hero_light-theme.sc-phn-level-0-h phn-p-icon.sc-phn-level-0{color:black}.hero_dark-theme.themed-background.sc-phn-level-0-h{background:linear-gradient(to bottom, hsla(0, 0%, 0%, 0.8) 0%, hsla(0, 0%, 0%, 0.8) 8.1%, hsla(0, 0%, 0%, 0.8) 15.5%, hsla(0, 0%, 0%, 0.8) 22.5%, hsla(0, 0%, 0%, 0.78) 29%, hsla(0, 0%, 0%, 0.73) 35.3%, hsla(0, 0%, 0%, 0.67) 41.2%, hsla(0, 0%, 0%, 0.6) 47.1%, hsla(0, 0%, 0%, 0.52) 52.9%, hsla(0, 0%, 0%, 0.44) 58.8%, hsla(0, 0%, 0%, 0.33) 64.7%, hsla(0, 0%, 0%, 0.22) 71%, hsla(0, 0%, 0%, 0.12) 77.5%, hsla(0, 0%, 0%, 0.05) 84.5%, hsla(0, 0%, 0%, 0.011) 91.9%, hsla(0, 0%, 0%, 0) 100%)}@media (min-width: 0px){.hero_dark-theme.themed-background.sc-phn-level-0-h{block-size:72px}}@media (min-width: 760px){.hero_dark-theme.themed-background.sc-phn-level-0-h{block-size:148px}}.hero_dark-theme.sc-phn-level-0-h phn-p-icon.sc-phn-level-0{color:white}.navbar-theme.themed-background.sc-phn-level-0-h{background:white;border-block-end:1px solid #e3e4e5}.navbar-theme.sc-phn-level-0-h phn-p-icon.sc-phn-level-0{color:black}.transparent_dark-theme.themed-background.sc-phn-level-0-h{background:transparent}.transparent_dark-theme.sc-phn-level-0-h phn-p-icon.sc-phn-level-0{color:white}.transparent_light-theme.themed-background.sc-phn-level-0-h{background:transparent}.transparent_light-theme.sc-phn-level-0-h phn-p-icon.sc-phn-level-0{color:white}@media (min-width: 1920px){.sc-phn-level-0-h header.limited-width.sc-phn-level-0{padding-block:0 calc(50vw - 2560px);padding-inline:calc(50vw - 2560px)}}.sc-phn-level-0-h header.sc-phn-level-0{margin:0 var(--pds-internal-grid-margin, 0);display:grid;padding:0 calc(50% - var(--pds-internal-grid-margin, 0px) - 1280px);grid-gap:clamp(16px, 1.25vw + 12px, 36px);max-width:var(--pds-internal-grid-width-max, 2560px);min-width:var(--pds-internal-grid-width-min, 320px);box-sizing:content-box;grid-template-columns:[full-start] minmax(0, var(--pds-internal-grid-outer-column, calc(var(--pds-internal-grid-safe-zone) - clamp(16px, 1.25vw + 12px, 36px)))) [wide-start extended-start basic-start narrow-start] repeat(6, minmax(0, 1fr)) [narrow-end basic-end extended-end wide-end] minmax(0, var(--pds-internal-grid-outer-column, calc(var(--pds-internal-grid-safe-zone) - clamp(16px, 1.25vw + 12px, 36px)))) [full-end];--pds-internal-grid-safe-zone:max(22px, 10.625vw - 12px);--pds-grid-basic-span-one-half:span 3;--pds-grid-basic-span-one-third:span 2;--pds-grid-narrow-span-one-half:span 3;--pds-grid-basic-span-two-thirds:span 4;--pds-grid-extended-span-one-half:span 3;font-family:\"Porsche Next\", \"Arial Narrow\", Arial, \"Heiti SC\", SimHei, sans-serif;font-weight:400;color:#fff;block-size:4.125rem}@media (min-width: 760px){.sc-phn-level-0-h header.sc-phn-level-0{grid-template-columns:[full-start] minmax(0, var(--pds-internal-grid-outer-column, calc(var(--pds-internal-grid-safe-zone) - clamp(16px, 1.25vw + 12px, 36px)))) [wide-start] minmax(0, 1fr) [extended-start] minmax(0, 1fr) [basic-start] repeat(2, minmax(0, 1fr)) [narrow-start] repeat(8, minmax(0, 1fr)) [narrow-end] repeat(2, minmax(0, 1fr)) [basic-end] minmax(0, 1fr) [extended-end] minmax(0, 1fr) [wide-end] minmax(0, var(--pds-internal-grid-outer-column, calc(var(--pds-internal-grid-safe-zone) - clamp(16px, 1.25vw + 12px, 36px)))) [full-end];--pds-internal-grid-safe-zone:calc(5vw - 16px);--pds-grid-basic-span-one-half:span 6;--pds-grid-basic-span-one-third:span 4;--pds-grid-narrow-span-one-half:span 4;--pds-grid-basic-span-two-thirds:span 8;--pds-grid-extended-span-one-half:span 7}}@media (min-width: 1920px){.sc-phn-level-0-h header.sc-phn-level-0{--pds-internal-grid-safe-zone:min(50vw - 880px, 400px)}}@media (min-width: 480px){.sc-phn-level-0-h header.sc-phn-level-0{block-size:5rem}}@media (min-width: 760px){.sc-phn-level-0-h header.sc-phn-level-0{block-size:4.5625rem}}@media (min-width: 1000px){.sc-phn-level-0-h header.sc-phn-level-0{block-size:4.75rem}}@media (min-width: 1300px){.sc-phn-level-0-h header.sc-phn-level-0{block-size:5.125rem;flex-direction:row}}.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0{block-size:100%;display:flex;justify-content:center;align-items:center;position:relative;grid-column:wide-start/wide-end}.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .level-0-content-logo.sc-phn-level-0{display:flex;align-items:center}.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .level-0-content-icons.sc-phn-level-0{position:absolute;inline-size:100%}.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 phn-burger-button.sc-phn-level-0,.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 phn-level-0-icons.sc-phn-level-0,.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 phn-logo.sc-phn-level-0,.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .skeleton-container.sc-phn-level-0{flex:1;display:flex}.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .skeleton-container.crest-skeleton.sc-phn-level-0{justify-content:center}.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 phn-logo.sc-phn-level-0{justify-content:center;z-index:1;transform:translate3d(0, 0, 0)}@media (min-width: 1300px){.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 phn-logo.sc-phn-level-0{z-index:1}}.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .label-bone.sc-phn-level-0{inline-size:7.75rem;block-size:1.5rem;border-radius:0.25rem;background:#eeeff2 linear-gradient(to right, transparent 0%, #f7f7f7 20%, transparent 50%) 0 0/200% 100%;display:block;border-radius:4px;animation:skeletonAnimation 0.6s cubic-bezier(0.25, 0.1, 0.25, 1) infinite}@keyframes skeletonAnimation{from{background-position-x:100%}to{background-position-x:-100%}}@media (min-width: 760px){.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .label-bone.sc-phn-level-0{inline-size:18rem}}.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .label-bone-dark.sc-phn-level-0{inline-size:7.75rem;block-size:1.5rem;border-radius:0.25rem;background:#212225 linear-gradient(to right, transparent 0%, #1a1b1e 20%, transparent 50%) 0 0/200% 100%;display:block;border-radius:4px;animation:skeletonAnimation 0.6s cubic-bezier(0.25, 0.1, 0.25, 1) infinite}@keyframes skeletonAnimation{from{background-position-x:100%}to{background-position-x:-100%}}@media (min-width: 760px){.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .label-bone-dark.sc-phn-level-0{inline-size:18rem}}.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .crest-bone.sc-phn-level-0{inline-size:1.403125rem;block-size:1.8125rem;background-image:url(\"data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='22.45px' height='29px' viewBox='0 0 29 37' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Ecrest-path-pixel-perfect-3%3C/title%3E%3Cg id='Design-Focus' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='832-skeleton-hn-1366' transform='translate(-130.000000, -39.000000)' fill='%23eeeff2' fill-rule='nonzero'%3E%3Cg id='Crest-bones' transform='translate(78.000000, 0.000000)'%3E%3Cpath d='M80.8612951,41.6891698 C79.3593022,41.0929534 77.8195513,40.5958533 76.2520235,40.2010916 C74.092052,39.6781633 71.8946453,39.3227949 69.6795438,39.1381787 C68.6125828,39.0425165 67.6096394,39.0106291 66.4786608,39 L66.4679912,39 C65.4010302,39 64.3340692,39 63.2671082,39.1381787 C61.0520067,39.3227949 58.8545999,39.6781633 56.6946284,40.2010916 C55.1285058,40.5930516 53.5918758,41.093847 52.0960265,41.6997989 L52,41.6997989 C52,41.6997989 52.1813834,52.8710141 52.3200883,58.3981614 C52.3624958,60.0288438 52.6613178,61.6428163 53.2056659,63.1812698 C53.83596,64.927843 54.6779771,66.5912102 55.7130243,68.1344441 C56.7974563,69.7321996 58.1093136,71.164397 59.6074319,72.3860959 C60.1877267,72.8817079 60.8047619,73.3329574 61.4532745,73.7359954 C62.4748439,74.3534515 63.5453697,74.8866836 64.6541575,75.3303648 C65.0062546,75.4791727 65.3263429,75.6279805 65.6144224,75.7236426 C65.9077386,75.8428333 66.2112826,75.9353298 66.5213392,76 C66.8313958,75.9353298 67.1349399,75.8428333 67.4282561,75.7236426 C67.7163355,75.6279805 68.0364238,75.4791727 68.3991906,75.3303648 C69.4899033,74.8845355 70.542531,74.3513317 71.5467255,73.7359954 C72.1928274,73.3294237 72.8096114,72.8783579 73.3925681,72.3860959 C74.8933275,71.167176 76.2055628,69.7345659 77.2869757,68.1344441 C78.3220229,66.5912102 79.16404,64.927843 79.7943341,63.1812698 C80.3386822,61.6428163 80.6375042,60.0288438 80.6799117,58.3981614 C80.8186166,52.8710141 81,41.6997989 81,41.6997989 L80.8612951,41.6891698 Z' id='crest-path-pixel-perfect-3'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E\");background-repeat:no-repeat;background-position:center}@media (min-width: 480px){.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .crest-bone.sc-phn-level-0{inline-size:1.6875rem;block-size:2.25rem;background-image:url(\"data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='27px' height='36px' viewBox='0 0 29 37' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Ecrest-path-pixel-perfect-3%3C/title%3E%3Cg id='Design-Focus' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='832-skeleton-hn-1366' transform='translate(-130.000000, -39.000000)' fill='%23eeeff2' fill-rule='nonzero'%3E%3Cg id='Crest-bones' transform='translate(78.000000, 0.000000)'%3E%3Cpath d='M80.8612951,41.6891698 C79.3593022,41.0929534 77.8195513,40.5958533 76.2520235,40.2010916 C74.092052,39.6781633 71.8946453,39.3227949 69.6795438,39.1381787 C68.6125828,39.0425165 67.6096394,39.0106291 66.4786608,39 L66.4679912,39 C65.4010302,39 64.3340692,39 63.2671082,39.1381787 C61.0520067,39.3227949 58.8545999,39.6781633 56.6946284,40.2010916 C55.1285058,40.5930516 53.5918758,41.093847 52.0960265,41.6997989 L52,41.6997989 C52,41.6997989 52.1813834,52.8710141 52.3200883,58.3981614 C52.3624958,60.0288438 52.6613178,61.6428163 53.2056659,63.1812698 C53.83596,64.927843 54.6779771,66.5912102 55.7130243,68.1344441 C56.7974563,69.7321996 58.1093136,71.164397 59.6074319,72.3860959 C60.1877267,72.8817079 60.8047619,73.3329574 61.4532745,73.7359954 C62.4748439,74.3534515 63.5453697,74.8866836 64.6541575,75.3303648 C65.0062546,75.4791727 65.3263429,75.6279805 65.6144224,75.7236426 C65.9077386,75.8428333 66.2112826,75.9353298 66.5213392,76 C66.8313958,75.9353298 67.1349399,75.8428333 67.4282561,75.7236426 C67.7163355,75.6279805 68.0364238,75.4791727 68.3991906,75.3303648 C69.4899033,74.8845355 70.542531,74.3513317 71.5467255,73.7359954 C72.1928274,73.3294237 72.8096114,72.8783579 73.3925681,72.3860959 C74.8933275,71.167176 76.2055628,69.7345659 77.2869757,68.1344441 C78.3220229,66.5912102 79.16404,64.927843 79.7943341,63.1812698 C80.3386822,61.6428163 80.6375042,60.0288438 80.6799117,58.3981614 C80.8186166,52.8710141 81,41.6997989 81,41.6997989 L80.8612951,41.6891698 Z' id='crest-path-pixel-perfect-3'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")}}@media (min-width: 760px){.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .crest-bone.sc-phn-level-0{inline-size:1.75rem;block-size:2.25rem;background-image:url(\"data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='28px' height='36px' viewBox='0 0 29 37' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Ecrest-path-pixel-perfect-3%3C/title%3E%3Cg id='Design-Focus' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='832-skeleton-hn-1366' transform='translate(-130.000000, -39.000000)' fill='%23eeeff2' fill-rule='nonzero'%3E%3Cg id='Crest-bones' transform='translate(78.000000, 0.000000)'%3E%3Cpath d='M80.8612951,41.6891698 C79.3593022,41.0929534 77.8195513,40.5958533 76.2520235,40.2010916 C74.092052,39.6781633 71.8946453,39.3227949 69.6795438,39.1381787 C68.6125828,39.0425165 67.6096394,39.0106291 66.4786608,39 L66.4679912,39 C65.4010302,39 64.3340692,39 63.2671082,39.1381787 C61.0520067,39.3227949 58.8545999,39.6781633 56.6946284,40.2010916 C55.1285058,40.5930516 53.5918758,41.093847 52.0960265,41.6997989 L52,41.6997989 C52,41.6997989 52.1813834,52.8710141 52.3200883,58.3981614 C52.3624958,60.0288438 52.6613178,61.6428163 53.2056659,63.1812698 C53.83596,64.927843 54.6779771,66.5912102 55.7130243,68.1344441 C56.7974563,69.7321996 58.1093136,71.164397 59.6074319,72.3860959 C60.1877267,72.8817079 60.8047619,73.3329574 61.4532745,73.7359954 C62.4748439,74.3534515 63.5453697,74.8866836 64.6541575,75.3303648 C65.0062546,75.4791727 65.3263429,75.6279805 65.6144224,75.7236426 C65.9077386,75.8428333 66.2112826,75.9353298 66.5213392,76 C66.8313958,75.9353298 67.1349399,75.8428333 67.4282561,75.7236426 C67.7163355,75.6279805 68.0364238,75.4791727 68.3991906,75.3303648 C69.4899033,74.8845355 70.542531,74.3513317 71.5467255,73.7359954 C72.1928274,73.3294237 72.8096114,72.8783579 73.3925681,72.3860959 C74.8933275,71.167176 76.2055628,69.7345659 77.2869757,68.1344441 C78.3220229,66.5912102 79.16404,64.927843 79.7943341,63.1812698 C80.3386822,61.6428163 80.6375042,60.0288438 80.6799117,58.3981614 C80.8186166,52.8710141 81,41.6997989 81,41.6997989 L80.8612951,41.6891698 Z' id='crest-path-pixel-perfect-3'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")}}@media (min-width: 1000px){.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .crest-bone.sc-phn-level-0{inline-size:1.9375rem;block-size:2.5625rem;background-image:url(\"data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='31px' height='41px' viewBox='0 0 29 37' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Ecrest-path-pixel-perfect-3%3C/title%3E%3Cg id='Design-Focus' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='832-skeleton-hn-1366' transform='translate(-130.000000, -39.000000)' fill='%23eeeff2' fill-rule='nonzero'%3E%3Cg id='Crest-bones' transform='translate(78.000000, 0.000000)'%3E%3Cpath d='M80.8612951,41.6891698 C79.3593022,41.0929534 77.8195513,40.5958533 76.2520235,40.2010916 C74.092052,39.6781633 71.8946453,39.3227949 69.6795438,39.1381787 C68.6125828,39.0425165 67.6096394,39.0106291 66.4786608,39 L66.4679912,39 C65.4010302,39 64.3340692,39 63.2671082,39.1381787 C61.0520067,39.3227949 58.8545999,39.6781633 56.6946284,40.2010916 C55.1285058,40.5930516 53.5918758,41.093847 52.0960265,41.6997989 L52,41.6997989 C52,41.6997989 52.1813834,52.8710141 52.3200883,58.3981614 C52.3624958,60.0288438 52.6613178,61.6428163 53.2056659,63.1812698 C53.83596,64.927843 54.6779771,66.5912102 55.7130243,68.1344441 C56.7974563,69.7321996 58.1093136,71.164397 59.6074319,72.3860959 C60.1877267,72.8817079 60.8047619,73.3329574 61.4532745,73.7359954 C62.4748439,74.3534515 63.5453697,74.8866836 64.6541575,75.3303648 C65.0062546,75.4791727 65.3263429,75.6279805 65.6144224,75.7236426 C65.9077386,75.8428333 66.2112826,75.9353298 66.5213392,76 C66.8313958,75.9353298 67.1349399,75.8428333 67.4282561,75.7236426 C67.7163355,75.6279805 68.0364238,75.4791727 68.3991906,75.3303648 C69.4899033,74.8845355 70.542531,74.3513317 71.5467255,73.7359954 C72.1928274,73.3294237 72.8096114,72.8783579 73.3925681,72.3860959 C74.8933275,71.167176 76.2055628,69.7345659 77.2869757,68.1344441 C78.3220229,66.5912102 79.16404,64.927843 79.7943341,63.1812698 C80.3386822,61.6428163 80.6375042,60.0288438 80.6799117,58.3981614 C80.8186166,52.8710141 81,41.6997989 81,41.6997989 L80.8612951,41.6891698 Z' id='crest-path-pixel-perfect-3'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")}}@media (min-width: 1300px){.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .crest-bone.sc-phn-level-0{inline-size:2rem;block-size:2.6875rem;background-image:url(\"data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='32px' height='43px' viewBox='0 0 29 37' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Ecrest-path-pixel-perfect-3%3C/title%3E%3Cg id='Design-Focus' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='832-skeleton-hn-1366' transform='translate(-130.000000, -39.000000)' fill='%23eeeff2' fill-rule='nonzero'%3E%3Cg id='Crest-bones' transform='translate(78.000000, 0.000000)'%3E%3Cpath d='M80.8612951,41.6891698 C79.3593022,41.0929534 77.8195513,40.5958533 76.2520235,40.2010916 C74.092052,39.6781633 71.8946453,39.3227949 69.6795438,39.1381787 C68.6125828,39.0425165 67.6096394,39.0106291 66.4786608,39 L66.4679912,39 C65.4010302,39 64.3340692,39 63.2671082,39.1381787 C61.0520067,39.3227949 58.8545999,39.6781633 56.6946284,40.2010916 C55.1285058,40.5930516 53.5918758,41.093847 52.0960265,41.6997989 L52,41.6997989 C52,41.6997989 52.1813834,52.8710141 52.3200883,58.3981614 C52.3624958,60.0288438 52.6613178,61.6428163 53.2056659,63.1812698 C53.83596,64.927843 54.6779771,66.5912102 55.7130243,68.1344441 C56.7974563,69.7321996 58.1093136,71.164397 59.6074319,72.3860959 C60.1877267,72.8817079 60.8047619,73.3329574 61.4532745,73.7359954 C62.4748439,74.3534515 63.5453697,74.8866836 64.6541575,75.3303648 C65.0062546,75.4791727 65.3263429,75.6279805 65.6144224,75.7236426 C65.9077386,75.8428333 66.2112826,75.9353298 66.5213392,76 C66.8313958,75.9353298 67.1349399,75.8428333 67.4282561,75.7236426 C67.7163355,75.6279805 68.0364238,75.4791727 68.3991906,75.3303648 C69.4899033,74.8845355 70.542531,74.3513317 71.5467255,73.7359954 C72.1928274,73.3294237 72.8096114,72.8783579 73.3925681,72.3860959 C74.8933275,71.167176 76.2055628,69.7345659 77.2869757,68.1344441 C78.3220229,66.5912102 79.16404,64.927843 79.7943341,63.1812698 C80.3386822,61.6428163 80.6375042,60.0288438 80.6799117,58.3981614 C80.8186166,52.8710141 81,41.6997989 81,41.6997989 L80.8612951,41.6891698 Z' id='crest-path-pixel-perfect-3'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")}}.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .crest-bone-dark.sc-phn-level-0{inline-size:1.403125rem;block-size:1.8125rem;background-image:url(\"data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='22.45px' height='29px' viewBox='0 0 29 37' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Ecrest-path-pixel-perfect-3%3C/title%3E%3Cg id='Design-Focus' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='832-skeleton-hn-1366' transform='translate(-130.000000, -39.000000)' fill='%23212225' fill-rule='nonzero'%3E%3Cg id='Crest-bones' transform='translate(78.000000, 0.000000)'%3E%3Cpath d='M80.8612951,41.6891698 C79.3593022,41.0929534 77.8195513,40.5958533 76.2520235,40.2010916 C74.092052,39.6781633 71.8946453,39.3227949 69.6795438,39.1381787 C68.6125828,39.0425165 67.6096394,39.0106291 66.4786608,39 L66.4679912,39 C65.4010302,39 64.3340692,39 63.2671082,39.1381787 C61.0520067,39.3227949 58.8545999,39.6781633 56.6946284,40.2010916 C55.1285058,40.5930516 53.5918758,41.093847 52.0960265,41.6997989 L52,41.6997989 C52,41.6997989 52.1813834,52.8710141 52.3200883,58.3981614 C52.3624958,60.0288438 52.6613178,61.6428163 53.2056659,63.1812698 C53.83596,64.927843 54.6779771,66.5912102 55.7130243,68.1344441 C56.7974563,69.7321996 58.1093136,71.164397 59.6074319,72.3860959 C60.1877267,72.8817079 60.8047619,73.3329574 61.4532745,73.7359954 C62.4748439,74.3534515 63.5453697,74.8866836 64.6541575,75.3303648 C65.0062546,75.4791727 65.3263429,75.6279805 65.6144224,75.7236426 C65.9077386,75.8428333 66.2112826,75.9353298 66.5213392,76 C66.8313958,75.9353298 67.1349399,75.8428333 67.4282561,75.7236426 C67.7163355,75.6279805 68.0364238,75.4791727 68.3991906,75.3303648 C69.4899033,74.8845355 70.542531,74.3513317 71.5467255,73.7359954 C72.1928274,73.3294237 72.8096114,72.8783579 73.3925681,72.3860959 C74.8933275,71.167176 76.2055628,69.7345659 77.2869757,68.1344441 C78.3220229,66.5912102 79.16404,64.927843 79.7943341,63.1812698 C80.3386822,61.6428163 80.6375042,60.0288438 80.6799117,58.3981614 C80.8186166,52.8710141 81,41.6997989 81,41.6997989 L80.8612951,41.6891698 Z' id='crest-path-pixel-perfect-3'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E\");background-repeat:no-repeat;background-position:center}@media (min-width: 480px){.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .crest-bone-dark.sc-phn-level-0{inline-size:1.6875rem;block-size:2.25rem;background-image:url(\"data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='27px' height='36px' viewBox='0 0 29 37' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Ecrest-path-pixel-perfect-3%3C/title%3E%3Cg id='Design-Focus' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='832-skeleton-hn-1366' transform='translate(-130.000000, -39.000000)' fill='%23212225' fill-rule='nonzero'%3E%3Cg id='Crest-bones' transform='translate(78.000000, 0.000000)'%3E%3Cpath d='M80.8612951,41.6891698 C79.3593022,41.0929534 77.8195513,40.5958533 76.2520235,40.2010916 C74.092052,39.6781633 71.8946453,39.3227949 69.6795438,39.1381787 C68.6125828,39.0425165 67.6096394,39.0106291 66.4786608,39 L66.4679912,39 C65.4010302,39 64.3340692,39 63.2671082,39.1381787 C61.0520067,39.3227949 58.8545999,39.6781633 56.6946284,40.2010916 C55.1285058,40.5930516 53.5918758,41.093847 52.0960265,41.6997989 L52,41.6997989 C52,41.6997989 52.1813834,52.8710141 52.3200883,58.3981614 C52.3624958,60.0288438 52.6613178,61.6428163 53.2056659,63.1812698 C53.83596,64.927843 54.6779771,66.5912102 55.7130243,68.1344441 C56.7974563,69.7321996 58.1093136,71.164397 59.6074319,72.3860959 C60.1877267,72.8817079 60.8047619,73.3329574 61.4532745,73.7359954 C62.4748439,74.3534515 63.5453697,74.8866836 64.6541575,75.3303648 C65.0062546,75.4791727 65.3263429,75.6279805 65.6144224,75.7236426 C65.9077386,75.8428333 66.2112826,75.9353298 66.5213392,76 C66.8313958,75.9353298 67.1349399,75.8428333 67.4282561,75.7236426 C67.7163355,75.6279805 68.0364238,75.4791727 68.3991906,75.3303648 C69.4899033,74.8845355 70.542531,74.3513317 71.5467255,73.7359954 C72.1928274,73.3294237 72.8096114,72.8783579 73.3925681,72.3860959 C74.8933275,71.167176 76.2055628,69.7345659 77.2869757,68.1344441 C78.3220229,66.5912102 79.16404,64.927843 79.7943341,63.1812698 C80.3386822,61.6428163 80.6375042,60.0288438 80.6799117,58.3981614 C80.8186166,52.8710141 81,41.6997989 81,41.6997989 L80.8612951,41.6891698 Z' id='crest-path-pixel-perfect-3'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")}}@media (min-width: 760px){.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .crest-bone-dark.sc-phn-level-0{inline-size:1.75rem;block-size:2.25rem;background-image:url(\"data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='28px' height='36px' viewBox='0 0 29 37' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Ecrest-path-pixel-perfect-3%3C/title%3E%3Cg id='Design-Focus' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='832-skeleton-hn-1366' transform='translate(-130.000000, -39.000000)' fill='%23212225' fill-rule='nonzero'%3E%3Cg id='Crest-bones' transform='translate(78.000000, 0.000000)'%3E%3Cpath d='M80.8612951,41.6891698 C79.3593022,41.0929534 77.8195513,40.5958533 76.2520235,40.2010916 C74.092052,39.6781633 71.8946453,39.3227949 69.6795438,39.1381787 C68.6125828,39.0425165 67.6096394,39.0106291 66.4786608,39 L66.4679912,39 C65.4010302,39 64.3340692,39 63.2671082,39.1381787 C61.0520067,39.3227949 58.8545999,39.6781633 56.6946284,40.2010916 C55.1285058,40.5930516 53.5918758,41.093847 52.0960265,41.6997989 L52,41.6997989 C52,41.6997989 52.1813834,52.8710141 52.3200883,58.3981614 C52.3624958,60.0288438 52.6613178,61.6428163 53.2056659,63.1812698 C53.83596,64.927843 54.6779771,66.5912102 55.7130243,68.1344441 C56.7974563,69.7321996 58.1093136,71.164397 59.6074319,72.3860959 C60.1877267,72.8817079 60.8047619,73.3329574 61.4532745,73.7359954 C62.4748439,74.3534515 63.5453697,74.8866836 64.6541575,75.3303648 C65.0062546,75.4791727 65.3263429,75.6279805 65.6144224,75.7236426 C65.9077386,75.8428333 66.2112826,75.9353298 66.5213392,76 C66.8313958,75.9353298 67.1349399,75.8428333 67.4282561,75.7236426 C67.7163355,75.6279805 68.0364238,75.4791727 68.3991906,75.3303648 C69.4899033,74.8845355 70.542531,74.3513317 71.5467255,73.7359954 C72.1928274,73.3294237 72.8096114,72.8783579 73.3925681,72.3860959 C74.8933275,71.167176 76.2055628,69.7345659 77.2869757,68.1344441 C78.3220229,66.5912102 79.16404,64.927843 79.7943341,63.1812698 C80.3386822,61.6428163 80.6375042,60.0288438 80.6799117,58.3981614 C80.8186166,52.8710141 81,41.6997989 81,41.6997989 L80.8612951,41.6891698 Z' id='crest-path-pixel-perfect-3'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")}}@media (min-width: 1000px){.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .crest-bone-dark.sc-phn-level-0{inline-size:1.9375rem;block-size:2.5625rem;background-image:url(\"data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='31px' height='41px' viewBox='0 0 29 37' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Ecrest-path-pixel-perfect-3%3C/title%3E%3Cg id='Design-Focus' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='832-skeleton-hn-1366' transform='translate(-130.000000, -39.000000)' fill='%23212225' fill-rule='nonzero'%3E%3Cg id='Crest-bones' transform='translate(78.000000, 0.000000)'%3E%3Cpath d='M80.8612951,41.6891698 C79.3593022,41.0929534 77.8195513,40.5958533 76.2520235,40.2010916 C74.092052,39.6781633 71.8946453,39.3227949 69.6795438,39.1381787 C68.6125828,39.0425165 67.6096394,39.0106291 66.4786608,39 L66.4679912,39 C65.4010302,39 64.3340692,39 63.2671082,39.1381787 C61.0520067,39.3227949 58.8545999,39.6781633 56.6946284,40.2010916 C55.1285058,40.5930516 53.5918758,41.093847 52.0960265,41.6997989 L52,41.6997989 C52,41.6997989 52.1813834,52.8710141 52.3200883,58.3981614 C52.3624958,60.0288438 52.6613178,61.6428163 53.2056659,63.1812698 C53.83596,64.927843 54.6779771,66.5912102 55.7130243,68.1344441 C56.7974563,69.7321996 58.1093136,71.164397 59.6074319,72.3860959 C60.1877267,72.8817079 60.8047619,73.3329574 61.4532745,73.7359954 C62.4748439,74.3534515 63.5453697,74.8866836 64.6541575,75.3303648 C65.0062546,75.4791727 65.3263429,75.6279805 65.6144224,75.7236426 C65.9077386,75.8428333 66.2112826,75.9353298 66.5213392,76 C66.8313958,75.9353298 67.1349399,75.8428333 67.4282561,75.7236426 C67.7163355,75.6279805 68.0364238,75.4791727 68.3991906,75.3303648 C69.4899033,74.8845355 70.542531,74.3513317 71.5467255,73.7359954 C72.1928274,73.3294237 72.8096114,72.8783579 73.3925681,72.3860959 C74.8933275,71.167176 76.2055628,69.7345659 77.2869757,68.1344441 C78.3220229,66.5912102 79.16404,64.927843 79.7943341,63.1812698 C80.3386822,61.6428163 80.6375042,60.0288438 80.6799117,58.3981614 C80.8186166,52.8710141 81,41.6997989 81,41.6997989 L80.8612951,41.6891698 Z' id='crest-path-pixel-perfect-3'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")}}@media (min-width: 1300px){.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .crest-bone-dark.sc-phn-level-0{inline-size:2rem;block-size:2.6875rem;background-image:url(\"data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='32px' height='43px' viewBox='0 0 29 37' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Ecrest-path-pixel-perfect-3%3C/title%3E%3Cg id='Design-Focus' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='832-skeleton-hn-1366' transform='translate(-130.000000, -39.000000)' fill='%23212225' fill-rule='nonzero'%3E%3Cg id='Crest-bones' transform='translate(78.000000, 0.000000)'%3E%3Cpath d='M80.8612951,41.6891698 C79.3593022,41.0929534 77.8195513,40.5958533 76.2520235,40.2010916 C74.092052,39.6781633 71.8946453,39.3227949 69.6795438,39.1381787 C68.6125828,39.0425165 67.6096394,39.0106291 66.4786608,39 L66.4679912,39 C65.4010302,39 64.3340692,39 63.2671082,39.1381787 C61.0520067,39.3227949 58.8545999,39.6781633 56.6946284,40.2010916 C55.1285058,40.5930516 53.5918758,41.093847 52.0960265,41.6997989 L52,41.6997989 C52,41.6997989 52.1813834,52.8710141 52.3200883,58.3981614 C52.3624958,60.0288438 52.6613178,61.6428163 53.2056659,63.1812698 C53.83596,64.927843 54.6779771,66.5912102 55.7130243,68.1344441 C56.7974563,69.7321996 58.1093136,71.164397 59.6074319,72.3860959 C60.1877267,72.8817079 60.8047619,73.3329574 61.4532745,73.7359954 C62.4748439,74.3534515 63.5453697,74.8866836 64.6541575,75.3303648 C65.0062546,75.4791727 65.3263429,75.6279805 65.6144224,75.7236426 C65.9077386,75.8428333 66.2112826,75.9353298 66.5213392,76 C66.8313958,75.9353298 67.1349399,75.8428333 67.4282561,75.7236426 C67.7163355,75.6279805 68.0364238,75.4791727 68.3991906,75.3303648 C69.4899033,74.8845355 70.542531,74.3513317 71.5467255,73.7359954 C72.1928274,73.3294237 72.8096114,72.8783579 73.3925681,72.3860959 C74.8933275,71.167176 76.2055628,69.7345659 77.2869757,68.1344441 C78.3220229,66.5912102 79.16404,64.927843 79.7943341,63.1812698 C80.3386822,61.6428163 80.6375042,60.0288438 80.6799117,58.3981614 C80.8186166,52.8710141 81,41.6997989 81,41.6997989 L80.8612951,41.6891698 Z' id='crest-path-pixel-perfect-3'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")}}.sc-phn-level-0-h header.sc-phn-level-0 phn-crest.sc-phn-level-0{z-index:1;transform:translate3d(0, 0, 0)}@media (min-width: 1300px){.sc-phn-level-0-h header.sc-phn-level-0 phn-crest.sc-phn-level-0{z-index:1}}.sc-phn-level-0-h header.sc-phn-level-0 .burger-button.sc-phn-level-0:focus{outline-width:2px;outline-style:solid;outline-offset:1px;outline-color:#1a44ea}.sc-phn-level-0-h .main-content.sc-phn-level-0{inline-size:1.25rem}.sc-phn-level-0-h .main-content.sc-phn-level-0:focus{outline-style:none}.sc-phn-level-0-h .sr-only.sc-phn-level-0{position:absolute;inline-size:0.0625rem;block-size:0.0625rem;white-space:nowrap;overflow:hidden;clip:rect(1px, 1px, 1px, 1px);clip-path:inset(50%)}.sc-phn-level-0-h phn-p-icon.sc-phn-level-0{inline-size:1.5rem}@media (min-width: 760px){.sc-phn-level-0-h phn-p-icon.sc-phn-level-0{inline-size:1.5rem}}@media (min-width: 1000px){.sc-phn-level-0-h phn-p-icon.sc-phn-level-0{inline-size:1.5rem}}.sc-phn-level-0-h:not(:has(.contextual-icons)) phn-level-0-icons.sc-phn-level-0{place-content:center}",
            i
        }
        )()
          , an = ( () => {
            let i = class {
                constructor(e) {
                    (0,
                    y.r)(this, e),
                    this.loadingContent = !1,
                    this.receivedContent = !1,
                    this.theme = a.r.light,
                    this.mode = a.t.navbar,
                    this.app = a.A.default,
                    this.displayLogoOnly = !1,
                    this.limitedWidth = !1,
                    this.showCountryRecommender = T.redirected,
                    this.countryRecommenderClosed = !1
                }
                componentWillLoad() {
                    this.el.style.setProperty("--scrollbar-width", T.getScrollbarWidth()),
                    (0,
                    a.B)(this.el),
                    _e_initialize()
                }
                render() {
                    const {shop: e, countryRecommender: t, accessibilityStatement: n} = a.u.state;
                    return (0,
                    y.h)(y.H, {
                        key: "5907e8062e7ceb00ec79f7ced580c05b49023756"
                    }, this.showCountryRecommender && !this.countryRecommenderClosed && this.receivedContent ? (0,
                    y.h)("phn-country-recommender", {
                        locale: this.locale,
                        content: t,
                        role: "region",
                        "aria-label": "Change recommended Region or Language",
                        onBannerClosed: () => {
                            this.countryRecommenderClosed = !0
                        }
                    }) : null, "en-US" === this.locale && null !== n ? (0,
                    y.h)("a", {
                        "aria-label": "accessibility statement",
                        class: "screenreader-only",
                        href: n.link
                    }, n.text) : null, (0,
                    y.h)("phn-level-0", {
                        key: "1399bbb948fc91ba17536a4c5d87214f362dd948",
                        theme: this.theme,
                        mode: this.mode,
                        locale: this.locale,
                        loadingContent: this.loadingContent,
                        receivedContent: this.receivedContent,
                        breakpoint: this.breakpoint,
                        app: this.app,
                        displayLogoOnly: this.displayLogoOnly,
                        limitedWidth: this.limitedWidth
                    }, (0,
                    M.i)(e, this.locale, this.app) ? (0,
                    y.h)("div", {
                        slot: "contextual_drawer_header"
                    }, (0,
                    y.h)("slot", {
                        name: "contextual_drawer_header"
                    })) : null), (0,
                    y.h)("div", {
                        key: "a2d3096538e4d2a2afbf4839cdceb5582b575971",
                        id: "main",
                        class: "main-content",
                        onBlur: r => {
                            const s = (0,
                            a.l)(r.currentTarget);
                            null !== s && s.removeAttribute("tabIndex")
                        }
                    }))
                }
                get el() {
                    return (0,
                    y.g)(this)
                }
            }
            ;
            return i.style = "@keyframes translate-forward-in-animation{from{translate:0 12px}to{translate:0 0px}}@keyframes fade-in-animation{from{opacity:0;background-color:rgba(0, 0, 0, 0)}to{opacity:1;background-color:rgba(0, 0, 0, 0.6)}}@keyframes translate-in-from-left{from{translate:calc(var(--isLTR, 1) * -1000px)}to{translate:0px}}@keyframes slideDown{from{max-block-size:0}to{max-block-size:1000px}}@keyframes slideUp{from{max-block-size:1000px}to{max-block-size:0}}@keyframes slideLeft{from{max-inline-size:0}to{max-inline-size:1000px}}@keyframes slideRight{from{max-inline-size:1000px}to{max-inline-size:0}}@keyframes darken{from{background-color:rgba(0, 0, 0, 0)}to{background-color:rgba(0, 0, 0, 0.8)}}@keyframes lighten{from{background-color:rgba(0, 0, 0, 0.8)}to{background-color:rgba(0, 0, 0, 0)}}@keyframes fadeInFromBottom{0%{opacity:0;inset-block-start:36px}100%{opacity:1;inset-block-start:0px}}@keyframes fadeInFromTop{0%{opacity:0;inset-block-end:36px}100%{opacity:1;inset-block-end:0px}}@keyframes fadeOutToTop{0%{opacity:1;inset-block-end:0px}100%{opacity:0;inset-block-end:16px}}@keyframes slideLeftDoubleDrawer{from{inset-inline-end:-1000px}to{inset-inline-end:0px}}@keyframes slideRightDoubleDrawer{from{inset-inline-end:0px}to{inset-inline-end:-1000px}}@keyframes fadeIn{0%{opacity:0}100%{opacity:1}}@keyframes fadeOut{to{opacity:0}}.translate-forward-animation-in.sc-phn-wrapper{opacity:1}@media (prefers-reduced-motion: no-preference){.translate-forward-animation-in.sc-phn-wrapper{animation:translate-forward-in-animation 0.25s cubic-bezier(0.25, 0.1, 0.25, 1)}}@media (prefers-reduced-motion: no-preference){.translate-forward-animation-out.sc-phn-wrapper,.translate-backward-animation-out.sc-phn-wrapper{animation:fadeOut 0.25s cubic-bezier(0.25, 0.1, 0.25, 1)}}@media (prefers-reduced-motion: no-preference){.translate-backward-animation-in.sc-phn-wrapper{animation:fadeIn 0.25s cubic-bezier(0.25, 0.1, 0.25, 1), translate-forward-in-animation 0.25s cubic-bezier(0.25, 0.1, 0.25, 1)}}@media (prefers-reduced-motion: no-preference){.mobile-transition-forward.sc-phn-wrapper{animation:fadeIn 0.25s cubic-bezier(0.25, 0.1, 0.25, 1), translate-forward-in-animation 0.25s cubic-bezier(0.25, 0.1, 0.25, 1)}}.phn-flex-col.sc-phn-wrapper{display:flex;flex-direction:column}.phn-flex-row.sc-phn-wrapper{display:flex;flex-direction:row}.phn-align-center.sc-phn-wrapper{align-items:center}.phn-justify-around.sc-phn-wrapper{justify-content:space-around}.phn-justify-between.sc-phn-wrapper{justify-content:space-between}.phn-flex-wrap.sc-phn-wrapper{flex-wrap:wrap}.tooltip.sc-phn-wrapper{position:relative;display:inline-block}.tooltip-text.sc-phn-wrapper{visibility:hidden;inline-size:inherit;white-space:nowrap;background-color:#404044;border-radius:2px;font-size:13px;color:#fff;text-align:center;padding-block:0px;padding-inline:4px;position:absolute;z-index:100;inset-inline-start:50%;transform:translateX(calc(var(--isLTR, 1) * -50%))}.tooltip.sc-phn-wrapper:hover .tooltip-text.sc-phn-wrapper{visibility:visible}.tooltip-text.level0_icon.sc-phn-wrapper{inset-block-end:-1.7em}.tooltip-text.search_icon.sc-phn-wrapper{inset-block-end:-1.65em}.tooltip-text.shop_icon.sc-phn-wrapper{inset-block-end:-1.5em}.tooltip-text.finder_icon.sc-phn-wrapper{inset-block-end:-1.5em}.tooltip-text.logo.sc-phn-wrapper{inset-block-end:-1.5em}.tooltip-text.my_porsche_icon.sc-phn-wrapper{inset-block-end:-1.5em}.sc-phn-wrapper-h{display:block}.sc-phn-wrapper-h .limit-stretch.sc-phn-wrapper{box-sizing:content-box;margin-block:0;margin-inline:0.5vw}.sc-phn-wrapper-h .main-content.sc-phn-wrapper{inline-size:20px}.sc-phn-wrapper-h .main-content.sc-phn-wrapper:focus{outline-style:none}.sc-phn-wrapper-h .screenreader-only.sc-phn-wrapper{position:absolute;inline-size:1px;block-size:1px;white-space:nowrap;overflow:hidden;clip:rect(1px, 1px, 1px, 1px);clip-path:inset(50%)}",
            i
        }
        )()
    }
}]);
//# sourceMappingURL=node_modules_porsche-customer_navigate-header_dist_esm_phn-header_3_entry_js.2355dcd6909504c9.js.map
