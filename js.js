"use strict";(self.webpackChunk_wetransfer_wallpaper_ui=self.webpackChunk_wetransfer_wallpaper_ui||[]).push([[460],{3909:function(e,t,s){Object.defineProperty(t,"__esModule",{value:!0}),t.RESERVED_READY_COMMAND=void 0;const i=s(7109),n=s(6457);t.RESERVED_READY_COMMAND="ready",t.default=class{constructor({childFrameNode:e,listeners:s,methods:a={},scripts:r}){if(this.events=[],this.eventEmitter=new i.default,!e.src)throw new Error(n.default.EMPTY_IFRAME);this.child=e,this.origin=window.origin,this.creativeUrl=new URL(this.child.src);const o=new URLSearchParams(this.child.src);if(this.placement=o.get("_placement")||"",!this.placement||""===this.placement)throw new Error(n.default.CANT_VALIDATE_PLACEMENT);this.scripts=r,this.listeners=s||null,this.methods=Object.keys(a),window.addEventListener("message",this.receiveEvent.bind(this)),this.methods&&this.methods.forEach((e=>{if(e===t.RESERVED_READY_COMMAND)return void console.error(n.default.CANT_USE_READY_COMMAND);const s=this.eventEmitter.on(e,a[e]);this.events.push(s)})),this.send(t.RESERVED_READY_COMMAND,void 0)}receiveEvent(e){if(this.creativeUrl.origin===e.origin)try{const{command:t,payload:s,placement:i}=this.parseMessage(e);if(this.placement!==i)return;this.eventEmitter.emit(t,s)}catch(e){console.error(e)}}parseMessage(e){return{command:e.data.command,payload:e.data.payload,placement:e.data.placement}}buildEventPayload(e,s){const i={};return e===t.RESERVED_READY_COMMAND&&(i.availableListeners=this.listeners,i.availableMethods=this.methods,i.scripts=this.scripts),Object.assign(Object.assign({},i),{command:e,payload:s,placement:this.placement})}send(e,s){if(this.listeners&&!this.listeners.includes(e)&&e!==t.RESERVED_READY_COMMAND)throw new Error(n.default.NOT_DEFINED_EVENT_NAME);if(!this.child.contentWindow)return;const{origin:i}=this.creativeUrl;if(i)try{const t=this.buildEventPayload(e,s);this.child.contentWindow.postMessage(t,i)}catch(e){console.error(e)}}destroy(){window.removeEventListener("message",this.receiveEvent.bind(this)),this.events.forEach((e=>{e.off()}))}}},4645:function(e,t,s){Object.defineProperty(t,"__esModule",{value:!0});const i=s(3909),n=s(7109),a=s(9582),r=s(6457);t.default=class{constructor(e){this.eventEmitter=new n.default;const t=new URLSearchParams(window.location.search);if(this.endpoint=t.get("_origin"),!this.endpoint)throw new Error(r.default.CANT_VALIDATE_ORIGIN);if(this.parentPlacement=t.get("_placement"),!this.parentPlacement)throw new Error(r.default.CANT_VALIDATE_PLACEMENT);this.callback=e,this.eventEmitter.on(i.RESERVED_READY_COMMAND,this.onParentReady.bind(this)),this.listeners={},this.run={},window.addEventListener("message",this.receiveEvent.bind(this))}receiveEvent(e){if(e.origin===this.endpoint)try{const{command:t,payload:s,parentPlacement:n}=this.parseMessage(e);if(n!==this.parentPlacement)return;t===i.RESERVED_READY_COMMAND?this.onParentReady(e.data):this.eventEmitter.emit(t,s)}catch(e){console.error(e)}}parseMessage(e){return{command:e.data.command,payload:e.data.payload,parentPlacement:e.data.placement}}onParentReady(e){const{availableListeners:t,availableMethods:s,scripts:i}=e;t&&t.forEach((e=>{this.listeners[e]=t=>{this.eventEmitter.on(e,t)}})),s&&s.forEach((e=>{this.run[e]=t=>{this.sendCommand(e,t)}})),i&&(0,a.loadScriptTags)(i),this.callback(e)}sendCommand(e,t){const s={command:e,payload:t,placement:this.parentPlacement};window.parent.postMessage(s,this.endpoint)}}},6457:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default={CANT_VALIDATE_ORIGIN:"Can't validate origin! please add ?_origin=PARENT_HOST to the iframe source",CANT_VALIDATE_PLACEMENT:"Can't validate placement! please add ?_placement=PLACEMENT_NAME to the iframe source",CANT_USE_READY_COMMAND:"ready is a reserved command",EMPTY_IFRAME:"Not src found. You can't run ParentFrame on an empty iframe element",NOT_DEFINED_EVENT_NAME:"Can't send a not defined event name. Make sure you add your event name first."}},6460:function(e,t,s){t.ChildFrame=void 0,s(3909).default;const i=s(4645);t.ChildFrame=i.default},7109:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=class{constructor(){this.subscribers={}}on(e,t){this.subscribers[e]||(this.subscribers[e]=[]);const s=this.subscribers[e].push(t)-1;return{off:()=>{this.subscribers[e].splice(s,1)}}}emit(e,t){this.subscribers[e]&&this.subscribers[e].forEach((e=>e(t)))}}},9582:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.loadScriptTags=void 0,t.loadScriptTags=e=>{if(!e||!e.length)return;const t=document.querySelector("head");if(t)for(let s of e){let e=document.createElement("div");e.innerHTML=s;const i=e.querySelector("script");if(!i||!i.attributes)continue;let n=document.createElement("script");for(let e=i.attributes.length;e--;)n.setAttribute(i.attributes[e].name,i.attributes[e].value);n.innerHTML=i.innerHTML,t.appendChild(n)}}}}]);