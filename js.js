HTTP/2 200 OK
Content-Type: text/javascript
X-Amz-Id-2: jhqhzpLHYaDh+ftwXyjOp1Z794z5DSU1fvhX8KAIp5Zlqz4GDWE90oOXHku0RWLNT43p2lT5x6E=
X-Amz-Request-Id: F5S2QM7T212R8064
Date: Mon, 15 Sep 2025 15:17:39 GMT
Access-Control-Allow-Origin: https://profile.porsche.com
Access-Control-Allow-Methods: POST, GET
Access-Control-Expose-Headers: ETag
Access-Control-Max-Age: 3000
Access-Control-Allow-Credentials: true
Last-Modified: Mon, 15 Sep 2025 14:22:41 GMT
Etag: W/"06f99cfcd37333d61ea0743ac70f6f5b"
Cache-Control: max-age=1209600
X-Amz-Version-Id: null
Server: AmazonS3
Vary: Accept-Encoding,Origin,Access-Control-Request-Headers,Access-Control-Request-Method
X-Cache: Hit from cloudfront
Via: 1.1 a5a8e743f28968822c126102a78bb7c6.cloudfront.net (CloudFront)
X-Amz-Cf-Pop: FRA60-P4
X-Amz-Cf-Id: O7TgP2EBIMddh8abagkdWjIW5N6530oYf_J0axb_PRqlxH2oZXANIQ==
Age: 189341
X-Xss-Protection: 1; mode=block
X-Frame-Options: DENY
Referrer-Policy: no-referrer-when-downgrade
Content-Security-Policy: default-src 'self' https://*.porsche.com;style-src 'self' 'unsafe-inline' https://*.porsche.com;script-src 'self' 'unsafe-inline' 'unsafe-eval' https://*.porsche-preview.com https://*.newrelic.com https://*.porsche.com https://*.googletagmanager.com https://*.fullstory.com https://cdn.matomo.cloud/porsche.matomo.cloud/matomo.js;img-src 'self' data: https://*.doubleclick.net https://*.google.de https://*.porsche.com https://*.porsche-preview.com https://*.usercentrics.eu;media-src 'self' https://porsche-design-system.github.io;connect-src 'self' https://*.adyen.com https://*.matomo.cloud https://*.google.com https://*.google-analytics.com https://*.doubleclick.net https://*.porsche.com https://*.porsche-preview.com https://*.porsche.services https://*.usercentrics.eu https://*.nr-data.net https://*.fullstory.com https://*.porsche.cloud https://*.porsche-preview.cloud;frame-src 'self' https://*.doubleclick.net https://*.porsche-preview.com https://*.porsche.com https://*.adyen.com;
X-Content-Type-Options: nosniff
Strict-Transport-Security: max-age=31536000; includeSubDomains
Permissions-Policy: accelerometer=(), ambient-light-sensor=(), autoplay=(), battery=(), camera=(), cross-origin-isolated=(), display-capture=(), document-domain=(), encrypted-media=(), execution-while-not-rendered=(), execution-while-out-of-viewport=(), fullscreen=(), geolocation=(), gyroscope=(), keyboard-map=(), magnetometer=(), microphone=(), midi=(), navigation-override=(), payment=(), picture-in-picture=(), publickey-credentials-get=(), screen-wake-lock=(), sync-xhr=(self "https://www.porsche.com"), usb=(), web-share=(), xr-spatial-tracking=()

import{b as Et}from"./chunk-CEBXGZTU.js";import{a as ee,b as kt}from"./chunk-Y3MN2MIM.js";import{a as yt,b as Ct}from"./chunk-F3ORSU5J.js";import{a as mt,b as ft,c as Z,d as de,e as gt,g as wt,h as vt}from"./chunk-OF4EPFQW.js";import{a as he,b as F}from"./chunk-5OTY5WF4.js";import{a as Le,b as Re,e as bt}from"./chunk-MRSWGA6B.js";import{A as _e,B as Ie,C as j,D as Te,E as Oe,F as ut,H as Se,J as N,K as T,N as z,O as D,a as it,b as $,c as ye,e as B,f as se,g as ot,h as Ce,i as ke,j as rt,k as at,l as st,m as ct,n as lt,o as U,p as Ee,q as ce,r as xe,s as dt,t as v,u as le,v as ht,w as pt,x as q,y as M,z as Q}from"./chunk-RP63QLMQ.js";import{a as pe,b as xt,c as y,d as Ne,e as ze,f as De,j as _t}from"./chunk-VAYCZXYT.js";import{a as P,h as p}from"./chunk-FXBCZJCY.js";var Xt={featureOverrideEnabled:{enabled:!0},navi_drawer_cdn:{enabled:!0},electricownership:{enabled:!0}},Yt={featureOverrideEnabled:{enabled:!0},navi_drawer_cdn:{enabled:!0},electricownership:{enabled:!0}},Jt={featureOverrideEnabled:{enabled:!0},navi_drawer_cdn:{enabled:!0},electricownership:{enabled:!0}},$t={featureOverrideEnabled:{enabled:!0},navi_drawer_cdn:{enabled:!0},navi_3:{enabled:!0},pcom_search:{enabled:!0},electricownership:{enabled:!0}},Bt={featureOverrideEnabled:{enabled:!1},navi_3:{enabled:!0},content_V4:{enabled:!0},dealer_search_china:{enabled:!0},mock_shop_content:{enabled:!1},shop:{enabled:!1},pcom_search:{enabled:!1},shop_wishlist:{enabled:!1},navi_drawer_cdn:{enabled:!1},mpi_integration:{enabled:!1}},qt={dev:Xt,local:Yt,preview:Jt,test:$t,production:Bt},Qt="ab",en="test_toggle",tn="newrelic",nn="electricownership",on="dsm-poc-singapore",oe=Ee({features:{}}),We=class{constructor(e=$(ot),t=qt){this.featureEnvMap=t,this.features={},this.env="local",this.isABTestingToggleActive=()=>this.isFeatureEnabled(Qt,window.location?.search??""),this.isNewRelicEnabled=()=>this.isFeatureEnabled(tn,window.location?.search??""),this.isTestToggleEnabled=()=>this.isFeatureEnabled(en,window.location?.search??""),this.isElectricOwnershipEnabled=()=>this.isFeatureEnabled(nn,window.location?.search??""),this.hasPorscheAtJewelsPOC=()=>this.isFeatureEnabled(on,window.location?.search??""),this.env=e,this.features=this.featureEnvMap[e],oe.state.features=this.featureEnvMap[e]}loadRemoteToggles(){return p(this,null,function*(){let e=j(this.env),{locale:t}=v.state,i=e.BFF_ENDPOINT;try{(t==="en-CN"||t==="zh-CN")&&(i=e.BFF_CN_ENDPOINT);let o=yield fetch(`${i}/toggles?env=${this.env}&fromAppConfig=true`);if(!o.ok){let a=yield o.text();throw new Error(a)}let r=yield o.json();this.features=r}catch(o){throw new N(`Load Feature Toggles failed with error: ${o} - ${i}/toggles?env=${this.env}&fromAppConfig=true`,T.GENERAL)}})}stringToBooleanStrict(e){return e==="true"?!0:e==="false"?!1:null}getUrlFeatureToggleOverride(e,t){let i=t.split("?")[1]?.split("&").find(o=>o.includes(e))?.split("=")[1];return M(i)?this.stringToBooleanStrict(i||""):null}isFeatureEnabled(e,t){if(this.features=oe.state.features,this.features===void 0||this.env===it.PRODUCTION&&this.isFeatureNotReadyForProd(e))return!1;let i=[this.getUrlFeatureToggleOverride(e,t)];for(let o of i)if(o!==null)return o;return this.features[e]!==void 0?this.features[e].enabled:!1}setEnvironment(e){this.env=$(e),this.features=this.featureEnvMap[e]}setFeatures(e){return p(this,null,function*(){this.env=$(e);try{if(e==="test"){this.features=this.featureEnvMap[e];return}yield this.loadRemoteToggles()}catch(t){Z.error("INIT_APP_CONFIG",t),this.features=this.featureEnvMap[e],oe.state.features=this.featureEnvMap[e]}})}isFeatureNotReadyForProd(e){return[].includes(e)}},Ae=new We;function rn(n,e){return p(this,null,function*(){let t=j(v.state.env),i={method:"GET",headers:{"X-Trace-Id":crypto.randomUUID(),Authorization:`${n}`,Accept:"application/json",Env:v.state.env}},o;try{o=yield fetch(Ie(`${t?.BFF_ENDPOINT}/user-information?locale=${e}`),i)}catch(r){let a=`Get User information request failed in Frontend with error: ${JSON.stringify(r.message)}`;throw new N(a,T.GENERAL)}if(o.status===yt.unauthorized)throw new N("AccessToken has expired",T.UNAUTHORIZED);if(!o.ok){let r=`Get User information request failed in Frontend with error: ${o.status} ${o.statusText}`;throw new N(r,T.GENERAL)}return o.json()})}function L(n,e){var t={};for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&e.indexOf(i)<0&&(t[i]=n[i]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function"){var o=0;for(i=Object.getOwnPropertySymbols(n);o<i.length;o++)e.indexOf(i[o])<0&&Object.prototype.propertyIsEnumerable.call(n,i[o])&&(t[i[o]]=n[i[o]])}return t}var W=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function et(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}function tt(n,e){return n(e={exports:{}},e.exports),e.exports}var G=tt(function(n,e){Object.defineProperty(e,"__esModule",{value:!0});var t=(function(){function i(){var o=this;this.locked=new Map,this.addToLocked=function(r,a){var l=o.locked.get(r);l===void 0?a===void 0?o.locked.set(r,[]):o.locked.set(r,[a]):a!==void 0&&(l.unshift(a),o.locked.set(r,l))},this.isLocked=function(r){return o.locked.has(r)},this.lock=function(r){return new Promise(function(a,l){o.isLocked(r)?o.addToLocked(r,a):(o.addToLocked(r),a())})},this.unlock=function(r){var a=o.locked.get(r);if(a!==void 0&&a.length!==0){var l=a.pop();o.locked.set(r,a),l!==void 0&&setTimeout(l,0)}else o.locked.delete(r)}}return i.getInstance=function(){return i.instance===void 0&&(i.instance=new i),i.instance},i})();e.default=function(){return t.getInstance()}});et(G);var an=et(tt(function(n,e){var t=W&&W.__awaiter||function(s,c,d,h){return new(d||(d=Promise))(function(m,b){function w(k){try{E(h.next(k))}catch(f){b(f)}}function C(k){try{E(h.throw(k))}catch(f){b(f)}}function E(k){k.done?m(k.value):new d(function(f){f(k.value)}).then(w,C)}E((h=h.apply(s,c||[])).next())})},i=W&&W.__generator||function(s,c){var d,h,m,b,w={label:0,sent:function(){if(1&m[0])throw m[1];return m[1]},trys:[],ops:[]};return b={next:C(0),throw:C(1),return:C(2)},typeof Symbol=="function"&&(b[Symbol.iterator]=function(){return this}),b;function C(E){return function(k){return(function(f){if(d)throw new TypeError("Generator is already executing.");for(;w;)try{if(d=1,h&&(m=2&f[0]?h.return:f[0]?h.throw||((m=h.return)&&m.call(h),0):h.next)&&!(m=m.call(h,f[1])).done)return m;switch(h=0,m&&(f=[2&f[0],m.value]),f[0]){case 0:case 1:m=f;break;case 4:return w.label++,{value:f[1],done:!1};case 5:w.label++,h=f[1],f=[0];continue;case 7:f=w.ops.pop(),w.trys.pop();continue;default:if(m=w.trys,!((m=m.length>0&&m[m.length-1])||f[0]!==6&&f[0]!==2)){w=0;continue}if(f[0]===3&&(!m||f[1]>m[0]&&f[1]<m[3])){w.label=f[1];break}if(f[0]===6&&w.label<m[1]){w.label=m[1],m=f;break}if(m&&w.label<m[2]){w.label=m[2],w.ops.push(f);break}m[2]&&w.ops.pop(),w.trys.pop();continue}f=c.call(s,w)}catch(I){f=[6,I],h=0}finally{d=m=0}if(5&f[0])throw f[1];return{value:f[0]?f[1]:void 0,done:!0}})([E,k])}}},o=W;Object.defineProperty(e,"__esModule",{value:!0});var r="browser-tabs-lock-key",a={key:function(s){return t(o,void 0,void 0,function(){return i(this,function(c){throw new Error("Unsupported")})})},getItem:function(s){return t(o,void 0,void 0,function(){return i(this,function(c){throw new Error("Unsupported")})})},clear:function(){return t(o,void 0,void 0,function(){return i(this,function(s){return[2,window.localStorage.clear()]})})},removeItem:function(s){return t(o,void 0,void 0,function(){return i(this,function(c){throw new Error("Unsupported")})})},setItem:function(s,c){return t(o,void 0,void 0,function(){return i(this,function(d){throw new Error("Unsupported")})})},keySync:function(s){return window.localStorage.key(s)},getItemSync:function(s){return window.localStorage.getItem(s)},clearSync:function(){return window.localStorage.clear()},removeItemSync:function(s){return window.localStorage.removeItem(s)},setItemSync:function(s,c){return window.localStorage.setItem(s,c)}};function l(s){return new Promise(function(c){return setTimeout(c,s)})}function u(s){for(var c="0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz",d="",h=0;h<s;h++)d+=c[Math.floor(Math.random()*c.length)];return d}var g=(function(){function s(c){this.acquiredIatSet=new Set,this.storageHandler=void 0,this.id=Date.now().toString()+u(15),this.acquireLock=this.acquireLock.bind(this),this.releaseLock=this.releaseLock.bind(this),this.releaseLock__private__=this.releaseLock__private__.bind(this),this.waitForSomethingToChange=this.waitForSomethingToChange.bind(this),this.refreshLockWhileAcquired=this.refreshLockWhileAcquired.bind(this),this.storageHandler=c,s.waiters===void 0&&(s.waiters=[])}return s.prototype.acquireLock=function(c,d){return d===void 0&&(d=5e3),t(this,void 0,void 0,function(){var h,m,b,w,C,E,k;return i(this,function(f){switch(f.label){case 0:h=Date.now()+u(4),m=Date.now()+d,b=r+"-"+c,w=this.storageHandler===void 0?a:this.storageHandler,f.label=1;case 1:return Date.now()<m?[4,l(30)]:[3,8];case 2:return f.sent(),w.getItemSync(b)!==null?[3,5]:(C=this.id+"-"+c+"-"+h,[4,l(Math.floor(25*Math.random()))]);case 3:return f.sent(),w.setItemSync(b,JSON.stringify({id:this.id,iat:h,timeoutKey:C,timeAcquired:Date.now(),timeRefreshed:Date.now()})),[4,l(30)];case 4:return f.sent(),(E=w.getItemSync(b))!==null&&(k=JSON.parse(E)).id===this.id&&k.iat===h?(this.acquiredIatSet.add(h),this.refreshLockWhileAcquired(b,h),[2,!0]):[3,7];case 5:return s.lockCorrector(this.storageHandler===void 0?a:this.storageHandler),[4,this.waitForSomethingToChange(m)];case 6:f.sent(),f.label=7;case 7:return h=Date.now()+u(4),[3,1];case 8:return[2,!1]}})})},s.prototype.refreshLockWhileAcquired=function(c,d){return t(this,void 0,void 0,function(){var h=this;return i(this,function(m){return setTimeout(function(){return t(h,void 0,void 0,function(){var b,w,C;return i(this,function(E){switch(E.label){case 0:return[4,G.default().lock(d)];case 1:return E.sent(),this.acquiredIatSet.has(d)?(b=this.storageHandler===void 0?a:this.storageHandler,(w=b.getItemSync(c))===null?(G.default().unlock(d),[2]):((C=JSON.parse(w)).timeRefreshed=Date.now(),b.setItemSync(c,JSON.stringify(C)),G.default().unlock(d),this.refreshLockWhileAcquired(c,d),[2])):(G.default().unlock(d),[2])}})})},1e3),[2]})})},s.prototype.waitForSomethingToChange=function(c){return t(this,void 0,void 0,function(){return i(this,function(d){switch(d.label){case 0:return[4,new Promise(function(h){var m=!1,b=Date.now(),w=!1;function C(){if(w||(window.removeEventListener("storage",C),s.removeFromWaiting(C),clearTimeout(E),w=!0),!m){m=!0;var k=50-(Date.now()-b);k>0?setTimeout(h,k):h(null)}}window.addEventListener("storage",C),s.addToWaiting(C);var E=setTimeout(C,Math.max(0,c-Date.now()))})];case 1:return d.sent(),[2]}})})},s.addToWaiting=function(c){this.removeFromWaiting(c),s.waiters!==void 0&&s.waiters.push(c)},s.removeFromWaiting=function(c){s.waiters!==void 0&&(s.waiters=s.waiters.filter(function(d){return d!==c}))},s.notifyWaiters=function(){s.waiters!==void 0&&s.waiters.slice().forEach(function(c){return c()})},s.prototype.releaseLock=function(c){return t(this,void 0,void 0,function(){return i(this,function(d){switch(d.label){case 0:return[4,this.releaseLock__private__(c)];case 1:return[2,d.sent()]}})})},s.prototype.releaseLock__private__=function(c){return t(this,void 0,void 0,function(){var d,h,m,b;return i(this,function(w){switch(w.label){case 0:return d=this.storageHandler===void 0?a:this.storageHandler,h=r+"-"+c,(m=d.getItemSync(h))===null?[2]:(b=JSON.parse(m)).id!==this.id?[3,2]:[4,G.default().lock(b.iat)];case 1:w.sent(),this.acquiredIatSet.delete(b.iat),d.removeItemSync(h),G.default().unlock(b.iat),s.notifyWaiters(),w.label=2;case 2:return[2]}})})},s.lockCorrector=function(c){for(var d=Date.now()-5e3,h=c,m=[],b=0;;){var w=h.keySync(b);if(w===null)break;m.push(w),b++}for(var C=!1,E=0;E<m.length;E++){var k=m[E];if(k.includes(r)){var f=h.getItemSync(k);if(f!==null){var I=JSON.parse(f);(I.timeRefreshed===void 0&&I.timeAcquired<d||I.timeRefreshed!==void 0&&I.timeRefreshed<d)&&(h.removeItemSync(k),C=!0)}}}C&&s.notifyWaiters()},s.waiters=void 0,s})();e.default=g})),sn={timeoutInSeconds:60},jt={name:"auth0-spa-js",version:"2.3.0"},Ft=()=>Date.now(),x=class n extends Error{constructor(e,t){super(t),this.error=e,this.error_description=t,Object.setPrototypeOf(this,n.prototype)}static fromPayload({error:e,error_description:t}){return new n(e,t)}},Ke=class n extends x{constructor(e,t,i,o=null){super(e,t),this.state=i,this.appState=o,Object.setPrototypeOf(this,n.prototype)}},re=class n extends x{constructor(){super("timeout","Timeout"),Object.setPrototypeOf(this,n.prototype)}},Me=class n extends re{constructor(e){super(),this.popup=e,Object.setPrototypeOf(this,n.prototype)}},Ze=class n extends x{constructor(e){super("cancelled","Popup closed"),this.popup=e,Object.setPrototypeOf(this,n.prototype)}},Ve=class n extends x{constructor(e,t,i){super(e,t),this.mfa_token=i,Object.setPrototypeOf(this,n.prototype)}},fe=class n extends x{constructor(e,t){super("missing_refresh_token",`Missing Refresh Token (audience: '${It(e,["default"])}', scope: '${It(t)}')`),this.audience=e,this.scope=t,Object.setPrototypeOf(this,n.prototype)}};function It(n,e=[]){return n&&!e.includes(n)?n:""}var me=()=>window.crypto,Pe=()=>{let n="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_~.",e="";return Array.from(me().getRandomValues(new Uint8Array(43))).forEach(t=>e+=n[t%n.length]),e},Tt=n=>btoa(n),He=n=>{var{clientId:e}=n,t=L(n,["clientId"]);return new URLSearchParams((i=>Object.keys(i).filter(o=>i[o]!==void 0).reduce((o,r)=>Object.assign(Object.assign({},o),{[r]:i[r]}),{}))(Object.assign({client_id:e},t))).toString()},Ot=n=>(e=>decodeURIComponent(atob(e).split("").map(t=>"%"+("00"+t.charCodeAt(0).toString(16)).slice(-2)).join("")))(n.replace(/_/g,"/").replace(/-/g,"+")),cn=(n,e)=>p(null,null,function*(){let t=yield fetch(n,e);return{ok:t.ok,json:yield t.json()}}),ln=(n,e,t)=>p(null,null,function*(){let i=new AbortController,o;return e.signal=i.signal,Promise.race([cn(n,e),new Promise((r,a)=>{o=setTimeout(()=>{i.abort(),a(new Error("Timeout when executing 'fetch'"))},t)})]).finally(()=>{clearTimeout(o)})}),dn=(n,e,t,i,o,r,a)=>p(null,null,function*(){return l={auth:{audience:e,scope:t},timeout:o,fetchUrl:n,fetchOptions:i,useFormData:a},u=r,new Promise(function(g,s){let c=new MessageChannel;c.port1.onmessage=function(d){d.data.error?s(new Error(d.data.error)):g(d.data),c.port1.close()},u.postMessage(l,[c.port2])});var l,u}),hn=(n,e,t,i,o,r,a=1e4)=>p(null,null,function*(){return o?dn(n,e,t,i,a,o,r):ln(n,i,a)});function pn(n,e){return p(this,null,function*(){var{baseUrl:t,timeout:i,audience:o,scope:r,auth0Client:a,useFormData:l}=n,u=L(n,["baseUrl","timeout","audience","scope","auth0Client","useFormData"]);let g=u.grant_type==="urn:ietf:params:oauth:grant-type:token-exchange",s=Object.assign(Object.assign(Object.assign({},u),g&&o&&{audience:o}),g&&r&&{scope:r}),c=l?He(s):JSON.stringify(s);return yield(function(d,h,m,b,w,C,E){return p(this,null,function*(){let k,f=null;for(let J=0;J<3;J++)try{k=yield hn(d,m,b,w,C,E,h),f=null;break}catch(Ht){f=Ht}if(f)throw f;let I=k.json,{error:R,error_description:be}=I,S=L(I,["error","error_description"]),{ok:ae}=k;if(!ae){let J=be||`HTTP error. Unable to fetch ${d}`;throw R==="mfa_required"?new Ve(R,J,S.mfa_token):R==="missing_refresh_token"?new fe(m,b):new x(R||"request_error",J)}return S})})(`${t}/oauth/token`,i,o||"default",r,{method:"POST",body:c,headers:{"Content-Type":l?"application/x-www-form-urlencoded":"application/json","Auth0-Client":btoa(JSON.stringify(a||jt))}},e,l)})}var te=(...n)=>{return(e=n.filter(Boolean).join(" ").trim().split(/\s+/),Array.from(new Set(e))).join(" ");var e},A=class n{constructor(e,t="@@auth0spajs@@",i){this.prefix=t,this.suffix=i,this.clientId=e.clientId,this.scope=e.scope,this.audience=e.audience}toKey(){return[this.prefix,this.clientId,this.audience,this.scope,this.suffix].filter(Boolean).join("::")}static fromKey(e){let[t,i,o,r]=e.split("::");return new n({clientId:i,scope:r,audience:o},t)}static fromCacheEntry(e){let{scope:t,audience:i,client_id:o}=e;return new n({scope:t,audience:i,clientId:o})}},Xe=class{set(e,t){localStorage.setItem(e,JSON.stringify(t))}get(e){let t=window.localStorage.getItem(e);if(t)try{return JSON.parse(t)}catch{return}}remove(e){localStorage.removeItem(e)}allKeys(){return Object.keys(window.localStorage).filter(e=>e.startsWith("@@auth0spajs@@"))}},ge=class{constructor(){this.enclosedCache=(function(){let e={};return{set(t,i){e[t]=i},get(t){let i=e[t];if(i)return i},remove(t){delete e[t]},allKeys:()=>Object.keys(e)}})()}},Ye=class{constructor(e,t,i){this.cache=e,this.keyManifest=t,this.nowProvider=i||Ft}setIdToken(e,t,i){return p(this,null,function*(){var o;let r=this.getIdTokenCacheKey(e);yield this.cache.set(r,{id_token:t,decodedToken:i}),yield(o=this.keyManifest)===null||o===void 0?void 0:o.add(r)})}getIdToken(e){return p(this,null,function*(){let t=yield this.cache.get(this.getIdTokenCacheKey(e.clientId));if(!t&&e.scope&&e.audience){let i=yield this.get(e);return!i||!i.id_token||!i.decodedToken?void 0:{id_token:i.id_token,decodedToken:i.decodedToken}}if(t)return{id_token:t.id_token,decodedToken:t.decodedToken}})}get(e,t=0){return p(this,null,function*(){var i;let o=yield this.cache.get(e.toKey());if(!o){let l=yield this.getCacheKeys();if(!l)return;let u=this.matchExistingCacheKey(e,l);u&&(o=yield this.cache.get(u))}if(!o)return;let r=yield this.nowProvider(),a=Math.floor(r/1e3);return o.expiresAt-t<a?o.body.refresh_token?(o.body={refresh_token:o.body.refresh_token},yield this.cache.set(e.toKey(),o),o.body):(yield this.cache.remove(e.toKey()),void(yield(i=this.keyManifest)===null||i===void 0?void 0:i.remove(e.toKey()))):o.body})}set(e){return p(this,null,function*(){var t;let i=new A({clientId:e.client_id,scope:e.scope,audience:e.audience}),o=yield this.wrapCacheEntry(e);yield this.cache.set(i.toKey(),o),yield(t=this.keyManifest)===null||t===void 0?void 0:t.add(i.toKey())})}clear(e){return p(this,null,function*(){var t;let i=yield this.getCacheKeys();i&&(yield i.filter(o=>!e||o.includes(e)).reduce((o,r)=>p(this,null,function*(){yield o,yield this.cache.remove(r)}),Promise.resolve()),yield(t=this.keyManifest)===null||t===void 0?void 0:t.clear())})}wrapCacheEntry(e){return p(this,null,function*(){let t=yield this.nowProvider();return{body:e,expiresAt:Math.floor(t/1e3)+e.expires_in}})}getCacheKeys(){return p(this,null,function*(){var e;return this.keyManifest?(e=yield this.keyManifest.get())===null||e===void 0?void 0:e.keys:this.cache.allKeys?this.cache.allKeys():void 0})}getIdTokenCacheKey(e){return new A({clientId:e},"@@auth0spajs@@","@@user@@").toKey()}matchExistingCacheKey(e,t){return t.filter(i=>{var o;let r=A.fromKey(i),a=new Set(r.scope&&r.scope.split(" ")),l=((o=e.scope)===null||o===void 0?void 0:o.split(" "))||[],u=r.scope&&l.reduce((g,s)=>g&&a.has(s),!0);return r.prefix==="@@auth0spajs@@"&&r.clientId===e.clientId&&r.audience===e.audience&&u})[0]}},Je=class{constructor(e,t,i){this.storage=e,this.clientId=t,this.cookieDomain=i,this.storageKey=`a0.spajs.txs.${this.clientId}`}create(e){this.storage.save(this.storageKey,e,{daysUntilExpire:1,cookieDomain:this.cookieDomain})}get(){return this.storage.get(this.storageKey)}remove(){this.storage.remove(this.storageKey,{cookieDomain:this.cookieDomain})}},ne=n=>typeof n=="number",un=["iss","aud","exp","nbf","iat","jti","azp","nonce","auth_time","at_hash","c_hash","acr","amr","sub_jwk","cnf","sip_from_tag","sip_date","sip_callid","sip_cseq_num","sip_via_branch","orig","dest","mky","events","toe","txn","rph","sid","vot","vtm"],mn=n=>{if(!n.id_token)throw new Error("ID token is required but missing");let e=(r=>{let a=r.split("."),[l,u,g]=a;if(a.length!==3||!l||!u||!g)throw new Error("ID token could not be decoded");let s=JSON.parse(Ot(u)),c={__raw:r},d={};return Object.keys(s).forEach(h=>{c[h]=s[h],un.includes(h)||(d[h]=s[h])}),{encoded:{header:l,payload:u,signature:g},header:JSON.parse(Ot(l)),claims:c,user:d}})(n.id_token);if(!e.claims.iss)throw new Error("Issuer (iss) claim must be a string present in the ID token");if(e.claims.iss!==n.iss)throw new Error(`Issuer (iss) claim mismatch in the ID token; expected "${n.iss}", found "${e.claims.iss}"`);if(!e.user.sub)throw new Error("Subject (sub) claim must be a string present in the ID token");if(e.header.alg!=="RS256")throw new Error(`Signature algorithm of "${e.header.alg}" is not supported. Expected the ID token to be signed with "RS256".`);if(!e.claims.aud||typeof e.claims.aud!="string"&&!Array.isArray(e.claims.aud))throw new Error("Audience (aud) claim must be a string or array of strings present in the ID token");if(Array.isArray(e.claims.aud)){if(!e.claims.aud.includes(n.aud))throw new Error(`Audience (aud) claim mismatch in the ID token; expected "${n.aud}" but was not one of "${e.claims.aud.join(", ")}"`);if(e.claims.aud.length>1){if(!e.claims.azp)throw new Error("Authorized Party (azp) claim must be a string present in the ID token when Audience (aud) claim has multiple values");if(e.claims.azp!==n.aud)throw new Error(`Authorized Party (azp) claim mismatch in the ID token; expected "${n.aud}", found "${e.claims.azp}"`)}}else if(e.claims.aud!==n.aud)throw new Error(`Audience (aud) claim mismatch in the ID token; expected "${n.aud}" but found "${e.claims.aud}"`);if(n.nonce){if(!e.claims.nonce)throw new Error("Nonce (nonce) claim must be a string present in the ID token");if(e.claims.nonce!==n.nonce)throw new Error(`Nonce (nonce) claim mismatch in the ID token; expected "${n.nonce}", found "${e.claims.nonce}"`)}if(n.max_age&&!ne(e.claims.auth_time))throw new Error("Authentication Time (auth_time) claim must be a number present in the ID token when Max Age (max_age) is specified");if(e.claims.exp==null||!ne(e.claims.exp))throw new Error("Expiration Time (exp) claim must be a number present in the ID token");if(!ne(e.claims.iat))throw new Error("Issued At (iat) claim must be a number present in the ID token");let t=n.leeway||60,i=new Date(n.now||Date.now()),o=new Date(0);if(o.setUTCSeconds(e.claims.exp+t),i>o)throw new Error(`Expiration Time (exp) claim error in the ID token; current time (${i}) is after expiration time (${o})`);if(e.claims.nbf!=null&&ne(e.claims.nbf)){let r=new Date(0);if(r.setUTCSeconds(e.claims.nbf-t),i<r)throw new Error(`Not Before time (nbf) claim in the ID token indicates that this token can't be used just yet. Current time (${i}) is before ${r}`)}if(e.claims.auth_time!=null&&ne(e.claims.auth_time)){let r=new Date(0);if(r.setUTCSeconds(parseInt(e.claims.auth_time)+n.max_age+t),i>r)throw new Error(`Authentication Time (auth_time) claim in the ID token indicates that too much time has passed since the last end-user authentication. Current time (${i}) is after last auth at ${r}`)}if(n.organization){let r=n.organization.trim();if(r.startsWith("org_")){let a=r;if(!e.claims.org_id)throw new Error("Organization ID (org_id) claim must be a string present in the ID token");if(a!==e.claims.org_id)throw new Error(`Organization ID (org_id) claim mismatch in the ID token; expected "${a}", found "${e.claims.org_id}"`)}else{let a=r.toLowerCase();if(!e.claims.org_name)throw new Error("Organization Name (org_name) claim must be a string present in the ID token");if(a!==e.claims.org_name)throw new Error(`Organization Name (org_name) claim mismatch in the ID token; expected "${a}", found "${e.claims.org_name}"`)}}return e},ve=tt(function(n,e){var t=W&&W.__assign||function(){return t=Object.assign||function(u){for(var g,s=1,c=arguments.length;s<c;s++)for(var d in g=arguments[s])Object.prototype.hasOwnProperty.call(g,d)&&(u[d]=g[d]);return u},t.apply(this,arguments)};function i(u,g){if(!g)return"";var s="; "+u;return g===!0?s:s+"="+g}function o(u,g,s){return encodeURIComponent(u).replace(/%(23|24|26|2B|5E|60|7C)/g,decodeURIComponent).replace(/\(/g,"%28").replace(/\)/g,"%29")+"="+encodeURIComponent(g).replace(/%(23|24|26|2B|3A|3C|3E|3D|2F|3F|40|5B|5D|5E|60|7B|7D|7C)/g,decodeURIComponent)+(function(c){if(typeof c.expires=="number"){var d=new Date;d.setMilliseconds(d.getMilliseconds()+864e5*c.expires),c.expires=d}return i("Expires",c.expires?c.expires.toUTCString():"")+i("Domain",c.domain)+i("Path",c.path)+i("Secure",c.secure)+i("SameSite",c.sameSite)})(s)}function r(u){for(var g={},s=u?u.split("; "):[],c=/(%[\dA-F]{2})+/gi,d=0;d<s.length;d++){var h=s[d].split("="),m=h.slice(1).join("=");m.charAt(0)==='"'&&(m=m.slice(1,-1));try{g[h[0].replace(c,decodeURIComponent)]=m.replace(c,decodeURIComponent)}catch{}}return g}function a(){return r(document.cookie)}function l(u,g,s){document.cookie=o(u,g,t({path:"/"},s))}e.__esModule=!0,e.encode=o,e.parse=r,e.getAll=a,e.get=function(u){return a()[u]},e.set=l,e.remove=function(u,g){l(u,"",t(t({},g),{expires:-1}))}});et(ve);var fn=ve.get,Gt=ve.set,Wt=ve.remove,V={get(n){let e=fn(n);if(e!==void 0)return JSON.parse(e)},save(n,e,t){let i={};window.location.protocol==="https:"&&(i={secure:!0,sameSite:"none"}),t?.daysUntilExpire&&(i.expires=t.daysUntilExpire),t?.cookieDomain&&(i.domain=t.cookieDomain),Gt(n,JSON.stringify(e),i)},remove(n,e){let t={};e?.cookieDomain&&(t.domain=e.cookieDomain),Wt(n,t)}},gn={get(n){return V.get(n)||V.get(`_legacy_${n}`)},save(n,e,t){let i={};window.location.protocol==="https:"&&(i={secure:!0}),t?.daysUntilExpire&&(i.expires=t.daysUntilExpire),t?.cookieDomain&&(i.domain=t.cookieDomain),Gt(`_legacy_${n}`,JSON.stringify(e),i),V.save(n,e,t)},remove(n,e){let t={};e?.cookieDomain&&(t.domain=e.cookieDomain),Wt(n,t),V.remove(n,e),V.remove(`_legacy_${n}`,e)}},wn={get(n){if(typeof sessionStorage>"u")return;let e=sessionStorage.getItem(n);return e!=null?JSON.parse(e):void 0},save(n,e){sessionStorage.setItem(n,JSON.stringify(e))},remove(n){sessionStorage.removeItem(n)}};function vn(n,e,t){var i=e===void 0?null:e,o=(function(u,g){var s=atob(u);if(g){for(var c=new Uint8Array(s.length),d=0,h=s.length;d<h;++d)c[d]=s.charCodeAt(d);return String.fromCharCode.apply(null,new Uint16Array(c.buffer))}return s})(n,t!==void 0&&t),r=o.indexOf(`
`,10)+1,a=o.substring(r)+(i?"//# sourceMappingURL="+i:""),l=new Blob([a],{type:"application/javascript"});return URL.createObjectURL(l)}var St,Lt,Rt,Ue,bn=(St="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",Lt=null,Rt=!1,function(n){return Ue=Ue||vn(St,Lt,Rt),new Worker(Ue,n)}),je={},$e=class{constructor(e,t){this.cache=e,this.clientId=t,this.manifestKey=this.createManifestKeyFrom(this.clientId)}add(e){return p(this,null,function*(){var t;let i=new Set(((t=yield this.cache.get(this.manifestKey))===null||t===void 0?void 0:t.keys)||[]);i.add(e),yield this.cache.set(this.manifestKey,{keys:[...i]})})}remove(e){return p(this,null,function*(){let t=yield this.cache.get(this.manifestKey);if(t){let i=new Set(t.keys);return i.delete(e),i.size>0?yield this.cache.set(this.manifestKey,{keys:[...i]}):yield this.cache.remove(this.manifestKey)}})}get(){return this.cache.get(this.manifestKey)}clear(){return this.cache.remove(this.manifestKey)}createManifestKeyFrom(e){return`@@auth0spajs@@::${e}`}},yn={memory:()=>new ge().enclosedCache,localstorage:()=>new Xe},Nt=n=>yn[n],zt=n=>{let{openUrl:e,onRedirect:t}=n,i=L(n,["openUrl","onRedirect"]);return Object.assign(Object.assign({},i),{openUrl:e===!1||e?e:t})},Fe=new an,Be=class{constructor(e){let t,i;if(this.userCache=new ge().enclosedCache,this.defaultOptions={authorizationParams:{scope:"openid profile email"},useRefreshTokensFallback:!1,useFormData:!0},this._releaseLockOnPageHide=()=>p(this,null,function*(){yield Fe.releaseLock("auth0.lock.getTokenSilently"),window.removeEventListener("pagehide",this._releaseLockOnPageHide)}),this.options=Object.assign(Object.assign(Object.assign({},this.defaultOptions),e),{authorizationParams:Object.assign(Object.assign({},this.defaultOptions.authorizationParams),e.authorizationParams)}),typeof window<"u"&&(()=>{if(!me())throw new Error("For security reasons, `window.crypto` is required to run `auth0-spa-js`.");if(me().subtle===void 0)throw new Error(`
      auth0-spa-js must run on a secure origin. See https://github.com/auth0/auth0-spa-js/blob/main/FAQ.md#why-do-i-get-auth0-spa-js-must-run-on-a-secure-origin for more information.
    `)})(),e.cache&&e.cacheLocation&&console.warn("Both `cache` and `cacheLocation` options have been specified in the Auth0Client configuration; ignoring `cacheLocation` and using `cache`."),e.cache)i=e.cache;else{if(t=e.cacheLocation||"memory",!Nt(t))throw new Error(`Invalid cache location "${t}"`);i=Nt(t)()}this.httpTimeoutMs=e.httpTimeoutInSeconds?1e3*e.httpTimeoutInSeconds:1e4,this.cookieStorage=e.legacySameSiteCookie===!1?V:gn,this.orgHintCookieName=`auth0.${this.options.clientId}.organization_hint`,this.isAuthenticatedCookieName=(a=>`auth0.${a}.is.authenticated`)(this.options.clientId),this.sessionCheckExpiryDays=e.sessionCheckExpiryDays||1;let o=e.useCookiesForTransactions?this.cookieStorage:wn;var r;this.scope=te("openid",this.options.authorizationParams.scope,this.options.useRefreshTokens?"offline_access":""),this.transactionManager=new Je(o,this.options.clientId,this.options.cookieDomain),this.nowProvider=this.options.nowProvider||Ft,this.cacheManager=new Ye(i,i.allKeys?void 0:new $e(i,this.options.clientId),this.nowProvider),this.domainUrl=(r=this.options.domain,/^https?:\/\//.test(r)?r:`https://${r}`),this.tokenIssuer=((a,l)=>a?a.startsWith("https://")?a:`https://${a}/`:`${l}/`)(this.options.issuer,this.domainUrl),typeof window<"u"&&window.Worker&&this.options.useRefreshTokens&&t==="memory"&&(this.options.workerUrl?this.worker=new Worker(this.options.workerUrl):this.worker=new bn)}_url(e){let t=encodeURIComponent(btoa(JSON.stringify(this.options.auth0Client||jt)));return`${this.domainUrl}${e}&auth0Client=${t}`}_authorizeUrl(e){return this._url(`/authorize?${He(e)}`)}_verifyIdToken(e,t,i){return p(this,null,function*(){let o=yield this.nowProvider();return mn({iss:this.tokenIssuer,aud:this.options.clientId,id_token:e,nonce:t,organization:i,leeway:this.options.leeway,max_age:(r=this.options.authorizationParams.max_age,typeof r!="string"?r:parseInt(r,10)||void 0),now:o});var r})}_processOrgHint(e){e?this.cookieStorage.save(this.orgHintCookieName,e,{daysUntilExpire:this.sessionCheckExpiryDays,cookieDomain:this.options.cookieDomain}):this.cookieStorage.remove(this.orgHintCookieName,{cookieDomain:this.options.cookieDomain})}_prepareAuthorizeUrl(e,t,i){return p(this,null,function*(){let o=Tt(Pe()),r=Tt(Pe()),a=Pe(),l=(s=>{let c=new Uint8Array(s);return(d=>{let h={"+":"-","/":"_","=":""};return d.replace(/[+/=]/g,m=>h[m])})(window.btoa(String.fromCharCode(...Array.from(c))))})(yield(s=>p(null,null,function*(){return yield me().subtle.digest({name:"SHA-256"},new TextEncoder().encode(s))}))(a)),u=((s,c,d,h,m,b,w,C)=>Object.assign(Object.assign(Object.assign({client_id:s.clientId},s.authorizationParams),d),{scope:te(c,d.scope),response_type:"code",response_mode:C||"query",state:h,nonce:m,redirect_uri:w||s.authorizationParams.redirect_uri,code_challenge:b,code_challenge_method:"S256"}))(this.options,this.scope,e,o,r,l,e.redirect_uri||this.options.authorizationParams.redirect_uri||i,t?.response_mode),g=this._authorizeUrl(u);return{nonce:r,code_verifier:a,scope:u.scope,audience:u.audience||"default",redirect_uri:u.redirect_uri,state:o,url:g}})}loginWithPopup(e,t){return p(this,null,function*(){var i;if(e=e||{},!(t=t||{}).popup&&(t.popup=(l=>{let u=window.screenX+(window.innerWidth-400)/2,g=window.screenY+(window.innerHeight-600)/2;return window.open(l,"auth0:authorize:popup",`left=${u},top=${g},width=400,height=600,resizable,scrollbars=yes,status=1`)})(""),!t.popup))throw new Error("Unable to open a popup for loginWithPopup - window.open returned `null`");let o=yield this._prepareAuthorizeUrl(e.authorizationParams||{},{response_mode:"web_message"},window.location.origin);t.popup.location.href=o.url;let r=yield(l=>new Promise((u,g)=>{let s,c=setInterval(()=>{l.popup&&l.popup.closed&&(clearInterval(c),clearTimeout(d),window.removeEventListener("message",s,!1),g(new Ze(l.popup)))},1e3),d=setTimeout(()=>{clearInterval(c),g(new Me(l.popup)),window.removeEventListener("message",s,!1)},1e3*(l.timeoutInSeconds||60));s=function(h){if(h.data&&h.data.type==="authorization_response"){if(clearTimeout(d),clearInterval(c),window.removeEventListener("message",s,!1),l.popup.close(),h.data.response.error)return g(x.fromPayload(h.data.response));u(h.data.response)}},window.addEventListener("message",s)}))(Object.assign(Object.assign({},t),{timeoutInSeconds:t.timeoutInSeconds||this.options.authorizeTimeoutInSeconds||60}));if(o.state!==r.state)throw new x("state_mismatch","Invalid state");let a=((i=e.authorizationParams)===null||i===void 0?void 0:i.organization)||this.options.authorizationParams.organization;yield this._requestToken({audience:o.audience,scope:o.scope,code_verifier:o.code_verifier,grant_type:"authorization_code",code:r.code,redirect_uri:o.redirect_uri},{nonceIn:o.nonce,organization:a})})}getUser(){return p(this,null,function*(){var e;let t=yield this._getIdTokenFromCache();return(e=t?.decodedToken)===null||e===void 0?void 0:e.user})}getIdTokenClaims(){return p(this,null,function*(){var e;let t=yield this._getIdTokenFromCache();return(e=t?.decodedToken)===null||e===void 0?void 0:e.claims})}loginWithRedirect(){return p(this,arguments,function*(e={}){var t;let i=zt(e),{openUrl:o,fragment:r,appState:a}=i,l=L(i,["openUrl","fragment","appState"]),u=((t=l.authorizationParams)===null||t===void 0?void 0:t.organization)||this.options.authorizationParams.organization,g=yield this._prepareAuthorizeUrl(l.authorizationParams||{}),{url:s}=g,c=L(g,["url"]);this.transactionManager.create(Object.assign(Object.assign(Object.assign({},c),{appState:a}),u&&{organization:u}));let d=r?`${s}#${r}`:s;o?yield o(d):window.location.assign(d)})}handleRedirectCallback(){return p(this,arguments,function*(e=window.location.href){let t=e.split("?").slice(1);if(t.length===0)throw new Error("There are no query params available for parsing.");let{state:i,code:o,error:r,error_description:a}=(c=>{c.indexOf("#")>-1&&(c=c.substring(0,c.indexOf("#")));let d=new URLSearchParams(c);return{state:d.get("state"),code:d.get("code")||void 0,error:d.get("error")||void 0,error_description:d.get("error_description")||void 0}})(t.join("")),l=this.transactionManager.get();if(!l)throw new x("missing_transaction","Invalid state");if(this.transactionManager.remove(),r)throw new Ke(r,a||r,i,l.appState);if(!l.code_verifier||l.state&&l.state!==i)throw new x("state_mismatch","Invalid state");let u=l.organization,g=l.nonce,s=l.redirect_uri;return yield this._requestToken(Object.assign({audience:l.audience,scope:l.scope,code_verifier:l.code_verifier,grant_type:"authorization_code",code:o},s?{redirect_uri:s}:{}),{nonceIn:g,organization:u}),{appState:l.appState}})}checkSession(e){return p(this,null,function*(){if(!this.cookieStorage.get(this.isAuthenticatedCookieName)){if(!this.cookieStorage.get("auth0.is.authenticated"))return;this.cookieStorage.save(this.isAuthenticatedCookieName,!0,{daysUntilExpire:this.sessionCheckExpiryDays,cookieDomain:this.options.cookieDomain}),this.cookieStorage.remove("auth0.is.authenticated")}try{yield this.getTokenSilently(e)}catch{}})}getTokenSilently(){return p(this,arguments,function*(e={}){var t;let i=Object.assign(Object.assign({cacheMode:"on"},e),{authorizationParams:Object.assign(Object.assign(Object.assign({},this.options.authorizationParams),e.authorizationParams),{scope:te(this.scope,(t=e.authorizationParams)===null||t===void 0?void 0:t.scope)})}),o=yield((r,a)=>{let l=je[a];return l||(l=r().finally(()=>{delete je[a],l=null}),je[a]=l),l})(()=>this._getTokenSilently(i),`${this.options.clientId}::${i.authorizationParams.audience}::${i.authorizationParams.scope}`);return e.detailedResponse?o:o?.access_token})}_getTokenSilently(e){return p(this,null,function*(){let{cacheMode:t}=e,i=L(e,["cacheMode"]);if(t!=="off"){let o=yield this._getEntryFromCache({scope:i.authorizationParams.scope,audience:i.authorizationParams.audience||"default",clientId:this.options.clientId});if(o)return o}if(t!=="cache-only"){if(!(yield((o,r=3)=>p(this,null,function*(){for(let a=0;a<r;a++)if(yield o())return!0;return!1}))(()=>Fe.acquireLock("auth0.lock.getTokenSilently",5e3),10)))throw new re;try{if(window.addEventListener("pagehide",this._releaseLockOnPageHide),t!=="off"){let g=yield this._getEntryFromCache({scope:i.authorizationParams.scope,audience:i.authorizationParams.audience||"default",clientId:this.options.clientId});if(g)return g}let o=this.options.useRefreshTokens?yield this._getTokenUsingRefreshToken(i):yield this._getTokenFromIFrame(i),{id_token:r,access_token:a,oauthTokenScope:l,expires_in:u}=o;return Object.assign(Object.assign({id_token:r,access_token:a},l?{scope:l}:null),{expires_in:u})}finally{yield Fe.releaseLock("auth0.lock.getTokenSilently"),window.removeEventListener("pagehide",this._releaseLockOnPageHide)}}})}getTokenWithPopup(){return p(this,arguments,function*(e={},t={}){var i;let o=Object.assign(Object.assign({},e),{authorizationParams:Object.assign(Object.assign(Object.assign({},this.options.authorizationParams),e.authorizationParams),{scope:te(this.scope,(i=e.authorizationParams)===null||i===void 0?void 0:i.scope)})});return t=Object.assign(Object.assign({},sn),t),yield this.loginWithPopup(o,t),(yield this.cacheManager.get(new A({scope:o.authorizationParams.scope,audience:o.authorizationParams.audience||"default",clientId:this.options.clientId}))).access_token})}isAuthenticated(){return p(this,null,function*(){return!!(yield this.getUser())})}_buildLogoutUrl(e){e.clientId!==null?e.clientId=e.clientId||this.options.clientId:delete e.clientId;let t=e.logoutParams||{},{federated:i}=t,o=L(t,["federated"]),r=i?"&federated":"";return this._url(`/v2/logout?${He(Object.assign({clientId:e.clientId},o))}`)+r}logout(){return p(this,arguments,function*(e={}){let t=zt(e),{openUrl:i}=t,o=L(t,["openUrl"]);e.clientId===null?yield this.cacheManager.clear():yield this.cacheManager.clear(e.clientId||this.options.clientId),this.cookieStorage.remove(this.orgHintCookieName,{cookieDomain:this.options.cookieDomain}),this.cookieStorage.remove(this.isAuthenticatedCookieName,{cookieDomain:this.options.cookieDomain}),this.userCache.remove("@@user@@");let r=this._buildLogoutUrl(o);i?yield i(r):i!==!1&&window.location.assign(r)})}_getTokenFromIFrame(e){return p(this,null,function*(){let t=Object.assign(Object.assign({},e.authorizationParams),{prompt:"none"}),i=this.cookieStorage.get(this.orgHintCookieName);i&&!t.organization&&(t.organization=i);let{url:o,state:r,nonce:a,code_verifier:l,redirect_uri:u,scope:g,audience:s}=yield this._prepareAuthorizeUrl(t,{response_mode:"web_message"},window.location.origin);try{if(window.crossOriginIsolated)throw new x("login_required","The application is running in a Cross-Origin Isolated context, silently retrieving a token without refresh token is not possible.");let c=e.timeoutInSeconds||this.options.authorizeTimeoutInSeconds,d;try{d=new URL(this.domainUrl).origin}catch{d=this.domainUrl}let h=yield((b,w,C=60)=>new Promise((E,k)=>{let f=window.document.createElement("iframe");f.setAttribute("width","0"),f.setAttribute("height","0"),f.style.display="none";let I=()=>{window.document.body.contains(f)&&(window.document.body.removeChild(f),window.removeEventListener("message",R,!1))},R,be=setTimeout(()=>{k(new re),I()},1e3*C);R=function(S){if(S.origin!=w||!S.data||S.data.type!=="authorization_response")return;let ae=S.source;ae&&ae.close(),S.data.response.error?k(x.fromPayload(S.data.response)):E(S.data.response),clearTimeout(be),window.removeEventListener("message",R,!1),setTimeout(I,2e3)},window.addEventListener("message",R,!1),window.document.body.appendChild(f),f.setAttribute("src",b)}))(o,d,c);if(r!==h.state)throw new x("state_mismatch","Invalid state");let m=yield this._requestToken(Object.assign(Object.assign({},e.authorizationParams),{code_verifier:l,code:h.code,grant_type:"authorization_code",redirect_uri:u,timeout:e.authorizationParams.timeout||this.httpTimeoutMs}),{nonceIn:a,organization:t.organization});return Object.assign(Object.assign({},m),{scope:g,oauthTokenScope:m.scope,audience:s})}catch(c){throw c.error==="login_required"&&this.logout({openUrl:!1}),c}})}_getTokenUsingRefreshToken(e){return p(this,null,function*(){let t=yield this.cacheManager.get(new A({scope:e.authorizationParams.scope,audience:e.authorizationParams.audience||"default",clientId:this.options.clientId}));if(!(t&&t.refresh_token||this.worker)){if(this.options.useRefreshTokensFallback)return yield this._getTokenFromIFrame(e);throw new fe(e.authorizationParams.audience||"default",e.authorizationParams.scope)}let i=e.authorizationParams.redirect_uri||this.options.authorizationParams.redirect_uri||window.location.origin,o=typeof e.timeoutInSeconds=="number"?1e3*e.timeoutInSeconds:null;try{let r=yield this._requestToken(Object.assign(Object.assign(Object.assign({},e.authorizationParams),{grant_type:"refresh_token",refresh_token:t&&t.refresh_token,redirect_uri:i}),o&&{timeout:o}));return Object.assign(Object.assign({},r),{scope:e.authorizationParams.scope,oauthTokenScope:r.scope,audience:e.authorizationParams.audience||"default"})}catch(r){if((r.message.indexOf("Missing Refresh Token")>-1||r.message&&r.message.indexOf("invalid refresh token")>-1)&&this.options.useRefreshTokensFallback)return yield this._getTokenFromIFrame(e);throw r}})}_saveEntryInCache(e){return p(this,null,function*(){let{id_token:t,decodedToken:i}=e,o=L(e,["id_token","decodedToken"]);this.userCache.set("@@user@@",{id_token:t,decodedToken:i}),yield this.cacheManager.setIdToken(this.options.clientId,e.id_token,e.decodedToken),yield this.cacheManager.set(o)})}_getIdTokenFromCache(){return p(this,null,function*(){let e=this.options.authorizationParams.audience||"default",t=yield this.cacheManager.getIdToken(new A({clientId:this.options.clientId,audience:e,scope:this.scope})),i=this.userCache.get("@@user@@");return t&&t.id_token===i?.id_token?i:(this.userCache.set("@@user@@",t),t)})}_getEntryFromCache(o){return p(this,arguments,function*({scope:e,audience:t,clientId:i}){let r=yield this.cacheManager.get(new A({scope:e,audience:t,clientId:i}),60);if(r&&r.access_token){let{access_token:a,oauthTokenScope:l,expires_in:u}=r,g=yield this._getIdTokenFromCache();return g&&Object.assign(Object.assign({id_token:g.id_token,access_token:a},l?{scope:l}:null),{expires_in:u})}})}_requestToken(e,t){return p(this,null,function*(){let{nonceIn:i,organization:o}=t||{},r=yield pn(Object.assign({baseUrl:this.domainUrl,client_id:this.options.clientId,auth0Client:this.options.auth0Client,useFormData:this.options.useFormData,timeout:this.httpTimeoutMs},e),this.worker),a=yield this._verifyIdToken(r.id_token,i,o);return yield this._saveEntryInCache(Object.assign(Object.assign(Object.assign(Object.assign({},r),{decodedToken:a,scope:e.scope,audience:e.audience||"default"}),r.scope?{oauthTokenScope:r.scope}:null),{client_id:this.options.clientId})),this.cookieStorage.save(this.isAuthenticatedCookieName,!0,{daysUntilExpire:this.sessionCheckExpiryDays,cookieDomain:this.options.cookieDomain}),this._processOrgHint(o||a.claims.org_id),Object.assign(Object.assign({},r),{decodedToken:a})})}exchangeToken(e){return p(this,null,function*(){return this._requestToken({grant_type:"urn:ietf:params:oauth:grant-type:token-exchange",subject_token:e.subject_token,subject_token_type:e.subject_token_type,scope:te(e.scope,this.scope),audience:e.audience||this.options.authorizationParams.audience})})}};function Cn(n){let e={clientId:n.IDENTITY_CLIENT_ID,domain:n.IDENTITY_PROVIDER_URL,authorizationParams:{audience:n.IDENTITY_AUDIENCE,scope:n.IDENTITY_USER_SCOPES}};return new Be(e)}var qe=class{constructor(){this.initialDocumentScrollTop=0,this.styleElement=null}set version(e){Object.assign(window,{PHN_HEADER_VERSION:e})}get version(){return window.PHN_HEADER_VERSION}set drawerVersion(e){Object.assign(window,{PHN_DRAWER_VERSION:e})}get drawerVersion(){return window.PHN_DRAWER_VERSION}set navigationLoaded(e){Object.assign(window,{PHN_NAVIGATION_LOADED:e})}getBreakpoint(){let e=Math.max(window.document.documentElement.clientWidth||0,window.innerWidth||0),t=Object.keys(U).reverse().find(i=>_e(U,i)&&e>=U[i]);return typeof t=="string"&&_e(U,t)?U[t]:0}get redirected(){return window.REDIRECTED===!0||window.location.search.includes("cs_redirect=")}redirectToUrl(e){window.location.assign(e)}cookieConsentGiven(){let e=window,t={googleMaps:!1,newRelic:!1};return Object.keys(xe).forEach(o=>{let r=e?.usercentrics?.getConsents(e.GlobalConsent?.Processor?.[o]??xe[o]);r?.consentStatus&&(t[o]=r.consentStatus)}),t}maybeCheckMarketingConsent(){typeof window?.usercentrics?.getConsents=="function"?(v.state.userConsent=this.cookieConsentGiven(),window.addEventListener("consents_changed_finished",()=>{v.state.userConsent=this.cookieConsentGiven()})):v.state.userConsent=dt}getScrollbarWidth(){let e=window.document.createElement("div");e.style.overflow="scroll",window.document.body.appendChild(e);let t=e.offsetWidth-e.clientWidth;return window.document.body.removeChild(e),Number.isNaN(t)?"0px":`${t}px`}fetchGoogleMapsApi(i){return p(this,arguments,function*(e,t=`https://maps.googleapis.com/maps/api/js?key=${e}&loading=async&libraries=places`){if(window.google?.maps?.version!==void 0||document.getElementById("googleMaps"))return window.google;let r=document.createElement("script");return r.src=t,r.id="googleMaps",document.head.appendChild(r),new Promise((a,l)=>{r.onerror=()=>{l(new N("Failed to load google maps script.",T.GENERAL))},r.onload=()=>{a(window.google)}})})}getNavigatorLatLong(){return p(this,null,function*(){return new Promise((e,t)=>{window.navigator.geolocation.getCurrentPosition(({coords:{latitude:i,longitude:o}})=>{e({latitude:i,longitude:o})},i=>t(new N(`geolocation.getCurrentPosition failed with ${i.code}: ${i.message}`,kn(i))))})})}getCookie(e){return Ie(document.cookie.split("; ").find(t=>t.startsWith(`${e}=`))?.split("=")[1])}setCookie(e,t){document.cookie=`${e}=${t};domain=${ct};expires=${new Date(Date.now()+st*10).toUTCString()};`}lockScroll(){this.initialDocumentScrollTop=document.documentElement.scrollTop,this.styleElement=document.createElement("style"),this.styleElement.textContent=`html {
      background-color: light-dark(#8b8b8b, #9f9f9f);
    }
    body {
      overflow-y: clip;
    }`,document.head.appendChild(this.styleElement),this.inertFooter()}unlockScroll(){this.styleElement?.remove(),this.removeInertFooter(),document.documentElement.scrollTop=this.initialDocumentScrollTop}removeInertFooter(){try{document.querySelector("pnav-footer")?.removeAttribute("inert")}catch{}}inertFooter(){try{document.querySelector("pnav-footer")?.setAttribute("inert","")}catch{}}hasScrollbar(){return window.document.body.offsetHeight>window.innerHeight}waitForElement(e,t){return p(this,null,function*(){if(!M(t))return e;let i=Q(e.querySelector(t));return i!==null?i:new Promise(o=>{let r=new MutationObserver(()=>{let a=Q(e.querySelector(t));a!==null&&(o(a),r.disconnect())});r.observe(e,{childList:!0,subtree:!0})})})}};function kn(n){switch(n.code){case n.PERMISSION_DENIED:return T.GEOLOCATION_DENIED;case n.POSITION_UNAVAILABLE:case n.TIMEOUT:return T.RETRY;default:return T.GENERAL}}var _=new qe,K=(function(n){return n.INIT="INIT_AUTHENTICATION",n.TOKEN_RECEIVED="TOKEN_RECEIVED",n.TOKEN_RECEIVED_ERROR="TOKEN_RECEIVED_ERROR",n.PREPARE_LOGOUT="PREPARE_LOGOUT",n.PREPARE_LOGIN_WITH_REDIRECT="PREPARE_LOGIN_WITH_REDIRECT",n.PREPARE_GET_TOKEN_SILENTLY="PREPARE_GET_TOKEN_SILENTLY",n.LEGACY_LOGOUT="LEGACY_LOGOUT",n.LOGOUT_DEFAULT_PREVENTED="LOGOUT_DEFAULT_PREVENTED",n.LOGIN_DEFAULT_PREVENTED="LOGIN_DEFAULT_PREVENTED",n.ACCOUNT_LOGIN="ACCOUNT_LOGIN",n.ACCOUNT_LOGIN_DEFAULT_PREVENTED="ACCOUNT_LOGIN_DEFAULT_PREVENTED",n.UPDATED_USER_STATE="UPDATED_USER_STATE",n.UPDATED_USER_STATE_ERROR="UPDATED_USER_STATE_ERROR",n.UNKNOWN="UNKNOWN",n})(K||{}),En="PHN_AUTHENTICATION";function H(n){window.dispatchEvent(new CustomEvent(En,{detail:n}))}function xn(n){window.dispatchEvent(new CustomEvent("getToken",{detail:{token:n}}))}function ue(n){window.PHN_NR_EVENT_QUEUE?.push(n)}var ie=(function(n){return n.NAVIGATION_LOADED="NAVIGATION_LOADED",n.NAVIGATION_DRAWER_LOADED="NAVIGATION_DRAWER_LOADED",n.NAVIGATION_LOGOUT_SUCCESS="NAVIGATION_LOGOUT_SUCCESS",n.NAVIGATION_LOGIN_SUCCESS="NAVIGATION_LOGIN_SUCCESS",n.NAVIGATION_LOGIN="NAVIGATION_LOGIN",n.NAVIGATION_LOGIN_ERROR="NAVIGATION_LOGIN_ERROR",n.NAVIGATION_LOGIN_REDIRECT_ERROR="NAVIGATION_LOGIN_REDIRECT_ERROR",n.THIRD_PARTY_LOGIN="NAVIGATION_THIRD_PARTY_LOGIN",n.NAVIGATION_FEATURES="NAVIGATION_FEATURES",n.NAVIGATION_SERIES_IMG_PRELOAD="NAVIGATION_SERIES_IMG_PRELOAD",n.NAVIGATION_SERIES_IMG_PRELOAD_ERROR="NAVIGATION_SERIES_IMG_PRELOAD_ERROR",n})(ie||{}),X=(function(n){return n.LOGIN="login",n.LOGOUT="logout",n})(X||{});function _n(n){if(!n)return"";let{firstName:e,lastName:t}=n;return[e,t].filter(M).join(" ")}function In(n,e){return p(this,null,function*(){let t=ke,i,o;try{let r=yield rn(n,e);if(r==null)throw new N("Invalid user data object extracted from user profile response",T.NO_RESULTS);de.state.savedSearchesCount=r.savedSearches,de.state.savedVehiclesCount=r.savedVehicles,v.state.unreadMessagesCount=r.unreadMessages;let{userData:a}=r;t=_n(a)||Ce,i=r.userData.userId??"",o=r.userData.ciamId??"",t&&(z.getInstance().setUser(i,o,Te(t)),z.getInstance().pushDataLayerEvent(ye.IDS_LOAD)),H({eventType:K.UPDATED_USER_STATE,state:t})}catch(r){throw(!(r instanceof N)||r.type!==T.UNAUTHORIZED)&&(t=Ce),v.state.unreadMessagesCount=at,v.state.loggedInState=t,H({eventType:K.UPDATED_USER_STATE_ERROR,state:t,error:r}),r}v.state.loggedInState=t,z.getInstance().setUser(i,o,Te(t))})}function Ge(){let n=sessionStorage.getItem("phn-flow")?.replace("navi-","");return n===X.LOGIN||n===X.LOGOUT?n:null}function Dt(){sessionStorage.removeItem("phn-flow")}function At(n){return p(this,null,function*(){try{H({eventType:K.PREPARE_GET_TOKEN_SILENTLY});let t=yield Cn(n).getTokenSilently({authorizationParams:{audience:n.IDENTITY_AUDIENCE,scope:n.IDENTITY_USER_SCOPES}});return xn(t),H({eventType:K.TOKEN_RECEIVED,token:t}),Ge()===X.LOGIN&&(ue({type:ie.NAVIGATION_LOGIN_SUCCESS}),Dt()),t}catch(e){if(Ge()===X.LOGIN){ue({type:ie.NAVIGATION_LOGIN_ERROR,options:e});let t="phn-login-redirect-error",i=sessionStorage.getItem(t);i&&(ue({type:ie.NAVIGATION_LOGIN_REDIRECT_ERROR,options:i}),sessionStorage.removeItem(t))}else Ge()===X.LOGOUT&&ue({type:ie.NAVIGATION_LOGOUT_SUCCESS});return Dt(),H({eventType:K.TOKEN_RECEIVED_ERROR,error:e}),{token:null,error:JSON.stringify(e)}}})}var O=Ee({routingKeyHistory:[],routingKeyPointer:-1,animatingPointer:-1,animatingHistory:[],breakpoint:_.getBreakpoint(),animatingBreakpoint:_.getBreakpoint(),initialRoutingKeyHistory:null,initialRoutingKeyPointer:null}),we=class{constructor(e){this.delay=e,this.timer=null}set(e){this.cancel(),this.timer=setTimeout(()=>{e(),this.cancel()},this.delay)}cancel(){this.timer!==null&&(clearTimeout(this.timer),typeof this.timer.unref=="function"&&this.timer.unref(),this.timer=null)}};function Tn(){setTimeout(()=>{H({eventType:K.INIT,state:ke})}),_.version=rt,z.getInstance().pushDataLayerEvent(ye.GENERAL_LOAD)}function On(n){ft(n)}var Sn=()=>{let n=j(v.state.env);return y(_t,null,y("script",{type:"module",src:`${n.PHN_DRAWER_CDN_URL}/navigation-drawer.esm.js`}),y("script",{type:"nomodule",src:`${n.PHN_DRAWER_CDN_URL}/navigation-drawer.js`}))},Ln="@keyframes translate-forward-in-animation{from{translate:0 12px}to{translate:0 0px}}@keyframes fade-in-animation{from{opacity:0;background-color:rgba(0, 0, 0, 0)}to{opacity:1;background-color:rgba(0, 0, 0, 0.6)}}@keyframes translate-in-from-left{from{translate:calc(var(--isLTR, 1) * -1000px)}to{translate:0px}}@keyframes slideDown{from{max-block-size:0}to{max-block-size:1000px}}@keyframes slideUp{from{max-block-size:1000px}to{max-block-size:0}}@keyframes slideLeft{from{max-inline-size:0}to{max-inline-size:1000px}}@keyframes slideRight{from{max-inline-size:1000px}to{max-inline-size:0}}@keyframes darken{from{background-color:rgba(0, 0, 0, 0)}to{background-color:rgba(0, 0, 0, 0.8)}}@keyframes lighten{from{background-color:rgba(0, 0, 0, 0.8)}to{background-color:rgba(0, 0, 0, 0)}}@keyframes fadeInFromBottom{0%{opacity:0;inset-block-start:36px}100%{opacity:1;inset-block-start:0px}}@keyframes fadeInFromTop{0%{opacity:0;inset-block-end:36px}100%{opacity:1;inset-block-end:0px}}@keyframes fadeOutToTop{0%{opacity:1;inset-block-end:0px}100%{opacity:0;inset-block-end:16px}}@keyframes slideLeftDoubleDrawer{from{inset-inline-end:-1000px}to{inset-inline-end:0px}}@keyframes slideRightDoubleDrawer{from{inset-inline-end:0px}to{inset-inline-end:-1000px}}@keyframes fadeIn{0%{opacity:0}100%{opacity:1}}@keyframes fadeOut{to{opacity:0}}.translate-forward-animation-in{opacity:1}@media (prefers-reduced-motion: no-preference){.translate-forward-animation-in{animation:translate-forward-in-animation 0.25s cubic-bezier(0.25, 0.1, 0.25, 1)}}@media (prefers-reduced-motion: no-preference){.translate-forward-animation-out,.translate-backward-animation-out{animation:fadeOut 0.25s cubic-bezier(0.25, 0.1, 0.25, 1)}}@media (prefers-reduced-motion: no-preference){.translate-backward-animation-in{animation:fadeIn 0.25s cubic-bezier(0.25, 0.1, 0.25, 1), translate-forward-in-animation 0.25s cubic-bezier(0.25, 0.1, 0.25, 1)}}@media (prefers-reduced-motion: no-preference){.mobile-transition-forward{animation:fadeIn 0.25s cubic-bezier(0.25, 0.1, 0.25, 1), translate-forward-in-animation 0.25s cubic-bezier(0.25, 0.1, 0.25, 1)}}.phn-flex-col{display:flex;flex-direction:column}.phn-flex-row{display:flex;flex-direction:row}.phn-align-center{align-items:center}.phn-justify-around{justify-content:space-around}.phn-justify-between{justify-content:space-between}.phn-flex-wrap{flex-wrap:wrap}.tooltip{position:relative;display:inline-block}.tooltip-text{visibility:hidden;inline-size:inherit;white-space:nowrap;background-color:#404044;border-radius:2px;font-size:13px;color:#fff;text-align:center;padding-block:0px;padding-inline:4px;position:absolute;z-index:100;inset-inline-start:50%;transform:translateX(calc(var(--isLTR, 1) * -50%))}.tooltip:hover .tooltip-text{visibility:visible}.tooltip-text.level0_icon{inset-block-end:-1.7em}.tooltip-text.search_icon{inset-block-end:-1.65em}.tooltip-text.shop_icon{inset-block-end:-1.5em}.tooltip-text.finder_icon{inset-block-end:-1.5em}.tooltip-text.logo{inset-block-end:-1.5em}.tooltip-text.account_icon{inset-block-end:-1.5em}:host{--isLTR:1;display:block;visibility:visible;position:static;z-index:500}:host :dir(rtl){--isLTR:-1}:host .header{min-block-size:4.125rem}@media (min-width: 480px){:host .header{min-block-size:5rem}}@media (min-width: 760px){:host .header{min-block-size:4.5625rem}}@media (min-width: 1000px){:host .header{min-block-size:4.75rem}}@media (min-width: 1300px){:host .header{min-block-size:5.125rem}}@media (min-width: 1920px){:host .limited-width{max-inline-size:160rem;margin-block:0;margin-inline:auto}}",Rn=Ln,hi=(()=>{let n=class{constructor(e){pe(this,e),this.navigationDidLoad=De(this,"navigationDidLoad",7),this.locale=lt,this.env="",this.clientId="",this.pageName="",this.theme=se.light,this.mode=B.navbar,this.limitedWidth="true",this.app=ce.default,this.initialRoutingPath="",this.displayLogoOnly=!1,this.numberOfShoppingItems=0,this.wishlistItemsNumber=0,this.shopInformation='{ "wishlistItemsNumber": 0, "numberOfShoppingItems": 0, "subTotal": "0" }',this.authOptions="{}",this.hideShowOnScroll=!1,this.loadingContent=!1,this.receivedContent=!1,this.breakpoint=_.getBreakpoint(),this.eventPreventedTimeout=new we(0),this.loadedToggles=!1}localeWatchHandler(){return p(this,null,function*(){v.state.locale=this.locale,yield this.initNavContentStore(),z.getInstance().setLocale(this.locale)})}pageNameWatchHandler(){v.state.pageName=this.pageName,z.getInstance().setPageName(this.pageName)}newEnvReceived(){return p(this,null,function*(){yield this.initEnvironment(),yield this.initNavContentStore(),At(j(v.state.env))})}appWatcher(e){v.state.app=ee(e)}watchShopVariable(e,t,i){let o=JSON.parse(e);Le.set(i,o)}initialRoutingPathWatcher(e){v.state.initialRoutingPath=e}themeWatcher(e){v.state.theme=e}modeWatcher(e){v.state.mode=e}auth0Watcher(e){this.setAuthConfig(JSON.parse(e),j(v.state.env))}resizeHandler(){this.breakpoint=_.getBreakpoint(),O.state.breakpoint=_.getBreakpoint()}newAuthMessageReceived(e){return p(this,null,function*(){if(M(e.detail?.token))try{yield In(e.detail.token,this.locale)}catch(t){Z.error("INIT_USER_DATA",t)}})}handleUserCentricsInitalization(){try{this.checkMarketingConsent()}catch(e){Z.error("INIT_COOKIE_CONSENT",e)}}navigationLoadedHandler({detail:e}){_.navigationLoaded=e,e&&this.navigationDidLoad.emit()}checkMarketingConsent(){_.maybeCheckMarketingConsent()}initEnvironment(){return p(this,null,function*(){v.state.env=$(this.env),v.state.app=ee(this.app),v.state.locale=this.locale,v.state.pageName=this.pageName,v.state.initialRoutingPath=this.initialRoutingPath,z.getInstance().setEnvironment(v.state.env),Ae.setEnvironment(v.state.env),v.state.theme=this.theme,v.state.mode=this.mode;let e=j(v.state.env);this.setAuthConfig(JSON.parse(this.authOptions),e),At(e),this.loadedToggles=!1,yield Ae.setFeatures(v.state.env),oe.state.features=Ae.features,this.loadedToggles=!0;let t=JSON.parse(this.shopInformation);bt({shopInformation:t})})}setAuthConfig(e,t){e.redirectUrl&&(t.IDENTITY_REDIRECT_URI=e.redirectUrl),e.logoutCallback&&(t.IDENTITY_LOGOUT_CALLBACK=e.logoutCallback),v.state.authOptions=e}initNavContentStore(){return p(this,null,function*(){let e=this.receivedContent;this.receivedContent=!1,this.loadingContent=!0;try{let t=this.locale,i=v.state.env,o=yield Ct(this.app);this.locale===t&&v.state.env===i&&(Object.assign(D.state,o),this.receivedContent=!0)}catch(t){Z.error("FETCH_CONTENT",t),e&&(this.receivedContent=!0)}this.loadingContent=!1})}componentWillLoad(){mt(),xt(On),z.getInstance().setDefaultProperties(this.locale,this.pageName),window.ncs=D,this.initEnvironment().then(()=>p(this,null,function*(){return this.initNavContentStore()}));let e=document.createElement("style");e.textContent=`
     phn-header + * {
      isolation: isolate;
     }`,document.head.appendChild(e)}componentDidLoad(){try{this.checkMarketingConsent()}catch(e){Z.error("INIT_COOKIE_CONSENT",e)}Tn()}disconnectedCallback(){this.eventPreventedTimeout.cancel()}hasLimitedWidth(){return this.mode===B.hero&&this.limitedWidth==="true"}render(){let{shop:e}=D.state;return this.loadedToggles?y("nav",{class:this.hasLimitedWidth()?"header limited-width":"header"},y(Sn,null),y("phn-wrapper",{theme:this.theme,mode:this.mode,locale:this.locale,loadingContent:this.loadingContent,receivedContent:this.receivedContent,breakpoint:this.breakpoint,app:ee(this.app),displayLogoOnly:this.displayLogoOnly,limitedWidth:this.hasLimitedWidth(),hideShowOnScroll:this.hideShowOnScroll},Re(e,this.locale,ee(this.app))?y("div",{slot:"contextual_drawer_header"},y("slot",{name:"contextual_drawer_header"})):null)):y("div",null)}static get assetsDirs(){return["auth"]}static get watchers(){return{locale:["localeWatchHandler"],pageName:["pageNameWatchHandler"],env:["newEnvReceived"],app:["appWatcher"],shopInformation:["watchShopVariable"],initialRoutingPath:["initialRoutingPathWatcher"],theme:["themeWatcher"],mode:["modeWatcher"],authOptions:["auth0Watcher"]}}};return n.style=Rn,n})();function Nn(i,o){return p(this,arguments,function*(n,e,t=_.getBreakpoint()>=U.s){if(n===null)return null;let r=Array.isArray(n),a=r?n?.[0]:n,l=c=>p(null,null,function*(){let d=yield _.waitForElement(a,c);return typeof d?.componentOnReady=="function"&&(yield d.componentOnReady()),d}),u=r?n.slice(1).map(l):[yield l()],g=new Promise(c=>e.set(()=>c(null))),s;return Promise.race([Promise.all([...u,t&&new Promise(c=>{s=zn(c),window.addEventListener("finishedAnimation",s)}).finally(()=>window.removeEventListener("finishedAnimation",s))]),g])})}function zn(n){return()=>n(null)}var Qe={},Y={},Pt=window.matchMedia?.("(prefers-reduced-motion: no-preference)").matches?1e3:0;function Dn(){le().addEventListener("transitionend",Ut),le().addEventListener("animationend",Ut)}function An(n,e){Y[n]=e}function Pn(){Object.keys(Y).forEach(n=>delete Y[n])}function Un(n){return p(this,null,function*(){let e=document.body.querySelector("phn-header")?.shadowRoot??document.body.querySelector(".phn-header");if(n.toLowerCase().includes("singledrawer")&&!e?.querySelector("phn-nd-single-drawer")||n.toLowerCase().includes("doubledrawer")&&!e?.querySelector("phn-nd-double-drawer"))return;let t=Y[n];return t===void 0?new Promise((i,o)=>o(new Error(`Sequence ${n} has not been registered`))):(t.running||(t.promise=new Promise((i,o)=>{t.resolve=()=>{t.running=!1,i()},t.reject=r=>{t.running=!1,o(r)},t.running=!0,Kt(t)})),t.promise)})}function Kt(n){return p(this,null,function*(){if(n.currentStepIndex>=n.steps.length){jn(n);return}let e=n.steps[n.currentStepIndex],t=[];for(let i of e)t.push(Fn(i));try{yield Promise.all(t)}catch(i){n.reject?.(i);return}n.currentStepIndex+=1,setTimeout(()=>p(null,null,function*(){return Kt(n)}),0)})}function jn(n){n.currentStepIndex=0,n.resolve?.(null),n.promise=void 0}function Ut(n){let e=Q(n.target);if(e===null)throw new Error(`${n.type} event target is null`);let t=e.dataset.phnAmId;t!=null&&Mt(t,e)}function Fn(o){return p(this,arguments,function*({onStart:n,onEnd:e,animation:t,elementSelector:i}){if(n){let l=n();q(l)&&(yield l)}let r=Array.from(le().querySelectorAll(i)),a=[];for(let l of r){let u=Gn(Wn)(l,t);a.push(u)}if(yield Promise.all(a),e){let l=e();q(l)&&(yield l)}})}function Gn(n){return function(e,t){return p(this,null,function*(){let{cleanUpBeforeAnimation:i,cleanUpAfterAnimation:o}=t,r=n(e,t);if(typeof i=="function"){let a=i(e);q(a)&&(yield a)}if(yield r,typeof o=="function"){let a=o(e);q(a)&&(yield a)}o!==!1&&e.classList.remove(t.className)})}}var Wn=(n,e)=>p(null,null,function*(){let t=Math.floor(Math.random()*1e7);n.classList.add(e.className),n.dataset.phnAmId=t.toString();let i,o,r,a=new Promise((l,u)=>{i=l,o=u,r=setTimeout(()=>{["closing-animation","translate-out-to-left"].includes(e.className)||console.warn(`Animation with className ${e.className} didn't finish after ${Pt}ms. Forcefully finishing animation and continuing with the rest of the sequence.`),Mt(String(t),n)},Pt)});return Qe[t]={animation:e,resolve:i,reject:o,promise:a,timeout:r},a});function Mt(n,e){let{resolve:t,timeout:i}=Qe[n];clearTimeout(i),delete Qe[n],delete e.dataset.phnAmId,t()}function Kn(n){let e=Y[n];if(e===void 0)throw new Error(`Sequence ${n} has not been registered`);return e.promise!==void 0}function Mn(){return Object.values(Y).some(n=>n?.promise!==void 0)}var nt={initialize:Dn,play:Un,isPlaying:Kn,register:An,reset:Pn,isPlayingAny:Mn};function Zn(n){return n.replace(/_([a-zA-Z])/g,(e,t)=>t.toUpperCase())}function Vn(n,e){let t=n,i=[];for(let o of e){if(t=t.children.find(r=>r.id===o),t==null)break;i.push(o)}return i}var Zt=(...n)=>{if(nt.isPlayingAny())setTimeout(()=>Zt(...n),20);else{let e=[he.MAIN_MENU,...n];O.state.routingKeyPointer=n.length,O.state.routingKeyHistory=e}},Hn=()=>{nt.isPlayingAny()||(O.state.routingKeyPointer=-1)},Xn=()=>O.state.animatingPointer===F.LEVEL_ZERO.valueOf()&&O.state.routingKeyPointer>=0,Yn=()=>!Vt(),Vt=()=>O.state.routingKeyPointer===F.LEVEL_ZERO.valueOf()&&O.state.animatingPointer===F.LEVEL_ZERO.valueOf();function Jn(n){let e=D.state.models?he.MODELS:he.VEHICLE_PURCHASE,t=ut(O.state.breakpoint)?[]:[e];try{if(!M(n))return t;let[i,...o]=n.split(">"),r=D.state[Zn(i)];if(!r)return t;let a=Vn(r,o);return $n([i,...a])}catch{return t}}function $n(n){let e=[n[0]];for(let t=1;t<n.length;t+=1)e.push(`${e[t-1]}/${n[t]}`);return e}var Bn=1500,qn=`@keyframes translate-forward-in-animation{from{translate:0 12px}to{translate:0 0px}}@keyframes fade-in-animation{from{opacity:0;background-color:rgba(0, 0, 0, 0)}to{opacity:1;background-color:rgba(0, 0, 0, 0.6)}}@keyframes translate-in-from-left{from{translate:calc(var(--isLTR, 1) * -1000px)}to{translate:0px}}@keyframes slideDown{from{max-block-size:0}to{max-block-size:1000px}}@keyframes slideUp{from{max-block-size:1000px}to{max-block-size:0}}@keyframes slideLeft{from{max-inline-size:0}to{max-inline-size:1000px}}@keyframes slideRight{from{max-inline-size:1000px}to{max-inline-size:0}}@keyframes darken{from{background-color:rgba(0, 0, 0, 0)}to{background-color:rgba(0, 0, 0, 0.8)}}@keyframes lighten{from{background-color:rgba(0, 0, 0, 0.8)}to{background-color:rgba(0, 0, 0, 0)}}@keyframes fadeInFromBottom{0%{opacity:0;inset-block-start:36px}100%{opacity:1;inset-block-start:0px}}@keyframes fadeInFromTop{0%{opacity:0;inset-block-end:36px}100%{opacity:1;inset-block-end:0px}}@keyframes fadeOutToTop{0%{opacity:1;inset-block-end:0px}100%{opacity:0;inset-block-end:16px}}@keyframes slideLeftDoubleDrawer{from{inset-inline-end:-1000px}to{inset-inline-end:0px}}@keyframes slideRightDoubleDrawer{from{inset-inline-end:0px}to{inset-inline-end:-1000px}}@keyframes fadeIn{0%{opacity:0}100%{opacity:1}}@keyframes fadeOut{to{opacity:0}}.translate-forward-animation-in.sc-phn-level-0{opacity:1}@media (prefers-reduced-motion: no-preference){.translate-forward-animation-in.sc-phn-level-0{animation:translate-forward-in-animation 0.25s cubic-bezier(0.25, 0.1, 0.25, 1)}}@media (prefers-reduced-motion: no-preference){.translate-forward-animation-out.sc-phn-level-0,.translate-backward-animation-out.sc-phn-level-0{animation:fadeOut 0.25s cubic-bezier(0.25, 0.1, 0.25, 1)}}@media (prefers-reduced-motion: no-preference){.translate-backward-animation-in.sc-phn-level-0{animation:fadeIn 0.25s cubic-bezier(0.25, 0.1, 0.25, 1), translate-forward-in-animation 0.25s cubic-bezier(0.25, 0.1, 0.25, 1)}}@media (prefers-reduced-motion: no-preference){.mobile-transition-forward.sc-phn-level-0{animation:fadeIn 0.25s cubic-bezier(0.25, 0.1, 0.25, 1), translate-forward-in-animation 0.25s cubic-bezier(0.25, 0.1, 0.25, 1)}}.phn-flex-col.sc-phn-level-0{display:flex;flex-direction:column}.phn-flex-row.sc-phn-level-0{display:flex;flex-direction:row}.phn-align-center.sc-phn-level-0{align-items:center}.phn-justify-around.sc-phn-level-0{justify-content:space-around}.phn-justify-between.sc-phn-level-0{justify-content:space-between}.phn-flex-wrap.sc-phn-level-0{flex-wrap:wrap}.tooltip.sc-phn-level-0{position:relative;display:inline-block}.tooltip-text.sc-phn-level-0{visibility:hidden;inline-size:inherit;white-space:nowrap;background-color:#404044;border-radius:2px;font-size:13px;color:#fff;text-align:center;padding-block:0px;padding-inline:4px;position:absolute;z-index:100;inset-inline-start:50%;transform:translateX(calc(var(--isLTR, 1) * -50%))}.tooltip.sc-phn-level-0:hover .tooltip-text.sc-phn-level-0{visibility:visible}.tooltip-text.level0_icon.sc-phn-level-0{inset-block-end:-1.7em}.tooltip-text.search_icon.sc-phn-level-0{inset-block-end:-1.65em}.tooltip-text.shop_icon.sc-phn-level-0{inset-block-end:-1.5em}.tooltip-text.finder_icon.sc-phn-level-0{inset-block-end:-1.5em}.tooltip-text.logo.sc-phn-level-0{inset-block-end:-1.5em}.tooltip-text.account_icon.sc-phn-level-0{inset-block-end:-1.5em}.sc-phn-level-0-h{display:block}.hero_light-theme.themed-background.sc-phn-level-0-h{background:linear-gradient(to bottom, rgba(224, 224, 224, 0.9) 0%, rgba(224, 224, 224, 0.9) 20%, rgba(224, 224, 224, 0.852589) 26.67%, rgba(225, 225, 225, 0.768225) 33.33%, rgba(226, 226, 226, 0.668116) 40%, rgba(227, 227, 227, 0.557309) 46.67%, rgba(228, 228, 228, 0.442691) 53.33%, rgba(229, 229, 229, 0.331884) 60%, rgba(230, 230, 230, 0.231775) 66.67%, rgba(231, 231, 231, 0.147411) 73.33%, rgba(232, 232, 232, 0.0816599) 80%, rgba(232, 232, 232, 0.03551) 86.67%, rgba(232, 232, 232, 0.0086472) 93.33%, rgba(232, 232, 232, 0) 100%)}@media (min-width: 0px){.hero_light-theme.themed-background.sc-phn-level-0-h{block-size:72px}}@media (min-width: 760px){.hero_light-theme.themed-background.sc-phn-level-0-h{block-size:148px}}.hero_light-theme.sc-phn-level-0-h phn-p-icon.sc-phn-level-0{color:black}.hero_dark-theme.themed-background.sc-phn-level-0-h{background:linear-gradient(to bottom, hsla(0, 0%, 0%, 0.8) 0%, hsla(0, 0%, 0%, 0.8) 8.1%, hsla(0, 0%, 0%, 0.8) 15.5%, hsla(0, 0%, 0%, 0.8) 22.5%, hsla(0, 0%, 0%, 0.78) 29%, hsla(0, 0%, 0%, 0.73) 35.3%, hsla(0, 0%, 0%, 0.67) 41.2%, hsla(0, 0%, 0%, 0.6) 47.1%, hsla(0, 0%, 0%, 0.52) 52.9%, hsla(0, 0%, 0%, 0.44) 58.8%, hsla(0, 0%, 0%, 0.33) 64.7%, hsla(0, 0%, 0%, 0.22) 71%, hsla(0, 0%, 0%, 0.12) 77.5%, hsla(0, 0%, 0%, 0.05) 84.5%, hsla(0, 0%, 0%, 0.011) 91.9%, hsla(0, 0%, 0%, 0) 100%)}@media (min-width: 0px){.hero_dark-theme.themed-background.sc-phn-level-0-h{block-size:72px}}@media (min-width: 760px){.hero_dark-theme.themed-background.sc-phn-level-0-h{block-size:148px}}.hero_dark-theme.sc-phn-level-0-h phn-p-icon.sc-phn-level-0{color:white}.navbar-theme.themed-background.sc-phn-level-0-h{background:white;border-block-end:1px solid #e3e4e5}.navbar-theme.sc-phn-level-0-h phn-p-icon.sc-phn-level-0{color:black}.transparent_dark-theme.themed-background.sc-phn-level-0-h{background:transparent}.transparent_dark-theme.sc-phn-level-0-h phn-p-icon.sc-phn-level-0{color:white}.transparent_light-theme.themed-background.sc-phn-level-0-h{background:transparent}.transparent_light-theme.sc-phn-level-0-h phn-p-icon.sc-phn-level-0{color:white}@media (min-width: 1920px){.sc-phn-level-0-h header.limited-width.sc-phn-level-0{padding-block:0 calc(50vw - 2560px);padding-inline:calc(50vw - 2560px)}}.hide-show-on-scroll.sc-phn-level-0-h{position:fixed;inline-size:100%;transform:translateY(var(--country-correction, 0%));transition:transform 0.25s cubic-bezier(0.25, 0.1, 0.25, 1);will-change:transform}.hide-show-on-scroll.hide.sc-phn-level-0-h{transform:translateY(-100%)}.hide-show-on-scroll.navi-opened.sc-phn-level-0-h{inset-block-start:var(--country-height, 0px)}.hide-show-on-scroll.initial-country-load.sc-phn-level-0-h{inset-block-start:0}.sc-phn-level-0-h header.sc-phn-level-0{margin:0 var(--pds-internal-grid-margin, 0);display:grid;padding:0 calc(50% - var(--pds-internal-grid-margin, 0px) - 1280px);grid-gap:clamp(16px, 1.25vw + 12px, 36px);max-width:var(--pds-internal-grid-width-max, 2560px);min-width:var(--pds-internal-grid-width-min, 320px);box-sizing:content-box;grid-template-columns:[full-start] minmax(0, var(--pds-internal-grid-outer-column, calc(var(--pds-internal-grid-safe-zone) - clamp(16px, 1.25vw + 12px, 36px)))) [wide-start extended-start basic-start narrow-start] repeat(6, minmax(0, 1fr)) [narrow-end basic-end extended-end wide-end] minmax(0, var(--pds-internal-grid-outer-column, calc(var(--pds-internal-grid-safe-zone) - clamp(16px, 1.25vw + 12px, 36px)))) [full-end];--pds-internal-grid-safe-zone:max(22px, 10.625vw - 12px);--pds-grid-basic-span-one-half:span 3;--pds-grid-basic-span-one-third:span 2;--pds-grid-narrow-span-one-half:span 3;--pds-grid-basic-span-two-thirds:span 4;--pds-grid-extended-span-one-half:span 3;font-family:"Porsche Next", "Arial Narrow", Arial, "Heiti SC", SimHei, sans-serif;font-weight:400;color:#fff;block-size:4.125rem}@media (min-width: 760px){.sc-phn-level-0-h header.sc-phn-level-0{grid-template-columns:[full-start] minmax(0, var(--pds-internal-grid-outer-column, calc(var(--pds-internal-grid-safe-zone) - clamp(16px, 1.25vw + 12px, 36px)))) [wide-start] minmax(0, 1fr) [extended-start] minmax(0, 1fr) [basic-start] repeat(2, minmax(0, 1fr)) [narrow-start] repeat(8, minmax(0, 1fr)) [narrow-end] repeat(2, minmax(0, 1fr)) [basic-end] minmax(0, 1fr) [extended-end] minmax(0, 1fr) [wide-end] minmax(0, var(--pds-internal-grid-outer-column, calc(var(--pds-internal-grid-safe-zone) - clamp(16px, 1.25vw + 12px, 36px)))) [full-end];--pds-internal-grid-safe-zone:calc(5vw - 16px);--pds-grid-basic-span-one-half:span 6;--pds-grid-basic-span-one-third:span 4;--pds-grid-narrow-span-one-half:span 4;--pds-grid-basic-span-two-thirds:span 8;--pds-grid-extended-span-one-half:span 7}}@media (min-width: 1920px){.sc-phn-level-0-h header.sc-phn-level-0{--pds-internal-grid-safe-zone:min(50vw - 880px, 400px)}}@media (min-width: 480px){.sc-phn-level-0-h header.sc-phn-level-0{block-size:5rem}}@media (min-width: 760px){.sc-phn-level-0-h header.sc-phn-level-0{block-size:4.5625rem}}@media (min-width: 1000px){.sc-phn-level-0-h header.sc-phn-level-0{block-size:4.75rem}}@media (min-width: 1300px){.sc-phn-level-0-h header.sc-phn-level-0{block-size:5.125rem;flex-direction:row}}.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0{block-size:100%;display:flex;justify-content:center;align-items:center;position:relative;grid-column:wide-start/wide-end}.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .level-0-content-logo.sc-phn-level-0{display:flex;align-items:center}.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .level-0-content-icons.sc-phn-level-0{position:absolute;inline-size:100%}.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 phn-burger-button.sc-phn-level-0,.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 phn-level-0-icons.sc-phn-level-0,.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 phn-logo.sc-phn-level-0,.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .skeleton-container.sc-phn-level-0{flex:1;display:flex}.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .skeleton-container.crest-skeleton.sc-phn-level-0{justify-content:center}.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 phn-logo.sc-phn-level-0{justify-content:center;z-index:1;transform:translate3d(0, 0, 0)}@media (min-width: 1300px){.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 phn-logo.sc-phn-level-0{z-index:1}}.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .label-bone.sc-phn-level-0{inline-size:7.75rem;block-size:1.5rem;border-radius:0.25rem;background:#eeeff2 linear-gradient(to right, transparent 0%, #f7f7f7 20%, transparent 50%) 0 0/200% 100%;display:block;border-radius:4px;animation:skeletonAnimation 0.6s cubic-bezier(0.25, 0.1, 0.25, 1) infinite}@keyframes skeletonAnimation{from{background-position-x:100%}to{background-position-x:-100%}}@media (min-width: 760px){.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .label-bone.sc-phn-level-0{inline-size:18rem}}.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .label-bone-dark.sc-phn-level-0{inline-size:7.75rem;block-size:1.5rem;border-radius:0.25rem;background:#212225 linear-gradient(to right, transparent 0%, #1a1b1e 20%, transparent 50%) 0 0/200% 100%;display:block;border-radius:4px;animation:skeletonAnimation 0.6s cubic-bezier(0.25, 0.1, 0.25, 1) infinite}@keyframes skeletonAnimation{from{background-position-x:100%}to{background-position-x:-100%}}@media (min-width: 760px){.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .label-bone-dark.sc-phn-level-0{inline-size:18rem}}.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .crest-bone.sc-phn-level-0{inline-size:1.403125rem;block-size:1.8125rem;background-image:url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='22.45px' height='29px' viewBox='0 0 29 37' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Ecrest-path-pixel-perfect-3%3C/title%3E%3Cg id='Design-Focus' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='832-skeleton-hn-1366' transform='translate(-130.000000, -39.000000)' fill='%23eeeff2' fill-rule='nonzero'%3E%3Cg id='Crest-bones' transform='translate(78.000000, 0.000000)'%3E%3Cpath d='M80.8612951,41.6891698 C79.3593022,41.0929534 77.8195513,40.5958533 76.2520235,40.2010916 C74.092052,39.6781633 71.8946453,39.3227949 69.6795438,39.1381787 C68.6125828,39.0425165 67.6096394,39.0106291 66.4786608,39 L66.4679912,39 C65.4010302,39 64.3340692,39 63.2671082,39.1381787 C61.0520067,39.3227949 58.8545999,39.6781633 56.6946284,40.2010916 C55.1285058,40.5930516 53.5918758,41.093847 52.0960265,41.6997989 L52,41.6997989 C52,41.6997989 52.1813834,52.8710141 52.3200883,58.3981614 C52.3624958,60.0288438 52.6613178,61.6428163 53.2056659,63.1812698 C53.83596,64.927843 54.6779771,66.5912102 55.7130243,68.1344441 C56.7974563,69.7321996 58.1093136,71.164397 59.6074319,72.3860959 C60.1877267,72.8817079 60.8047619,73.3329574 61.4532745,73.7359954 C62.4748439,74.3534515 63.5453697,74.8866836 64.6541575,75.3303648 C65.0062546,75.4791727 65.3263429,75.6279805 65.6144224,75.7236426 C65.9077386,75.8428333 66.2112826,75.9353298 66.5213392,76 C66.8313958,75.9353298 67.1349399,75.8428333 67.4282561,75.7236426 C67.7163355,75.6279805 68.0364238,75.4791727 68.3991906,75.3303648 C69.4899033,74.8845355 70.542531,74.3513317 71.5467255,73.7359954 C72.1928274,73.3294237 72.8096114,72.8783579 73.3925681,72.3860959 C74.8933275,71.167176 76.2055628,69.7345659 77.2869757,68.1344441 C78.3220229,66.5912102 79.16404,64.927843 79.7943341,63.1812698 C80.3386822,61.6428163 80.6375042,60.0288438 80.6799117,58.3981614 C80.8186166,52.8710141 81,41.6997989 81,41.6997989 L80.8612951,41.6891698 Z' id='crest-path-pixel-perfect-3'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");background-repeat:no-repeat;background-position:center}@media (min-width: 480px){.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .crest-bone.sc-phn-level-0{inline-size:1.6875rem;block-size:2.25rem;background-image:url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='27px' height='36px' viewBox='0 0 29 37' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Ecrest-path-pixel-perfect-3%3C/title%3E%3Cg id='Design-Focus' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='832-skeleton-hn-1366' transform='translate(-130.000000, -39.000000)' fill='%23eeeff2' fill-rule='nonzero'%3E%3Cg id='Crest-bones' transform='translate(78.000000, 0.000000)'%3E%3Cpath d='M80.8612951,41.6891698 C79.3593022,41.0929534 77.8195513,40.5958533 76.2520235,40.2010916 C74.092052,39.6781633 71.8946453,39.3227949 69.6795438,39.1381787 C68.6125828,39.0425165 67.6096394,39.0106291 66.4786608,39 L66.4679912,39 C65.4010302,39 64.3340692,39 63.2671082,39.1381787 C61.0520067,39.3227949 58.8545999,39.6781633 56.6946284,40.2010916 C55.1285058,40.5930516 53.5918758,41.093847 52.0960265,41.6997989 L52,41.6997989 C52,41.6997989 52.1813834,52.8710141 52.3200883,58.3981614 C52.3624958,60.0288438 52.6613178,61.6428163 53.2056659,63.1812698 C53.83596,64.927843 54.6779771,66.5912102 55.7130243,68.1344441 C56.7974563,69.7321996 58.1093136,71.164397 59.6074319,72.3860959 C60.1877267,72.8817079 60.8047619,73.3329574 61.4532745,73.7359954 C62.4748439,74.3534515 63.5453697,74.8866836 64.6541575,75.3303648 C65.0062546,75.4791727 65.3263429,75.6279805 65.6144224,75.7236426 C65.9077386,75.8428333 66.2112826,75.9353298 66.5213392,76 C66.8313958,75.9353298 67.1349399,75.8428333 67.4282561,75.7236426 C67.7163355,75.6279805 68.0364238,75.4791727 68.3991906,75.3303648 C69.4899033,74.8845355 70.542531,74.3513317 71.5467255,73.7359954 C72.1928274,73.3294237 72.8096114,72.8783579 73.3925681,72.3860959 C74.8933275,71.167176 76.2055628,69.7345659 77.2869757,68.1344441 C78.3220229,66.5912102 79.16404,64.927843 79.7943341,63.1812698 C80.3386822,61.6428163 80.6375042,60.0288438 80.6799117,58.3981614 C80.8186166,52.8710141 81,41.6997989 81,41.6997989 L80.8612951,41.6891698 Z' id='crest-path-pixel-perfect-3'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E")}}@media (min-width: 760px){.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .crest-bone.sc-phn-level-0{inline-size:1.75rem;block-size:2.25rem;background-image:url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='28px' height='36px' viewBox='0 0 29 37' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Ecrest-path-pixel-perfect-3%3C/title%3E%3Cg id='Design-Focus' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='832-skeleton-hn-1366' transform='translate(-130.000000, -39.000000)' fill='%23eeeff2' fill-rule='nonzero'%3E%3Cg id='Crest-bones' transform='translate(78.000000, 0.000000)'%3E%3Cpath d='M80.8612951,41.6891698 C79.3593022,41.0929534 77.8195513,40.5958533 76.2520235,40.2010916 C74.092052,39.6781633 71.8946453,39.3227949 69.6795438,39.1381787 C68.6125828,39.0425165 67.6096394,39.0106291 66.4786608,39 L66.4679912,39 C65.4010302,39 64.3340692,39 63.2671082,39.1381787 C61.0520067,39.3227949 58.8545999,39.6781633 56.6946284,40.2010916 C55.1285058,40.5930516 53.5918758,41.093847 52.0960265,41.6997989 L52,41.6997989 C52,41.6997989 52.1813834,52.8710141 52.3200883,58.3981614 C52.3624958,60.0288438 52.6613178,61.6428163 53.2056659,63.1812698 C53.83596,64.927843 54.6779771,66.5912102 55.7130243,68.1344441 C56.7974563,69.7321996 58.1093136,71.164397 59.6074319,72.3860959 C60.1877267,72.8817079 60.8047619,73.3329574 61.4532745,73.7359954 C62.4748439,74.3534515 63.5453697,74.8866836 64.6541575,75.3303648 C65.0062546,75.4791727 65.3263429,75.6279805 65.6144224,75.7236426 C65.9077386,75.8428333 66.2112826,75.9353298 66.5213392,76 C66.8313958,75.9353298 67.1349399,75.8428333 67.4282561,75.7236426 C67.7163355,75.6279805 68.0364238,75.4791727 68.3991906,75.3303648 C69.4899033,74.8845355 70.542531,74.3513317 71.5467255,73.7359954 C72.1928274,73.3294237 72.8096114,72.8783579 73.3925681,72.3860959 C74.8933275,71.167176 76.2055628,69.7345659 77.2869757,68.1344441 C78.3220229,66.5912102 79.16404,64.927843 79.7943341,63.1812698 C80.3386822,61.6428163 80.6375042,60.0288438 80.6799117,58.3981614 C80.8186166,52.8710141 81,41.6997989 81,41.6997989 L80.8612951,41.6891698 Z' id='crest-path-pixel-perfect-3'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E")}}@media (min-width: 1000px){.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .crest-bone.sc-phn-level-0{inline-size:1.9375rem;block-size:2.5625rem;background-image:url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='31px' height='41px' viewBox='0 0 29 37' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Ecrest-path-pixel-perfect-3%3C/title%3E%3Cg id='Design-Focus' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='832-skeleton-hn-1366' transform='translate(-130.000000, -39.000000)' fill='%23eeeff2' fill-rule='nonzero'%3E%3Cg id='Crest-bones' transform='translate(78.000000, 0.000000)'%3E%3Cpath d='M80.8612951,41.6891698 C79.3593022,41.0929534 77.8195513,40.5958533 76.2520235,40.2010916 C74.092052,39.6781633 71.8946453,39.3227949 69.6795438,39.1381787 C68.6125828,39.0425165 67.6096394,39.0106291 66.4786608,39 L66.4679912,39 C65.4010302,39 64.3340692,39 63.2671082,39.1381787 C61.0520067,39.3227949 58.8545999,39.6781633 56.6946284,40.2010916 C55.1285058,40.5930516 53.5918758,41.093847 52.0960265,41.6997989 L52,41.6997989 C52,41.6997989 52.1813834,52.8710141 52.3200883,58.3981614 C52.3624958,60.0288438 52.6613178,61.6428163 53.2056659,63.1812698 C53.83596,64.927843 54.6779771,66.5912102 55.7130243,68.1344441 C56.7974563,69.7321996 58.1093136,71.164397 59.6074319,72.3860959 C60.1877267,72.8817079 60.8047619,73.3329574 61.4532745,73.7359954 C62.4748439,74.3534515 63.5453697,74.8866836 64.6541575,75.3303648 C65.0062546,75.4791727 65.3263429,75.6279805 65.6144224,75.7236426 C65.9077386,75.8428333 66.2112826,75.9353298 66.5213392,76 C66.8313958,75.9353298 67.1349399,75.8428333 67.4282561,75.7236426 C67.7163355,75.6279805 68.0364238,75.4791727 68.3991906,75.3303648 C69.4899033,74.8845355 70.542531,74.3513317 71.5467255,73.7359954 C72.1928274,73.3294237 72.8096114,72.8783579 73.3925681,72.3860959 C74.8933275,71.167176 76.2055628,69.7345659 77.2869757,68.1344441 C78.3220229,66.5912102 79.16404,64.927843 79.7943341,63.1812698 C80.3386822,61.6428163 80.6375042,60.0288438 80.6799117,58.3981614 C80.8186166,52.8710141 81,41.6997989 81,41.6997989 L80.8612951,41.6891698 Z' id='crest-path-pixel-perfect-3'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E")}}@media (min-width: 1300px){.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .crest-bone.sc-phn-level-0{inline-size:2rem;block-size:2.6875rem;background-image:url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='32px' height='43px' viewBox='0 0 29 37' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Ecrest-path-pixel-perfect-3%3C/title%3E%3Cg id='Design-Focus' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='832-skeleton-hn-1366' transform='translate(-130.000000, -39.000000)' fill='%23eeeff2' fill-rule='nonzero'%3E%3Cg id='Crest-bones' transform='translate(78.000000, 0.000000)'%3E%3Cpath d='M80.8612951,41.6891698 C79.3593022,41.0929534 77.8195513,40.5958533 76.2520235,40.2010916 C74.092052,39.6781633 71.8946453,39.3227949 69.6795438,39.1381787 C68.6125828,39.0425165 67.6096394,39.0106291 66.4786608,39 L66.4679912,39 C65.4010302,39 64.3340692,39 63.2671082,39.1381787 C61.0520067,39.3227949 58.8545999,39.6781633 56.6946284,40.2010916 C55.1285058,40.5930516 53.5918758,41.093847 52.0960265,41.6997989 L52,41.6997989 C52,41.6997989 52.1813834,52.8710141 52.3200883,58.3981614 C52.3624958,60.0288438 52.6613178,61.6428163 53.2056659,63.1812698 C53.83596,64.927843 54.6779771,66.5912102 55.7130243,68.1344441 C56.7974563,69.7321996 58.1093136,71.164397 59.6074319,72.3860959 C60.1877267,72.8817079 60.8047619,73.3329574 61.4532745,73.7359954 C62.4748439,74.3534515 63.5453697,74.8866836 64.6541575,75.3303648 C65.0062546,75.4791727 65.3263429,75.6279805 65.6144224,75.7236426 C65.9077386,75.8428333 66.2112826,75.9353298 66.5213392,76 C66.8313958,75.9353298 67.1349399,75.8428333 67.4282561,75.7236426 C67.7163355,75.6279805 68.0364238,75.4791727 68.3991906,75.3303648 C69.4899033,74.8845355 70.542531,74.3513317 71.5467255,73.7359954 C72.1928274,73.3294237 72.8096114,72.8783579 73.3925681,72.3860959 C74.8933275,71.167176 76.2055628,69.7345659 77.2869757,68.1344441 C78.3220229,66.5912102 79.16404,64.927843 79.7943341,63.1812698 C80.3386822,61.6428163 80.6375042,60.0288438 80.6799117,58.3981614 C80.8186166,52.8710141 81,41.6997989 81,41.6997989 L80.8612951,41.6891698 Z' id='crest-path-pixel-perfect-3'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E")}}.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .crest-bone-dark.sc-phn-level-0{inline-size:1.403125rem;block-size:1.8125rem;background-image:url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='22.45px' height='29px' viewBox='0 0 29 37' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Ecrest-path-pixel-perfect-3%3C/title%3E%3Cg id='Design-Focus' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='832-skeleton-hn-1366' transform='translate(-130.000000, -39.000000)' fill='%23212225' fill-rule='nonzero'%3E%3Cg id='Crest-bones' transform='translate(78.000000, 0.000000)'%3E%3Cpath d='M80.8612951,41.6891698 C79.3593022,41.0929534 77.8195513,40.5958533 76.2520235,40.2010916 C74.092052,39.6781633 71.8946453,39.3227949 69.6795438,39.1381787 C68.6125828,39.0425165 67.6096394,39.0106291 66.4786608,39 L66.4679912,39 C65.4010302,39 64.3340692,39 63.2671082,39.1381787 C61.0520067,39.3227949 58.8545999,39.6781633 56.6946284,40.2010916 C55.1285058,40.5930516 53.5918758,41.093847 52.0960265,41.6997989 L52,41.6997989 C52,41.6997989 52.1813834,52.8710141 52.3200883,58.3981614 C52.3624958,60.0288438 52.6613178,61.6428163 53.2056659,63.1812698 C53.83596,64.927843 54.6779771,66.5912102 55.7130243,68.1344441 C56.7974563,69.7321996 58.1093136,71.164397 59.6074319,72.3860959 C60.1877267,72.8817079 60.8047619,73.3329574 61.4532745,73.7359954 C62.4748439,74.3534515 63.5453697,74.8866836 64.6541575,75.3303648 C65.0062546,75.4791727 65.3263429,75.6279805 65.6144224,75.7236426 C65.9077386,75.8428333 66.2112826,75.9353298 66.5213392,76 C66.8313958,75.9353298 67.1349399,75.8428333 67.4282561,75.7236426 C67.7163355,75.6279805 68.0364238,75.4791727 68.3991906,75.3303648 C69.4899033,74.8845355 70.542531,74.3513317 71.5467255,73.7359954 C72.1928274,73.3294237 72.8096114,72.8783579 73.3925681,72.3860959 C74.8933275,71.167176 76.2055628,69.7345659 77.2869757,68.1344441 C78.3220229,66.5912102 79.16404,64.927843 79.7943341,63.1812698 C80.3386822,61.6428163 80.6375042,60.0288438 80.6799117,58.3981614 C80.8186166,52.8710141 81,41.6997989 81,41.6997989 L80.8612951,41.6891698 Z' id='crest-path-pixel-perfect-3'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");background-repeat:no-repeat;background-position:center}@media (min-width: 480px){.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .crest-bone-dark.sc-phn-level-0{inline-size:1.6875rem;block-size:2.25rem;background-image:url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='27px' height='36px' viewBox='0 0 29 37' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Ecrest-path-pixel-perfect-3%3C/title%3E%3Cg id='Design-Focus' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='832-skeleton-hn-1366' transform='translate(-130.000000, -39.000000)' fill='%23212225' fill-rule='nonzero'%3E%3Cg id='Crest-bones' transform='translate(78.000000, 0.000000)'%3E%3Cpath d='M80.8612951,41.6891698 C79.3593022,41.0929534 77.8195513,40.5958533 76.2520235,40.2010916 C74.092052,39.6781633 71.8946453,39.3227949 69.6795438,39.1381787 C68.6125828,39.0425165 67.6096394,39.0106291 66.4786608,39 L66.4679912,39 C65.4010302,39 64.3340692,39 63.2671082,39.1381787 C61.0520067,39.3227949 58.8545999,39.6781633 56.6946284,40.2010916 C55.1285058,40.5930516 53.5918758,41.093847 52.0960265,41.6997989 L52,41.6997989 C52,41.6997989 52.1813834,52.8710141 52.3200883,58.3981614 C52.3624958,60.0288438 52.6613178,61.6428163 53.2056659,63.1812698 C53.83596,64.927843 54.6779771,66.5912102 55.7130243,68.1344441 C56.7974563,69.7321996 58.1093136,71.164397 59.6074319,72.3860959 C60.1877267,72.8817079 60.8047619,73.3329574 61.4532745,73.7359954 C62.4748439,74.3534515 63.5453697,74.8866836 64.6541575,75.3303648 C65.0062546,75.4791727 65.3263429,75.6279805 65.6144224,75.7236426 C65.9077386,75.8428333 66.2112826,75.9353298 66.5213392,76 C66.8313958,75.9353298 67.1349399,75.8428333 67.4282561,75.7236426 C67.7163355,75.6279805 68.0364238,75.4791727 68.3991906,75.3303648 C69.4899033,74.8845355 70.542531,74.3513317 71.5467255,73.7359954 C72.1928274,73.3294237 72.8096114,72.8783579 73.3925681,72.3860959 C74.8933275,71.167176 76.2055628,69.7345659 77.2869757,68.1344441 C78.3220229,66.5912102 79.16404,64.927843 79.7943341,63.1812698 C80.3386822,61.6428163 80.6375042,60.0288438 80.6799117,58.3981614 C80.8186166,52.8710141 81,41.6997989 81,41.6997989 L80.8612951,41.6891698 Z' id='crest-path-pixel-perfect-3'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E")}}@media (min-width: 760px){.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .crest-bone-dark.sc-phn-level-0{inline-size:1.75rem;block-size:2.25rem;background-image:url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='28px' height='36px' viewBox='0 0 29 37' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Ecrest-path-pixel-perfect-3%3C/title%3E%3Cg id='Design-Focus' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='832-skeleton-hn-1366' transform='translate(-130.000000, -39.000000)' fill='%23212225' fill-rule='nonzero'%3E%3Cg id='Crest-bones' transform='translate(78.000000, 0.000000)'%3E%3Cpath d='M80.8612951,41.6891698 C79.3593022,41.0929534 77.8195513,40.5958533 76.2520235,40.2010916 C74.092052,39.6781633 71.8946453,39.3227949 69.6795438,39.1381787 C68.6125828,39.0425165 67.6096394,39.0106291 66.4786608,39 L66.4679912,39 C65.4010302,39 64.3340692,39 63.2671082,39.1381787 C61.0520067,39.3227949 58.8545999,39.6781633 56.6946284,40.2010916 C55.1285058,40.5930516 53.5918758,41.093847 52.0960265,41.6997989 L52,41.6997989 C52,41.6997989 52.1813834,52.8710141 52.3200883,58.3981614 C52.3624958,60.0288438 52.6613178,61.6428163 53.2056659,63.1812698 C53.83596,64.927843 54.6779771,66.5912102 55.7130243,68.1344441 C56.7974563,69.7321996 58.1093136,71.164397 59.6074319,72.3860959 C60.1877267,72.8817079 60.8047619,73.3329574 61.4532745,73.7359954 C62.4748439,74.3534515 63.5453697,74.8866836 64.6541575,75.3303648 C65.0062546,75.4791727 65.3263429,75.6279805 65.6144224,75.7236426 C65.9077386,75.8428333 66.2112826,75.9353298 66.5213392,76 C66.8313958,75.9353298 67.1349399,75.8428333 67.4282561,75.7236426 C67.7163355,75.6279805 68.0364238,75.4791727 68.3991906,75.3303648 C69.4899033,74.8845355 70.542531,74.3513317 71.5467255,73.7359954 C72.1928274,73.3294237 72.8096114,72.8783579 73.3925681,72.3860959 C74.8933275,71.167176 76.2055628,69.7345659 77.2869757,68.1344441 C78.3220229,66.5912102 79.16404,64.927843 79.7943341,63.1812698 C80.3386822,61.6428163 80.6375042,60.0288438 80.6799117,58.3981614 C80.8186166,52.8710141 81,41.6997989 81,41.6997989 L80.8612951,41.6891698 Z' id='crest-path-pixel-perfect-3'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E")}}@media (min-width: 1000px){.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .crest-bone-dark.sc-phn-level-0{inline-size:1.9375rem;block-size:2.5625rem;background-image:url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='31px' height='41px' viewBox='0 0 29 37' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Ecrest-path-pixel-perfect-3%3C/title%3E%3Cg id='Design-Focus' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='832-skeleton-hn-1366' transform='translate(-130.000000, -39.000000)' fill='%23212225' fill-rule='nonzero'%3E%3Cg id='Crest-bones' transform='translate(78.000000, 0.000000)'%3E%3Cpath d='M80.8612951,41.6891698 C79.3593022,41.0929534 77.8195513,40.5958533 76.2520235,40.2010916 C74.092052,39.6781633 71.8946453,39.3227949 69.6795438,39.1381787 C68.6125828,39.0425165 67.6096394,39.0106291 66.4786608,39 L66.4679912,39 C65.4010302,39 64.3340692,39 63.2671082,39.1381787 C61.0520067,39.3227949 58.8545999,39.6781633 56.6946284,40.2010916 C55.1285058,40.5930516 53.5918758,41.093847 52.0960265,41.6997989 L52,41.6997989 C52,41.6997989 52.1813834,52.8710141 52.3200883,58.3981614 C52.3624958,60.0288438 52.6613178,61.6428163 53.2056659,63.1812698 C53.83596,64.927843 54.6779771,66.5912102 55.7130243,68.1344441 C56.7974563,69.7321996 58.1093136,71.164397 59.6074319,72.3860959 C60.1877267,72.8817079 60.8047619,73.3329574 61.4532745,73.7359954 C62.4748439,74.3534515 63.5453697,74.8866836 64.6541575,75.3303648 C65.0062546,75.4791727 65.3263429,75.6279805 65.6144224,75.7236426 C65.9077386,75.8428333 66.2112826,75.9353298 66.5213392,76 C66.8313958,75.9353298 67.1349399,75.8428333 67.4282561,75.7236426 C67.7163355,75.6279805 68.0364238,75.4791727 68.3991906,75.3303648 C69.4899033,74.8845355 70.542531,74.3513317 71.5467255,73.7359954 C72.1928274,73.3294237 72.8096114,72.8783579 73.3925681,72.3860959 C74.8933275,71.167176 76.2055628,69.7345659 77.2869757,68.1344441 C78.3220229,66.5912102 79.16404,64.927843 79.7943341,63.1812698 C80.3386822,61.6428163 80.6375042,60.0288438 80.6799117,58.3981614 C80.8186166,52.8710141 81,41.6997989 81,41.6997989 L80.8612951,41.6891698 Z' id='crest-path-pixel-perfect-3'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E")}}@media (min-width: 1300px){.sc-phn-level-0-h header.sc-phn-level-0 .level-0-content.sc-phn-level-0 .crest-bone-dark.sc-phn-level-0{inline-size:2rem;block-size:2.6875rem;background-image:url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='32px' height='43px' viewBox='0 0 29 37' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Ecrest-path-pixel-perfect-3%3C/title%3E%3Cg id='Design-Focus' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='832-skeleton-hn-1366' transform='translate(-130.000000, -39.000000)' fill='%23212225' fill-rule='nonzero'%3E%3Cg id='Crest-bones' transform='translate(78.000000, 0.000000)'%3E%3Cpath d='M80.8612951,41.6891698 C79.3593022,41.0929534 77.8195513,40.5958533 76.2520235,40.2010916 C74.092052,39.6781633 71.8946453,39.3227949 69.6795438,39.1381787 C68.6125828,39.0425165 67.6096394,39.0106291 66.4786608,39 L66.4679912,39 C65.4010302,39 64.3340692,39 63.2671082,39.1381787 C61.0520067,39.3227949 58.8545999,39.6781633 56.6946284,40.2010916 C55.1285058,40.5930516 53.5918758,41.093847 52.0960265,41.6997989 L52,41.6997989 C52,41.6997989 52.1813834,52.8710141 52.3200883,58.3981614 C52.3624958,60.0288438 52.6613178,61.6428163 53.2056659,63.1812698 C53.83596,64.927843 54.6779771,66.5912102 55.7130243,68.1344441 C56.7974563,69.7321996 58.1093136,71.164397 59.6074319,72.3860959 C60.1877267,72.8817079 60.8047619,73.3329574 61.4532745,73.7359954 C62.4748439,74.3534515 63.5453697,74.8866836 64.6541575,75.3303648 C65.0062546,75.4791727 65.3263429,75.6279805 65.6144224,75.7236426 C65.9077386,75.8428333 66.2112826,75.9353298 66.5213392,76 C66.8313958,75.9353298 67.1349399,75.8428333 67.4282561,75.7236426 C67.7163355,75.6279805 68.0364238,75.4791727 68.3991906,75.3303648 C69.4899033,74.8845355 70.542531,74.3513317 71.5467255,73.7359954 C72.1928274,73.3294237 72.8096114,72.8783579 73.3925681,72.3860959 C74.8933275,71.167176 76.2055628,69.7345659 77.2869757,68.1344441 C78.3220229,66.5912102 79.16404,64.927843 79.7943341,63.1812698 C80.3386822,61.6428163 80.6375042,60.0288438 80.6799117,58.3981614 C80.8186166,52.8710141 81,41.6997989 81,41.6997989 L80.8612951,41.6891698 Z' id='crest-path-pixel-perfect-3'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E")}}.sc-phn-level-0-h header.sc-phn-level-0 phn-crest.sc-phn-level-0{z-index:1;transform:translate3d(0, 0, 0)}@media (min-width: 1300px){.sc-phn-level-0-h header.sc-phn-level-0 phn-crest.sc-phn-level-0{z-index:1}}.sc-phn-level-0-h header.sc-phn-level-0 .burger-button.sc-phn-level-0:focus{outline-width:2px;outline-style:solid;outline-offset:1px;outline-color:#1a44ea}.sc-phn-level-0-h .main-content.sc-phn-level-0{inline-size:1.25rem}.sc-phn-level-0-h .main-content.sc-phn-level-0:focus{outline-style:none}.sc-phn-level-0-h .sr-only.sc-phn-level-0{position:absolute;inline-size:0.0625rem;block-size:0.0625rem;white-space:nowrap;overflow:hidden;clip:rect(1px, 1px, 1px, 1px);clip-path:inset(50%)}.sc-phn-level-0-h phn-p-icon.sc-phn-level-0{inline-size:1.5rem}@media (min-width: 760px){.sc-phn-level-0-h phn-p-icon.sc-phn-level-0{inline-size:1.5rem}}@media (min-width: 1000px){.sc-phn-level-0-h phn-p-icon.sc-phn-level-0{inline-size:1.5rem}}.sc-phn-level-0-h:not(:has(.contextual-icons)) phn-level-0-icons.sc-phn-level-0{place-content:center}`,Qn=qn,pi=(()=>{let n=class{constructor(e){pe(this,e),this.navigationLoaded=De(this,"navigationLoaded",7),this.loadingContent=!1,this.receivedContent=!1,this.theme=se.light,this.mode=B.navbar,this.app=ce.default,this.displayLogoOnly=!1,this.limitedWidth=!1,this.usingKeyboard=!1,this.scrolledPastHeader=!1,this.isNavigationLoaded=!1,this.isLoading=!1,this.isAccountHoveredOn=!1,this.eventController=new AbortController,this.drawerTimeout=new we(Bn)}getCountryHeight(){this.setCountryHeightCorrection(!1)}hideShowOnScrollWatcher(e){this.connectedCallback(),e||this.eventController.abort()}loadingContentWatcher(){this.setNavigationLoaded()}breakpointWatcher(){this.updateRoutingKeyPointerOfMobileLevel1(),this.setNavigationLoaded()}receivedContentWatcher(){this.setNavigationLoaded()}showCountryRecommenderWatcher(e){e?this.setCountryHeightCorrection():(this.el.style.removeProperty("--country-correction"),this.el.style.removeProperty("--country-height"))}setCountryHeightCorrection(e=!0){let t=pt("phn-country-recommender");t?(e?Promise.all(t.getAnimations().map(i=>i.finished)):Promise.resolve()).then(()=>{this.recommenderHeight=t.offsetHeight,this.el.style.setProperty("--country-height",`${this.recommenderHeight}px`)}):setTimeout(()=>this.setCountryHeightCorrection(),50)}scrollHandler(){this.scrolledPastHeader=this.el.getBoundingClientRect().bottom<=0}handleKeyDown(e){e.key==="Escape"&&(this.usingKeyboard=!0,v.state.usingKeyboard=!0,vt(),Vt()||Hn())}handleClick(e){this.usingKeyboard=Se(e),v.state.usingKeyboard=Se(e)}handleOpenDrawer(e){return p(this,null,function*(){yield this.openDrawer(e.detail)})}componentWillLoad(){this.setNavigationLoaded()}updateRoutingKeyPointerOfMobileLevel1(){O.state.routingKeyPointer===F.LEVEL_ONE.valueOf()&&(O.state.routingKeyPointer=Oe(this.breakpoint)?F.LEVEL_TWO:F.LEVEL_ONE)}setNavigationLoaded(){this.isNavigationLoaded=!this.loadingContent,this.navigationLoaded.emit(this.isNavigationLoaded)}openDrawer(e){return p(this,null,function*(){let{initialRoutingKey:t,isKeyboardClick:i}=e;this.isLoading=!0,this.usingKeyboard=i,v.state.usingKeyboard=i;let o="phn-nd-backdrop",r=t===""?v.state.initialRoutingPath:t;this.openMenuDrawer(r??"");try{yield Nn([this.el,".drawer",o],this.drawerTimeout)}finally{this.isLoading=!1}})}openMenuDrawer(e){wt(e),Zt(...Jn(e))}connectedCallback(){this.hideShowOnScroll&&setTimeout(()=>{try{let o=function(){t||(requestAnimationFrame(i),t=!0)},r=function(){let a=window.scrollY;if(this.isLoading){this.el.classList.add("navi-opened"),t=!1;return}this.el.classList.remove("navi-opened"),Math.abs(a-e)>3&&(a>e?(this.el.classList.add("hide"),this.el.dataset.pinned="false",this.el.dispatchEvent(new CustomEvent("headerPinned",{composed:!0,detail:!1})),this.el.classList.contains("initial-country-load")&&this.el.addEventListener("transitionend",()=>this.el.classList.remove("initial-country-load"),{once:!0})):(this.el.classList.remove("hide"),this.el.dataset.pinned="true",this.el.dispatchEvent(new CustomEvent("headerPinned",{composed:!0,detail:!0})))),this.showCountryRecommender&&this.el.style.setProperty("--country-correction",`-${Math.min(this.recommenderHeight??0,a)}px`),e=a,t=!1},e=window.scrollY,t=!1,i=r.bind(this);this.initialShowCountryRecommender&&(e&&this.el.classList.add("initial-country-load"),this.setCountryHeightCorrection()),window.addEventListener("scroll",o,{passive:!0,signal:this.eventController.signal})}catch{}},1e3)}disconnectedCallback(){this.drawerTimeout.cancel(),this.eventController.abort()}getLogoSize(){return this.breakpoint>=gt.m?"medium":"small"}getLogo(){let{shop:e,level0:t,crest:i}=D.state;return y("div",{class:"level-0-content-logo tooltip"},y("phn-logo",{app:this.app,theme:this.theme,mode:this.mode,size:this.getLogoSize(),breakpoint:this.breakpoint}),i&&Oe(this.breakpoint)?y("span",{class:"tooltip-text logo"},Et(this.locale,this.app,e,t)):null)}render(){return y(Ne,{key:"a09dce8d7519baab477302f689728d48ca97d2ae",class:`${kt(this.mode,this.theme)}-theme themed-background ${this.hideShowOnScroll?"hide-show-on-scroll":"default"}`},y("header",{key:"127e44252920e15cc6d539ddafdbc8a46181a4a7",class:this.limitedWidth?"limited-width":""},Yn()||Xn()?y("phn-backdrop",{style:this.isLoading?{display:"none"}:{}}):null,y("div",{key:"831462cab30280eba760ce4f4e6cf5b051abedfb",class:"level-0-content"},this.displayLogoOnly?this.getLogo():y("div",{class:"level-0-content-icons"},y("phn-level-0-icons",{breakpoint:this.breakpoint,isLoading:this.isLoading,isNavigationLoaded:this.isNavigationLoaded},this.getLogo())))))}get el(){return ze(this)}static get watchers(){return{hideShowOnScroll:["hideShowOnScrollWatcher"],loadingContent:["loadingContentWatcher"],breakpoint:["breakpointWatcher"],receivedContent:["receivedContentWatcher"],showCountryRecommender:["showCountryRecommenderWatcher"]}}};return n.style=Qn,n})(),ei=()=>{let n={navContent:P({},D.state),navStateContent:P({},v.state),shopStoreContent:P({},Le.state),finderStoreContent:P({},de.state),featureToggleStateStore:P({},oe.state)};return y("phn-nd-navigation-drawer-initiater",P({},n),y("phn-nd-contextual-drawer",null,y("div",{slot:"contextual_drawer_header"},y("slot",{name:"contextual_drawer_header"}))))},ti="@keyframes translate-forward-in-animation{from{translate:0 12px}to{translate:0 0px}}@keyframes fade-in-animation{from{opacity:0;background-color:rgba(0, 0, 0, 0)}to{opacity:1;background-color:rgba(0, 0, 0, 0.6)}}@keyframes translate-in-from-left{from{translate:calc(var(--isLTR, 1) * -1000px)}to{translate:0px}}@keyframes slideDown{from{max-block-size:0}to{max-block-size:1000px}}@keyframes slideUp{from{max-block-size:1000px}to{max-block-size:0}}@keyframes slideLeft{from{max-inline-size:0}to{max-inline-size:1000px}}@keyframes slideRight{from{max-inline-size:1000px}to{max-inline-size:0}}@keyframes darken{from{background-color:rgba(0, 0, 0, 0)}to{background-color:rgba(0, 0, 0, 0.8)}}@keyframes lighten{from{background-color:rgba(0, 0, 0, 0.8)}to{background-color:rgba(0, 0, 0, 0)}}@keyframes fadeInFromBottom{0%{opacity:0;inset-block-start:36px}100%{opacity:1;inset-block-start:0px}}@keyframes fadeInFromTop{0%{opacity:0;inset-block-end:36px}100%{opacity:1;inset-block-end:0px}}@keyframes fadeOutToTop{0%{opacity:1;inset-block-end:0px}100%{opacity:0;inset-block-end:16px}}@keyframes slideLeftDoubleDrawer{from{inset-inline-end:-1000px}to{inset-inline-end:0px}}@keyframes slideRightDoubleDrawer{from{inset-inline-end:0px}to{inset-inline-end:-1000px}}@keyframes fadeIn{0%{opacity:0}100%{opacity:1}}@keyframes fadeOut{to{opacity:0}}.translate-forward-animation-in.sc-phn-wrapper{opacity:1}@media (prefers-reduced-motion: no-preference){.translate-forward-animation-in.sc-phn-wrapper{animation:translate-forward-in-animation 0.25s cubic-bezier(0.25, 0.1, 0.25, 1)}}@media (prefers-reduced-motion: no-preference){.translate-forward-animation-out.sc-phn-wrapper,.translate-backward-animation-out.sc-phn-wrapper{animation:fadeOut 0.25s cubic-bezier(0.25, 0.1, 0.25, 1)}}@media (prefers-reduced-motion: no-preference){.translate-backward-animation-in.sc-phn-wrapper{animation:fadeIn 0.25s cubic-bezier(0.25, 0.1, 0.25, 1), translate-forward-in-animation 0.25s cubic-bezier(0.25, 0.1, 0.25, 1)}}@media (prefers-reduced-motion: no-preference){.mobile-transition-forward.sc-phn-wrapper{animation:fadeIn 0.25s cubic-bezier(0.25, 0.1, 0.25, 1), translate-forward-in-animation 0.25s cubic-bezier(0.25, 0.1, 0.25, 1)}}.phn-flex-col.sc-phn-wrapper{display:flex;flex-direction:column}.phn-flex-row.sc-phn-wrapper{display:flex;flex-direction:row}.phn-align-center.sc-phn-wrapper{align-items:center}.phn-justify-around.sc-phn-wrapper{justify-content:space-around}.phn-justify-between.sc-phn-wrapper{justify-content:space-between}.phn-flex-wrap.sc-phn-wrapper{flex-wrap:wrap}.tooltip.sc-phn-wrapper{position:relative;display:inline-block}.tooltip-text.sc-phn-wrapper{visibility:hidden;inline-size:inherit;white-space:nowrap;background-color:#404044;border-radius:2px;font-size:13px;color:#fff;text-align:center;padding-block:0px;padding-inline:4px;position:absolute;z-index:100;inset-inline-start:50%;transform:translateX(calc(var(--isLTR, 1) * -50%))}.tooltip.sc-phn-wrapper:hover .tooltip-text.sc-phn-wrapper{visibility:visible}.tooltip-text.level0_icon.sc-phn-wrapper{inset-block-end:-1.7em}.tooltip-text.search_icon.sc-phn-wrapper{inset-block-end:-1.65em}.tooltip-text.shop_icon.sc-phn-wrapper{inset-block-end:-1.5em}.tooltip-text.finder_icon.sc-phn-wrapper{inset-block-end:-1.5em}.tooltip-text.logo.sc-phn-wrapper{inset-block-end:-1.5em}.tooltip-text.account_icon.sc-phn-wrapper{inset-block-end:-1.5em}.sc-phn-wrapper-h{display:block}.sc-phn-wrapper-h .limit-stretch.sc-phn-wrapper{box-sizing:content-box;margin-block:0;margin-inline:0.5vw}.sc-phn-wrapper-h .main-content.sc-phn-wrapper{inline-size:20px}.sc-phn-wrapper-h .main-content.sc-phn-wrapper:focus{outline-style:none}.sc-phn-wrapper-h .screenreader-only.sc-phn-wrapper{position:absolute;inline-size:1px;block-size:1px;white-space:nowrap;overflow:hidden;clip:rect(1px, 1px, 1px, 1px);clip-path:inset(50%)}.sc-phn-wrapper-h:has(phn-country-recommender) .hide-show-on-scroll.hide.sc-phn-wrapper{transform:translateY(calc(-100% - var(--country-height, 0px)))}",ni=ti,ui=(()=>{let n=class{constructor(e){pe(this,e),this.loadingContent=!1,this.receivedContent=!1,this.theme=se.light,this.mode=B.navbar,this.app=ce.default,this.displayLogoOnly=!1,this.limitedWidth=!1,this.showCountryRecommender=_.redirected,this.countryRecommenderClosed=!1}componentWillLoad(){this.el.style.setProperty("--scrollbar-width",_.getScrollbarWidth()),_.hasScrollbar()&&(document.documentElement.style.scrollbarGutter="stable"),ht(this.el),nt.initialize()}render(){let{shop:e,countryRecommender:t,accessibilityStatement:i}=D.state,o=this.showCountryRecommender&&!this.countryRecommenderClosed&&this.receivedContent;return y(Ne,{key:"43afbeaeeefed1b1a241616dda73adfe6344ada7"},o?y("phn-country-recommender",{locale:this.locale,content:t,role:"region","aria-label":"Change recommended Region or Language",onBannerClosed:()=>{this.countryRecommenderClosed=!0}}):null,this.locale==="en-US"&&i!==null?y("a",{"aria-label":"accessibility statement",class:"screenreader-only",href:i.link},i.text):null,y("phn-level-0",{key:"efa23ae2f37bb1b95a2c9b0feb8d4710d4e8a720",theme:this.theme,mode:this.mode,locale:this.locale,loadingContent:this.loadingContent,receivedContent:this.receivedContent,breakpoint:this.breakpoint,app:this.app,displayLogoOnly:this.displayLogoOnly,limitedWidth:this.limitedWidth,hideShowOnScroll:this.hideShowOnScroll,initialShowCountryRecommender:this.showCountryRecommender,showCountryRecommender:o},Re(e,this.locale,this.app)?y("div",{slot:"contextual_drawer_header"},y("slot",{name:"contextual_drawer_header"})):null),y(ei,{key:"83e821f4b656011b84821d2bc257faaf81e15338"}),y("div",{key:"cb6b1e253d763ae29f7c97eff36996e284abc974",id:"main",class:"main-content",onBlur:r=>{let a=Q(r.currentTarget);a!==null&&a.removeAttribute("tabIndex")}}))}get el(){return ze(this)}};return n.style=ni,n})();export{hi as phn_header,pi as phn_level_0,ui as phn_wrapper};
//# sourceMappingURL=chunk-PACUCIOJ.js.map
