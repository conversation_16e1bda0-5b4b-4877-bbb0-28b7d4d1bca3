( () => {
    "use strict";
    var t = {
        d: (e, n) => {
            for (var r in n)
                t.o(n, r) && !t.o(e, r) && Object.defineProperty(e, r, {
                    enumerable: !0,
                    get: n[r]
                })
        }
        ,
        o: (t, e) => Object.prototype.hasOwnProperty.call(t, e),
        r: t => {
            "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(t, Symbol.toStringTag, {
                value: "Module"
            }),
            Object.defineProperty(t, "__esModule", {
                value: !0
            })
        }
    }
      , e = {};
    t.r(e),
    t.d(e, {
        clickOut: () => q,
        openLink: () => Z,
        openPanel: () => j,
        rotation: () => z,
        sendAction: () => F,
        sendError: () => D,
        sendEvent: () => U,
        sendTiming: () => B,
        showDropzone: () => G,
        startJurassicWorldTransferBoxEffect: () => W,
        timer: () => H,
        ui: () => L,
        vast: () => M
    });
    const n = t => {
        if (!t)
            return;
        (new Image).setAttribute("src", t)
    }
    ;
    function r(t) {
        for (var e = 1; e < arguments.length; e++) {
            var n = arguments[e];
            for (var r in n)
                t[r] = n[r]
        }
        return t
    }
    (function t(e, n) {
        function i(t, i, o) {
            if ("undefined" != typeof document) {
                "number" == typeof (o = r({}, n, o)).expires && (o.expires = new Date(Date.now() + 864e5 * o.expires)),
                o.expires && (o.expires = o.expires.toUTCString()),
                t = encodeURIComponent(t).replace(/%(2[346B]|5E|60|7C)/g, decodeURIComponent).replace(/[()]/g, escape);
                var a = "";
                for (var s in o)
                    o[s] && (a += "; " + s,
                    !0 !== o[s] && (a += "=" + o[s].split(";")[0]));
                return document.cookie = t + "=" + e.write(i, t) + a
            }
        }
        return Object.create({
            set: i,
            get: function(t) {
                if ("undefined" != typeof document && (!arguments.length || t)) {
                    for (var n = document.cookie ? document.cookie.split("; ") : [], r = {}, i = 0; i < n.length; i++) {
                        var o = n[i].split("=")
                          , a = o.slice(1).join("=");
                        try {
                            var s = decodeURIComponent(o[0]);
                            if (r[s] = e.read(a, s),
                            t === s)
                                break
                        } catch (u) {}
                    }
                    return t ? r[t] : r
                }
            },
            remove: function(t, e) {
                i(t, "", r({}, e, {
                    expires: -1
                }))
            },
            withAttributes: function(e) {
                return t(this.converter, r({}, this.attributes, e))
            },
            withConverter: function(e) {
                return t(r({}, this.converter, e), this.attributes)
            }
        }, {
            attributes: {
                value: Object.freeze(n)
            },
            converter: {
                value: Object.freeze(e)
            }
        })
    }
    )({
        read: function(t) {
            return '"' === t[0] && (t = t.slice(1, -1)),
            t.replace(/(%[\dA-F]{2})+/gi, decodeURIComponent)
        },
        write: function(t) {
            return encodeURIComponent(t).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g, decodeURIComponent)
        }
    }, {
        path: "/"
    });
    const i = {
        clientTracking: {},
        timings: [],
        initFromClient(t) {
            this.clientTracking = {
                ...t
            }
        },
        init({envName: t, serviceName: e, version: n, sampleRate: r, applicationId: i, clientToken: o, useCrossSiteSessionCookie: a=!1}) {
            i && o && window.DD_RUM && window.DD_RUM.init({
                applicationId: i,
                clientToken: o,
                site: "datadoghq.eu",
                service: e,
                env: t,
                version: n,
                sessionSampleRate: r,
                useCrossSiteSessionCookie: a
            })
        },
        trackError(t, e) {
            return this.clientTracking.trackError ? this.clientTracking.trackError(t, e) : window.DD_RUM ? window.DD_RUM.addError(t, e) : void 0
        },
        addMonitorAction({name: t, context: e, forceRUMLogging: n}) {
            return !0 !== n && this.clientTracking.addAction ? this.clientTracking.addAction(t, e) : window.DD_RUM ? window.DD_RUM.addAction(t, e) : void 0
        },
        enqueueMonitorTiming(t) {
            this.timings.push({
                key: t,
                time: performance.now()
            })
        },
        addMonitorTiming(t) {
            return this.clientTracking.addTiming ? this.clientTracking.addTiming(t) : window.DD_RUM ? window.DD_RUM.addTiming(t) : void 0
        },
        publishTimingQueue() {
            for (const t of this.timings)
                this.clientTracking.addTiming ? this.clientTracking.addTiming(t.key, t.time) : window.DD_RUM && window.DD_RUM.addTiming(t.key, t.time);
            this.timings = []
        }
    };
    let o = function(t) {
        return t.Local = "local",
        t.Development = "development",
        t.Staging = "staging",
        t.Production = "production",
        t
    }({});
    const a = {
        start: 70,
        firstQuartile: 71,
        midpoint: 72,
        thirdQuartile: 73,
        complete: 74,
        mute: 75,
        unmute: 76,
        pause: 77,
        resume: 79
    };
    let s = function(t) {
        return t.Image = "image",
        t.Html = "html",
        t
    }({});
    const u = async t => {
        const {url: e, requestInit: n} = (t => {
            const e = t.method ?? "get"
              , n = "get" === e
              , r = t.query || {}
              , i = n ? null : t.params
              , o = n ? {
                ...r,
                ...t.params
            } : r
              , a = new URL(t.path);
            return a.search = new URLSearchParams(o).toString(),
            {
                url: a.toString(),
                requestInit: {
                    method: e,
                    headers: {
                        ...t.headers
                    },
                    signal: t.timeout ? AbortSignal.timeout(t.timeout) : void 0,
                    body: null === i ? null : JSON.stringify(i),
                    credentials: t.withCredentials ? "include" : void 0
                }
            }
        }
        )(t);
        try {
            return (async t => ({
                status: t.status,
                statusText: t.statusText,
                ok: !0,
                aborted: !1,
                body: await t.json()
            }))(await fetch(e, n))
        } catch (i) {
            throw r = i,
            {
                ...r,
                body: r?.response?.data ?? {},
                status: r?.response?.status ?? 0,
                statusText: r?.response?.statusText ?? "",
                ok: !1
            }
        }
        var r
    }
    ;
    const c = {
        CANT_VALIDATE_ORIGIN: "Can't validate origin! please add ?_origin=PARENT_HOST to the iframe source",
        CANT_VALIDATE_PLACEMENT: "Can't validate placement! please add ?_placement=PLACEMENT_NAME to the iframe source",
        CANT_USE_READY_COMMAND: "ready is a reserved command",
        EMPTY_IFRAME: "Not src found. You can't run ParentFrame on an empty iframe element",
        NOT_DEFINED_EVENT_NAME: "Can't send a not defined event name. Make sure you add your event name first."
    };
    class Events {
        constructor() {
            this.subscribers = {}
        }
        on(t, e) {
            this.subscribers[t] || (this.subscribers[t] = []);
            const n = this.subscribers[t].push(e) - 1;
            return {
                off: () => {
                    this.subscribers[t].splice(n, 1)
                }
            }
        }
        emit(t, e) {
            this.subscribers[t] && this.subscribers[t].forEach((t => t(e)))
        }
    }
    const l = "ready";
    class ParentFrame {
        events = [];
        eventEmitter = new Events;
        constructor({childFrameNode: t, listeners: e, methods: n={}, scripts: r}) {
            if (!t.src)
                throw new Error(c.EMPTY_IFRAME);
            this.child = t,
            this.origin = window.origin,
            this.creativeUrl = new URL(this.child.src);
            const i = new URLSearchParams(this.child.src);
            if (this.placement = i.get("_placement") || "",
            !this.placement || "" === this.placement)
                throw new Error(c.CANT_VALIDATE_PLACEMENT);
            this.scripts = r,
            this.listeners = e || null,
            this.methods = Object.keys(n),
            window.addEventListener("message", this.receiveEvent.bind(this)),
            this.methods && this.methods.forEach((t => {
                if (t === l)
                    return void console.error(c.CANT_USE_READY_COMMAND);
                const e = this.eventEmitter.on(t, n[t]);
                this.events.push(e)
            }
            )),
            this.send(l, void 0)
        }
        receiveEvent(t) {
            if (this.creativeUrl.origin === t.origin)
                try {
                    const {command: e, payload: n, placement: r} = this.parseMessage(t);
                    if (this.placement !== r)
                        return;
                    this.eventEmitter.emit(e, n)
                } catch (e) {
                    console.error(e)
                }
        }
        parseMessage(t) {
            return {
                command: t.data.command,
                payload: t.data.payload,
                placement: t.data.placement
            }
        }
        buildEventPayload(t, e) {
            const n = {};
            return t === l && (n.availableListeners = this.listeners,
            n.availableMethods = this.methods,
            n.scripts = this.scripts),
            {
                ...n,
                command: t,
                payload: e,
                placement: this.placement
            }
        }
        send(t, e) {
            if (this.listeners && !this.listeners.includes(t) && t !== l)
                throw new Error(c.NOT_DEFINED_EVENT_NAME);
            if (!this.child.contentWindow)
                return;
            const {origin: n} = this.creativeUrl;
            if (n)
                try {
                    const r = this.buildEventPayload(t, e);
                    this.child.contentWindow.postMessage(r, n)
                } catch (r) {
                    console.error(r)
                }
        }
        destroy() {
            window.removeEventListener("message", this.receiveEvent.bind(this)),
            this.events.forEach((t => {
                t.off()
            }
            ))
        }
    }
    class ChildFrame {
        eventEmitter = new Events;
        constructor(t) {
            const e = new URLSearchParams(window.location.search);
            if (this.endpoint = e.get("_origin"),
            !this.endpoint)
                throw new Error(c.CANT_VALIDATE_ORIGIN);
            if (this.parentPlacement = e.get("_placement"),
            !this.parentPlacement)
                throw new Error(c.CANT_VALIDATE_PLACEMENT);
            this.callback = t,
            this.eventEmitter.on(l, this.onParentReady.bind(this)),
            this.listeners = {},
            this.run = {},
            window.addEventListener("message", this.receiveEvent.bind(this))
        }
        receiveEvent(t) {
            if (t.origin === this.endpoint)
                try {
                    const {command: e, payload: n, parentPlacement: r} = this.parseMessage(t);
                    if (r !== this.parentPlacement)
                        return;
                    e === l ? this.onParentReady(t.data) : this.eventEmitter.emit(e, n)
                } catch (e) {
                    console.error(e)
                }
        }
        parseMessage(t) {
            return {
                command: t.data.command,
                payload: t.data.payload,
                parentPlacement: t.data.placement
            }
        }
        onParentReady(t) {
            const {availableListeners: e, availableMethods: n, scripts: r} = t;
            e && e.forEach((t => {
                this.listeners[t] = e => {
                    this.eventEmitter.on(t, e)
                }
            }
            )),
            n && n.forEach((t => {
                this.run[t] = e => {
                    this.sendCommand(t, e)
                }
            }
            )),
            r && (t => {
                if (!t || !t.length)
                    return;
                const e = document.querySelector("head");
                if (e)
                    for (const n of t) {
                        const t = document.createElement("div");
                        t.innerHTML = n;
                        const r = t.querySelector("script");
                        if (!r || !r.attributes)
                            continue;
                        const i = document.createElement("script");
                        for (let e = r.attributes.length; e--; )
                            i.setAttribute(r.attributes[e].name, r.attributes[e].value);
                        i.innerHTML = r.innerHTML,
                        e.appendChild(i)
                    }
            }
            )(r),
            this.callback(t)
        }
        sendCommand(t, e) {
            const n = {
                command: t,
                payload: e,
                placement: this.parentPlacement
            };
            window.parent.postMessage(n, this.endpoint)
        }
    }
    const d = "unable_to_setup_onParentStateUpdate_listener"
      , f = "unable_to_fire_impression_trackers"
      , p = "unknown_vast_event_type";
    let h = function(t) {
        return t.WALLPAPER_ERROR = "wallpaper_error",
        t.CREATIVE_SERVICE_ERROR = "creative_service_error",
        t.IFRAME_FAILED_TO_LOAD_ERROR = "ifame_failed_to_load_error",
        t.IFRAME_MAX_RETRIES_ERROR = "ifame_max_retries_error",
        t.RENDERER_FAILED_TO_CONNECT_PARENT = "renderer_failed_to_connect_to_parent_app",
        t.NO_CLICK_URL_ERROR = "no_click_url_error",
        t.NETWORK_RESPONSE_NOT_OK_ERROR = "network_response_not_ok_error",
        t.EVENT_PROXY_ERROR = "event_proxy_error",
        t
    }({})
      , v = function(t) {
        return t.PARENT_APP_READY = "desktop_wallpaper_app_ready",
        t
    }({});
    const m = {
        ...{
            name: o.Development,
            production: !1,
            datadogSampleRate: 100,
            datadogAppId: "03ee8b36-940d-43ac-b013-de709befea50",
            datadogClientToken: "pubd26aceef5e2edd716e41259bd529db3a"
        },
        name: o.Production,
        production: !0,
        datadogSampleRate: 1
    };
    function g(t) {
        return "Minified Redux error #" + t + "; visit https://redux.js.org/Errors?code=" + t + " for the full message or use the non-minified dev environment for full errors. "
    }
    var _ = "function" == typeof Symbol && Symbol.observable || "@@observable"
      , y = function() {
        return Math.random().toString(36).substring(7).split("").join(".")
    }
      , b = {
        INIT: "@@redux/INIT" + y(),
        REPLACE: "@@redux/REPLACE" + y(),
        PROBE_UNKNOWN_ACTION: function() {
            return "@@redux/PROBE_UNKNOWN_ACTION" + y()
        }
    };
    function w(t) {
        if ("object" != typeof t || null === t)
            return !1;
        for (var e = t; null !== Object.getPrototypeOf(e); )
            e = Object.getPrototypeOf(e);
        return Object.getPrototypeOf(t) === e
    }
    function E(t, e, n) {
        var r;
        if ("function" == typeof e && "function" == typeof n || "function" == typeof n && "function" == typeof arguments[3])
            throw new Error(g(0));
        if ("function" == typeof e && void 0 === n && (n = e,
        e = void 0),
        void 0 !== n) {
            if ("function" != typeof n)
                throw new Error(g(1));
            return n(E)(t, e)
        }
        if ("function" != typeof t)
            throw new Error(g(2));
        var i = t
          , o = e
          , a = []
          , s = a
          , u = !1;
        function c() {
            s === a && (s = a.slice())
        }
        function l() {
            if (u)
                throw new Error(g(3));
            return o
        }
        function d(t) {
            if ("function" != typeof t)
                throw new Error(g(4));
            if (u)
                throw new Error(g(5));
            var e = !0;
            return c(),
            s.push(t),
            function() {
                if (e) {
                    if (u)
                        throw new Error(g(6));
                    e = !1,
                    c();
                    var n = s.indexOf(t);
                    s.splice(n, 1),
                    a = null
                }
            }
        }
        function f(t) {
            if (!w(t))
                throw new Error(g(7));
            if (void 0 === t.type)
                throw new Error(g(8));
            if (u)
                throw new Error(g(9));
            try {
                u = !0,
                o = i(o, t)
            } finally {
                u = !1
            }
            for (var e = a = s, n = 0; n < e.length; n++) {
                (0,
                e[n])()
            }
            return t
        }
        return f({
            type: b.INIT
        }),
        (r = {
            dispatch: f,
            subscribe: d,
            getState: l,
            replaceReducer: function(t) {
                if ("function" != typeof t)
                    throw new Error(g(10));
                i = t,
                f({
                    type: b.REPLACE
                })
            }
        })[_] = function() {
            var t, e = d;
            return (t = {
                subscribe: function(t) {
                    if ("object" != typeof t || null === t)
                        throw new Error(g(11));
                    function n() {
                        t.next && t.next(l())
                    }
                    return n(),
                    {
                        unsubscribe: e(n)
                    }
                }
            })[_] = function() {
                return this
            }
            ,
            t
        }
        ,
        r
    }
    let S = function(t) {
        return t.UPDATE_STATE_FROM_PARENT = "UPDATE_STATE_FROM_PARENT",
        t.UPDATE_PARENT_CONNECTED = "UPDATE_PARENT_CONNECTED",
        t
    }({});
    const T = {
        creative: null,
        parentConnected: !1
    };
    function C(t=T, e) {
        switch (e.type) {
        case S.UPDATE_STATE_FROM_PARENT:
            return {
                ...t,
                ...e.stateFromParent
            };
        case S.UPDATE_PARENT_CONNECTED:
            return {
                ...t,
                parentConnected: e.connected
            };
        default:
            return t
        }
    }
    const A = E(C);
    let k = function(t) {
        return t.PAUSE = "PAUSE",
        t.RESUME = "RESUME",
        t.RESET = "RESET",
        t
    }({})
      , R = function(t) {
        return t.CHANGE = "CHANGE",
        t.FORCE = "FORCE",
        t
    }({})
      , x = function(t) {
        return t.SHOW = "SHOW",
        t.HIDE = "HIDE",
        t
    }({})
      , I = function(t) {
        return t.PAUSE_TIMER = "pauseTimer",
        t.RESUME_TIMER = "resumeTimer",
        t.RESET_TIMER = "resetTimer",
        t.CHANGE = "change",
        t.FORCE_CHANGE = "forceChange",
        t.SHOW_APP = "showApp",
        t.HIDE_APP = "hideApp",
        t.SHOW_DROPZONE = "showDropzone",
        t.OPEN_PANEL = "openPanel",
        t.SENT_EVENT = "sendEvent",
        t.FIRE_IMPRESSION_TRACKERS = "fireImpressionTrackers",
        t.START_JURASSIC_WORLD_TRANSFER_BOX_EFFECT = "startJurassicWorldTransferBoxEffect",
        t
    }({});
    const O = {
        api: void 0,
        commands: void 0,
        debug: !1,
        init(t) {
            this.api = t.api,
            this.commands = t.api.run ? Object.keys(t.api.run) : [],
            this.debug = t.debug || !1;
            try {
                this.api.listeners.onParentStateUpdate(function(t, e=50) {
                    let n, r, i;
                    return (...o) => {
                        n ? (clearTimeout(r),
                        r = setTimeout(( () => {
                            Date.now() - i >= e && (t.apply(this, o),
                            i = Date.now())
                        }
                        ), Math.max(e - (Date.now() - i), 0))) : (t.apply(this, o),
                        i = Date.now(),
                        n = !0)
                    }
                }(t.onStateUpdate, 50))
            } catch {
                i.trackError(new Error(d))
            }
        },
        runParentCommand(t, e) {
            (this.commands || t !== I.SHOW_DROPZONE) && (this.commands.includes(t) ? (this.api.run[t](e),
            this.debug && console.log(`Running command ${t}`)) : this.debug && console.error(`Command ${t} does not exist in Wallpaper App`))
        },
        runTimerAction(t) {
            switch (t) {
            case k.PAUSE:
                this.runParentCommand(I.PAUSE_TIMER);
                break;
            case k.RESUME:
                this.runParentCommand(I.RESUME_TIMER);
                break;
            case k.RESET:
                this.runParentCommand(I.RESET_TIMER)
            }
        },
        runRotationAction(t) {
            switch (t) {
            case R.CHANGE:
                this.runParentCommand(I.CHANGE);
                break;
            case R.FORCE:
                this.runParentCommand(I.FORCE_CHANGE)
            }
        },
        runUIAction(t) {
            switch (t) {
            case x.SHOW:
                this.runParentCommand(I.SHOW_APP);
                break;
            case x.HIDE:
                this.runParentCommand(I.HIDE_APP)
            }
        },
        showDropzone() {
            this.runParentCommand(I.SHOW_DROPZONE)
        },
        openPanel(t) {
            this.runParentCommand(I.OPEN_PANEL, t)
        },
        sendEvent(t) {
            this.runParentCommand(I.SENT_EVENT, t)
        },
        fireImpressions() {
            try {
                if (this.api.run.fireImpressionTrackers)
                    this.runParentCommand(I.FIRE_IMPRESSION_TRACKERS);
                else {
                    const {creative: t} = A.getState();
                    t?.impressionTrackers?.forEach((t => {
                        const e = ( (t, e={}) => {
                            const n = new Image(1,1);
                            return n.border = "0",
                            n.alt = "",
                            n.src = Object.keys(e).reduce(( (t, n) => t.replace(new RegExp(`%{${n}}`,"g"), e[n])), t),
                            n
                        }
                        )(t);
                        e && document?.body?.appendChild?.(e)
                    }
                    ))
                }
            } catch {
                i.trackError(new Error(f))
            }
        },
        startJurassicWorldTransferBoxEffect() {
            this.runParentCommand(I.START_JURASSIC_WORLD_TRANSFER_BOX_EFFECT)
        }
    }
      , N = t => {
        if (!t || !t.dataTransfer)
            return;
        if (!t.dataTransfer.types)
            return;
        t.preventDefault(),
        t.stopPropagation();
        -1 !== Array.from(t.dataTransfer.types).indexOf("Files") && O.showDropzone()
    }
      , P = {
        fireSnowplowImpression() {
            this.sendEvent({
                eC: "impressions",
                eA: "sendSnowplowWallpaperImpressions"
            })
        },
        sendEvent(t) {
            const {creative: e} = A.getState();
            O.sendEvent({
                ...t,
                creative: e,
                version: "0.6.2"
            })
        }
    }
      , L = t => {
        switch (t) {
        case x.SHOW:
        case x.HIDE:
            O.runUIAction(t);
            break;
        default:
            console.error(`Unknown UI action: ${t}`)
        }
    }
      , M = t => {
        const {creative: e} = A.getState()
          , r = a[t];
        r ? n(e.events[r]) : console.error(`Unknown VAST event name: ${t}`)
    }
      , U = t => {
        t.eC && t.eA ? P.sendEvent(t) : console.error("Both Category (eC) and Action (eA) are required")
    }
      , D = t => {
        const {name: e, context: n={}, error: r} = t
          , {creative: o} = A.getState();
        if (!e && !r)
            return void console.error("Either 'name' or 'error' must be provided.");
        const a = e || h.WALLPAPER_ERROR
          , s = r || new Error(a);
        i.trackError(new Error(a), {
            error: s,
            creative: o,
            context: n
        })
    }
      , F = t => {
        if ("string" != typeof t.name)
            return;
        const {creative: e} = A.getState();
        i.addMonitorAction({
            name: t.name,
            context: {
                ...t.context,
                ...e
            }
        })
    }
      , B = t => {
        i.addMonitorTiming(t)
    }
      , z = t => {
        switch (t) {
        case R.CHANGE:
        case R.FORCE:
            O.runRotationAction(t);
            break;
        default:
            console.error(`Unknown rotation action: ${t}`)
        }
    }
      , H = t => {
        switch (t) {
        case k.PAUSE:
        case k.RESET:
        case k.RESUME:
            O.runTimerAction(t);
            break;
        default:
            console.error(`Unknown timer action: ${t}`)
        }
    }
      , j = t => {
        const {path: e, trk: n} = t
          , {creative: r} = A.getState()
          , {adId: i} = r;
        O.openPanel({
            path: e,
            trk: n,
            adId: i,
            shouldTrack: !!n
        })
    }
      , q = () => {
        const t = A.getState().creative
          , {clickUrl: e, rawClickUrl: n} = t;
        if (!e && !n)
            return void i.trackError(new Error(h.NO_CLICK_URL_ERROR), {
                creative: t
            });
        (async () => {
            try {
                n ? (await V(e),
                Z(n)) : Z(e),
                P.sendEvent({
                    eC: "click",
                    eA: "clickThrough"
                })
            } catch (t) {
                i.trackError(new Error(h.EVENT_PROXY_ERROR), {
                    error: t.message
                })
            }
        }
        )()
    }
      , V = async t => {
        const e = new URL(t)
          , n = Object.fromEntries(e.searchParams.entries())
          , r = {
            method: "GET",
            path: `${e.origin}${e.pathname}`,
            query: {
                ...n,
                ct: "true"
            }
        }
          , o = await u(r);
        o.ok || i.trackError(new Error(h.NETWORK_RESPONSE_NOT_OK_ERROR), {
            response: o
        })
    }
      , Z = t => {
        const e = window.open(t, "_blank");
        e && (e.opener = null)
    }
      , G = () => {
        O.showDropzone()
    }
      , W = () => {
        O.startJurassicWorldTransferBoxEffect()
    }
    ;
    let Y = function(t) {
        return t.OnStateUpdate = "onStateUpdate",
        t
    }({});
    const K = {
        api: ParentFrame,
        init(t) {
            const {creative: n} = A.getState()
              , r = [...n?.scripts ? n.scripts : [], "\n<script>document.body.addEventListener('dragover', function() { window.wetransfer?.api?.run?.showDropzone?.() });<\/script>\n"];
            try {
                this.api = new ParentFrame({
                    childFrameNode: t,
                    scripts: r,
                    methods: e,
                    listeners: Object.values(Y)
                })
            } catch (o) {
                i.trackError(new Error(h.CREATIVE_SERVICE_ERROR), {
                    error: o,
                    creative: n
                })
            }
        },
        send(t, e) {
            this.api?.send?.(t, e)
        }
    }
      , $ = K
      , X = t => {
        console.info(`${t} is deprecated`)
    }
      , J = {
        api_show() {
            O.runUIAction(x.SHOW)
        },
        api_hide() {
            O.runUIAction(x.HIDE)
        },
        getCurrentState(t) {
            const {creative: e, currentPosition: n, geo: r, userAction: i, isPanelOpen: o, language: a, currentTime: s, userAgent: u, transferBox: c, nav: l, firstVisit: d} = A.getState()
              , {scripts: f, clickUrl: p} = e;
            return {
                _analytics: f || [],
                ads_viewed: n - 1,
                requester: t,
                geo: r,
                state: i,
                panel: o,
                language: a,
                time_on_site: (Date.now() - d) / 1e3,
                remaining: s / 1e3 || 30,
                user_agent: u,
                _click: p,
                position: c,
                nav_position: l
            }
        },
        api_openPanel(t, e="", n=!1) {
            const {creative: r} = A.getState()
              , {adId: i} = r;
            O.openPanel({
                path: t,
                trk: e,
                adId: i,
                shouldTrack: n
            })
        },
        api_pauseTimer() {
            O.runTimerAction(k.PAUSE)
        },
        api_resumeTimer() {
            O.runTimerAction(k.RESUME)
        },
        api_resetTimer() {
            O.runTimerAction(k.RESET)
        },
        api_change() {
            O.runRotationAction(R.CHANGE)
        },
        api_forceChange() {
            O.runRotationAction(R.FORCE)
        },
        api_vast(t) {
            const {creative: e} = A.getState()
              , r = a[t];
            r ? n(e.events[r]) : i.trackError(new Error(p), {
                eventName: t,
                creative: e
            })
        },
        api_getState(t) {
            return this.getCurrentState(t)
        },
        api_sendSnowplowEvent(t) {
            t.eC && t.eA && P.sendEvent(t)
        },
        api_sendError(t) {
            const {creative: e} = A.getState();
            i.trackError(t, {
                creative: e,
                errorOrigin: "studio_wallpaper"
            })
        },
        api_hideDropZone() {
            X("api_hideDropZone")
        },
        api_toggleDragOver() {
            X("api_toggleDragOver")
        },
        api_addFiles() {
            X("api_addFiles")
        },
        api_setWallpaperUIStyle() {
            X("api_setWallpaperUIStyle")
        },
        api_setAppTheme() {
            X("api_setAppTheme")
        },
        api_getCurrentBid() {
            X("api_getCurrentBid")
        },
        api_sendCustomEventWalter() {
            X("api_sendCustomEventWalter")
        },
        api_sendCustomEvent() {
            X("api_sendCustomEvent")
        },
        api_showDropZone() {
            O.showDropzone()
        }
    };
    let Q = null
      , tt = null;
    const et = t => new URL(t.sourceUrl).origin
      , nt = [/MSFAPI/, /nuanria_messaging/, /scaInfoOriginatorASID/, /iasCommonId/, /p\|\*\|sca\|\*\|/]
      , rt = (t, e) => {
        if (!nt.some((t => t.test(e))))
            throw new Error(`${t}: ${e}`)
    }
      , it = (t, e="start") => {
        tt.contentWindow.postMessage(( (t, ...e) => JSON.stringify({
            command: t,
            data: e
        }))(e, t), et(Q))
    }
      , ot = async t => {
        if (et(Q) === t.origin)
            try {
                const {method: e, data: n} = (t => {
                    const {data: e} = t;
                    let n = {};
                    try {
                        n = e.raw ? e : JSON.parse(e)
                    } catch {
                        rt("UnableToParseMessage", e)
                    }
                    n.command || rt("EmptyWallpaperCommand", e);
                    const r = `api_${n.command}`;
                    return J[r] || rt(`MethodNotFound: ${r}`, e),
                    {
                        method: r,
                        data: n.data
                    }
                }
                )(t);
                if ("api_undefined" === e)
                    return;
                const r = [...n]
                  , i = J[e](...r);
                "api_getState" === e && it(i, "update")
            } catch (e) {
                i.trackError(e)
            }
    }
      , at = {
        init(t, e) {
            Q = e,
            tt = t,
            window.addEventListener("message", ot);
            const n = J.getCurrentState();
            it(n)
        }
    };
    var st = {
        log: "log",
        debug: "debug",
        info: "info",
        warn: "warn",
        error: "error"
    }
      , ut = function(t) {
        for (var e = [], n = 1; n < arguments.length; n++)
            e[n - 1] = arguments[n];
        Object.prototype.hasOwnProperty.call(st, t) || (t = st.log),
        ut[t].apply(ut, e)
    };
    function ct(t, e) {
        return function() {
            for (var n = [], r = 0; r < arguments.length; r++)
                n[r] = arguments[r];
            try {
                return t.apply(void 0, n)
            } catch (i) {
                ut.error(e, i)
            }
        }
    }
    ut.debug = console.debug.bind(console),
    ut.log = console.log.bind(console),
    ut.info = console.info.bind(console),
    ut.warn = console.warn.bind(console),
    ut.error = console.error.bind(console);
    var lt, dt, ft = function(t, e, n) {
        if (n || 2 === arguments.length)
            for (var r, i = 0, o = e.length; i < o; i++)
                !r && i in e || (r || (r = Array.prototype.slice.call(e, 0, i)),
                r[i] = e[i]);
        return t.concat(r || Array.prototype.slice.call(e))
    }, pt = !1;
    function ht(t) {
        pt = t
    }
    function vt(t) {
        return function() {
            return mt(t, this, arguments)
        }
    }
    function mt(t, e, n) {
        try {
            return t.apply(e, n)
        } catch (r) {
            if (gt(st.error, r),
            lt)
                try {
                    lt(r)
                } catch (r) {
                    gt(st.error, r)
                }
        }
    }
    function gt(t) {
        for (var e = [], n = 1; n < arguments.length; n++)
            e[n - 1] = arguments[n];
        pt && ut.apply(void 0, ft([t, "[MONITOR]"], e, !1))
    }
    function _t(t, e) {
        return -1 !== t.indexOf(e)
    }
    function yt(t) {
        if (Array.from)
            return Array.from(t);
        var e = [];
        if (t instanceof Set)
            t.forEach((function(t) {
                return e.push(t)
            }
            ));
        else
            for (var n = 0; n < t.length; n++)
                e.push(t[n]);
        return e
    }
    function bt(t, e) {
        for (var n = 0; n < t.length; n += 1) {
            var r = t[n];
            if (e(r, n))
                return r
        }
    }
    function wt(t) {
        return Object.keys(t).map((function(e) {
            return t[e]
        }
        ))
    }
    function Et(t) {
        return Object.keys(t).map((function(e) {
            return [e, t[e]]
        }
        ))
    }
    function St(t, e) {
        return t.slice(0, e.length) === e
    }
    function Tt(t) {
        return window.CSS && window.CSS.escape ? window.CSS.escape(t) : t.replace(/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g, (function(t, e) {
            return e ? "\0" === t ? "\ufffd" : "".concat(t.slice(0, -1), "\\").concat(t.charCodeAt(t.length - 1).toString(16), " ") : "\\".concat(t)
        }
        ))
    }
    function Ct(t) {
        for (var e = [], n = 1; n < arguments.length; n++)
            e[n - 1] = arguments[n];
        return e.forEach((function(e) {
            for (var n in e)
                Object.prototype.hasOwnProperty.call(e, n) && (t[n] = e[n])
        }
        )),
        t
    }
    function At() {
        if ("object" == typeof globalThis)
            return globalThis;
        Object.defineProperty(Object.prototype, "_dd_temp_", {
            get: function() {
                return this
            },
            configurable: !0
        });
        var t = _dd_temp_;
        return delete Object.prototype._dd_temp_,
        "object" != typeof t && (t = "object" == typeof self ? self : "object" == typeof window ? window : {}),
        t
    }
    !function(t) {
        t.PAGEHIDE = "pagehide",
        t.FEATURE_FLAGS = "feature_flags",
        t.RESOURCE_PAGE_STATES = "resource_page_states",
        t.COLLECT_FLUSH_REASON = "collect_flush_reason",
        t.SANITIZE_INPUTS = "sanitize_inputs"
    }(dt || (dt = {}));
    var kt = new Set;
    function Rt(t) {
        return kt.has(t)
    }
    function xt() {
        return kt
    }
    var It = 1024
      , Ot = 1024 * It
      , Nt = /[^\u0000-\u007F]/;
    function Pt(t) {
        return Nt.test(t) ? void 0 !== window.TextEncoder ? (new TextEncoder).encode(t).length : new Blob([t]).size : t.length
    }
    function Lt(t, e) {
        var n, r = At();
        return r.Zone && "function" == typeof r.Zone.__symbol__ && (n = t[r.Zone.__symbol__(e)]),
        n || (n = t[e]),
        n
    }
    function Mt(t, e) {
        return Lt(At(), "setTimeout")(vt(t), e)
    }
    function Ut(t) {
        Lt(At(), "clearTimeout")(t)
    }
    function Dt(t, e) {
        return Lt(window, "setInterval")(vt(t), e)
    }
    function Ft(t) {
        Lt(window, "clearInterval")(t)
    }
    function Bt(t, e, n) {
        var r, i, o = !n || void 0 === n.leading || n.leading, a = !n || void 0 === n.trailing || n.trailing, s = !1;
        return {
            throttled: function() {
                for (var n = [], u = 0; u < arguments.length; u++)
                    n[u] = arguments[u];
                s ? r = n : (o ? t.apply(void 0, n) : r = n,
                s = !0,
                i = Mt((function() {
                    a && r && t.apply(void 0, r),
                    s = !1,
                    r = void 0
                }
                ), e))
            },
            cancel: function() {
                Ut(i),
                s = !1,
                r = void 0
            }
        }
    }
    function zt() {}
    function Ht(t) {
        return null === t ? "null" : Array.isArray(t) ? "array" : typeof t
    }
    function jt(t, e, n) {
        if (void 0 === n && (n = function() {
            if ("undefined" != typeof WeakSet) {
                var t = new WeakSet;
                return {
                    hasAlreadyBeenSeen: function(e) {
                        var n = t.has(e);
                        return n || t.add(e),
                        n
                    }
                }
            }
            var e = [];
            return {
                hasAlreadyBeenSeen: function(t) {
                    var n = e.indexOf(t) >= 0;
                    return n || e.push(t),
                    n
                }
            }
        }()),
        void 0 === e)
            return t;
        if ("object" != typeof e || null === e)
            return e;
        if (e instanceof Date)
            return new Date(e.getTime());
        if (e instanceof RegExp) {
            var r = e.flags || [e.global ? "g" : "", e.ignoreCase ? "i" : "", e.multiline ? "m" : "", e.sticky ? "y" : "", e.unicode ? "u" : ""].join("");
            return new RegExp(e.source,r)
        }
        if (!n.hasAlreadyBeenSeen(e)) {
            if (Array.isArray(e)) {
                for (var i = Array.isArray(t) ? t : [], o = 0; o < e.length; ++o)
                    i[o] = jt(i[o], e[o], n);
                return i
            }
            var a = "object" === Ht(t) ? t : {};
            for (var s in e)
                Object.prototype.hasOwnProperty.call(e, s) && (a[s] = jt(a[s], e[s], n));
            return a
        }
    }
    function qt(t) {
        return jt(void 0, t)
    }
    function Vt() {
        for (var t, e = [], n = 0; n < arguments.length; n++)
            e[n] = arguments[n];
        for (var r = 0, i = e; r < i.length; r++) {
            var o = i[r];
            null != o && (t = jt(t, o))
        }
        return t
    }
    function Zt(t, e, n) {
        if ("object" != typeof t || null === t)
            return JSON.stringify(t);
        var r = Gt(Object.prototype)
          , i = Gt(Array.prototype)
          , o = Gt(Object.getPrototypeOf(t))
          , a = Gt(t);
        try {
            return JSON.stringify(t, e, n)
        } catch (Zs) {
            return "<error: unable to serialize object>"
        } finally {
            r(),
            i(),
            o(),
            a()
        }
    }
    function Gt(t) {
        var e = t
          , n = e.toJSON;
        return n ? (delete e.toJSON,
        function() {
            e.toJSON = n
        }
        ) : zt
    }
    var Wt = 220 * It
      , Yt = "$"
      , Kt = 3;
    function $t(t, e) {
        var n;
        void 0 === e && (e = Wt);
        var r = Gt(Object.prototype)
          , i = Gt(Array.prototype)
          , o = []
          , a = new WeakMap
          , s = Xt(t, Yt, void 0, o, a)
          , u = (null === (n = JSON.stringify(s)) || void 0 === n ? void 0 : n.length) || 0;
        if (!(u > e)) {
            for (; o.length > 0 && u < e; ) {
                var c = o.shift()
                  , l = 0;
                if (Array.isArray(c.source))
                    for (var d = 0; d < c.source.length; d++) {
                        if (u += void 0 !== (f = Xt(c.source[d], c.path, d, o, a)) ? JSON.stringify(f).length : 4,
                        u += l,
                        l = 1,
                        u > e) {
                            Jt(e, "truncated", t);
                            break
                        }
                        c.target[d] = f
                    }
                else
                    for (var d in c.source)
                        if (Object.prototype.hasOwnProperty.call(c.source, d)) {
                            var f;
                            if (void 0 !== (f = Xt(c.source[d], c.path, d, o, a)) && (u += JSON.stringify(f).length + l + d.length + Kt,
                            l = 1),
                            u > e) {
                                Jt(e, "truncated", t);
                                break
                            }
                            c.target[d] = f
                        }
            }
            return r(),
            i(),
            s
        }
        Jt(e, "discarded", t)
    }
    function Xt(t, e, n, r, i) {
        var o = function(t) {
            var e = t;
            if (e && "function" == typeof e.toJSON)
                try {
                    return e.toJSON()
                } catch (Zs) {}
            return t
        }(t);
        if (!o || "object" != typeof o)
            return function(t) {
                if ("bigint" == typeof t)
                    return "[BigInt] ".concat(t.toString());
                if ("function" == typeof t)
                    return "[Function] ".concat(t.name || "unknown");
                if ("symbol" == typeof t)
                    return "[Symbol] ".concat(t.description || t.toString());
                return t
            }(o);
        var a = function(t) {
            try {
                if (t instanceof Event)
                    return {
                        isTrusted: t.isTrusted
                    };
                var e = Object.prototype.toString.call(t).match(/\[object (.*)\]/);
                if (e && e[1])
                    return "[".concat(e[1], "]")
            } catch (Zs) {}
            return "[Unserializable]"
        }(o);
        if ("[Object]" !== a && "[Array]" !== a)
            return a;
        var s = t;
        if (i.has(s))
            return "[Reference seen at ".concat(i.get(s), "]");
        var u = void 0 !== n ? "".concat(e, ".").concat(n) : e
          , c = Array.isArray(o) ? [] : {};
        return i.set(s, u),
        r.push({
            source: o,
            target: c,
            path: u
        }),
        c
    }
    function Jt(t, e, n) {
        ut.warn("The data provided has been ".concat(e, " as it is over the limit of ").concat(t, " characters:"), n)
    }
    var Qt = 3 * It;
    function te(t, e) {
        return t > Qt && (ut.warn("The ".concat(e, " data is over ").concat(Qt / It, "KiB. On low connectivity, the SDK has the potential to exhaust the user's upload bandwidth.")),
        !0)
    }
    var ee = 200;
    function ne(t, e) {
        void 0 === e && (e = Pt);
        var n, r = {}, i = !1, o = Bt((function(r) {
            n = e(Zt(r)),
            i || (i = te(n, t))
        }
        ), ee).throttled;
        return {
            getBytesCount: function() {
                return n
            },
            get: function() {
                return r
            },
            add: function(t, e) {
                r[t] = e,
                o(r)
            },
            remove: function(t) {
                delete r[t],
                o(r)
            },
            set: function(t) {
                o(r = t)
            },
            getContext: function() {
                return qt(r)
            },
            setContext: function(t) {
                r = Rt(dt.SANITIZE_INPUTS) ? $t(t) : qt(t),
                o(r)
            },
            setContextProperty: function(t, e) {
                r[t] = Rt(dt.SANITIZE_INPUTS) ? $t(e) : qt(e),
                o(r)
            },
            removeContextProperty: function(t) {
                delete r[t],
                o(r)
            },
            clearContext: function() {
                r = {},
                n = 0
            }
        }
    }
    var re = function() {
        function t() {
            this.buffer = []
        }
        return t.prototype.add = function(t) {
            this.buffer.push(t) > 500 && this.buffer.splice(0, 1)
        }
        ,
        t.prototype.drain = function() {
            this.buffer.forEach((function(t) {
                return t()
            }
            )),
            this.buffer.length = 0
        }
        ,
        t
    }();
    function ie(t) {
        return 0 !== t && 100 * Math.random() <= t
    }
    function oe(t, e) {
        return +t.toFixed(e)
    }
    function ae(t) {
        return se(t) && t >= 0 && t <= 100
    }
    function se(t) {
        return "number" == typeof t
    }
    var ue, ce = 1e3, le = 60 * ce, de = 60 * le, fe = 365 * (24 * de);
    function pe(t) {
        return {
            relative: t,
            timeStamp: he(t)
        }
    }
    function he(t) {
        var e = me() - performance.now();
        return e > Te() ? Math.round(Ee(e, t)) : function(t) {
            return Math.round(Ee(Te(), t))
        }(t)
    }
    function ve(t) {
        return se(t) ? oe(1e6 * t, 0) : t
    }
    function me() {
        return (new Date).getTime()
    }
    function ge() {
        return me()
    }
    function _e() {
        return performance.now()
    }
    function ye() {
        return {
            relative: _e(),
            timeStamp: ge()
        }
    }
    function be() {
        return {
            relative: 0,
            timeStamp: Te()
        }
    }
    function we(t, e) {
        return e - t
    }
    function Ee(t, e) {
        return t + e
    }
    function Se(t) {
        return t - Te()
    }
    function Te() {
        return void 0 === ue && (ue = performance.timing.navigationStart),
        ue
    }
    function Ce(t) {
        return t ? (parseInt(t, 10) ^ 16 * Math.random() >> parseInt(t, 10) / 4).toString(16) : "".concat(1e7, "-").concat(1e3, "-").concat(4e3, "-").concat(8e3, "-").concat(1e11).replace(/[018]/g, Ce)
    }
    function Ae(t, e) {
        var n = new RegExp("(?:^|;)\\s*".concat(e, "\\s*=\\s*([^;]+)")).exec(t);
        return n ? n[1] : void 0
    }
    function ke(t, e, n) {
        void 0 === n && (n = "");
        var r = t.charCodeAt(e - 1)
          , i = r >= 55296 && r <= 56319 ? e + 1 : e;
        return t.length <= i ? t : "".concat(t.slice(0, i)).concat(n)
    }
    var Re, xe = ce;
    function Ie(t, e, n, r) {
        var i = new Date;
        i.setTime(i.getTime() + n);
        var o = "expires=".concat(i.toUTCString())
          , a = r && r.crossSite ? "none" : "strict"
          , s = r && r.domain ? ";domain=".concat(r.domain) : ""
          , u = r && r.secure ? ";secure" : "";
        document.cookie = "".concat(t, "=").concat(e, ";").concat(o, ";path=/;samesite=").concat(a).concat(s).concat(u)
    }
    function Oe(t) {
        return Ae(document.cookie, t)
    }
    function Ne(t, e) {
        Ie(t, "", 0, e)
    }
    var Pe = "datadog-synthetics-public-id"
      , Le = "datadog-synthetics-result-id"
      , Me = "datadog-synthetics-injects-rum";
    function Ue() {
        return Boolean(window._DATADOG_SYNTHETICS_INJECTS_RUM || Oe(Me))
    }
    function De() {
        var t = At().DatadogEventBridge;
        if (t)
            return {
                getAllowedWebViewHosts: function() {
                    return JSON.parse(t.getAllowedWebViewHosts())
                },
                send: function(e, n) {
                    t.send(JSON.stringify({
                        eventType: e,
                        event: n
                    }))
                }
            }
    }
    function Fe(t) {
        var e;
        void 0 === t && (t = null === (e = At().location) || void 0 === e ? void 0 : e.hostname);
        var n = De();
        return !!n && n.getAllowedWebViewHosts().some((function(e) {
            return t === e || (n = t,
            r = ".".concat(e),
            n.slice(-r.length) === r);
            var n, r
        }
        ))
    }
    var Be = "?";
    function ze(t) {
        var e = []
          , n = Ke(t, "stack")
          , r = String(t);
        return n && St(n, r) && (n = n.slice(r.length)),
        n && n.split("\n").forEach((function(t) {
            var n = function(t) {
                var e = qe.exec(t);
                if (!e)
                    return;
                var n = e[2] && 0 === e[2].indexOf("native")
                  , r = e[2] && 0 === e[2].indexOf("eval")
                  , i = Ve.exec(e[2]);
                r && i && (e[2] = i[1],
                e[3] = i[2],
                e[4] = i[3]);
                return {
                    args: n ? [e[2]] : [],
                    column: e[4] ? +e[4] : void 0,
                    func: e[1] || Be,
                    line: e[3] ? +e[3] : void 0,
                    url: n ? void 0 : e[2]
                }
            }(t) || function(t) {
                var e = Ze.exec(t);
                if (!e)
                    return;
                return {
                    args: [],
                    column: e[3] ? +e[3] : void 0,
                    func: Be,
                    line: e[2] ? +e[2] : void 0,
                    url: e[1]
                }
            }(t) || function(t) {
                var e = Ge.exec(t);
                if (!e)
                    return;
                return {
                    args: [],
                    column: e[4] ? +e[4] : void 0,
                    func: e[1] || Be,
                    line: +e[3],
                    url: e[2]
                }
            }(t) || function(t) {
                var e = We.exec(t);
                if (!e)
                    return;
                var n = e[3] && e[3].indexOf(" > eval") > -1
                  , r = Ye.exec(e[3]);
                n && r && (e[3] = r[1],
                e[4] = r[2],
                e[5] = void 0);
                return {
                    args: e[2] ? e[2].split(",") : [],
                    column: e[5] ? +e[5] : void 0,
                    func: e[1] || Be,
                    line: e[4] ? +e[4] : void 0,
                    url: e[3]
                }
            }(t);
            n && (!n.func && n.line && (n.func = Be),
            e.push(n))
        }
        )),
        {
            message: Ke(t, "message"),
            name: Ke(t, "name"),
            stack: e
        }
    }
    var He = "((?:file|https?|blob|chrome-extension|native|eval|webpack|snippet|<anonymous>|\\w+\\.|\\/).*?)"
      , je = "(?::(\\d+))"
      , qe = new RegExp("^\\s*at (.*?) ?\\(".concat(He).concat(je, "?").concat(je, "?\\)?\\s*$"),"i")
      , Ve = new RegExp("\\((\\S*)".concat(je).concat(je, "\\)"));
    var Ze = new RegExp("^\\s*at ?".concat(He).concat(je, "?").concat(je, "??\\s*$"),"i");
    var Ge = /^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|webpack|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i;
    var We = /^\s*(.*?)(?:\((.*?)\))?(?:^|@)((?:file|https?|blob|chrome|webpack|resource|capacitor|\[native).*?|[^@]*bundle)(?::(\d+))?(?::(\d+))?\s*$/i
      , Ye = /(\S+) line (\d+)(?: > eval line \d+)* > eval/i;
    function Ke(t, e) {
        if ("object" == typeof t && t && e in t) {
            var n = t[e];
            return "string" == typeof n ? n : void 0
        }
    }
    var $e = "No stack, consider using an instance of Error";
    function Xe(t) {
        var e = t.stackTrace
          , n = t.originalError
          , r = t.handlingStack
          , i = t.startClocks
          , o = t.nonErrorPrefix
          , a = t.source
          , s = t.handling;
        if (!e || void 0 === e.message && !(n instanceof Error)) {
            var u = Rt(dt.SANITIZE_INPUTS) ? $t(n) : n;
            return {
                startClocks: i,
                source: a,
                handling: s,
                originalError: u,
                message: "".concat(o, " ").concat(Zt(u)),
                stack: $e,
                handlingStack: r,
                type: e && e.name
            }
        }
        return {
            startClocks: i,
            source: a,
            handling: s,
            originalError: n,
            message: e.message || "Empty message",
            stack: Je(e),
            handlingStack: r,
            type: e.name,
            causes: en(n, a)
        }
    }
    function Je(t) {
        var e = Qe(t);
        return t.stack.forEach((function(t) {
            var n = "?" === t.func ? "<anonymous>" : t.func
              , r = t.args && t.args.length > 0 ? "(".concat(t.args.join(", "), ")") : ""
              , i = t.line ? ":".concat(t.line) : ""
              , o = t.line && t.column ? ":".concat(t.column) : "";
            e += "\n  at ".concat(n).concat(r, " @ ").concat(t.url).concat(i).concat(o)
        }
        )),
        e
    }
    function Qe(t) {
        return "".concat(t.name || "Error", ": ").concat(t.message)
    }
    function tn() {
        var t, e = new Error;
        if (!e.stack)
            try {
                throw e
            } catch (n) {}
        return mt((function() {
            var n = ze(e);
            n.stack = n.stack.slice(2),
            t = Je(n)
        }
        )),
        t
    }
    function en(t, e) {
        for (var n = t, r = []; (null == n ? void 0 : n.cause)instanceof Error && r.length < 10; ) {
            var i = ze(n.cause);
            r.push({
                message: n.cause.message,
                source: e,
                type: null == i ? void 0 : i.name,
                stack: i && Je(i)
            }),
            n = n.cause
        }
        return r.length ? r : void 0
    }
    function nn(t) {
        var e = Ct({}, t);
        return ["id", "name", "email"].forEach((function(t) {
            t in e && (e[t] = String(e[t]))
        }
        )),
        e
    }
    function rn(t) {
        return Ct({}, t)
    }
    function on(t, e) {
        return Object.keys(t).some((function(n) {
            return t[n] === e
        }
        ))
    }
    function an(t) {
        return 0 === Object.keys(t).length
    }
    function sn(t) {
        return cn(t, fn(window.location)).href
    }
    function un(t) {
        return fn(cn(t))
    }
    function cn(t, e) {
        var n = function() {
            if (void 0 === ln)
                try {
                    var t = new dn("http://test/path");
                    ln = "http://test/path" === t.href
                } catch (Zs) {
                    ln = !1
                }
            return ln ? dn : void 0
        }();
        if (n)
            try {
                return void 0 !== e ? new n(t,e) : new n(t)
            } catch (a) {
                throw new Error("Failed to construct URL: ".concat(String(a), " ").concat(Zt({
                    url: t,
                    base: e
                })))
            }
        if (void 0 === e && !/:/.test(t))
            throw new Error("Invalid URL: '".concat(t, "'"));
        var r = document
          , i = r.createElement("a");
        if (void 0 !== e) {
            var o = (r = document.implementation.createHTMLDocument("")).createElement("base");
            o.href = e,
            r.head.appendChild(o),
            r.body.appendChild(i)
        }
        return i.href = t,
        i
    }
    var ln, dn = URL;
    function fn(t) {
        if (t.origin)
            return t.origin;
        var e = t.host.replace(/(:80|:443)$/, "");
        return "".concat(t.protocol, "//").concat(e)
    }
    var pn = "datad0g.com"
      , hn = "datadoghq.com"
      , vn = "datadoghq.eu"
      , mn = "ap1.datadoghq.com"
      , gn = {
        logs: "logs",
        rum: "rum",
        sessionReplay: "session-replay"
    }
      , _n = {
        logs: "logs",
        rum: "rum",
        sessionReplay: "replay"
    };
    function yn(t, e, n) {
        var r = function(t, e) {
            var n = "/api/v2/".concat(_n[e])
              , r = t.proxy
              , i = t.proxyUrl;
            if (r) {
                var o = sn(r);
                return function(t) {
                    return "".concat(o, "?ddforward=").concat(encodeURIComponent("".concat(n, "?").concat(t)))
                }
            }
            var a = function(t, e) {
                var n = t.site
                  , r = void 0 === n ? hn : n
                  , i = t.internalAnalyticsSubdomain;
                if (i && r === hn)
                    return "".concat(i, ".").concat(hn);
                var o = r.split(".")
                  , a = o.pop()
                  , s = r !== mn ? "".concat(gn[e], ".") : "";
                return "".concat(s, "browser-intake-").concat(o.join("-"), ".").concat(a)
            }(t, e);
            if (void 0 === r && i) {
                var s = sn(i);
                return function(t) {
                    return "".concat(s, "?ddforward=").concat(encodeURIComponent("https://".concat(a).concat(n, "?").concat(t)))
                }
            }
            return function(t) {
                return "https://".concat(a).concat(n, "?").concat(t)
            }
        }(t, e);
        return {
            build: function(i, o, a) {
                var s = function(t, e, n, r, i, o) {
                    var a = t.clientToken
                      , s = t.internalAnalyticsSubdomain
                      , u = ["sdk_version:".concat("4.40.0"), "api:".concat(r)].concat(n);
                    i && Rt(dt.COLLECT_FLUSH_REASON) && u.push("flush_reason:".concat(i));
                    o && u.push("retry_count:".concat(o.count), "retry_after:".concat(o.lastFailureStatus));
                    var c = ["ddsource=browser", "ddtags=".concat(encodeURIComponent(u.join(","))), "dd-api-key=".concat(a), "dd-evp-origin-version=".concat(encodeURIComponent("4.40.0")), "dd-evp-origin=browser", "dd-request-id=".concat(Ce())];
                    "rum" === e && c.push("batch_time=".concat(ge()));
                    s && c.reverse();
                    return c.join("&")
                }(t, e, n, i, o, a);
                return r(s)
            },
            urlPrefix: r(""),
            endpointType: e
        }
    }
    var bn = 200;
    var wn = /[^a-z0-9_:./-]/;
    function En(t, e) {
        var n = bn - t.length - 1;
        (e.length > n || wn.test(e)) && ut.warn("".concat(t, " value doesn't meet tag requirements and will be sanitized"));
        var r = e.replace(/,/g, "_");
        return "".concat(t, ":").concat(r)
    }
    function Sn(t) {
        var e = function(t) {
            var e = t.env
              , n = t.service
              , r = t.version
              , i = t.datacenter
              , o = [];
            return e && o.push(En("env", e)),
            n && o.push(En("service", n)),
            r && o.push(En("version", r)),
            i && o.push(En("datacenter", i)),
            o
        }(t)
          , n = function(t, e) {
            return {
                logsEndpointBuilder: yn(t, "logs", e),
                rumEndpointBuilder: yn(t, "rum", e),
                sessionReplayEndpointBuilder: yn(t, "sessionReplay", e)
            }
        }(t, e)
          , r = wt(n).map((function(t) {
            return t.urlPrefix
        }
        ))
          , i = function(t, e, n) {
            if (!t.replica)
                return;
            var r = Ct({}, t, {
                site: hn,
                clientToken: t.replica.clientToken
            })
              , i = {
                logsEndpointBuilder: yn(r, "logs", n),
                rumEndpointBuilder: yn(r, "rum", n)
            };
            return e.push.apply(e, wt(i).map((function(t) {
                return t.urlPrefix
            }
            ))),
            Ct({
                applicationId: t.replica.applicationId
            }, i)
        }(t, r, e);
        return Ct({
            isIntakeUrl: function(t) {
                return r.some((function(e) {
                    return 0 === t.indexOf(e)
                }
                ))
            },
            replica: i,
            site: t.site || hn
        }, n)
    }
    var Tn = {
        ALLOW: "allow",
        MASK: "mask",
        MASK_USER_INPUT: "mask-user-input"
    };
    function Cn(t) {
        var e, n, r;
        if (t && t.clientToken) {
            var i = null !== (e = t.sessionSampleRate) && void 0 !== e ? e : t.sampleRate;
            if (void 0 === i || ae(i))
                if (void 0 === t.telemetrySampleRate || ae(t.telemetrySampleRate)) {
                    if (void 0 === t.telemetryConfigurationSampleRate || ae(t.telemetryConfigurationSampleRate))
                        return Array.isArray(t.enableExperimentalFeatures) && t.enableExperimentalFeatures.filter((function(t) {
                            return on(dt, t)
                        }
                        )).forEach((function(t) {
                            kt.add(t)
                        }
                        )),
                        Ct({
                            beforeSend: t.beforeSend && ct(t.beforeSend, "beforeSend threw an error:"),
                            cookieOptions: An(t),
                            sessionSampleRate: null != i ? i : 100,
                            telemetrySampleRate: null !== (n = t.telemetrySampleRate) && void 0 !== n ? n : 20,
                            telemetryConfigurationSampleRate: null !== (r = t.telemetryConfigurationSampleRate) && void 0 !== r ? r : 5,
                            service: t.service,
                            silentMultipleInit: !!t.silentMultipleInit,
                            batchBytesLimit: 16 * It,
                            eventRateLimiterThreshold: 3e3,
                            maxTelemetryEventsPerPage: 15,
                            flushTimeout: 30 * ce,
                            batchMessagesLimit: 50,
                            messageBytesLimit: 256 * It
                        }, Sn(t));
                    ut.error("Telemetry Configuration Sample Rate should be a number between 0 and 100")
                } else
                    ut.error("Telemetry Sample Rate should be a number between 0 and 100");
            else
                ut.error("Session Sample Rate should be a number between 0 and 100")
        } else
            ut.error("Client Token is not configured, we will not send any data.")
    }
    function An(t) {
        var e = {};
        return e.secure = function(t) {
            return !!t.useSecureSessionCookie || !!t.useCrossSiteSessionCookie
        }(t),
        e.crossSite = !!t.useCrossSiteSessionCookie,
        t.trackSessionAcrossSubdomains && (e.domain = function() {
            if (void 0 === Re) {
                for (var t = "dd_site_test_".concat(Ce()), e = window.location.hostname.split("."), n = e.pop(); e.length && !Oe(t); )
                    n = "".concat(e.pop(), ".").concat(n),
                    Ie(t, "test", ce, {
                        domain: n
                    });
                Ne(t, {
                    domain: n
                }),
                Re = n
            }
            return Re
        }()),
        e
    }
    function kn(t) {
        var e = Ht(t);
        return "string" === e || "function" === e || t instanceof RegExp
    }
    function Rn(t, e, n) {
        return void 0 === n && (n = !1),
        t.some((function(t) {
            try {
                if ("function" == typeof t)
                    return t(e);
                if (t instanceof RegExp)
                    return t.test(e);
                if ("string" == typeof t)
                    return n ? St(e, t) : t === e
            } catch (r) {
                ut.error(r)
            }
            return !1
        }
        ))
    }
    function xn(t) {
        0 !== t.status || t.isAborted || (t.traceId = void 0,
        t.spanId = void 0,
        t.traceSampled = void 0)
    }
    function In(t, e, n, r) {
        if (void 0 !== On() && n.findTrackedSession()) {
            var i, o, a, s, u, c = bt(t.allowedTracingUrls, (function(t) {
                return Rn([t.match], e.url, !0)
            }
            ));
            if (c)
                e.traceId = new Nn,
                e.spanId = new Nn,
                e.traceSampled = !se(t.traceSampleRate) || ie(t.traceSampleRate),
                r((i = e.traceId,
                o = e.spanId,
                a = e.traceSampled,
                s = c.propagatorTypes,
                u = {},
                s.forEach((function(t) {
                    switch (t) {
                    case "datadog":
                        Ct(u, {
                            "x-datadog-origin": "rum",
                            "x-datadog-parent-id": o.toDecimalString(),
                            "x-datadog-sampling-priority": a ? "1" : "0",
                            "x-datadog-trace-id": i.toDecimalString()
                        });
                        break;
                    case "tracecontext":
                        Ct(u, {
                            traceparent: "00-0000000000000000".concat(i.toPaddedHexadecimalString(), "-").concat(o.toPaddedHexadecimalString(), "-0").concat(a ? "1" : "0")
                        });
                        break;
                    case "b3":
                        Ct(u, {
                            b3: "".concat(i.toPaddedHexadecimalString(), "-").concat(o.toPaddedHexadecimalString(), "-").concat(a ? "1" : "0")
                        });
                        break;
                    case "b3multi":
                        Ct(u, {
                            "X-B3-TraceId": i.toPaddedHexadecimalString(),
                            "X-B3-SpanId": o.toPaddedHexadecimalString(),
                            "X-B3-Sampled": a ? "1" : "0"
                        })
                    }
                }
                )),
                u))
        }
    }
    function On() {
        return window.crypto || window.msCrypto
    }
    var Nn = function() {
        function t() {
            this.buffer = new Uint8Array(8),
            On().getRandomValues(this.buffer),
            this.buffer[0] = 127 & this.buffer[0]
        }
        return t.prototype.toString = function(t) {
            var e = this.readInt32(0)
              , n = this.readInt32(4)
              , r = "";
            do {
                var i = e % t * 4294967296 + n;
                e = Math.floor(e / t),
                n = Math.floor(i / t),
                r = (i % t).toString(t) + r
            } while (e || n);
            return r
        }
        ,
        t.prototype.toDecimalString = function() {
            return this.toString(10)
        }
        ,
        t.prototype.toPaddedHexadecimalString = function() {
            var t = this.toString(16);
            return Array(17 - t.length).join("0") + t
        }
        ,
        t.prototype.readInt32 = function(t) {
            return 16777216 * this.buffer[t] + (this.buffer[t + 1] << 16) + (this.buffer[t + 2] << 8) + this.buffer[t + 3]
        }
        ,
        t
    }();
    function Pn(t) {
        var e, n, r, i, o, a;
        if (t.applicationId)
            if (void 0 === t.sessionReplaySampleRate || ae(t.sessionReplaySampleRate)) {
                var s = null !== (e = t.premiumSampleRate) && void 0 !== e ? e : t.replaySampleRate;
                if (void 0 !== s && void 0 !== t.sessionReplaySampleRate && (ut.warn("Ignoring Premium Sample Rate because Session Replay Sample Rate is set"),
                s = void 0),
                void 0 === s || ae(s)) {
                    var u = null !== (n = t.traceSampleRate) && void 0 !== n ? n : t.tracingSampleRate;
                    if (void 0 === u || ae(u))
                        if (void 0 === t.excludedActivityUrls || Array.isArray(t.excludedActivityUrls)) {
                            var c = function(t) {
                                void 0 !== t.allowedTracingUrls && void 0 !== t.allowedTracingOrigins && ut.warn("Both allowedTracingUrls and allowedTracingOrigins (deprecated) have been defined. The parameter allowedTracingUrls will override allowedTracingOrigins.");
                                if (void 0 !== t.allowedTracingUrls) {
                                    if (!Array.isArray(t.allowedTracingUrls))
                                        return void ut.error("Allowed Tracing URLs should be an array");
                                    if (0 !== t.allowedTracingUrls.length && void 0 === t.service)
                                        return void ut.error("Service needs to be configured when tracing is enabled");
                                    var e = [];
                                    return t.allowedTracingUrls.forEach((function(t) {
                                        var n;
                                        kn(t) ? e.push({
                                            match: t,
                                            propagatorTypes: ["datadog"]
                                        }) : "object" === Ht(n = t) && kn(n.match) && Array.isArray(n.propagatorTypes) ? e.push(t) : ut.warn("Allowed Tracing Urls parameters should be a string, RegExp, function, or an object. Ignoring parameter", t)
                                    }
                                    )),
                                    e
                                }
                                if (void 0 !== t.allowedTracingOrigins) {
                                    if (!Array.isArray(t.allowedTracingOrigins))
                                        return void ut.error("Allowed Tracing Origins should be an array");
                                    if (0 !== t.allowedTracingOrigins.length && void 0 === t.service)
                                        return void ut.error("Service needs to be configured when tracing is enabled");
                                    var n = [];
                                    return t.allowedTracingOrigins.forEach((function(t) {
                                        var e = function(t) {
                                            var e;
                                            "string" == typeof t ? e = t : t instanceof RegExp ? e = function(e) {
                                                return t.test(un(e))
                                            }
                                            : "function" == typeof t && (e = function(e) {
                                                return t(un(e))
                                            }
                                            );
                                            if (void 0 === e)
                                                return void ut.warn("Allowed Tracing Origins parameters should be a string, RegExp or function. Ignoring parameter", t);
                                            return {
                                                match: e,
                                                propagatorTypes: ["datadog"]
                                            }
                                        }(t);
                                        e && n.push(e)
                                    }
                                    )),
                                    n
                                }
                                return []
                            }(t);
                            if (c) {
                                var l = Cn(t);
                                if (l) {
                                    var d = !!(null !== (r = t.trackUserInteractions) && void 0 !== r ? r : t.trackInteractions)
                                      , f = !!t.trackFrustrations;
                                    return Ct({
                                        applicationId: t.applicationId,
                                        version: t.version,
                                        actionNameAttribute: t.actionNameAttribute,
                                        sessionReplaySampleRate: null !== (o = null !== (i = t.sessionReplaySampleRate) && void 0 !== i ? i : s) && void 0 !== o ? o : 100,
                                        oldPlansBehavior: void 0 === t.sessionReplaySampleRate,
                                        traceSampleRate: u,
                                        allowedTracingUrls: c,
                                        excludedActivityUrls: null !== (a = t.excludedActivityUrls) && void 0 !== a ? a : [],
                                        trackUserInteractions: d || f,
                                        trackFrustrations: f,
                                        trackViewsManually: !!t.trackViewsManually,
                                        trackResources: t.trackResources,
                                        trackLongTasks: t.trackLongTasks,
                                        subdomain: t.subdomain,
                                        defaultPrivacyLevel: on(Tn, t.defaultPrivacyLevel) ? t.defaultPrivacyLevel : Tn.MASK_USER_INPUT,
                                        customerDataTelemetrySampleRate: 1
                                    }, l)
                                }
                            }
                        } else
                            ut.error("Excluded Activity Urls should be an array");
                    else
                        ut.error("Trace Sample Rate should be a number between 0 and 100")
                } else
                    ut.error("Premium Sample Rate should be a number between 0 and 100")
            } else
                ut.error("Session Replay Sample Rate should be a number between 0 and 100");
        else
            ut.error("Application ID is not configured, no RUM data will be collected.")
    }
    function Ln(t) {
        var e = new Set;
        return Array.isArray(t.allowedTracingUrls) && t.allowedTracingUrls.length > 0 && t.allowedTracingUrls.forEach((function(t) {
            kn(t) ? e.add("datadog") : "object" === Ht(t) && Array.isArray(t.propagatorTypes) && t.propagatorTypes.forEach((function(t) {
                return e.add(t)
            }
            ))
        }
        )),
        Array.isArray(t.allowedTracingOrigins) && t.allowedTracingOrigins.length > 0 && e.add("datadog"),
        yt(e)
    }
    function Mn(t, e, n) {
        return {
            context: t.getContext(),
            user: e.getContext(),
            hasReplay: !!n.isRecording() || void 0
        }
    }
    function Un(t, e) {
        var n = window.__ddBrowserSdkExtensionCallback;
        n && n({
            type: t,
            payload: e
        })
    }
    var Dn = function() {
        function t(t) {
            this.onFirstSubscribe = t,
            this.observers = []
        }
        return t.prototype.subscribe = function(t) {
            var e = this;
            return !this.observers.length && this.onFirstSubscribe && (this.onLastUnsubscribe = this.onFirstSubscribe() || void 0),
            this.observers.push(t),
            {
                unsubscribe: function() {
                    e.observers = e.observers.filter((function(e) {
                        return t !== e
                    }
                    )),
                    !e.observers.length && e.onLastUnsubscribe && e.onLastUnsubscribe()
                }
            }
        }
        ,
        t.prototype.notify = function(t) {
            this.observers.forEach((function(e) {
                return e(t)
            }
            ))
        }
        ,
        t
    }();
    function Fn() {
        for (var t = [], e = 0; e < arguments.length; e++)
            t[e] = arguments[e];
        var n = new Dn((function() {
            var e = t.map((function(t) {
                return t.subscribe((function(t) {
                    return n.notify(t)
                }
                ))
            }
            ));
            return function() {
                return e.forEach((function(t) {
                    return t.unsubscribe()
                }
                ))
            }
        }
        ));
        return n
    }
    function Bn(t, e, n, r) {
        return zn(t, [e], n, r)
    }
    function zn(t, e, n, r) {
        var i = void 0 === r ? {} : r
          , o = i.once
          , a = i.capture
          , s = i.passive
          , u = vt(o ? function(t) {
            d(),
            n(t)
        }
        : n)
          , c = s ? {
            capture: a,
            passive: s
        } : a
          , l = Lt(t, "addEventListener");
        function d() {
            var n = Lt(t, "removeEventListener");
            e.forEach((function(e) {
                return n.call(t, e, u, c)
            }
            ))
        }
        return e.forEach((function(e) {
            return l.call(t, e, u, c)
        }
        )),
        {
            stop: d
        }
    }
    var Hn = {
        HIDDEN: "visibility_hidden",
        UNLOADING: "before_unload",
        PAGEHIDE: "page_hide",
        FROZEN: "page_frozen"
    };
    function jn(t) {
        return _t(wt(Hn), t)
    }
    var qn, Vn = {
        log: "log",
        configuration: "configuration"
    }, Zn = ["https://www.datadoghq-browser-agent.com", "https://www.datad0g-browser-agent.com", "http://localhost", "<anonymous>"], Gn = ["ddog-gov.com"], Wn = {
        maxEventsPerPage: 0,
        sentEventCount: 0,
        telemetryEnabled: !1,
        telemetryConfigurationEnabled: !1
    };
    function Yn(t, e) {
        var n, r = new Dn;
        return Wn.telemetryEnabled = !_t(Gn, e.site) && ie(e.telemetrySampleRate),
        Wn.telemetryConfigurationEnabled = Wn.telemetryEnabled && ie(e.telemetryConfigurationSampleRate),
        qn = function(e) {
            if (Wn.telemetryEnabled) {
                var i = function(t, e) {
                    return Vt({
                        type: "telemetry",
                        date: ge(),
                        service: t,
                        version: "4.40.0",
                        source: "browser",
                        _dd: {
                            format_version: 2
                        },
                        telemetry: e,
                        experimental_features: yt(xt())
                    }, void 0 !== n ? n() : {})
                }(t, e);
                r.notify(i),
                Un("telemetry", i)
            }
        }
        ,
        lt = $n,
        Ct(Wn, {
            maxEventsPerPage: e.maxTelemetryEventsPerPage,
            sentEventCount: 0
        }),
        {
            setContextProvider: function(t) {
                n = t
            },
            observable: r,
            enabled: Wn.telemetryEnabled
        }
    }
    function Kn(t, e) {
        gt(st.debug, t, e),
        Xn(Ct({
            type: Vn.log,
            message: t,
            status: "debug"
        }, e))
    }
    function $n(t) {
        Xn(Ct({
            type: Vn.log,
            status: "error"
        }, function(t) {
            if (t instanceof Error) {
                var e = ze(t);
                return {
                    error: {
                        kind: e.name,
                        stack: Je(Jn(e))
                    },
                    message: e.message
                }
            }
            return {
                error: {
                    stack: $e
                },
                message: "".concat("Uncaught", " ").concat(Zt(t))
            }
        }(t)))
    }
    function Xn(t) {
        qn && Wn.sentEventCount < Wn.maxEventsPerPage && (Wn.sentEventCount += 1,
        qn(t))
    }
    function Jn(t) {
        return t.stack = t.stack.filter((function(t) {
            return !t.url || Zn.some((function(e) {
                return St(t.url, e)
            }
            ))
        }
        )),
        t
    }
    function Qn() {
        var t, e = window;
        if (e.Zone && (t = Lt(e, "MutationObserver"),
        e.MutationObserver && t === e.MutationObserver)) {
            var n = Lt(new e.MutationObserver(zt), "originalInstance");
            t = n && n.constructor
        }
        return t || (t = e.MutationObserver),
        t
    }
    function tr(t, e) {
        document.readyState === t || "complete" === document.readyState ? e() : Bn(window, "complete" === t ? "load" : "DOMContentLoaded", e, {
            once: !0
        })
    }
    var er = "initial_document"
      , nr = [["document", function(t) {
        return er === t
    }
    ], ["xhr", function(t) {
        return "xmlhttprequest" === t
    }
    ], ["fetch", function(t) {
        return "fetch" === t
    }
    ], ["beacon", function(t) {
        return "beacon" === t
    }
    ], ["css", function(t, e) {
        return /\.css$/i.test(e)
    }
    ], ["js", function(t, e) {
        return /\.js$/i.test(e)
    }
    ], ["image", function(t, e) {
        return _t(["image", "img", "icon"], t) || null !== /\.(gif|jpg|jpeg|tiff|png|svg|ico)$/i.exec(e)
    }
    ], ["font", function(t, e) {
        return null !== /\.(woff|eot|woff2|ttf)$/i.exec(e)
    }
    ], ["media", function(t, e) {
        return _t(["audio", "video"], t) || null !== /\.(mp3|mp4)$/i.exec(e)
    }
    ]];
    function rr(t) {
        var e = t.name;
        if (!function(t) {
            try {
                return !!cn(t)
            } catch (o) {
                return !1
            }
        }(e))
            return Kn('Failed to construct URL for "'.concat(t.name, '"')),
            "other";
        for (var n = function(t) {
            var e = cn(t).pathname;
            return "/" === e[0] ? e : "/".concat(e)
        }(e), r = 0, i = nr; r < i.length; r++) {
            var o = i[r]
              , a = o[0];
            if ((0,
            o[1])(t.initiatorType, n))
                return a
        }
        return "other"
    }
    function ir() {
        for (var t = [], e = 0; e < arguments.length; e++)
            t[e] = arguments[e];
        for (var n = 1; n < t.length; n += 1)
            if (t[n - 1] > t[n])
                return !1;
        return !0
    }
    function or(t) {
        var e = ar(t);
        if (e) {
            var n = e.startTime
              , r = e.fetchStart
              , i = e.redirectStart
              , o = e.redirectEnd
              , a = e.domainLookupStart
              , s = e.domainLookupEnd
              , u = e.connectStart
              , c = e.secureConnectionStart
              , l = e.connectEnd
              , d = e.requestStart
              , f = e.responseStart
              , p = {
                download: ur(n, f, e.responseEnd),
                first_byte: ur(n, d, f)
            };
            return l !== r && (p.connect = ur(n, u, l),
            ir(u, c, l) && (p.ssl = ur(n, c, l))),
            s !== r && (p.dns = ur(n, a, s)),
            sr(t) && (p.redirect = ur(n, i, o)),
            p
        }
    }
    function ar(t) {
        if (ir(t.startTime, t.fetchStart, t.domainLookupStart, t.domainLookupEnd, t.connectStart, t.connectEnd, t.requestStart, t.responseStart, t.responseEnd)) {
            if (!sr(t))
                return t;
            var e = t.redirectStart
              , n = t.redirectEnd;
            if (e < t.startTime && (e = t.startTime),
            n < t.startTime && (n = t.fetchStart),
            ir(t.startTime, e, n, t.fetchStart))
                return Ct({}, t, {
                    redirectEnd: n,
                    redirectStart: e
                })
        }
    }
    function sr(t) {
        return t.fetchStart !== t.startTime
    }
    function ur(t, e, n) {
        return {
            duration: ve(we(e, n)),
            start: ve(we(t, e))
        }
    }
    function cr(t) {
        if (t.startTime < t.responseStart)
            return t.decodedBodySize
    }
    function lr(t, e) {
        return e && !t.isIntakeUrl(e)
    }
    function dr(t) {
        return t.nodeType === Node.TEXT_NODE
    }
    function fr(t) {
        return t.nodeType === Node.ELEMENT_NODE
    }
    function pr(t) {
        return fr(t) && Boolean(t.shadowRoot)
    }
    function hr(t) {
        var e = t;
        return !!e.host && e.nodeType === Node.DOCUMENT_FRAGMENT_NODE && fr(e.host)
    }
    function vr(t) {
        return hr(t) ? t.host : t.parentNode
    }
    var mr = 2 * le;
    function gr(t) {
        var e = function(t) {
            var e = t.querySelector("meta[name=dd-trace-id]")
              , n = t.querySelector("meta[name=dd-trace-time]");
            return _r(e && e.content, n && n.content)
        }(t) || function(t) {
            var e = function(t) {
                for (var e = 0; e < t.childNodes.length; e += 1) {
                    if (n = yr(t.childNodes[e]))
                        return n
                }
                if (t.body)
                    for (e = t.body.childNodes.length - 1; e >= 0; e -= 1) {
                        var n, r = t.body.childNodes[e];
                        if (n = yr(r))
                            return n;
                        if (!dr(r))
                            break
                    }
            }(t);
            if (!e)
                return;
            return _r(Ae(e, "trace-id"), Ae(e, "trace-time"))
        }(t);
        if (e && !(e.traceTime <= me() - mr))
            return e.traceId
    }
    function _r(t, e) {
        var n = e && Number(e);
        if (t && n)
            return {
                traceId: t,
                traceTime: n
            }
    }
    function yr(t) {
        if (t && function(t) {
            return t.nodeType === Node.COMMENT_NODE
        }(t)) {
            var e = /^\s*DATADOG;(.*?)\s*$/.exec(t.data);
            if (e)
                return e[1]
        }
    }
    function br() {
        return void 0 !== window.performance && "getEntries"in performance
    }
    function wr(t) {
        return window.PerformanceObserver && void 0 !== PerformanceObserver.supportedEntryTypes && PerformanceObserver.supportedEntryTypes.includes(t)
    }
    function Er(t, e) {
        var n;
        if (n = function(n) {
            Tr(t, e, [n])
        }
        ,
        tr("interactive", (function() {
            var t, e = {
                entryType: "resource",
                initiatorType: er,
                traceId: gr(document)
            };
            if (wr("navigation") && performance.getEntriesByType("navigation").length > 0)
                t = Ct(performance.getEntriesByType("navigation")[0].toJSON(), e);
            else {
                var r = Sr();
                t = Ct(r, {
                    decodedBodySize: 0,
                    duration: r.responseEnd,
                    name: window.location.href,
                    startTime: 0
                }, e)
            }
            n(t)
        }
        )),
        br()) {
            var r = performance.getEntries();
            Mt((function() {
                return Tr(t, e, r)
            }
            ))
        }
        if (window.PerformanceObserver) {
            var i = vt((function(n) {
                return Tr(t, e, n.getEntries())
            }
            ))
              , o = ["resource", "navigation", "longtask", "paint"]
              , a = ["largest-contentful-paint", "first-input", "layout-shift"];
            try {
                a.forEach((function(t) {
                    new PerformanceObserver(i).observe({
                        type: t,
                        buffered: !0
                    })
                }
                ))
            } catch (s) {
                o.push.apply(o, a)
            }
            new PerformanceObserver(i).observe({
                entryTypes: o
            }),
            br() && "addEventListener"in performance && Bn(performance, "resourcetimingbufferfull", (function() {
                performance.clearResourceTimings()
            }
            ))
        }
        wr("navigation") || function(t) {
            function e() {
                t(Ct(Sr(), {
                    entryType: "navigation"
                }))
            }
            tr("complete", (function() {
                Mt(e)
            }
            ))
        }((function(n) {
            Tr(t, e, [n])
        }
        )),
        wr("first-input") || function(t) {
            var e = me()
              , n = !1
              , r = zn(window, ["click", "mousedown", "keydown", "touchstart", "pointerdown"], (function(t) {
                if (t.cancelable) {
                    var e = {
                        entryType: "first-input",
                        processingStart: _e(),
                        startTime: t.timeStamp
                    };
                    "pointerdown" === t.type ? i(e) : o(e)
                }
            }
            ), {
                passive: !0,
                capture: !0
            }).stop;
            function i(t) {
                zn(window, ["pointerup", "pointercancel"], (function(e) {
                    "pointerup" === e.type && o(t)
                }
                ), {
                    once: !0
                })
            }
            function o(i) {
                if (!n) {
                    n = !0,
                    r();
                    var o = i.processingStart - i.startTime;
                    o >= 0 && o < me() - e && t(i)
                }
            }
        }((function(n) {
            Tr(t, e, [n])
        }
        ))
    }
    function Sr() {
        var t = {}
          , e = performance.timing;
        for (var n in e)
            if (se(e[n])) {
                var r = n
                  , i = e[r];
                t[r] = 0 === i ? 0 : Se(i)
            }
        return t
    }
    function Tr(t, e, n) {
        var r = n.filter((function(t) {
            return "resource" === t.entryType || "navigation" === t.entryType || "paint" === t.entryType || "longtask" === t.entryType || "largest-contentful-paint" === t.entryType || "first-input" === t.entryType || "layout-shift" === t.entryType
        }
        )).filter((function(t) {
            return !function(t) {
                return "navigation" === t.entryType && t.loadEventEnd <= 0
            }(t) && !function(t, e) {
                return "resource" === e.entryType && !lr(t, e.name)
            }(e, t)
        }
        ));
        r.length && t.notify(0, r)
    }
    var Cr, Ar, kr = {
        AGENT: "agent",
        CONSOLE: "console",
        CUSTOM: "custom",
        LOGGER: "logger",
        NETWORK: "network",
        SOURCE: "source",
        REPORT: "report"
    };
    function Rr(t, e, n) {
        var r = 0
          , i = !1;
        return {
            isLimitReached: function() {
                if (0 === r && Mt((function() {
                    r = 0
                }
                ), le),
                (r += 1) <= e || i)
                    return i = !1,
                    !1;
                if (r === e + 1) {
                    i = !0;
                    try {
                        n({
                            message: "Reached max number of ".concat(t, "s by minute: ").concat(e),
                            source: kr.AGENT,
                            startClocks: ye()
                        })
                    } finally {
                        i = !1
                    }
                }
                return !0
            }
        }
    }
    function xr() {
        var t, e = "string" == typeof (t = window._DATADOG_SYNTHETICS_PUBLIC_ID || Oe(Pe)) ? t : void 0, n = function() {
            var t = window._DATADOG_SYNTHETICS_RESULT_ID || Oe(Le);
            return "string" == typeof t ? t : void 0
        }();
        if (e && n)
            return {
                test_id: e,
                result_id: n,
                injected: Ue()
            }
    }
    function Ir() {
        var t;
        return Cr || (t = new Dn((function() {
            var e = Bt((function() {
                t.notify(Or())
            }
            ), 200).throttled;
            return Bn(window, "resize", e, {
                capture: !0,
                passive: !0
            }).stop
        }
        )),
        Cr = t),
        Cr
    }
    function Or() {
        var t = window.visualViewport;
        return t ? {
            width: Number(t.width * t.scale),
            height: Number(t.height * t.scale)
        } : {
            width: Number(window.innerWidth || 0),
            height: Number(window.innerHeight || 0)
        }
    }
    function Nr(t, e) {
        for (var n = t, r = 0, i = e.split("."); r < i.length; r++) {
            var o = i[r];
            if (!Lr(n, o))
                return;
            n = n[o]
        }
        return n
    }
    function Pr(t, e, n) {
        for (var r = t, i = e.split("."), o = 0; o < i.length; o += 1) {
            var a = i[o];
            if (!Lr(r, a))
                return;
            o !== i.length - 1 ? r = r[a] : r[a] = n
        }
    }
    function Lr(t, e) {
        return "object" == typeof t && null !== t && Object.prototype.hasOwnProperty.call(t, e)
    }
    var Mr = ["view.url", "view.referrer", "action.target.name", "error.message", "error.stack", "error.resource.url", "resource.url"]
      , Ur = Mr.concat(["context"]);
    function Dr(t, e, n, r, i, o, a, s) {
        var u, c = ((u = {}).error = Rr("error", t.eventRateLimiterThreshold, s),
        u.action = Rr("action", t.eventRateLimiterThreshold, s),
        u), l = xr(), d = function() {
            var t, e = null === (t = window.Cypress) || void 0 === t ? void 0 : t.env("traceId");
            if ("string" == typeof e)
                return {
                    test_execution_id: e
                }
        }();
        e.subscribe(10, (function(s) {
            var u, f = s.startTime, p = s.rawRumEvent, h = s.domainContext, v = s.savedCommonContext, m = s.customerContext, g = r.findView(f), _ = i.findUrl(f), y = n.findTrackedSession(f);
            if (y && g && _) {
                var b = v || a()
                  , w = o.findActionId(f)
                  , E = Vt({
                    _dd: {
                        format_version: 2,
                        drift: Math.round(me() - Ee(Te(), performance.now())),
                        session: {
                            plan: y.plan
                        },
                        browser_sdk_version: Fe() ? "4.40.0" : void 0
                    },
                    application: {
                        id: t.applicationId
                    },
                    date: ge(),
                    service: g.service || t.service,
                    version: g.version || t.version,
                    source: "browser",
                    session: {
                        id: y.id,
                        type: l ? "synthetics" : d ? "ci_test" : "user"
                    },
                    view: {
                        id: g.id,
                        name: g.name,
                        url: _.url,
                        referrer: _.referrer
                    },
                    action: (u = p,
                    -1 !== ["error", "resource", "long_task"].indexOf(u.type) && w ? {
                        id: w
                    } : void 0),
                    synthetics: l,
                    ci_test: d,
                    display: (Ar || (Ar = Or(),
                    Ir().subscribe((function(t) {
                        Ar = t
                    }
                    )).unsubscribe),
                    {
                        viewport: Ar
                    })
                }, p);
                E.context = Vt(b.context, m),
                "has_replay"in E.session || (E.session.has_replay = b.hasReplay),
                an(b.user) || (E.usr = b.user),
                function(t, e, n, r) {
                    var i;
                    if (e) {
                        var o = function(t, e, n) {
                            var r = qt(t)
                              , i = n(r);
                            return e.forEach((function(e) {
                                var n = Nr(t, e)
                                  , i = Nr(r, e)
                                  , o = Ht(n)
                                  , a = Ht(i);
                                a === o ? Pr(t, e, Rt(dt.SANITIZE_INPUTS) ? $t(i) : i) : "object" !== o || "undefined" !== a && "null" !== a || Pr(t, e, {})
                            }
                            )),
                            i
                        }(t, "view" === t.type ? Mr : Ur, (function(t) {
                            return e(t, n)
                        }
                        ));
                        if (!1 === o && "view" !== t.type)
                            return !1;
                        !1 === o && ut.warn("Can't dismiss view events using beforeSend!")
                    }
                    var a = null === (i = r[t.type]) || void 0 === i ? void 0 : i.isLimitReached();
                    return !a
                }(E, t.beforeSend, h, c) && (an(E.context) && delete E.context,
                e.notify(11, E))
            }
        }
        ))
    }
    var Fr = 500
      , Br = 2500
      , zr = [];
    function Hr() {
        document.hasFocus() && jr();
        var t, e, n = (t = jr,
        Bn(window, "focus", (function(e) {
            e.isTrusted && t()
        }
        ))).stop, r = (e = qr,
        Bn(window, "blur", (function(t) {
            t.isTrusted && e()
        }
        ))).stop;
        return {
            isInForegroundAt: Vr,
            selectInForegroundPeriodsFor: Zr,
            stop: function() {
                zr = [],
                n(),
                r()
            }
        }
    }
    function jr() {
        if (!(zr.length > Br)) {
            var t = zr[zr.length - 1]
              , e = _e();
            void 0 !== t && void 0 === t.end || zr.push({
                start: e
            })
        }
    }
    function qr() {
        if (0 !== zr.length) {
            var t = zr[zr.length - 1]
              , e = _e();
            void 0 === t.end && (t.end = e)
        }
    }
    function Vr(t) {
        for (var e = zr.length - 1; e >= 0; e--) {
            var n = zr[e];
            if (void 0 !== n.end && t > n.end)
                break;
            if (t > n.start && (void 0 === n.end || t < n.end))
                return !0
        }
        return !1
    }
    function Zr(t, e) {
        for (var n = Ee(t, e), r = [], i = Math.max(0, zr.length - Fr), o = zr.length - 1; o >= i; o--) {
            var a = zr[o];
            if (void 0 !== a.end && t > a.end)
                break;
            if (!(n < a.start)) {
                var s = t > a.start ? t : a.start
                  , u = we(t, s)
                  , c = we(s, void 0 === a.end || n < a.end ? n : a.end);
                r.unshift({
                    start: ve(u),
                    duration: ve(c)
                })
            }
        }
        return r
    }
    var Gr, Wr = function() {
        function t() {
            this.callbacks = {}
        }
        return t.prototype.notify = function(t, e) {
            var n = this.callbacks[t];
            n && n.forEach((function(t) {
                return t(e)
            }
            ))
        }
        ,
        t.prototype.subscribe = function(t, e) {
            var n = this;
            return this.callbacks[t] || (this.callbacks[t] = []),
            this.callbacks[t].push(e),
            {
                unsubscribe: function() {
                    n.callbacks[t] = n.callbacks[t].filter((function(t) {
                        return e !== t
                    }
                    ))
                }
            }
        }
        ,
        t
    }(), Yr = 4 * de, Kr = 15 * le, $r = 1 / 0, Xr = le, Jr = function() {
        function t(t) {
            var e = this;
            this.expireDelay = t,
            this.entries = [],
            this.clearOldValuesInterval = Dt((function() {
                return e.clearOldValues()
            }
            ), Xr)
        }
        return t.prototype.add = function(t, e) {
            var n = this
              , r = {
                value: t,
                startTime: e,
                endTime: $r,
                remove: function() {
                    var t = n.entries.indexOf(r);
                    t >= 0 && n.entries.splice(t, 1)
                },
                close: function(t) {
                    r.endTime = t
                }
            };
            return this.entries.unshift(r),
            r
        }
        ,
        t.prototype.find = function(t) {
            void 0 === t && (t = $r);
            for (var e = 0, n = this.entries; e < n.length; e++) {
                var r = n[e];
                if (r.startTime <= t) {
                    if (t <= r.endTime)
                        return r.value;
                    break
                }
            }
        }
        ,
        t.prototype.closeActive = function(t) {
            var e = this.entries[0];
            e && e.endTime === $r && e.close(t)
        }
        ,
        t.prototype.findAll = function(t) {
            return void 0 === t && (t = $r),
            this.entries.filter((function(e) {
                return e.startTime <= t && t <= e.endTime
            }
            )).map((function(t) {
                return t.value
            }
            ))
        }
        ,
        t.prototype.reset = function() {
            this.entries = []
        }
        ,
        t.prototype.stop = function() {
            Ft(this.clearOldValuesInterval)
        }
        ,
        t.prototype.clearOldValues = function() {
            for (var t = _e() - this.expireDelay; this.entries.length > 0 && this.entries[this.entries.length - 1].endTime < t; )
                this.entries.pop()
        }
        ,
        t
    }(), Qr = Yr;
    function ti(t, e, n) {
        var r = t[e]
          , i = n(r)
          , o = function() {
            if ("function" == typeof i)
                return i.apply(this, arguments)
        };
        return t[e] = o,
        {
            stop: function() {
                t[e] === o ? t[e] = r : i = r
            }
        }
    }
    function ei(t, e, n) {
        var r = n.before
          , i = n.after;
        return ti(t, e, (function(t) {
            return function() {
                var e, n = arguments;
                return r && mt(r, this, n),
                "function" == typeof t && (e = t.apply(this, n)),
                i && mt(i, this, n),
                e
            }
        }
        ))
    }
    function ni(t, e, n) {
        var r = Object.getOwnPropertyDescriptor(t, e);
        if (!r || !r.set || !r.configurable)
            return {
                stop: zt
            };
        var i = function(t, e) {
            Mt((function() {
                n(t, e)
            }
            ), 0)
        }
          , o = function(t) {
            r.set.call(this, t),
            i(this, t)
        };
        return Object.defineProperty(t, e, {
            set: o
        }),
        {
            stop: function() {
                var n;
                (null === (n = Object.getOwnPropertyDescriptor(t, e)) || void 0 === n ? void 0 : n.set) === o ? Object.defineProperty(t, e, r) : i = zt
            }
        }
    }
    var ri, ii = new WeakMap;
    function oi() {
        var t;
        return Gr || (t = new Dn((function() {
            var e = ei(XMLHttpRequest.prototype, "open", {
                before: ai
            }).stop
              , n = ei(XMLHttpRequest.prototype, "send", {
                before: function() {
                    si.call(this, t)
                }
            }).stop
              , r = ei(XMLHttpRequest.prototype, "abort", {
                before: ui
            }).stop;
            return function() {
                e(),
                n(),
                r()
            }
        }
        )),
        Gr = t),
        Gr
    }
    function ai(t, e) {
        ii.set(this, {
            state: "open",
            method: t,
            url: sn(String(e))
        })
    }
    function si(t) {
        var e = this
          , n = ii.get(this);
        if (n) {
            var r = n;
            r.state = "start",
            r.startTime = _e(),
            r.startClocks = ye(),
            r.isAborted = !1,
            r.xhr = this;
            var i = !1
              , o = ei(this, "onreadystatechange", {
                before: function() {
                    this.readyState === XMLHttpRequest.DONE && a()
                }
            }).stop
              , a = function() {
                if (s(),
                o(),
                !i) {
                    i = !0;
                    var a = n;
                    a.state = "complete",
                    a.duration = we(r.startClocks.timeStamp, ge()),
                    a.status = e.status,
                    t.notify(rn(a))
                }
            }
              , s = Bn(this, "loadend", a).stop;
            t.notify(r)
        }
    }
    function ui() {
        var t = ii.get(this);
        t && (t.isAborted = !0)
    }
    function ci() {
        var t;
        return ri || (t = new Dn((function() {
            if (window.fetch) {
                var e = ti(window, "fetch", (function(e) {
                    return function(n, r) {
                        var i, o = mt(li, null, [t, n, r]);
                        return o ? (i = e.call(this, o.input, o.init),
                        mt(di, null, [t, i, o])) : i = e.call(this, n, r),
                        i
                    }
                }
                )).stop;
                return e
            }
        }
        )),
        ri = t),
        ri
    }
    function li(t, e, n) {
        var r = n && n.method || e instanceof Request && e.method || "GET"
          , i = e instanceof Request ? e.url : sn(String(e))
          , o = {
            state: "start",
            init: n,
            input: e,
            method: r,
            startClocks: ye(),
            url: i
        };
        return t.notify(o),
        o
    }
    function di(t, e, n) {
        var r = function(e) {
            var r = n;
            r.state = "resolve",
            "stack"in e || e instanceof Error ? (r.status = 0,
            r.isAborted = e instanceof DOMException && e.code === DOMException.ABORT_ERR,
            r.error = e) : "status"in e && (r.response = e,
            r.responseType = e.type,
            r.status = e.status,
            r.isAborted = !1),
            t.notify(r)
        };
        e.then(vt(r), vt(r))
    }
    var fi = 1;
    function pi(t, e, n) {
        var r = function(t, e) {
            return {
                clearTracingIfNeeded: xn,
                traceFetch: function(n) {
                    return In(t, n, e, (function(t) {
                        var e;
                        if (n.input instanceof Request && !(null === (e = n.init) || void 0 === e ? void 0 : e.headers))
                            n.input = new Request(n.input),
                            Object.keys(t).forEach((function(e) {
                                n.input.headers.append(e, t[e])
                            }
                            ));
                        else {
                            n.init = rn(n.init);
                            var r = [];
                            n.init.headers instanceof Headers ? n.init.headers.forEach((function(t, e) {
                                r.push([e, t])
                            }
                            )) : Array.isArray(n.init.headers) ? n.init.headers.forEach((function(t) {
                                r.push(t)
                            }
                            )) : n.init.headers && Object.keys(n.init.headers).forEach((function(t) {
                                r.push([t, n.init.headers[t]])
                            }
                            )),
                            n.init.headers = r.concat(Et(t))
                        }
                    }
                    ))
                },
                traceXhr: function(n, r) {
                    return In(t, n, e, (function(t) {
                        Object.keys(t).forEach((function(e) {
                            r.setRequestHeader(e, t[e])
                        }
                        ))
                    }
                    ))
                }
            }
        }(e, n);
        !function(t, e, n) {
            var r = oi().subscribe((function(r) {
                var i = r;
                if (lr(e, i.url))
                    switch (i.state) {
                    case "start":
                        n.traceXhr(i, i.xhr),
                        i.requestIndex = hi(),
                        t.notify(5, {
                            requestIndex: i.requestIndex,
                            url: i.url
                        });
                        break;
                    case "complete":
                        n.clearTracingIfNeeded(i),
                        t.notify(6, {
                            duration: i.duration,
                            method: i.method,
                            requestIndex: i.requestIndex,
                            spanId: i.spanId,
                            startClocks: i.startClocks,
                            status: i.status,
                            traceId: i.traceId,
                            traceSampled: i.traceSampled,
                            type: "xhr",
                            url: i.url,
                            xhr: i.xhr
                        })
                    }
            }
            ))
        }(t, e, r),
        function(t, e, n) {
            var r = ci().subscribe((function(r) {
                var i = r;
                if (lr(e, i.url))
                    switch (i.state) {
                    case "start":
                        n.traceFetch(i),
                        i.requestIndex = hi(),
                        t.notify(5, {
                            requestIndex: i.requestIndex,
                            url: i.url
                        });
                        break;
                    case "resolve":
                        !function(t, e) {
                            var n = t.response && function(t) {
                                try {
                                    return t.clone()
                                } catch (e) {
                                    return
                                }
                            }(t.response);
                            n && n.body ? function(t, e, n) {
                                var r = t.getReader()
                                  , i = []
                                  , o = 0;
                                function a() {
                                    var t, a;
                                    if (r.cancel().catch(zt),
                                    n.collectStreamBody) {
                                        var s;
                                        if (1 === i.length)
                                            s = i[0];
                                        else {
                                            s = new Uint8Array(o);
                                            var u = 0;
                                            i.forEach((function(t) {
                                                s.set(t, u),
                                                u += t.length
                                            }
                                            ))
                                        }
                                        t = s.slice(0, n.bytesLimit),
                                        a = s.length > n.bytesLimit
                                    }
                                    e(void 0, t, a)
                                }
                                !function t() {
                                    r.read().then(vt((function(e) {
                                        e.done ? a() : (n.collectStreamBody && i.push(e.value),
                                        (o += e.value.length) > n.bytesLimit ? a() : t())
                                    }
                                    )), vt((function(t) {
                                        return e(t)
                                    }
                                    )))
                                }()
                            }(n.body, (function() {
                                e(we(t.startClocks.timeStamp, ge()))
                            }
                            ), {
                                bytesLimit: Number.POSITIVE_INFINITY,
                                collectStreamBody: !1
                            }) : e(we(t.startClocks.timeStamp, ge()))
                        }(i, (function(e) {
                            n.clearTracingIfNeeded(i),
                            t.notify(6, {
                                duration: e,
                                method: i.method,
                                requestIndex: i.requestIndex,
                                responseType: i.responseType,
                                spanId: i.spanId,
                                startClocks: i.startClocks,
                                status: i.status,
                                traceId: i.traceId,
                                traceSampled: i.traceSampled,
                                type: "fetch",
                                url: i.url,
                                response: i.response,
                                init: i.init,
                                input: i.input
                            })
                        }
                        ))
                    }
            }
            ))
        }(t, e, r)
    }
    function hi() {
        var t = fi;
        return fi += 1,
        t
    }
    function vi(t) {
        var e = t.lifeCycle
          , n = t.isChildEvent
          , r = t.onChange
          , i = void 0 === r ? zt : r
          , o = {
            errorCount: 0,
            longTaskCount: 0,
            resourceCount: 0,
            actionCount: 0,
            frustrationCount: 0
        }
          , a = e.subscribe(11, (function(t) {
            if ("view" !== t.type && n(t))
                switch (t.type) {
                case "error":
                    o.errorCount += 1,
                    i();
                    break;
                case "action":
                    o.actionCount += 1,
                    t.action.frustration && (o.frustrationCount += t.action.frustration.type.length),
                    i();
                    break;
                case "long_task":
                    o.longTaskCount += 1,
                    i();
                    break;
                case "resource":
                    o.resourceCount += 1,
                    i()
                }
        }
        ));
        return {
            stop: function() {
                a.unsubscribe()
            },
            eventCounts: o
        }
    }
    var mi = 100
      , gi = 100;
    function _i(t, e, n, r, i) {
        var o = function(t, e, n) {
            var r = new Dn((function() {
                var i, o = [], a = 0;
                o.push(e.subscribe(c), t.subscribe(0, (function(t) {
                    t.some((function(t) {
                        return "resource" === t.entryType && !yi(n, t.name)
                    }
                    )) && c()
                }
                )), t.subscribe(5, (function(t) {
                    yi(n, t.url) || (void 0 === i && (i = t.requestIndex),
                    a += 1,
                    c())
                }
                )), t.subscribe(6, (function(t) {
                    yi(n, t.url) || void 0 === i || t.requestIndex < i || (a -= 1,
                    c())
                }
                )));
                var s, u = (s = c,
                ei(window, "open", {
                    before: s
                })).stop;
                return function() {
                    u(),
                    o.forEach((function(t) {
                        return t.unsubscribe()
                    }
                    ))
                }
                ;
                function c() {
                    r.notify({
                        isBusy: a > 0
                    })
                }
            }
            ));
            return r
        }(t, e, n);
        return function(t, e, n) {
            var r, i = !1, o = Mt(vt((function() {
                return c({
                    hadActivity: !1
                })
            }
            )), mi), a = void 0 !== n ? Mt(vt((function() {
                return c({
                    hadActivity: !0,
                    end: ge()
                })
            }
            )), n) : void 0, s = t.subscribe((function(t) {
                var e = t.isBusy;
                Ut(o),
                Ut(r);
                var n = ge();
                e || (r = Mt(vt((function() {
                    return c({
                        hadActivity: !0,
                        end: n
                    })
                }
                )), gi))
            }
            )), u = function() {
                i = !0,
                Ut(o),
                Ut(r),
                Ut(a),
                s.unsubscribe()
            };
            function c(t) {
                i || (u(),
                e(t))
            }
            return {
                stop: u
            }
        }(o, r, i)
    }
    function yi(t, e) {
        return Rn(t.excludedActivityUrls, e)
    }
    var bi = ce
      , wi = 100;
    function Ei(t, e) {
        var n, r = [], i = 0;
        function o(t) {
            t.stopObservable.subscribe(a),
            r.push(t),
            Ut(n),
            n = Mt(s, bi)
        }
        function a() {
            1 === i && r.every((function(t) {
                return t.isStopped()
            }
            )) && (i = 2,
            e(r))
        }
        function s() {
            Ut(n),
            0 === i && (i = 1,
            a())
        }
        return o(t),
        {
            tryAppend: function(t) {
                return 0 === i && (r.length > 0 && (e = r[r.length - 1].event,
                n = t.event,
                !(e.target === n.target && (a = e,
                u = n,
                Math.sqrt(Math.pow(a.clientX - u.clientX, 2) + Math.pow(a.clientY - u.clientY, 2)) <= wi) && e.timeStamp - n.timeStamp <= bi)) ? (s(),
                !1) : (o(t),
                !0));
                var e, n, a, u
            },
            stop: function() {
                s()
            }
        }
    }
    var Si = "data-dd-action-name";
    function Ti(t, e) {
        return Ci(t, Si) || e && Ci(t, e) || Oi(t, e, Ri) || Oi(t, e, xi) || ""
    }
    function Ci(t, e) {
        var n;
        if (function() {
            void 0 === ki && (ki = "closest"in HTMLElement.prototype);
            return ki
        }())
            n = t.closest("[".concat(e, "]"));
        else
            for (var r = t; r; ) {
                if (r.hasAttribute(e)) {
                    n = r;
                    break
                }
                r = r.parentElement
            }
        if (n)
            return Pi(Ni(n.getAttribute(e).trim()))
    }
    var Ai, ki, Ri = [function(t, e) {
        if (function() {
            void 0 === Ai && (Ai = "labels"in HTMLInputElement.prototype);
            return Ai
        }()) {
            if ("labels"in t && t.labels && t.labels.length > 0)
                return Li(t.labels[0], e)
        } else if (t.id) {
            var n = t.ownerDocument && bt(t.ownerDocument.querySelectorAll("label"), (function(e) {
                return e.htmlFor === t.id
            }
            ));
            return n && Li(n, e)
        }
    }
    , function(t) {
        if ("INPUT" === t.nodeName) {
            var e = t
              , n = e.getAttribute("type");
            if ("button" === n || "submit" === n || "reset" === n)
                return e.value
        }
    }
    , function(t, e) {
        if ("BUTTON" === t.nodeName || "LABEL" === t.nodeName || "button" === t.getAttribute("role"))
            return Li(t, e)
    }
    , function(t) {
        return t.getAttribute("aria-label")
    }
    , function(t, e) {
        var n = t.getAttribute("aria-labelledby");
        if (n)
            return n.split(/\s+/).map((function(e) {
                return function(t, e) {
                    return t.ownerDocument ? t.ownerDocument.getElementById(e) : null
                }(t, e)
            }
            )).filter((function(t) {
                return Boolean(t)
            }
            )).map((function(t) {
                return Li(t, e)
            }
            )).join(" ")
    }
    , function(t) {
        return t.getAttribute("alt")
    }
    , function(t) {
        return t.getAttribute("name")
    }
    , function(t) {
        return t.getAttribute("title")
    }
    , function(t) {
        return t.getAttribute("placeholder")
    }
    , function(t, e) {
        if ("options"in t && t.options.length > 0)
            return Li(t.options[0], e)
    }
    ], xi = [function(t, e) {
        return Li(t, e)
    }
    ], Ii = 10;
    function Oi(t, e, n) {
        for (var r = t, i = 0; i <= Ii && r && "BODY" !== r.nodeName && "HTML" !== r.nodeName && "HEAD" !== r.nodeName; ) {
            for (var o = 0, a = n; o < a.length; o++) {
                var s = (0,
                a[o])(r, e);
                if ("string" == typeof s) {
                    var u = s.trim();
                    if (u)
                        return Pi(Ni(u))
                }
            }
            if ("FORM" === r.nodeName)
                break;
            r = r.parentElement,
            i += 1
        }
    }
    function Ni(t) {
        return t.replace(/\s+/g, " ")
    }
    function Pi(t) {
        return t.length > 100 ? "".concat(ke(t, 100), " [...]") : t
    }
    function Li(t, e) {
        if (!t.isContentEditable) {
            if ("innerText"in t) {
                var n = t.innerText
                  , r = function(e) {
                    for (var r = t.querySelectorAll(e), i = 0; i < r.length; i += 1) {
                        var o = r[i];
                        if ("innerText"in o) {
                            var a = o.innerText;
                            a && a.trim().length > 0 && (n = n.replace(a, ""))
                        }
                    }
                };
                return Boolean(document.documentMode) && r("script, style"),
                r("[".concat(Si, "]")),
                e && r("[".concat(e, "]")),
                n
            }
            return t.textContent
        }
    }
    var Mi, Ui = [Si, "data-testid", "data-test", "data-qa", "data-cy", "data-test-id", "data-qa-id", "data-testing", "data-component", "data-element", "data-source-file"], Di = [Hi, function(t) {
        if (t.id && !zi(t.id))
            return "#".concat(Tt(t.id))
    }
    ], Fi = [Hi, function(t) {
        if ("BODY" === t.tagName)
            return;
        if (t.classList.length > 0)
            for (var e = 0; e < t.classList.length; e += 1) {
                var n = t.classList[e];
                if (!zi(n))
                    return "".concat(t.tagName, ".").concat(Tt(n))
            }
    }
    , function(t) {
        return t.tagName
    }
    ];
    function Bi(t, e) {
        for (var n = "", r = t; r && "HTML" !== r.nodeName; ) {
            var i = qi(r, Di, Vi, e, n);
            if (i)
                return i;
            n = qi(r, Fi, Zi, e, n) || Gi(ji(r), n),
            r = r.parentElement
        }
        return n
    }
    function zi(t) {
        return /[0-9]/.test(t)
    }
    function Hi(t, e) {
        if (e && (i = o(e)))
            return i;
        for (var n = 0, r = Ui; n < r.length; n++) {
            var i;
            if (i = o(r[n]))
                return i
        }
        function o(e) {
            if (t.hasAttribute(e))
                return "".concat(t.tagName, "[").concat(e, '="').concat(Tt(t.getAttribute(e)), '"]')
        }
    }
    function ji(t) {
        for (var e = t.parentElement.firstElementChild, n = 1; e && e !== t; )
            e.tagName === t.tagName && (n += 1),
            e = e.nextElementSibling;
        return "".concat(t.tagName, ":nth-of-type(").concat(n, ")")
    }
    function qi(t, e, n, r, i) {
        for (var o = 0, a = e; o < a.length; o++) {
            var s = (0,
            a[o])(t, r);
            if (s) {
                var u = Gi(s, i);
                if (n(t, u))
                    return u
            }
        }
    }
    function Vi(t, e) {
        return 1 === t.ownerDocument.querySelectorAll(e).length
    }
    function Zi(t, e) {
        return 1 === t.parentElement.querySelectorAll(function() {
            if (void 0 === Mi)
                try {
                    document.querySelector(":scope"),
                    Mi = !0
                } catch (Zs) {
                    Mi = !1
                }
            return Mi
        }() ? Gi(":scope", e) : e).length
    }
    function Gi(t, e) {
        return e ? "".concat(t, ">").concat(e) : t
    }
    function Wi() {
        var t = window.getSelection();
        return !t || t.isCollapsed
    }
    function Yi(t) {
        return t.target instanceof Element && !1 !== t.isPrimary
    }
    var Ki = 3;
    function $i(t, e) {
        if (function(t) {
            if (t.some((function(t) {
                return t.getUserActivity().selection
            }
            )))
                return !1;
            for (var e = 0; e < t.length - (Ki - 1); e += 1)
                if (t[e + Ki - 1].event.timeStamp - t[e].event.timeStamp <= ce)
                    return !0;
            return !1
        }(t))
            return e.addFrustration("rage_click"),
            t.some(Ji) && e.addFrustration("dead_click"),
            e.hasError && e.addFrustration("error_click"),
            {
                isRage: !0
            };
        var n = t.some((function(t) {
            return t.getUserActivity().selection
        }
        ));
        return t.forEach((function(t) {
            t.hasError && t.addFrustration("error_click"),
            Ji(t) && !n && t.addFrustration("dead_click")
        }
        )),
        {
            isRage: !1
        }
    }
    var Xi = 'input:not([type="checkbox"]):not([type="radio"]):not([type="button"]):not([type="submit"]):not([type="reset"]):not([type="range"]),textarea,select,[contenteditable],[contenteditable] *,canvas,a[href],a[href] *';
    function Ji(t) {
        return !t.hasPageActivity && !t.getUserActivity().input && (e = t.event.target,
        n = Xi,
        !(e.matches ? e.matches(n) : e.msMatchesSelector && e.msMatchesSelector(n)));
        var e, n
    }
    var Qi = 10 * ce
      , to = 5 * le;
    function eo(t, e, n) {
        var r, i = new Jr(to), o = new Dn;
        t.subscribe(8, (function() {
            i.reset()
        }
        )),
        t.subscribe(4, u);
        var a = function(t) {
            var e, n, r = t.onPointerDown, i = t.onPointerUp, o = {
                selection: !1,
                input: !1
            }, a = [Bn(window, "pointerdown", (function(t) {
                Yi(t) && (e = Wi(),
                o = {
                    selection: !1,
                    input: !1
                },
                n = r(t))
            }
            ), {
                capture: !0
            }), Bn(window, "selectionchange", (function() {
                e && Wi() || (o.selection = !0)
            }
            ), {
                capture: !0
            }), Bn(window, "pointerup", (function(t) {
                if (Yi(t) && n) {
                    var e = o;
                    i(n, t, (function() {
                        return e
                    }
                    )),
                    n = void 0
                }
            }
            ), {
                capture: !0
            }), Bn(window, "input", (function() {
                o.input = !0
            }
            ), {
                capture: !0
            })];
            return {
                stop: function() {
                    a.forEach((function(t) {
                        return t.stop()
                    }
                    ))
                }
            }
        }({
            onPointerDown: function(r) {
                return function(t, e, n, r, i) {
                    if (!t.trackFrustrations && r.find())
                        return;
                    var o = (a = i,
                    s = t.actionNameAttribute,
                    u = a.target.getBoundingClientRect(),
                    {
                        type: "click",
                        target: {
                            width: Math.round(u.width),
                            height: Math.round(u.height),
                            selector: Bi(a.target, s)
                        },
                        position: {
                            x: Math.round(a.clientX - u.left),
                            y: Math.round(a.clientY - u.top)
                        },
                        name: Ti(a.target, s)
                    });
                    var a, s, u;
                    if (!t.trackFrustrations && !o.name)
                        return;
                    var c = !1;
                    return _i(e, n, t, (function(t) {
                        c = t.hadActivity
                    }
                    ), mi),
                    {
                        clickActionBase: o,
                        hadActivityOnPointerDown: function() {
                            return c
                        }
                    }
                }(n, t, e, i, r)
            },
            onPointerUp: function(r, a, u) {
                var c = r.clickActionBase
                  , l = r.hadActivityOnPointerDown;
                return function(t, e, n, r, i, o, a, s, u, c) {
                    var l = no(e, r, u, a, s);
                    t.trackFrustrations && o(l);
                    var d = _i(e, n, t, (function(e) {
                        e.hadActivity && e.end < l.startClocks.timeStamp ? l.discard() : (e.hadActivity ? l.stop(e.end) : c() ? l.stop(l.startClocks.timeStamp) : l.stop(),
                        t.trackFrustrations || (e.hadActivity ? l.validate() : l.discard()))
                    }
                    ), Qi).stop
                      , f = e.subscribe(4, (function(t) {
                        var e = t.endClocks;
                        l.stop(e.timeStamp)
                    }
                    ))
                      , p = i.subscribe((function() {
                        l.stop()
                    }
                    ));
                    l.stopObservable.subscribe((function() {
                        f.unsubscribe(),
                        d(),
                        p.unsubscribe()
                    }
                    ))
                }(n, t, e, i, o, s, c, a, u, l)
            }
        }).stop;
        return {
            stop: function() {
                u(),
                o.notify(),
                a()
            },
            actionContexts: {
                findActionId: function(t) {
                    return n.trackFrustrations ? i.findAll(t) : i.find(t)
                }
            }
        };
        function s(t) {
            if (!r || !r.tryAppend(t)) {
                var e = t.clone();
                r = Ei(t, (function(t) {
                    !function(t, e) {
                        var n = $i(t, e).isRage;
                        n ? (t.forEach((function(t) {
                            return t.discard()
                        }
                        )),
                        e.stop(ge()),
                        e.validate(t.map((function(t) {
                            return t.event
                        }
                        )))) : (e.discard(),
                        t.forEach((function(t) {
                            return t.validate()
                        }
                        )))
                    }(t, e)
                }
                ))
            }
        }
        function u() {
            r && r.stop()
        }
    }
    function no(t, e, n, r, i) {
        var o, a = Ce(), s = ye(), u = e.add(a, s.relative), c = vi({
            lifeCycle: t,
            isChildEvent: function(t) {
                return void 0 !== t.action && (Array.isArray(t.action.id) ? _t(t.action.id, a) : t.action.id === a)
            }
        }), l = 0, d = [], f = new Dn;
        function p(t) {
            0 === l && (l = 1,
            (o = t) ? u.close(Se(o)) : u.remove(),
            c.stop(),
            f.notify())
        }
        return {
            event: i,
            stop: p,
            stopObservable: f,
            get hasError() {
                return c.eventCounts.errorCount > 0
            },
            get hasPageActivity() {
                return void 0 !== o
            },
            getUserActivity: n,
            addFrustration: function(t) {
                d.push(t)
            },
            startClocks: s,
            isStopped: function() {
                return 1 === l || 2 === l
            },
            clone: function() {
                return no(t, e, n, r, i)
            },
            validate: function(e) {
                if (p(),
                1 === l) {
                    var n = c.eventCounts
                      , u = n.resourceCount
                      , f = n.errorCount
                      , h = n.longTaskCount
                      , v = Ct({
                        type: "click",
                        duration: o && we(s.timeStamp, o),
                        startClocks: s,
                        id: a,
                        frustrationTypes: d,
                        counts: {
                            resourceCount: u,
                            errorCount: f,
                            longTaskCount: h
                        },
                        events: null != e ? e : [i],
                        event: i
                    }, r);
                    t.notify(1, v),
                    l = 2
                }
            },
            discard: function() {
                p(),
                l = 2
            }
        }
    }
    function ro(t, e) {
        var n = io(t) ? {
            action: {
                id: t.id,
                loading_time: ve(t.duration),
                frustration: {
                    type: t.frustrationTypes
                },
                error: {
                    count: t.counts.errorCount
                },
                long_task: {
                    count: t.counts.longTaskCount
                },
                resource: {
                    count: t.counts.resourceCount
                }
            },
            _dd: {
                action: {
                    target: t.target,
                    position: t.position
                }
            }
        } : void 0
          , r = io(t) ? void 0 : t.context
          , i = Vt({
            action: {
                id: Ce(),
                target: {
                    name: t.name
                },
                type: t.type
            },
            date: t.startClocks.timeStamp,
            type: "action"
        }, n)
          , o = e.isInForegroundAt(t.startClocks.relative);
        return void 0 !== o && (i.view = {
            in_foreground: o
        }),
        {
            customerContext: r,
            rawRumEvent: i,
            startTime: t.startClocks.relative,
            domainContext: io(t) ? {
                event: t.event,
                events: t.events
            } : {}
        }
    }
    function io(t) {
        return "custom" !== t.type
    }
    var oo = /^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/;
    function ao(t) {
        var e = function(t) {
            return ei(window, "onerror", {
                before: function(e, n, r, i, o) {
                    var a;
                    if (o)
                        a = ze(o),
                        t(a, o);
                    else {
                        var s, u = {
                            url: n,
                            column: i,
                            line: r
                        }, c = e;
                        if ("[object String]" === {}.toString.call(e)) {
                            var l = oo.exec(c);
                            l && (s = l[1],
                            c = l[2])
                        }
                        t(a = {
                            name: s,
                            message: "string" == typeof c ? c : void 0,
                            stack: [u]
                        }, e)
                    }
                }
            })
        }(t).stop
          , n = function(t) {
            return ei(window, "onunhandledrejection", {
                before: function(e) {
                    var n = e.reason || "Empty reason"
                      , r = ze(n);
                    t(r, n)
                }
            })
        }(t).stop;
        return {
            stop: function() {
                e(),
                n()
            }
        }
    }
    var so = {};
    function uo(t) {
        var e = t.map((function(t) {
            return so[t] || (so[t] = function(t) {
                var e = new Dn((function() {
                    var n = console[t];
                    return console[t] = function() {
                        for (var r = [], i = 0; i < arguments.length; i++)
                            r[i] = arguments[i];
                        n.apply(console, r);
                        var o = tn();
                        mt((function() {
                            e.notify(function(t, e, n) {
                                var r, i = t.map((function(t) {
                                    return function(t) {
                                        if ("string" == typeof t)
                                            return Rt(dt.SANITIZE_INPUTS) ? $t(t) : t;
                                        if (t instanceof Error)
                                            return Qe(ze(t));
                                        return Zt(Rt(dt.SANITIZE_INPUTS) ? $t(t) : t, void 0, 2)
                                    }(t)
                                }
                                )).join(" ");
                                if (e === st.error) {
                                    var o = bt(t, (function(t) {
                                        return t instanceof Error
                                    }
                                    ));
                                    r = o ? Je(ze(o)) : void 0,
                                    i = "console error: ".concat(i)
                                }
                                return {
                                    api: e,
                                    message: i,
                                    stack: r,
                                    handlingStack: n
                                }
                            }(r, t, o))
                        }
                        ))
                    }
                    ,
                    function() {
                        console[t] = n
                    }
                }
                ));
                return e
            }(t)),
            so[t]
        }
        ));
        return Fn.apply(void 0, e)
    }
    var co, lo, fo = {
        intervention: "intervention",
        deprecation: "deprecation",
        cspViolation: "csp_violation"
    };
    function po(t) {
        var e, n = [];
        _t(t, fo.cspViolation) && n.push(e = new Dn((function() {
            return Bn(document, "securitypolicyviolation", (function(t) {
                e.notify(function(t) {
                    var e = fo.cspViolation
                      , n = "'".concat(t.blockedURI, "' blocked by '").concat(t.effectiveDirective, "' directive");
                    return {
                        type: fo.cspViolation,
                        subtype: t.effectiveDirective,
                        message: "".concat(e, ": ").concat(n),
                        stack: ho(t.effectiveDirective, t.originalPolicy ? "".concat(n, ' of the policy "').concat(ke(t.originalPolicy, 100, "..."), '"') : "no policy", t.sourceFile, t.lineNumber, t.columnNumber)
                    }
                }(t))
            }
            )).stop
        }
        )));
        var r = t.filter((function(t) {
            return t !== fo.cspViolation
        }
        ));
        return r.length && n.push(function(t) {
            var e = new Dn((function() {
                if (window.ReportingObserver) {
                    var n = vt((function(t) {
                        return t.forEach((function(t) {
                            e.notify(function(t) {
                                var e = t.type
                                  , n = t.body;
                                return {
                                    type: e,
                                    subtype: n.id,
                                    message: "".concat(e, ": ").concat(n.message),
                                    stack: ho(n.id, n.message, n.sourceFile, n.lineNumber, n.columnNumber)
                                }
                            }(t))
                        }
                        ))
                    }
                    ))
                      , r = new window.ReportingObserver(n,{
                        types: t,
                        buffered: !0
                    });
                    return r.observe(),
                    function() {
                        r.disconnect()
                    }
                }
            }
            ));
            return e
        }(r)),
        Fn.apply(void 0, n)
    }
    function ho(t, e, n, r, i) {
        return n && Je({
            name: t,
            message: e,
            stack: [{
                func: "?",
                url: n,
                line: r,
                column: i
            }]
        })
    }
    function vo(t, e, n) {
        var r = new Dn;
        return function(t) {
            var e = uo([st.error]).subscribe((function(e) {
                return t.notify({
                    startClocks: ye(),
                    message: e.message,
                    stack: e.stack,
                    source: kr.CONSOLE,
                    handling: "handled",
                    handlingStack: e.handlingStack
                })
            }
            ))
        }(r),
        function(t) {
            ao((function(e, n) {
                t.notify(Xe({
                    stackTrace: e,
                    originalError: n,
                    startClocks: ye(),
                    nonErrorPrefix: "Uncaught",
                    source: kr.SOURCE,
                    handling: "unhandled"
                }))
            }
            ))
        }(r),
        function(t) {
            var e = po([fo.cspViolation, fo.intervention]).subscribe((function(e) {
                return t.notify({
                    startClocks: ye(),
                    message: e.message,
                    stack: e.stack,
                    type: e.subtype,
                    source: kr.REPORT,
                    handling: "unhandled"
                })
            }
            ))
        }(r),
        r.subscribe((function(e) {
            return t.notify(12, {
                error: e
            })
        }
        )),
        function(t, e, n) {
            return t.subscribe(12, (function(r) {
                var i = r.error
                  , o = r.customerContext
                  , a = r.savedCommonContext;
                t.notify(10, Ct({
                    customerContext: o,
                    savedCommonContext: a
                }, function(t, e, n) {
                    var r = {
                        date: t.startClocks.timeStamp,
                        error: {
                            id: Ce(),
                            message: t.message,
                            source: t.source,
                            stack: t.stack,
                            handling_stack: t.handlingStack,
                            type: t.type,
                            handling: t.handling,
                            causes: t.causes,
                            source_type: "browser"
                        },
                        type: "error"
                    }
                      , i = e.isInForegroundAt(t.startClocks.relative);
                    i && (r.view = {
                        in_foreground: i
                    });
                    var o = n.findFeatureFlagEvaluations(t.startClocks.relative);
                    o && !an(o) && (r.feature_flags = o);
                    return {
                        rawRumEvent: r,
                        startTime: t.startClocks.relative,
                        domainContext: {
                            error: t.originalError
                        }
                    }
                }(i, e, n)))
            }
            )),
            {
                addError: function(e, n) {
                    var r = e.error
                      , i = e.handlingStack
                      , o = e.startClocks
                      , a = e.context
                      , s = Xe({
                        stackTrace: r instanceof Error ? ze(r) : void 0,
                        originalError: r,
                        handlingStack: i,
                        startClocks: o,
                        nonErrorPrefix: "Provided",
                        source: kr.CUSTOM,
                        handling: "handled"
                    });
                    t.notify(12, {
                        customerContext: a,
                        savedCommonContext: n,
                        error: s
                    })
                }
            }
        }(t, e, n)
    }
    function mo(t) {
        if (performance && "getEntriesByName"in performance) {
            var e = performance.getEntriesByName(t.url, "resource");
            if (e.length && "toJSON"in e[0]) {
                var n = e.map((function(t) {
                    return t.toJSON()
                }
                )).filter(ar).filter((function(e) {
                    return n = e,
                    r = t.startClocks.relative,
                    i = go({
                        startTime: t.startClocks.relative,
                        duration: t.duration
                    }),
                    o = 1,
                    n.startTime >= r - o && go(n) <= Ee(i, o);
                    var n, r, i, o
                }
                ));
                return 1 === n.length ? n[0] : void 0
            }
        }
    }
    function go(t) {
        return Ee(t.startTime, t.duration)
    }
    function _o(t, e, n, r) {
        t.subscribe(6, (function(i) {
            t.notify(10, function(t, e, n, r) {
                var i, o = "xhr" === t.type ? "xhr" : "fetch", a = mo(t), s = a ? pe(a.startTime) : t.startClocks, u = a ? bo(a) : void 0, c = function(t, e) {
                    var n = t.traceSampled && t.traceId && t.spanId;
                    if (!n)
                        return;
                    return {
                        _dd: {
                            span_id: t.spanId.toDecimalString(),
                            trace_id: t.traceId.toDecimalString(),
                            rule_psr: wo(e)
                        }
                    }
                }(t, e), l = Eo(n, s), d = ve(t.duration), f = So(r, s, null !== (i = null == a ? void 0 : a.duration) && void 0 !== i ? i : t.duration), p = Vt({
                    date: s.timeStamp,
                    resource: {
                        id: Ce(),
                        type: o,
                        duration: d,
                        method: t.method,
                        status_code: t.status,
                        url: t.url
                    },
                    type: "resource"
                }, c, u, l, f);
                return {
                    startTime: s.relative,
                    rawRumEvent: p,
                    domainContext: {
                        performanceEntry: a && a,
                        xhr: t.xhr,
                        response: t.response,
                        requestInput: t.input,
                        requestInit: t.init,
                        error: t.error
                    }
                }
            }(i, e, n, r))
        }
        )),
        t.subscribe(0, (function(i) {
            for (var o = 0, a = i; o < a.length; o++) {
                var s = a[o];
                "resource" === s.entryType && ("xmlhttprequest" !== (u = s).initiatorType && "fetch" !== u.initiatorType) && t.notify(10, yo(s, e, n, r))
            }
            var u
        }
        ))
    }
    function yo(t, e, n, r) {
        var i = rr(t)
          , o = bo(t)
          , a = pe(t.startTime)
          , s = function(t, e) {
            var n = t.traceId;
            if (!n)
                return;
            return {
                _dd: {
                    trace_id: t.traceId,
                    rule_psr: wo(e)
                }
            }
        }(t, e)
          , u = Eo(n, a)
          , c = So(r, a, t.duration)
          , l = Vt({
            date: a.timeStamp,
            resource: {
                id: Ce(),
                type: i,
                url: t.name
            },
            type: "resource"
        }, s, o, u, c);
        return {
            startTime: a.relative,
            rawRumEvent: l,
            domainContext: {
                performanceEntry: t
            }
        }
    }
    function bo(t) {
        return {
            resource: Ct({
                duration: (e = t,
                n = e.duration,
                r = e.startTime,
                i = e.responseEnd,
                ve(0 === n && r < i ? we(r, i) : n)),
                size: cr(t)
            }, or(t))
        };
        var e, n, r, i
    }
    function wo(t) {
        return se(t.traceSampleRate) ? t.traceSampleRate / 100 : void 0
    }
    function Eo(t, e) {
        var n = t.findTrackedSession(e.relative);
        return {
            _dd: {
                discarded: !n || !n.resourceAllowed
            }
        }
    }
    function So(t, e, n) {
        if (Rt(dt.RESOURCE_PAGE_STATES))
            return {
                _dd: {
                    page_states: t.findAll(e.relative, n),
                    page_was_discarded: String(document.wasDiscarded)
                }
            }
    }
    function To(t) {
        return void 0 === t && (t = window),
        co || ("hidden" === document.visibilityState ? co = {
            timeStamp: 0
        } : (co = {
            timeStamp: 1 / 0
        },
        lo = zn(t, ["pagehide", "visibilitychange"], (function(t) {
            "pagehide" !== t.type && "hidden" !== document.visibilityState || (co.timeStamp = t.timeStamp,
            lo())
        }
        ), {
            capture: !0
        }).stop)),
        co
    }
    var Co = 10 * le
      , Ao = 5 * le;
    function ko(t, e, n) {
        var r = {};
        function i(t) {
            Ct(r, t),
            n()
        }
        var o = function(t, e) {
            var n = t.subscribe(0, (function(t) {
                for (var n = 0, r = t; n < r.length; n++) {
                    var i = r[n];
                    "navigation" === i.entryType && e({
                        domComplete: i.domComplete,
                        domContentLoaded: i.domContentLoadedEventEnd,
                        domInteractive: i.domInteractive,
                        loadEvent: i.loadEventEnd,
                        firstByte: i.responseStart >= 0 && i.responseStart <= _e() ? i.responseStart : void 0
                    })
                }
            }
            )).unsubscribe;
            return {
                stop: n
            }
        }(t, (function(t) {
            e(t.loadEvent),
            i(t)
        }
        )).stop
          , a = function(t, e) {
            var n = To()
              , r = t.subscribe(0, (function(t) {
                var r = bt(t, (function(t) {
                    return "paint" === t.entryType && "first-contentful-paint" === t.name && t.startTime < n.timeStamp && t.startTime < Co
                }
                ));
                r && e(r.startTime)
            }
            )).unsubscribe;
            return {
                stop: r
            }
        }(t, (function(t) {
            return i({
                firstContentfulPaint: t
            })
        }
        )).stop
          , s = function(t, e, n) {
            var r = To()
              , i = 1 / 0
              , o = zn(e, ["pointerdown", "keydown"], (function(t) {
                i = t.timeStamp
            }
            ), {
                capture: !0,
                once: !0
            }).stop
              , a = t.subscribe(0, (function(t) {
                var e = function(t, e) {
                    for (var n = t.length - 1; n >= 0; n -= 1) {
                        var r = t[n];
                        if (e(r, n, t))
                            return r
                    }
                }(t, (function(t) {
                    return "largest-contentful-paint" === t.entryType && t.startTime < i && t.startTime < r.timeStamp && t.startTime < Co
                }
                ));
                e && n(e.startTime)
            }
            )).unsubscribe;
            return {
                stop: function() {
                    o(),
                    a()
                }
            }
        }(t, window, (function(t) {
            i({
                largestContentfulPaint: t
            })
        }
        )).stop
          , u = function(t, e) {
            var n = To()
              , r = t.subscribe(0, (function(t) {
                var r = bt(t, (function(t) {
                    return "first-input" === t.entryType && t.startTime < n.timeStamp
                }
                ));
                if (r) {
                    var i = we(r.startTime, r.processingStart);
                    e({
                        firstInputDelay: i >= 0 ? i : 0,
                        firstInputTime: r.startTime
                    })
                }
            }
            )).unsubscribe;
            return {
                stop: r
            }
        }(t, (function(t) {
            i({
                firstInputDelay: t.firstInputDelay,
                firstInputTime: t.firstInputTime
            })
        }
        )).stop;
        function c() {
            o(),
            a(),
            s(),
            u()
        }
        return {
            stop: c,
            timings: r,
            scheduleStop: function() {
                Mt(c, Ao)
            }
        }
    }
    function Ro(t, e, n, r, i, o) {
        var a, s = {}, u = function(t, e, n, r, i, o) {
            var a = "initial_load" === r
              , s = !0
              , u = [];
            function c() {
                !s && !a && u.length > 0 && o(Math.max.apply(Math, u))
            }
            var l = _i(t, e, n, (function(t) {
                s && (s = !1,
                t.hadActivity && u.push(we(i.timeStamp, t.end)),
                c())
            }
            )).stop;
            return {
                stop: l,
                setLoadEvent: function(t) {
                    a && (a = !1,
                    u.push(t),
                    c())
                }
            }
        }(t, e, n, i, o, (function(t) {
            s.loadingTime = t,
            r()
        }
        )), c = u.stop, l = u.setLoadEvent;
        return wr("layout-shift") ? (s.cumulativeLayoutShift = 0,
        a = function(t, e) {
            var n = 0
              , r = function() {
                var t, e, n = 0;
                return {
                    update: function(r) {
                        void 0 === t || r.startTime - e >= ce || r.startTime - t >= 5 * ce ? (t = e = r.startTime,
                        n = r.value) : (n += r.value,
                        e = r.startTime)
                    },
                    value: function() {
                        return n
                    }
                }
            }()
              , i = t.subscribe(0, (function(t) {
                for (var i = 0, o = t; i < o.length; i++) {
                    var a = o[i];
                    "layout-shift" !== a.entryType || a.hadRecentInput || (r.update(a),
                    r.value() > n && (n = r.value(),
                    e(oe(n, 4))))
                }
            }
            )).unsubscribe;
            return {
                stop: i
            }
        }(t, (function(t) {
            s.cumulativeLayoutShift = t,
            r()
        }
        )).stop) : a = zt,
        {
            stop: function() {
                c(),
                a()
            },
            setLoadEvent: l,
            viewMetrics: s
        }
    }
    var xo = 5 * le;
    var Io = 3e3
      , Oo = 5 * le;
    function No(t, e, n, r, i, o, a) {
        var s, u = c("initial_load", be(), a);
        function c(i, o, a) {
            return function(t, e, n, r, i, o, a) {
                void 0 === o && (o = ye());
                var s, u, c, l, d = Ce(), f = {}, p = 0, h = rn(r), v = !0;
                a && (u = a.name,
                c = a.service,
                l = a.version);
                t.notify(2, {
                    id: d,
                    name: u,
                    startClocks: o,
                    service: c,
                    version: l
                });
                var m = Bt(I, Io, {
                    leading: !1
                })
                  , g = m.throttled
                  , _ = m.cancel
                  , y = Ro(t, e, n, g, i, o)
                  , b = y.setLoadEvent
                  , w = y.stop
                  , E = y.viewMetrics
                  , S = "initial_load" === i ? ko(t, b, g) : {
                    scheduleStop: zt,
                    timings: {}
                }
                  , T = S.scheduleStop
                  , C = S.timings
                  , A = function(t, e, n) {
                    var r = vi({
                        lifeCycle: t,
                        isChildEvent: function(t) {
                            return t.view.id === e
                        },
                        onChange: n
                    })
                      , i = r.stop;
                    return {
                        scheduleStop: function() {
                            Mt(i, xo)
                        },
                        eventCounts: r.eventCounts
                    }
                }(t, d, g)
                  , k = A.scheduleStop
                  , R = A.eventCounts
                  , x = Dt(I, Oo);
                function I() {
                    _(),
                    p += 1;
                    var e = void 0 === s ? ge() : s.timeStamp;
                    t.notify(3, Ct({
                        customTimings: f,
                        documentVersion: p,
                        id: d,
                        name: u,
                        service: c,
                        version: l,
                        loadingType: i,
                        location: h,
                        startClocks: o,
                        timings: C,
                        duration: we(o.timeStamp, e),
                        isActive: void 0 === s,
                        sessionIsActive: v,
                        eventCounts: R
                    }, E))
                }
                return I(),
                {
                    name: u,
                    service: c,
                    version: l,
                    end: function(e) {
                        var n, r;
                        void 0 === e && (e = {}),
                        s || (s = null !== (n = e.endClocks) && void 0 !== n ? n : ye(),
                        v = null === (r = e.sessionIsActive) || void 0 === r || r,
                        t.notify(4, {
                            endClocks: s
                        }),
                        Ft(x),
                        w(),
                        T(),
                        k(),
                        I())
                    },
                    addTiming: function(t, e) {
                        if (!s) {
                            var n = function(t) {
                                return t < fe
                            }(e) ? e : we(o.timeStamp, e);
                            f[function(t) {
                                var e = t.replace(/[^a-zA-Z0-9-_.@$]/g, "_");
                                e !== t && ut.warn("Invalid timing name: ".concat(t, ", sanitized to: ").concat(e));
                                return e
                            }(t)] = n,
                            g()
                        }
                    }
                }
            }(e, n, r, t, i, o, a)
        }
        return e.subscribe(8, (function() {
            u = c("route_change", void 0, {
                name: u.name,
                service: u.service,
                version: u.version
            })
        }
        )),
        e.subscribe(7, (function() {
            u.end({
                sessionIsActive: !1
            })
        }
        )),
        e.subscribe(9, (function(t) {
            t.reason !== Hn.UNLOADING && t.reason !== Hn.PAGEHIDE || u.end()
        }
        )),
        o && (s = function(t) {
            return t.subscribe((function(t) {
                var e, n, r, i, o = t.oldLocation, a = t.newLocation;
                n = a,
                (e = o).pathname === n.pathname && (r = n.hash,
                i = r.substr(1),
                document.getElementById(i) || Po(n.hash) === Po(e.hash)) || (u.end(),
                u = c("route_change"))
            }
            ))
        }(i)),
        {
            addTiming: function(t, e) {
                void 0 === e && (e = ge()),
                u.addTiming(t, e)
            },
            startView: function(t, e) {
                u.end({
                    endClocks: e
                }),
                u = c("route_change", e, t)
            },
            stop: function() {
                null == s || s.unsubscribe(),
                u.end()
            }
        }
    }
    function Po(t) {
        var e = t.indexOf("?");
        return e < 0 ? t : t.slice(0, e)
    }
    function Lo(t, e, n, r, i, o, a, s, u) {
        return t.subscribe(3, (function(e) {
            return t.notify(10, function(t, e, n, r) {
                var i = r.getReplayStats(t.id)
                  , o = n.findFeatureFlagEvaluations(t.startClocks.relative)
                  , a = {
                    _dd: {
                        document_version: t.documentVersion,
                        replay_stats: i
                    },
                    date: t.startClocks.timeStamp,
                    type: "view",
                    view: {
                        action: {
                            count: t.eventCounts.actionCount
                        },
                        frustration: {
                            count: t.eventCounts.frustrationCount
                        },
                        cumulative_layout_shift: t.cumulativeLayoutShift,
                        first_byte: ve(t.timings.firstByte),
                        dom_complete: ve(t.timings.domComplete),
                        dom_content_loaded: ve(t.timings.domContentLoaded),
                        dom_interactive: ve(t.timings.domInteractive),
                        error: {
                            count: t.eventCounts.errorCount
                        },
                        first_contentful_paint: ve(t.timings.firstContentfulPaint),
                        first_input_delay: ve(t.timings.firstInputDelay),
                        first_input_time: ve(t.timings.firstInputTime),
                        is_active: t.isActive,
                        name: t.name,
                        largest_contentful_paint: ve(t.timings.largestContentfulPaint),
                        load_event: ve(t.timings.loadEvent),
                        loading_time: Mo(ve(t.loadingTime)),
                        loading_type: t.loadingType,
                        long_task: {
                            count: t.eventCounts.longTaskCount
                        },
                        resource: {
                            count: t.eventCounts.resourceCount
                        },
                        time_spent: ve(t.duration),
                        in_foreground_periods: e.selectInForegroundPeriodsFor(t.startClocks.relative, t.duration)
                    },
                    feature_flags: o && !an(o) ? o : void 0,
                    session: {
                        has_replay: !!i || void 0,
                        is_active: !!t.sessionIsActive && void 0
                    }
                };
                an(t.customTimings) || (a.view.custom_timings = function(t, e) {
                    for (var n = {}, r = 0, i = Object.keys(t); r < i.length; r++) {
                        var o = i[r];
                        n[o] = e(t[o])
                    }
                    return n
                }(t.customTimings, ve));
                return {
                    rawRumEvent: a,
                    startTime: t.startClocks.relative,
                    domainContext: {
                        location: t.location
                    }
                }
            }(e, o, a, s))
        }
        )),
        No(n, t, r, e, i, !e.trackViewsManually, u)
    }
    function Mo(t) {
        return se(t) && t < 0 ? void 0 : t
    }
    var Uo, Do = /^([a-z]+)=([a-z0-9-]+)$/, Fo = "&", Bo = "_dd_s", zo = 10, Ho = 100, jo = [];
    function qo(t, e) {
        var n;
        if (void 0 === e && (e = 0),
        Uo || (Uo = t),
        t === Uo)
            if (e >= Ho)
                Go();
            else {
                var r, i = Ko();
                if (Vo()) {
                    if (i.lock)
                        return void Zo(t, e);
                    if (r = Ce(),
                    i.lock = r,
                    Yo(i, t.options),
                    (i = Ko()).lock !== r)
                        return void Zo(t, e)
                }
                var o = t.process(i);
                if (Vo() && (i = Ko()).lock !== r)
                    Zo(t, e);
                else {
                    if (o && Wo(o, t.options),
                    Vo() && (!o || !Xo(o))) {
                        if ((i = Ko()).lock !== r)
                            return void Zo(t, e);
                        delete i.lock,
                        Yo(i, t.options),
                        o = i
                    }
                    null === (n = t.after) || void 0 === n || n.call(t, o || i),
                    Go()
                }
            }
        else
            jo.push(t)
    }
    function Vo() {
        return !!window.chrome || /HeadlessChrome/.test(window.navigator.userAgent)
    }
    function Zo(t, e) {
        Mt((function() {
            qo(t, e + 1)
        }
        ), zo)
    }
    function Go() {
        Uo = void 0;
        var t = jo.shift();
        t && qo(t)
    }
    function Wo(t, e) {
        Xo(t) ? $o(e) : (t.expire = String(me() + Kr),
        Yo(t, e))
    }
    function Yo(t, e) {
        Ie(Bo, function(t) {
            return Et(t).map((function(t) {
                var e = t[0]
                  , n = t[1];
                return "".concat(e, "=").concat(n)
            }
            )).join(Fo)
        }(t), Kr, e)
    }
    function Ko() {
        var t = Oe(Bo)
          , e = {};
        return function(t) {
            return void 0 !== t && (-1 !== t.indexOf(Fo) || Do.test(t))
        }(t) && t.split(Fo).forEach((function(t) {
            var n = Do.exec(t);
            if (null !== n) {
                var r = n[1]
                  , i = n[2];
                e[r] = i
            }
        }
        )),
        e
    }
    function $o(t) {
        Ne(Bo, t)
    }
    function Xo(t) {
        return an(t)
    }
    var Jo = "_dd"
      , Qo = "_dd_r"
      , ta = "_dd_l"
      , ea = "rum"
      , na = "logs";
    function ra(t, e, n) {
        var r = new Dn
          , i = new Dn
          , o = Dt((function() {
            qo({
                options: t,
                process: function(t) {
                    return c(t) ? void 0 : {}
                },
                after: s
            })
        }
        ), xe)
          , a = function() {
            var t = Ko();
            if (c(t))
                return t;
            return {}
        }();
        function s(t) {
            return c(t) || (t = {}),
            u() && (!function(t) {
                return a.id !== t.id || a[e] !== t[e]
            }(t) ? a = t : (a = {},
            i.notify())),
            t
        }
        function u() {
            return void 0 !== a[e]
        }
        function c(t) {
            return (void 0 === t.created || me() - Number(t.created) < Yr) && (void 0 === t.expire || me() < Number(t.expire))
        }
        return {
            expandOrRenewSession: Bt((function() {
                var i;
                qo({
                    options: t,
                    process: function(t) {
                        var r = s(t);
                        return i = function(t) {
                            var r = n(t[e])
                              , i = r.trackingType
                              , o = r.isTracked;
                            t[e] = i,
                            o && !t.id && (t.id = Ce(),
                            t.created = String(me()));
                            return o
                        }(r),
                        r
                    },
                    after: function(t) {
                        i && !u() && function(t) {
                            a = t,
                            r.notify()
                        }(t),
                        a = t
                    }
                })
            }
            ), xe).throttled,
            expandSession: function() {
                qo({
                    options: t,
                    process: function(t) {
                        return u() ? s(t) : void 0
                    }
                })
            },
            getSession: function() {
                return a
            },
            renewObservable: r,
            expireObservable: i,
            expire: function() {
                $o(t),
                s({})
            },
            stop: function() {
                Ft(o)
            }
        }
    }
    var ia = le
      , oa = Yr
      , aa = [];
    function sa(t, e, n) {
        !function(t) {
            var e = Oe(Bo)
              , n = Oe(Jo)
              , r = Oe(Qo)
              , i = Oe(ta);
            if (!e) {
                var o = {};
                n && (o.id = n),
                i && /^[01]$/.test(i) && (o[na] = i),
                r && /^[012]$/.test(r) && (o[ea] = r),
                Wo(o, t)
            }
        }(t);
        var r = ra(t, e, n);
        aa.push((function() {
            return r.stop()
        }
        ));
        var i, o = new Jr(oa);
        function a() {
            return {
                id: r.getSession().id,
                trackingType: r.getSession()[e]
            }
        }
        return aa.push((function() {
            return o.stop()
        }
        )),
        r.renewObservable.subscribe((function() {
            o.add(a(), _e())
        }
        )),
        r.expireObservable.subscribe((function() {
            o.closeActive(_e())
        }
        )),
        r.expandOrRenewSession(),
        o.add(a(), be().relative),
        i = zn(window, ["click", "touchstart", "keydown", "scroll"], (function() {
            return r.expandOrRenewSession()
        }
        ), {
            capture: !0,
            passive: !0
        }).stop,
        aa.push(i),
        function(t) {
            var e = function() {
                "visible" === document.visibilityState && t()
            }
              , n = Bn(document, "visibilitychange", e).stop;
            aa.push(n);
            var r = Dt(e, ia);
            aa.push((function() {
                Ft(r)
            }
            ))
        }((function() {
            return r.expandSession()
        }
        )),
        {
            findActiveSession: function(t) {
                return o.find(t)
            },
            renewObservable: r.renewObservable,
            expireObservable: r.expireObservable,
            expire: r.expire
        }
    }
    var ua = "rum";
    function ca(t, e) {
        var n = sa(t.cookieOptions, ua, (function(e) {
            return function(t, e) {
                var n;
                n = function(t) {
                    return "0" === t || "1" === t || "2" === t
                }(e) ? e : ie(t.sessionSampleRate) ? ie(t.sessionReplaySampleRate) ? "1" : "2" : "0";
                return {
                    trackingType: n,
                    isTracked: la(n)
                }
            }(t, e)
        }
        ));
        return n.expireObservable.subscribe((function() {
            e.notify(7)
        }
        )),
        n.renewObservable.subscribe((function() {
            e.notify(8)
        }
        )),
        {
            findTrackedSession: function(e) {
                var r = n.findActiveSession(e);
                if (r && la(r.trackingType)) {
                    var i = "1" === r.trackingType ? 2 : 1;
                    return {
                        id: r.id,
                        plan: i,
                        sessionReplayAllowed: 2 === i,
                        longTaskAllowed: void 0 !== t.trackLongTasks ? t.trackLongTasks : t.oldPlansBehavior && 2 === i,
                        resourceAllowed: void 0 !== t.trackResources ? t.trackResources : t.oldPlansBehavior && 2 === i
                    }
                }
            },
            expire: n.expire,
            expireObservable: n.expireObservable
        }
    }
    function la(t) {
        return "2" === t || "1" === t
    }
    function da(t) {
        var e = t.messagesLimit
          , n = t.bytesLimit
          , r = t.durationLimit
          , i = t.pageExitObservable
          , o = t.sessionExpireObservable
          , a = new Dn;
        i.subscribe((function(t) {
            return l(t.reason)
        }
        )),
        o.subscribe((function() {
            return l("session_expire")
        }
        ));
        var s, u = 0, c = 0;
        function l(t) {
            if (0 !== c) {
                var e = c
                  , n = u;
                c = 0,
                u = 0,
                d(),
                a.notify({
                    reason: t,
                    messagesCount: e,
                    bytesCount: n
                })
            }
        }
        function d() {
            Ut(s),
            s = void 0
        }
        return {
            flushObservable: a,
            get messagesCount() {
                return c
            },
            notifyBeforeAddMessage: function(t) {
                u + t >= n && l("bytes_limit"),
                c += 1,
                u += t,
                void 0 === s && (s = Mt((function() {
                    l("duration_limit")
                }
                ), r))
            },
            notifyAfterAddMessage: function() {
                c >= e ? l("messages_limit") : u >= n && l("bytes_limit")
            },
            notifyAfterRemoveMessage: function(t) {
                u -= t,
                0 === (c -= 1) && d()
            }
        }
    }
    var fa = function() {
        function t(t, e, n) {
            var r = this;
            this.request = t,
            this.flushController = e,
            this.messageBytesLimit = n,
            this.pushOnlyBuffer = [],
            this.upsertBuffer = {},
            this.flushController.flushObservable.subscribe((function(t) {
                return r.flush(t)
            }
            ))
        }
        return t.prototype.add = function(t) {
            this.addOrUpdate(t)
        }
        ,
        t.prototype.upsert = function(t, e) {
            this.addOrUpdate(t, e)
        }
        ,
        t.prototype.flush = function(t) {
            var e = this.pushOnlyBuffer.concat(wt(this.upsertBuffer));
            this.pushOnlyBuffer = [],
            this.upsertBuffer = {};
            var n = {
                data: e.join("\n"),
                bytesCount: t.bytesCount,
                flushReason: t.reason
            };
            jn(t.reason) ? this.request.sendOnExit(n) : this.request.send(n)
        }
        ,
        t.prototype.addOrUpdate = function(t, e) {
            var n = this.process(t)
              , r = n.processedMessage
              , i = n.messageBytesCount;
            i >= this.messageBytesLimit ? ut.warn("Discarded a message whose size was bigger than the maximum allowed size ".concat(this.messageBytesLimit, "KB.")) : (this.hasMessageFor(e) && this.remove(e),
            this.push(r, i, e))
        }
        ,
        t.prototype.process = function(t) {
            var e = Zt(t);
            return {
                processedMessage: e,
                messageBytesCount: Pt(e)
            }
        }
        ,
        t.prototype.push = function(t, e, n) {
            var r = this.flushController.messagesCount > 0 ? 1 : 0;
            this.flushController.notifyBeforeAddMessage(e + r),
            void 0 !== n ? this.upsertBuffer[n] = t : this.pushOnlyBuffer.push(t),
            this.flushController.notifyAfterAddMessage()
        }
        ,
        t.prototype.remove = function(t) {
            var e = this.upsertBuffer[t];
            delete this.upsertBuffer[t];
            var n = Pt(e)
              , r = this.flushController.messagesCount > 1 ? 1 : 0;
            this.flushController.notifyAfterRemoveMessage(n + r)
        }
        ,
        t.prototype.hasMessageFor = function(t) {
            return void 0 !== t && void 0 !== this.upsertBuffer[t]
        }
        ,
        t
    }()
      , pa = 80 * It
      , ha = 32
      , va = 3 * Ot
      , ma = le
      , ga = ce;
    function _a(t, e, n, r, i) {
        0 === e.transportStatus && 0 === e.queuedPayloads.size() && e.bandwidthMonitor.canHandle(t) ? ba(t, e, n, {
            onSuccess: function() {
                return wa(0, e, n, r, i)
            },
            onFailure: function() {
                e.queuedPayloads.enqueue(t),
                ya(e, n, r, i)
            }
        }) : e.queuedPayloads.enqueue(t)
    }
    function ya(t, e, n, r) {
        2 === t.transportStatus && Mt((function() {
            ba(t.queuedPayloads.first(), t, e, {
                onSuccess: function() {
                    t.queuedPayloads.dequeue(),
                    t.currentBackoffTime = ga,
                    wa(1, t, e, n, r)
                },
                onFailure: function() {
                    t.currentBackoffTime = Math.min(ma, 2 * t.currentBackoffTime),
                    ya(t, e, n, r)
                }
            })
        }
        ), t.currentBackoffTime)
    }
    function ba(t, e, n, r) {
        var i = r.onSuccess
          , o = r.onFailure;
        e.bandwidthMonitor.add(t),
        n(t, (function(n) {
            e.bandwidthMonitor.remove(t),
            !function(t) {
                return "opaque" !== t.type && (0 === t.status && !navigator.onLine || 408 === t.status || 429 === t.status || (e = t.status,
                e >= 500));
                var e
            }(n) ? (e.transportStatus = 0,
            i()) : (e.transportStatus = e.bandwidthMonitor.ongoingRequestCount > 0 ? 1 : 2,
            t.retry = {
                count: t.retry ? t.retry.count + 1 : 1,
                lastFailureStatus: n.status
            },
            o())
        }
        ))
    }
    function wa(t, e, n, r, i) {
        0 === t && e.queuedPayloads.isFull() && !e.queueFullReported && (i({
            message: "Reached max ".concat(r, " events size queued for upload: ").concat(va / Ot, "MiB"),
            source: kr.AGENT,
            startClocks: ye()
        }),
        e.queueFullReported = !0);
        var o = e.queuedPayloads;
        for (e.queuedPayloads = Ea(); o.size() > 0; )
            _a(o.dequeue(), e, n, r, i)
    }
    function Ea() {
        var t = [];
        return {
            bytesCount: 0,
            enqueue: function(e) {
                this.isFull() || (t.push(e),
                this.bytesCount += e.bytesCount)
            },
            first: function() {
                return t[0]
            },
            dequeue: function() {
                var e = t.shift();
                return e && (this.bytesCount -= e.bytesCount),
                e
            },
            size: function() {
                return t.length
            },
            isFull: function() {
                return this.bytesCount >= va
            }
        }
    }
    function Sa(t, e, n) {
        var r = {
            transportStatus: 0,
            currentBackoffTime: ga,
            bandwidthMonitor: {
                ongoingRequestCount: 0,
                ongoingByteCount: 0,
                canHandle: function(t) {
                    return 0 === this.ongoingRequestCount || this.ongoingByteCount + t.bytesCount <= pa && this.ongoingRequestCount < ha
                },
                add: function(t) {
                    this.ongoingRequestCount += 1,
                    this.ongoingByteCount += t.bytesCount
                },
                remove: function(t) {
                    this.ongoingRequestCount -= 1,
                    this.ongoingByteCount -= t.bytesCount
                }
            },
            queuedPayloads: Ea(),
            queueFullReported: !1
        }
          , i = function(n, r) {
            return function(t, e, n, r) {
                var i = n.data
                  , o = n.bytesCount
                  , a = n.flushReason
                  , s = n.retry
                  , u = function() {
                    try {
                        return window.Request && "keepalive"in new Request("http://a")
                    } catch (n) {
                        return !1
                    }
                }() && o < e;
                if (u) {
                    var c = t.build("fetch", a, s);
                    fetch(c, {
                        method: "POST",
                        body: i,
                        keepalive: !0,
                        mode: "cors"
                    }).then(vt((function(t) {
                        return null == r ? void 0 : r({
                            status: t.status,
                            type: t.type
                        })
                    }
                    )), vt((function() {
                        Ca(t.build("xhr", a, s), i, r)
                    }
                    )))
                } else {
                    Ca(t.build("xhr", a, s), i, r)
                }
            }(t, e, n, r)
        };
        return {
            send: function(e) {
                _a(e, r, i, t.endpointType, n)
            },
            sendOnExit: function(n) {
                !function(t, e, n) {
                    var r = n.data
                      , i = n.bytesCount
                      , o = n.flushReason
                      , a = !!navigator.sendBeacon && i < e;
                    if (a)
                        try {
                            var s = t.build("beacon", o);
                            if (navigator.sendBeacon(s, r))
                                return
                        } catch (c) {
                            !function(t) {
                                Ta || (Ta = !0,
                                $n(t))
                            }(c)
                        }
                    var u = t.build("xhr", o);
                    Ca(u, r)
                }(t, e, n)
            }
        }
    }
    var Ta = !1;
    function Ca(t, e, n) {
        var r = new XMLHttpRequest;
        r.open("POST", t, !0),
        Bn(r, "loadend", (function() {
            null == n || n({
                status: r.status
            })
        }
        ), {
            once: !0
        }),
        r.send(e)
    }
    function Aa(t, e, n, r, i, o) {
        var a = function(t, e, n, r) {
            var i, o = c(t.rumEndpointBuilder), a = o.batch, s = o.flushController, u = t.replica;
            void 0 !== u && (i = c(u.rumEndpointBuilder).batch);
            function c(i) {
                var o = da({
                    messagesLimit: t.batchMessagesLimit,
                    bytesLimit: t.batchBytesLimit,
                    durationLimit: t.flushTimeout,
                    pageExitObservable: n,
                    sessionExpireObservable: r
                });
                return {
                    batch: new fa(Sa(i, t.batchBytesLimit, e),o,t.messageBytesLimit),
                    flushController: o
                }
            }
            function l(t) {
                return Vt(t, {
                    application: {
                        id: u.applicationId
                    }
                })
            }
            return {
                flushObservable: s.flushObservable,
                add: function(t, e) {
                    void 0 === e && (e = !0),
                    a.add(t),
                    i && e && i.add(l(t))
                },
                upsert: function(t, e) {
                    a.upsert(t, e),
                    i && i.upsert(l(t), e)
                }
            }
        }(t, r, i, o);
        return e.subscribe(11, (function(t) {
            "view" === t.type ? a.upsert(t, t.view.id) : a.add(t)
        }
        )),
        n.subscribe((function(e) {
            return a.add(e, function(t) {
                return t.site === pn
            }(t))
        }
        )),
        a
    }
    var ka = Yr;
    function Ra(t) {
        var e = rn(t)
          , n = new Dn((function() {
            var t, e, n, i, o, a = (t = r,
            e = ei(history, "pushState", {
                after: t
            }).stop,
            n = ei(history, "replaceState", {
                after: t
            }).stop,
            i = Bn(window, "popstate", t).stop,
            {
                stop: function() {
                    e(),
                    n(),
                    i()
                }
            }).stop, s = (o = r,
            Bn(window, "hashchange", o)).stop;
            return function() {
                a(),
                s()
            }
        }
        ));
        function r() {
            if (e.href !== t.href) {
                var r = rn(t);
                n.notify({
                    newLocation: r,
                    oldLocation: e
                }),
                e = r
            }
        }
        return n
    }
    var xa = Yr
      , Ia = 200;
    var Oa, Na, Pa, La = 10 * ce;
    function Ma() {
        0 !== Oa.batchCount && (Kn("Customer data measures", Oa),
        Fa())
    }
    function Ua(t, e) {
        t.sum += e,
        t.min = Math.min(t.min, e),
        t.max = Math.max(t.max, e)
    }
    function Da(t, e) {
        t.sum += e.sum,
        t.min = Math.min(t.min, e.min),
        t.max = Math.max(t.max, e.max)
    }
    function Fa() {
        Oa = {
            batchCount: 0,
            batchBytesCount: {
                min: 1 / 0,
                max: 0,
                sum: 0
            },
            batchMessagesCount: {
                min: 1 / 0,
                max: 0,
                sum: 0
            },
            globalContextBytes: {
                min: 1 / 0,
                max: 0,
                sum: 0
            },
            userContextBytes: {
                min: 1 / 0,
                max: 0,
                sum: 0
            },
            featureFlagBytes: {
                min: 1 / 0,
                max: 0,
                sum: 0
            }
        }
    }
    function Ba() {
        Pa = !1,
        Na = {
            globalContextBytes: {
                min: 1 / 0,
                max: 0,
                sum: 0
            },
            userContextBytes: {
                min: 1 / 0,
                max: 0,
                sum: 0
            },
            featureFlagBytes: {
                min: 1 / 0,
                max: 0,
                sum: 0
            }
        }
    }
    var za, Ha = 500, ja = [];
    function qa() {
        return "hidden" === document.visibilityState ? "hidden" : document.hasFocus() ? "active" : "passive"
    }
    function Va(t, e) {
        void 0 === e && (e = Ha),
        t !== za && (za = t,
        ja.length === e && ja.shift(),
        ja.push({
            state: za,
            startTime: _e()
        }))
    }
    function Za(t, e) {
        var n = e.session
          , r = e.viewContext
          , i = e.errorType
          , o = n ? n.id : "no-session-id"
          , a = [];
        void 0 !== i && a.push("error-type=".concat(i)),
        r && (a.push("seed=".concat(r.id)),
        a.push("from=".concat(r.startClocks.timeStamp)));
        var s, u, c, l = (u = (s = t).site,
        c = s.subdomain || function(t) {
            switch (t.site) {
            case hn:
            case vn:
                return "app";
            case pn:
                return "dd";
            default:
                return
            }
        }(s),
        "https://".concat(c ? "".concat(c, ".") : "").concat(u)), d = "/rum/replay/sessions/".concat(o);
        return "".concat(l).concat(d, "?").concat(a.join("&"))
    }
    var Ga = {
        FullSnapshot: 2,
        IncrementalSnapshot: 3,
        Meta: 4,
        Focus: 6,
        ViewEnd: 7,
        VisualViewport: 8,
        FrustrationRecord: 9
    }
      , Wa = {
        Document: 0,
        DocumentType: 1,
        Element: 2,
        Text: 3,
        CDATA: 4,
        DocumentFragment: 11
    }
      , Ya = {
        Mutation: 0,
        MouseMove: 1,
        MouseInteraction: 2,
        Scroll: 3,
        ViewportResize: 4,
        Input: 5,
        TouchMove: 6,
        MediaInteraction: 7,
        StyleSheetRule: 8
    }
      , Ka = {
        MouseUp: 0,
        MouseDown: 1,
        Click: 2,
        ContextMenu: 3,
        DblClick: 4,
        Focus: 5,
        Blur: 6,
        TouchStart: 7,
        TouchEnd: 9
    }
      , $a = {
        Play: 0,
        Pause: 1
    };
    function Xa(t, e) {
        return {
            data: Ct({
                source: t
            }, e),
            type: Ga.IncrementalSnapshot,
            timestamp: ge()
        }
    }
    var Ja = {
        IGNORE: "ignore",
        HIDDEN: "hidden",
        ALLOW: Tn.ALLOW,
        MASK: Tn.MASK,
        MASK_USER_INPUT: Tn.MASK_USER_INPUT
    }
      , Qa = "data-dd-privacy"
      , ts = "allow"
      , es = "mask"
      , ns = "mask-user-input"
      , rs = "hidden"
      , is = "dd-privacy-allow"
      , os = "dd-privacy-mask"
      , as = "dd-privacy-mask-user-input"
      , ss = "dd-privacy-hidden"
      , us = "***"
      , cs = "data:image/gif;base64,R0lGODlhAQABAIAAAMLCwgAAACH5BAAAAAAALAAAAAABAAEAAAICRAEAOw=="
      , ls = {
        INPUT: !0,
        OUTPUT: !0,
        TEXTAREA: !0,
        SELECT: !0,
        OPTION: !0,
        DATALIST: !0,
        OPTGROUP: !0
    }
      , ds = 1e5;
    function fs(t, e) {
        var n = vr(t)
          , r = n ? fs(n, e) : e;
        return ps(hs(t), r)
    }
    function ps(t, e) {
        switch (e) {
        case Ja.HIDDEN:
        case Ja.IGNORE:
            return e
        }
        switch (t) {
        case Ja.ALLOW:
        case Ja.MASK:
        case Ja.MASK_USER_INPUT:
        case Ja.HIDDEN:
        case Ja.IGNORE:
            return t;
        default:
            return e
        }
    }
    function hs(t) {
        if (fr(t)) {
            var e = t.getAttribute(Qa);
            if ("BASE" === t.tagName)
                return Ja.ALLOW;
            if ("INPUT" === t.tagName) {
                var n = t;
                if ("password" === n.type || "email" === n.type || "tel" === n.type)
                    return Ja.MASK;
                if ("hidden" === n.type)
                    return Ja.MASK;
                var r = n.getAttribute("autocomplete");
                if (r && 0 === r.indexOf("cc-"))
                    return Ja.MASK
            }
            return e === rs || t.classList.contains(ss) ? Ja.HIDDEN : e === es || t.classList.contains(os) ? Ja.MASK : e === ns || t.classList.contains(as) ? Ja.MASK_USER_INPUT : e === ts || t.classList.contains(is) ? Ja.ALLOW : function(t) {
                if ("SCRIPT" === t.nodeName)
                    return !0;
                if ("LINK" === t.nodeName) {
                    var e = i("rel");
                    return /preload|prefetch/i.test(e) && "script" === i("as") || "shortcut icon" === e || "icon" === e
                }
                if ("META" === t.nodeName) {
                    var n = i("name")
                      , r = (e = i("rel"),
                    i("property"));
                    return /^msapplication-tile(image|color)$/.test(n) || "application-name" === n || "icon" === e || "apple-touch-icon" === e || "shortcut icon" === e || "keywords" === n || "description" === n || /^(og|twitter|fb):/.test(r) || /^(og|twitter):/.test(n) || "pinterest" === n || "robots" === n || "googlebot" === n || "bingbot" === n || t.hasAttribute("http-equiv") || "author" === n || "generator" === n || "framework" === n || "publisher" === n || "progid" === n || /^article:/.test(r) || /^product:/.test(r) || "google-site-verification" === n || "yandex-verification" === n || "csrf-token" === n || "p:domain_verify" === n || "verify-v1" === n || "verification" === n || "shopify-checkout-api-token" === n
                }
                function i(e) {
                    return (t.getAttribute(e) || "").toLowerCase()
                }
                return !1
            }(t) ? Ja.IGNORE : void 0
        }
    }
    function vs(t, e) {
        switch (e) {
        case Ja.MASK:
        case Ja.HIDDEN:
        case Ja.IGNORE:
            return !0;
        case Ja.MASK_USER_INPUT:
            return dr(t) ? ms(t.parentNode) : ms(t);
        default:
            return !1
        }
    }
    function ms(t) {
        if (!t || t.nodeType !== t.ELEMENT_NODE)
            return !1;
        var e = t;
        if ("INPUT" === e.tagName)
            switch (e.type) {
            case "button":
            case "color":
            case "reset":
            case "submit":
                return !1
            }
        return !!ls[e.tagName]
    }
    var gs = function(t) {
        return t.replace(/\S/g, "x")
    };
    function _s(t, e, n) {
        var r, i = null === (r = t.parentElement) || void 0 === r ? void 0 : r.tagName, o = t.textContent || "";
        if (!e || o.trim()) {
            var a = n
              , s = "STYLE" === i || void 0;
            if ("SCRIPT" === i)
                o = us;
            else if (a === Ja.HIDDEN)
                o = us;
            else if (vs(t, a) && !s)
                if ("DATALIST" === i || "SELECT" === i || "OPTGROUP" === i) {
                    if (!o.trim())
                        return
                } else
                    o = "OPTION" === i ? us : gs(o);
            return o
        }
    }
    var ys = new WeakMap;
    function bs(t) {
        return ys.has(t)
    }
    function ws(t) {
        return ys.get(t)
    }
    function Es(t, e) {
        var n = t.tagName
          , r = t.value;
        if (vs(t, e)) {
            var i = t.type;
            if ("INPUT" === n && ("button" === i || "submit" === i || "reset" === i))
                return r;
            if (!r || "OPTION" === n)
                return;
            return us
        }
        return "OPTION" === n || "SELECT" === n ? t.value : "INPUT" === n || "TEXTAREA" === n ? r : void 0
    }
    var Ss = /url\((?:(')([^']*)'|(")([^"]*)"|([^)]*))\)/gm
      , Ts = /^[A-Za-z]+:|^\/\//
      , Cs = /^data:.*,/i;
    function As(t, e) {
        return t.replace(Ss, (function(t, n, r, i, o, a) {
            var s = r || o || a;
            if (!e || !s || Ts.test(s) || Cs.test(s))
                return t;
            var u = n || i || "";
            return "url(".concat(u).concat(function(t, e) {
                try {
                    return cn(t, e).href
                } catch (n) {
                    return t
                }
            }(s, e)).concat(u, ")")
        }
        ))
    }
    var ks = /[^a-z1-6-_]/;
    function Rs(t) {
        var e = t.toLowerCase().trim();
        return ks.test(e) ? "div" : e
    }
    function xs(t) {
        if (void 0 !== t && 0 !== t.length)
            return t.map((function(t) {
                var e = t.cssRules || t.rules;
                return {
                    cssRules: Array.from(e, (function(t) {
                        return t.cssText
                    }
                    )),
                    disabled: t.disabled || void 0,
                    media: t.media.length > 0 ? Array.from(t.media) : void 0
                }
            }
            ))
    }
    function Is(t, e, n, r) {
        if (e === Ja.HIDDEN)
            return null;
        var i = t.getAttribute(n);
        if (e === Ja.MASK && n !== Qa && !Ui.includes(n) && n !== r.actionNameAttribute) {
            var o = t.tagName;
            switch (n) {
            case "title":
            case "alt":
            case "placeholder":
                return us
            }
            if (!("IMG" !== o && "SOURCE" !== o || "src" !== n && "srcset" !== n))
                return cs;
            if ("A" === o && "href" === n)
                return us;
            if (i && St(n, "data-"))
                return us
        }
        return i && "string" == typeof i && i.length > ds && "data:" === i.slice(0, 5) ? "data:truncated" : i
    }
    function Os(t) {
        if (!t)
            return null;
        var e;
        try {
            e = t.rules || t.cssRules
        } catch (Zs) {}
        return e ? As(Array.from(e, Ns).join(""), t.href) : null
    }
    function Ns(t) {
        return function(t) {
            return "styleSheet"in t
        }(t) ? Os(t.styleSheet) || "" : t.cssText
    }
    function Ps(t, e) {
        var n = function(t, e) {
            switch (t.nodeType) {
            case t.DOCUMENT_NODE:
                return function(t, e) {
                    return {
                        type: Wa.Document,
                        childNodes: Ms(t, e),
                        adoptedStyleSheets: xs(t.adoptedStyleSheets)
                    }
                }(t, e);
            case t.DOCUMENT_FRAGMENT_NODE:
                return function(t, e) {
                    var n = [];
                    t.childNodes.length && (n = Ms(t, e));
                    var r = hr(t);
                    r && e.serializationContext.shadowRootsController.addShadowRoot(t);
                    return {
                        type: Wa.DocumentFragment,
                        childNodes: n,
                        isShadowRoot: r,
                        adoptedStyleSheets: r ? xs(t.adoptedStyleSheets) : void 0
                    }
                }(t, e);
            case t.DOCUMENT_TYPE_NODE:
                return n = t,
                {
                    type: Wa.DocumentType,
                    name: n.name,
                    publicId: n.publicId,
                    systemId: n.systemId
                };
            case t.ELEMENT_NODE:
                return function(t, e) {
                    var n, r = Rs(t.tagName), i = (a = t,
                    "svg" === a.tagName || a instanceof SVGElement || void 0), o = ps(hs(t), e.parentNodePrivacyLevel);
                    var a;
                    if (o === Ja.HIDDEN) {
                        var s = t.getBoundingClientRect()
                          , u = s.width
                          , c = s.height;
                        return {
                            type: Wa.Element,
                            tagName: r,
                            attributes: (n = {
                                rr_width: "".concat(u, "px"),
                                rr_height: "".concat(c, "px")
                            },
                            n[Qa] = rs,
                            n),
                            childNodes: [],
                            isSVG: i
                        }
                    }
                    if (o === Ja.IGNORE)
                        return;
                    var l = function(t, e, n) {
                        var r;
                        if (e === Ja.HIDDEN)
                            return {};
                        for (var i = {}, o = Rs(t.tagName), a = t.ownerDocument, s = 0; s < t.attributes.length; s += 1) {
                            var u = t.attributes.item(s).name
                              , c = Is(t, e, u, n.configuration);
                            null !== c && (i[u] = c)
                        }
                        if (t.value && ("textarea" === o || "select" === o || "option" === o || "input" === o)) {
                            var l = Es(t, e);
                            void 0 !== l && (i.value = l)
                        }
                        if ("option" === o && e === Ja.ALLOW) {
                            var d = t;
                            d.selected && (i.selected = d.selected)
                        }
                        if ("link" === o) {
                            var f, p = Array.from(a.styleSheets).find((function(e) {
                                return e.href === t.href
                            }
                            ));
                            (f = Os(p)) && p && (i._cssText = f)
                        }
                        "style" === o && t.sheet && !(t.innerText || t.textContent || "").trim().length && (f = Os(t.sheet)) && (i._cssText = f);
                        var h, v, m = t;
                        if ("input" !== o || "radio" !== m.type && "checkbox" !== m.type || (e === Ja.ALLOW ? i.checked = !!m.checked : vs(m, e) && delete i.checked),
                        "audio" === o || "video" === o) {
                            var g = t;
                            i.rr_mediaState = g.paused ? "paused" : "played"
                        }
                        var _ = n.serializationContext;
                        switch (_.status) {
                        case 0:
                            h = Math.round(t.scrollTop),
                            v = Math.round(t.scrollLeft),
                            (h || v) && _.elementsScrollPositions.set(t, {
                                scrollTop: h,
                                scrollLeft: v
                            });
                            break;
                        case 1:
                            _.elementsScrollPositions.has(t) && (h = (r = _.elementsScrollPositions.get(t)).scrollTop,
                            v = r.scrollLeft)
                        }
                        return v && (i.rr_scrollLeft = v),
                        h && (i.rr_scrollTop = h),
                        i
                    }(t, o, e)
                      , d = [];
                    if (t.childNodes.length) {
                        d = Ms(t, e.parentNodePrivacyLevel === o && e.ignoreWhiteSpace === ("head" === r) ? e : Ct({}, e, {
                            parentNodePrivacyLevel: o,
                            ignoreWhiteSpace: "head" === r
                        }))
                    }
                    if (pr(t)) {
                        var f = Ps(t.shadowRoot, e);
                        null !== f && d.push(f)
                    }
                    return {
                        type: Wa.Element,
                        tagName: r,
                        attributes: l,
                        childNodes: d,
                        isSVG: i
                    }
                }(t, e);
            case t.TEXT_NODE:
                return function(t, e) {
                    var n, r = null === (n = t.parentElement) || void 0 === n ? void 0 : n.tagName, i = _s(t, e.ignoreWhiteSpace || !1, e.parentNodePrivacyLevel);
                    if (!i)
                        return;
                    return {
                        type: Wa.Text,
                        textContent: i,
                        isStyle: "STYLE" === r || void 0
                    }
                }(t, e);
            case t.CDATA_SECTION_NODE:
                return {
                    type: Wa.CDATA,
                    textContent: ""
                }
            }
            var n
        }(t, e);
        if (!n)
            return null;
        var r = ws(t) || Ls++
          , i = n;
        return i.id = r,
        function(t, e) {
            ys.set(t, e)
        }(t, r),
        e.serializedNodeIds && e.serializedNodeIds.add(r),
        i
    }
    var Ls = 1;
    function Ms(t, e) {
        var n = [];
        return t.childNodes.forEach((function(t) {
            var r = Ps(t, e);
            r && n.push(r)
        }
        )),
        n
    }
    function Us(t, e, n) {
        return Ps(t, {
            serializationContext: n,
            parentNodePrivacyLevel: e.defaultPrivacyLevel,
            configuration: e
        })
    }
    function Ds(t) {
        return Boolean(t.changedTouches)
    }
    function Fs(t) {
        return !0 === t.composed && pr(t.target) ? t.composedPath()[0] : t.target
    }
    var Bs = function(t, e) {
        var n = window.visualViewport
          , r = {
            layoutViewportX: t,
            layoutViewportY: e,
            visualViewportX: t,
            visualViewportY: e
        };
        return n ? (!function(t) {
            return Math.abs(t.pageTop - t.offsetTop - window.scrollY) > 25 || Math.abs(t.pageLeft - t.offsetLeft - window.scrollX) > 25
        }(n) ? (r.visualViewportX = Math.round(t - n.offsetLeft),
        r.visualViewportY = Math.round(e - n.offsetTop)) : (r.layoutViewportX = Math.round(t + n.offsetLeft),
        r.layoutViewportY = Math.round(e + n.offsetTop)),
        r) : r
    }
      , zs = function(t) {
        return {
            scale: t.scale,
            offsetLeft: t.offsetLeft,
            offsetTop: t.offsetTop,
            pageLeft: t.pageLeft,
            pageTop: t.pageTop,
            height: t.height,
            width: t.width
        }
    };
    function Hs() {
        var t, e = window.visualViewport;
        return t = e ? e.pageLeft - e.offsetLeft : void 0 !== window.scrollX ? window.scrollX : window.pageXOffset || 0,
        Math.round(t)
    }
    function js() {
        var t, e = window.visualViewport;
        return t = e ? e.pageTop - e.offsetTop : void 0 !== window.scrollY ? window.scrollY : window.pageYOffset || 0,
        Math.round(t)
    }
    var qs = 50;
    function Vs(t) {
        var e = Ds(t) ? t.changedTouches[0] : t
          , n = e.clientX
          , r = e.clientY;
        if (window.visualViewport) {
            var i = Bs(n, r);
            n = i.visualViewportX,
            r = i.visualViewportY
        }
        if (Number.isFinite(n) && Number.isFinite(r))
            return {
                x: n,
                y: r
            };
        t.isTrusted && Kn("mouse/touch event without x/y")
    }
    var Zs, Gs = 100;
    var Ws = ((Zs = {}).pointerup = Ka.MouseUp,
    Zs.mousedown = Ka.MouseDown,
    Zs.click = Ka.Click,
    Zs.contextmenu = Ka.ContextMenu,
    Zs.dblclick = Ka.DblClick,
    Zs.focus = Ka.Focus,
    Zs.blur = Ka.Blur,
    Zs.touchstart = Ka.TouchStart,
    Zs.touchend = Ka.TouchEnd,
    Zs);
    function Ys(t, e, n) {
        var r = void 0 === n ? {} : n
          , i = r.domEvents
          , o = void 0 === i ? ["input", "change"] : i
          , a = r.target
          , s = void 0 === a ? document : a
          , u = new WeakMap;
        function c(t) {
            var n = fs(t, e);
            if (n !== Ja.HIDDEN) {
                var r, i = t.type;
                if ("radio" === i || "checkbox" === i) {
                    if (vs(t, n))
                        return;
                    r = {
                        isChecked: t.checked
                    }
                } else {
                    var o = Es(t, n);
                    if (void 0 === o)
                        return;
                    r = {
                        text: o
                    }
                }
                l(t, r);
                var a, s, u = t.name;
                "radio" === i && u && t.checked && (a = document.querySelectorAll('input[type="radio"][name="'.concat(u, '"]')),
                s = function(e) {
                    e !== t && l(e, {
                        isChecked: !1
                    })
                }
                ,
                Array.prototype.forEach.call(a, s))
            }
        }
        function l(e, n) {
            if (bs(e)) {
                var r = u.get(e);
                r && r.text === n.text && r.isChecked === n.isChecked || (u.set(e, n),
                t(Ct({
                    id: ws(e)
                }, n)))
            }
        }
        var d = zn(s, o, (function(t) {
            var e = Fs(t);
            (e instanceof HTMLInputElement || e instanceof HTMLTextAreaElement || e instanceof HTMLSelectElement) && c(e)
        }
        ), {
            capture: !0,
            passive: !0
        }).stop
          , f = [ni(HTMLInputElement.prototype, "value", c), ni(HTMLInputElement.prototype, "checked", c), ni(HTMLSelectElement.prototype, "value", c), ni(HTMLTextAreaElement.prototype, "value", c), ni(HTMLSelectElement.prototype, "selectedIndex", c)];
        return function() {
            f.forEach((function(t) {
                return t.stop()
            }
            )),
            d()
        }
    }
    function Ks(t) {
        for (var e = [], n = t; n.parentRule; ) {
            var r = Array.from(n.parentRule.cssRules).indexOf(n);
            e.unshift(r),
            n = n.parentRule
        }
        if (n.parentStyleSheet) {
            var i = Array.from(n.parentStyleSheet.cssRules).indexOf(n);
            return e.unshift(i),
            e
        }
    }
    var $s = 200;
    var Xs = 100;
    function Js(t) {
        var e = zt
          , n = [];
        function r() {
            e(),
            t(n),
            n = []
        }
        return {
            addMutations: function(t) {
                0 === n.length && (e = function(t, e) {
                    if (window.requestIdleCallback && window.cancelIdleCallback) {
                        var n = window.requestIdleCallback(vt(t), e);
                        return function() {
                            return window.cancelIdleCallback(n)
                        }
                    }
                    var r = window.requestAnimationFrame(vt(t));
                    return function() {
                        return window.cancelAnimationFrame(r)
                    }
                }(r, {
                    timeout: Xs
                })),
                n.push.apply(n, t)
            },
            flush: r,
            stop: function() {
                e()
            }
        }
    }
    function Qs(t, e, n, r) {
        var i = Qn();
        if (!i)
            return {
                stop: zt,
                flush: zt
            };
        var o = Js((function(i) {
            !function(t, e, n, r, i) {
                t.filter((function(t) {
                    return "childList" === t.type
                }
                )).forEach((function(t) {
                    t.removedNodes.forEach((function(t) {
                        tu(t, r.removeShadowRoot)
                    }
                    ))
                }
                ));
                var o = t.filter((function(t) {
                    return i.contains(t.target) && function(t) {
                        for (var e = t; e; ) {
                            if (!bs(e) && !hr(e))
                                return !1;
                            e = vr(e)
                        }
                        return !0
                    }(t.target) && fs(t.target, n.defaultPrivacyLevel) !== Ja.HIDDEN
                }
                ))
                  , a = function(t, e, n) {
                    for (var r = new Set, i = new Map, o = function(t) {
                        t.addedNodes.forEach((function(t) {
                            r.add(t)
                        }
                        )),
                        t.removedNodes.forEach((function(e) {
                            r.has(e) || i.set(e, t.target),
                            r.delete(e)
                        }
                        ))
                    }, a = 0, s = t; a < s.length; a++) {
                        o(s[a])
                    }
                    var u = Array.from(r);
                    c = u,
                    c.sort((function(t, e) {
                        var n = t.compareDocumentPosition(e);
                        return n & Node.DOCUMENT_POSITION_CONTAINED_BY ? -1 : n & Node.DOCUMENT_POSITION_CONTAINS || n & Node.DOCUMENT_POSITION_FOLLOWING ? 1 : n & Node.DOCUMENT_POSITION_PRECEDING ? -1 : 0
                    }
                    ));
                    var c;
                    for (var l = new Set, d = [], f = 0, p = u; f < p.length; f++) {
                        var h = p[f];
                        if (!y(h)) {
                            var v = fs(h.parentNode, e.defaultPrivacyLevel);
                            if (v !== Ja.HIDDEN && v !== Ja.IGNORE) {
                                var m = Ps(h, {
                                    serializedNodeIds: l,
                                    parentNodePrivacyLevel: v,
                                    serializationContext: {
                                        status: 2,
                                        shadowRootsController: n
                                    },
                                    configuration: e
                                });
                                if (m) {
                                    var g = vr(h);
                                    d.push({
                                        nextId: b(h),
                                        parentId: ws(g),
                                        node: m
                                    })
                                }
                            }
                        }
                    }
                    var _ = [];
                    return i.forEach((function(t, e) {
                        bs(e) && _.push({
                            parentId: ws(t),
                            id: ws(e)
                        })
                    }
                    )),
                    {
                        adds: d,
                        removes: _,
                        hasBeenSerialized: y
                    };
                    function y(t) {
                        return bs(t) && l.has(ws(t))
                    }
                    function b(t) {
                        for (var e = t.nextSibling; e; ) {
                            if (bs(e))
                                return ws(e);
                            e = e.nextSibling
                        }
                        return null
                    }
                }(o.filter((function(t) {
                    return "childList" === t.type
                }
                )), n, r)
                  , s = a.adds
                  , u = a.removes
                  , c = a.hasBeenSerialized
                  , l = function(t, e) {
                    for (var n, r = [], i = new Set, o = t.filter((function(t) {
                        return !i.has(t.target) && (i.add(t.target),
                        !0)
                    }
                    )), a = 0, s = o; a < s.length; a++) {
                        var u = s[a];
                        if (u.target.textContent !== u.oldValue) {
                            var c = fs(vr(u.target), e.defaultPrivacyLevel);
                            c !== Ja.HIDDEN && c !== Ja.IGNORE && r.push({
                                id: ws(u.target),
                                value: null !== (n = _s(u.target, !1, c)) && void 0 !== n ? n : null
                            })
                        }
                    }
                    return r
                }(o.filter((function(t) {
                    return "characterData" === t.type && !c(t.target)
                }
                )), n)
                  , d = function(t, e) {
                    for (var n = [], r = new Map, i = t.filter((function(t) {
                        var e = r.get(t.target);
                        return !(null == e ? void 0 : e.has(t.attributeName)) && (e ? e.add(t.attributeName) : r.set(t.target, new Set([t.attributeName])),
                        !0)
                    }
                    )), o = new Map, a = 0, s = i; a < s.length; a++) {
                        var u = s[a];
                        if (u.target.getAttribute(u.attributeName) !== u.oldValue) {
                            var c = fs(u.target, e.defaultPrivacyLevel)
                              , l = Is(u.target, c, u.attributeName, e)
                              , d = void 0;
                            if ("value" === u.attributeName) {
                                var f = Es(u.target, c);
                                if (void 0 === f)
                                    continue;
                                d = f
                            } else
                                d = "string" == typeof l ? l : null;
                            var p = o.get(u.target);
                            p || (p = {
                                id: ws(u.target),
                                attributes: {}
                            },
                            n.push(p),
                            o.set(u.target, p)),
                            p.attributes[u.attributeName] = d
                        }
                    }
                    return n
                }(o.filter((function(t) {
                    return "attributes" === t.type && !c(t.target)
                }
                )), n);
                if (!(l.length || d.length || u.length || s.length))
                    return;
                e({
                    adds: s,
                    removes: u,
                    texts: l,
                    attributes: d
                })
            }(i.concat(a.takeRecords()), t, e, n, r)
        }
        ))
          , a = new i(vt(o.addMutations));
        return a.observe(r, {
            attributeOldValue: !0,
            attributes: !0,
            characterData: !0,
            characterDataOldValue: !0,
            childList: !0,
            subtree: !0
        }),
        {
            stop: function() {
                a.disconnect(),
                o.stop()
            },
            flush: function() {
                o.flush()
            }
        }
    }
    function tu(t, e) {
        var n;
        pr(t) && e(t.shadowRoot),
        (n = t,
        pr(n) ? n.shadowRoot.childNodes : n.childNodes).forEach((function(t) {
            return tu(t, e)
        }
        ))
    }
    function eu(t) {
        var e, n, r, i, o, a = function() {
            var t = new WeakMap
              , e = 1;
            return {
                getIdForEvent: function(n) {
                    return t.has(n) || t.set(n, e++),
                    t.get(n)
                }
            }
        }(), s = Qs(t.mutationCb, t.configuration, t.shadowRootsController, document), u = (e = t.mousemoveCb,
        n = Bt((function(t) {
            var n = Fs(t);
            if (bs(n)) {
                var r = Vs(t);
                if (!r)
                    return;
                var i = {
                    id: ws(n),
                    timeOffset: 0,
                    x: r.x,
                    y: r.y
                };
                e([i], Ds(t) ? Ya.TouchMove : Ya.MouseMove)
            }
        }
        ), qs, {
            trailing: !1
        }).throttled,
        zn(document, ["mousemove", "touchmove"], n, {
            capture: !0,
            passive: !0
        }).stop), c = function(t, e, n) {
            return zn(document, Object.keys(Ws), (function(r) {
                var i = Fs(r);
                if (fs(i, e) !== Ja.HIDDEN && bs(i)) {
                    var o, a = ws(i), s = Ws[r.type];
                    if (s !== Ka.Blur && s !== Ka.Focus) {
                        var u = Vs(r);
                        if (!u)
                            return;
                        o = {
                            id: a,
                            type: s,
                            x: u.x,
                            y: u.y
                        }
                    } else
                        o = {
                            id: a,
                            type: s
                        };
                    var c = Ct({
                        id: n.getIdForEvent(r)
                    }, Xa(Ya.MouseInteraction, o));
                    t(c)
                }
            }
            ), {
                capture: !0,
                passive: !0
            }).stop
        }(t.mouseInteractionCb, t.configuration.defaultPrivacyLevel, a), l = function(t, e, n) {
            var r = Bt((function(r) {
                var i = Fs(r);
                if (i && fs(i, e) !== Ja.HIDDEN && bs(i)) {
                    var o = ws(i)
                      , a = i === document ? {
                        scrollTop: js(),
                        scrollLeft: Hs()
                    } : {
                        scrollTop: Math.round(i.scrollTop),
                        scrollLeft: Math.round(i.scrollLeft)
                    };
                    n.set(i, a),
                    t({
                        id: o,
                        x: a.scrollLeft,
                        y: a.scrollTop
                    })
                }
            }
            ), Gs).throttled;
            return Bn(document, "scroll", r, {
                capture: !0,
                passive: !0
            }).stop
        }(t.scrollCb, t.configuration.defaultPrivacyLevel, t.elementsScrollPositions), d = function(t) {
            return Ir().subscribe(t).unsubscribe
        }(t.viewportResizeCb), f = Ys(t.inputCb, t.configuration.defaultPrivacyLevel), p = (r = t.mediaInteractionCb,
        i = t.configuration.defaultPrivacyLevel,
        zn(document, ["play", "pause"], (function(t) {
            var e = Fs(t);
            e && fs(e, i) !== Ja.HIDDEN && bs(e) && r({
                id: ws(e),
                type: "play" === t.type ? $a.Play : $a.Pause
            })
        }
        ), {
            capture: !0,
            passive: !0
        }).stop), h = function(t) {
            function e(t, e) {
                t && bs(t.ownerNode) && e(ws(t.ownerNode))
            }
            var n = [ei(CSSStyleSheet.prototype, "insertRule", {
                before: function(n, r) {
                    e(this, (function(e) {
                        return t({
                            id: e,
                            adds: [{
                                rule: n,
                                index: r
                            }]
                        })
                    }
                    ))
                }
            }), ei(CSSStyleSheet.prototype, "deleteRule", {
                before: function(n) {
                    e(this, (function(e) {
                        return t({
                            id: e,
                            removes: [{
                                index: n
                            }]
                        })
                    }
                    ))
                }
            })];
            function r(r) {
                n.push(ei(r.prototype, "insertRule", {
                    before: function(n, r) {
                        var i = this;
                        e(this.parentStyleSheet, (function(e) {
                            var o = Ks(i);
                            o && (o.push(r || 0),
                            t({
                                id: e,
                                adds: [{
                                    rule: n,
                                    index: o
                                }]
                            }))
                        }
                        ))
                    }
                }), ei(r.prototype, "deleteRule", {
                    before: function(n) {
                        var r = this;
                        e(this.parentStyleSheet, (function(e) {
                            var i = Ks(r);
                            i && (i.push(n),
                            t({
                                id: e,
                                removes: [{
                                    index: i
                                }]
                            }))
                        }
                        ))
                    }
                }))
            }
            return "undefined" != typeof CSSGroupingRule ? r(CSSGroupingRule) : (r(CSSMediaRule),
            r(CSSSupportsRule)),
            function() {
                return n.forEach((function(t) {
                    return t.stop()
                }
                ))
            }
        }(t.styleSheetCb), v = (o = t.focusCb,
        zn(window, ["focus", "blur"], (function() {
            o({
                has_focus: document.hasFocus()
            })
        }
        )).stop), m = function(t) {
            var e = window.visualViewport;
            if (!e)
                return zt;
            var n = Bt((function() {
                t(zs(e))
            }
            ), $s, {
                trailing: !1
            })
              , r = n.throttled
              , i = n.cancel
              , o = zn(e, ["resize", "scroll"], r, {
                capture: !0,
                passive: !0
            }).stop;
            return function() {
                o(),
                i()
            }
        }(t.visualViewportResizeCb), g = function(t, e, n) {
            return t.subscribe(10, (function(t) {
                var r, i, o;
                "action" === t.rawRumEvent.type && "click" === t.rawRumEvent.action.type && (null === (i = null === (r = t.rawRumEvent.action.frustration) || void 0 === r ? void 0 : r.type) || void 0 === i ? void 0 : i.length) && "events"in t.domainContext && (null === (o = t.domainContext.events) || void 0 === o ? void 0 : o.length) && e({
                    timestamp: t.rawRumEvent.date,
                    type: Ga.FrustrationRecord,
                    data: {
                        frustrationTypes: t.rawRumEvent.action.frustration.type,
                        recordIds: t.domainContext.events.map((function(t) {
                            return n.getIdForEvent(t)
                        }
                        ))
                    }
                })
            }
            )).unsubscribe
        }(t.lifeCycle, t.frustrationCb, a);
        return {
            flush: function() {
                s.flush()
            },
            stop: function() {
                s.stop(),
                u(),
                c(),
                l(),
                d(),
                f(),
                p(),
                h(),
                v(),
                m(),
                g()
            }
        }
    }
    var nu = function(t, e) {
        var n = e.mutationCb
          , r = e.inputCb
          , i = new Map
          , o = {
            addShadowRoot: function(e) {
                var a = Qs(n, t, o, e)
                  , s = a.stop
                  , u = a.flush
                  , c = Ys(r, t.defaultPrivacyLevel, {
                    target: e,
                    domEvents: ["change"]
                });
                i.set(e, {
                    flush: u,
                    stop: function() {
                        s(),
                        c()
                    }
                })
            },
            removeShadowRoot: function(t) {
                var e = i.get(t);
                e && (e.stop(),
                i.delete(t))
            },
            stop: function() {
                i.forEach((function(t) {
                    return (0,
                    t.stop)()
                }
                ))
            },
            flush: function() {
                i.forEach((function(t) {
                    return (0,
                    t.flush)()
                }
                ))
            }
        };
        return o
    };
    function ru(t) {
        var e = t.emit;
        if (!e)
            throw new Error("emit function is required");
        var n, r = (n = new WeakMap,
        {
            set: function(t, e) {
                (t !== document || document.scrollingElement) && n.set(t === document ? document.scrollingElement : t, e)
            },
            get: function(t) {
                return n.get(t)
            },
            has: function(t) {
                return n.has(t)
            }
        }), i = function(t) {
            e(Xa(Ya.Mutation, t))
        }, o = function(t) {
            return e(Xa(Ya.Input, t))
        }, a = nu(t.configuration, {
            mutationCb: i,
            inputCb: o
        }), s = function(n, i) {
            void 0 === n && (n = ge()),
            void 0 === i && (i = {
                status: 0,
                elementsScrollPositions: r,
                shadowRootsController: a
            });
            var o = Or()
              , s = o.width
              , u = o.height;
            e({
                data: {
                    height: u,
                    href: window.location.href,
                    width: s
                },
                type: Ga.Meta,
                timestamp: n
            }),
            e({
                data: {
                    has_focus: document.hasFocus()
                },
                type: Ga.Focus,
                timestamp: n
            }),
            e({
                data: {
                    node: Us(document, t.configuration, i),
                    initialOffset: {
                        left: Hs(),
                        top: js()
                    }
                },
                type: Ga.FullSnapshot,
                timestamp: n
            }),
            window.visualViewport && e({
                data: zs(window.visualViewport),
                type: Ga.VisualViewport,
                timestamp: n
            })
        };
        s();
        var u = eu({
            lifeCycle: t.lifeCycle,
            configuration: t.configuration,
            elementsScrollPositions: r,
            inputCb: o,
            mediaInteractionCb: function(t) {
                return e(Xa(Ya.MediaInteraction, t))
            },
            mouseInteractionCb: function(t) {
                return e(t)
            },
            mousemoveCb: function(t, n) {
                return e(Xa(n, {
                    positions: t
                }))
            },
            mutationCb: i,
            scrollCb: function(t) {
                return e(Xa(Ya.Scroll, t))
            },
            styleSheetCb: function(t) {
                return e(Xa(Ya.StyleSheetRule, t))
            },
            viewportResizeCb: function(t) {
                return e(Xa(Ya.ViewportResize, t))
            },
            frustrationCb: function(t) {
                return e(t)
            },
            focusCb: function(t) {
                return e({
                    data: t,
                    type: Ga.Focus,
                    timestamp: ge()
                })
            },
            visualViewportResizeCb: function(t) {
                e({
                    data: t,
                    type: Ga.VisualViewport,
                    timestamp: ge()
                })
            },
            shadowRootsController: a
        })
          , c = u.stop
          , l = u.flush;
        function d() {
            a.flush(),
            l()
        }
        return {
            stop: function() {
                a.stop(),
                c()
            },
            takeSubsequentFullSnapshot: function(t) {
                d(),
                s(t, {
                    shadowRootsController: a,
                    status: 1,
                    elementsScrollPositions: r
                })
            },
            flushMutations: d,
            shadowRootsController: a
        }
    }
    var iu, ou = 10;
    function au(t) {
        return cu(t).segments_count
    }
    function su(t) {
        cu(t).records_count += 1
    }
    function uu(t) {
        return null == iu ? void 0 : iu.get(t)
    }
    function cu(t) {
        var e;
        return iu || (iu = new Map),
        iu.has(t) ? e = iu.get(t) : (e = {
            records_count: 0,
            segments_count: 0,
            segments_total_raw_size: 0
        },
        iu.set(t, e),
        iu.size > ou && function() {
            if (!iu)
                return;
            if (iu.keys)
                iu.delete(iu.keys().next().value);
            else {
                var t = !0;
                iu.forEach((function(e, n) {
                    t && (iu.delete(n),
                    t = !1)
                }
                ))
            }
        }()),
        e
    }
    var lu, du = 0, fu = function() {
        function t(t, e, n, r, i, o) {
            var a = this;
            this.worker = t,
            this.id = du++;
            var s = e.view.id;
            this.metadata = Ct({
                start: r.timestamp,
                end: r.timestamp,
                creation_reason: n,
                records_count: 1,
                has_full_snapshot: r.type === Ga.FullSnapshot,
                index_in_view: au(s),
                source: "browser"
            }, e),
            function(t) {
                cu(t).segments_count += 1
            }(s),
            su(s);
            var u = Bn(t, "message", (function(t) {
                var e = t.data;
                "errored" !== e.type && "initialized" !== e.type && (e.id === a.id ? (!function(t, e) {
                    cu(t).segments_total_raw_size += e
                }(s, e.additionalBytesCount),
                "flushed" === e.type ? (o(e.result, e.rawBytesCount),
                u()) : i(e.compressedBytesCount)) : e.id > a.id && (u(),
                Kn("Segment did not receive a 'flush' response before being replaced.")))
            }
            )).stop;
            Un("record", {
                record: r,
                segment: this.metadata
            }),
            this.worker.postMessage({
                data: '{"records":['.concat(JSON.stringify(r)),
                id: this.id,
                action: "write"
            })
        }
        return t.prototype.addRecord = function(t) {
            var e;
            this.metadata.start = Math.min(this.metadata.start, t.timestamp),
            this.metadata.end = Math.max(this.metadata.end, t.timestamp),
            this.metadata.records_count += 1,
            su(this.metadata.view.id),
            (e = this.metadata).has_full_snapshot || (e.has_full_snapshot = t.type === Ga.FullSnapshot),
            Un("record", {
                record: t,
                segment: this.metadata
            }),
            this.worker.postMessage({
                data: ",".concat(JSON.stringify(t)),
                id: this.id,
                action: "write"
            })
        }
        ,
        t.prototype.flush = function(t) {
            this.worker.postMessage({
                data: "],".concat(JSON.stringify(this.metadata).slice(1), "\n"),
                id: this.id,
                action: "flush"
            }),
            this.flushReason = t
        }
        ,
        t
    }(), pu = 30 * ce, hu = 6e4;
    function vu(t, e, n, r, i, o) {
        return function(t, e, n, r) {
            var i = {
                status: 0,
                nextSegmentCreationReason: "init"
            }
              , o = t.subscribe(2, (function() {
                s("view_change")
            }
            )).unsubscribe
              , a = t.subscribe(9, (function(t) {
                s(t.reason)
            }
            )).unsubscribe;
            function s(t) {
                1 === i.status && (i.segment.flush(t),
                Ut(i.expirationTimeoutId)),
                i = "stop" !== t ? {
                    status: 0,
                    nextSegmentCreationReason: t
                } : {
                    status: 2
                }
            }
            function u(t, o) {
                var a = e();
                if (a) {
                    var u = new fu(r,a,t,o,(function(t) {
                        !u.flushReason && t > hu && s("segment_bytes_limit")
                    }
                    ),(function(t, e) {
                        var r = function(t, e, n) {
                            var r = new FormData;
                            r.append("segment", new Blob([t],{
                                type: "application/octet-stream"
                            }), "".concat(e.session.id, "-").concat(e.start));
                            var i = Ct({
                                raw_segment_size: n,
                                compressed_segment_size: t.byteLength
                            }, e)
                              , o = JSON.stringify(i);
                            return r.append("event", new Blob([o],{
                                type: "application/json"
                            })),
                            {
                                data: r,
                                bytesCount: t.byteLength
                            }
                        }(t, u.metadata, e);
                        jn(u.flushReason) ? n.sendOnExit(r) : n.send(r)
                    }
                    ));
                    i = {
                        status: 1,
                        segment: u,
                        expirationTimeoutId: Mt((function() {
                            s("segment_duration_limit")
                        }
                        ), pu)
                    }
                }
            }
            return {
                addRecord: function(t) {
                    switch (i.status) {
                    case 0:
                        u(i.nextSegmentCreationReason, t);
                        break;
                    case 1:
                        i.segment.addRecord(t)
                    }
                },
                stop: function() {
                    s("stop"),
                    o(),
                    a()
                }
            }
        }(t, (function() {
            return function(t, e, n) {
                var r = e.findTrackedSession()
                  , i = n.findView();
                if (!r || !i)
                    return;
                return {
                    application: {
                        id: t
                    },
                    session: {
                        id: r.id
                    },
                    view: {
                        id: i.id
                    }
                }
            }(e, n, r)
        }
        ), i, o)
    }
    function mu() {
        return lu || (lu = URL.createObjectURL(new Blob(["(".concat(gu, ")(self)")]))),
        new Worker(lu)
    }
    function gu() {
        function t(t) {
            return function() {
                try {
                    return t.apply(this, arguments)
                } catch (e) {
                    try {
                        self.postMessage({
                            type: "errored",
                            error: e
                        })
                    } catch (n) {
                        self.postMessage({
                            type: "errored",
                            error: "".concat(e)
                        })
                    }
                }
            }
        }
        t((function() {
            var e = function() {
                var t = 4
                  , e = 0
                  , n = 1
                  , r = 2;
                function i(t) {
                    for (var e = t.length; --e >= 0; )
                        t[e] = 0
                }
                var o = 0
                  , a = 1
                  , s = 2
                  , u = 3
                  , c = 258
                  , l = 29
                  , d = 256
                  , f = d + 1 + l
                  , p = 30
                  , h = 19
                  , v = 2 * f + 1
                  , m = 15
                  , g = 16
                  , _ = 7
                  , y = 256
                  , b = 16
                  , w = 17
                  , E = 18
                  , S = new Uint8Array([0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 0])
                  , T = new Uint8Array([0, 0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13])
                  , C = new Uint8Array([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 3, 7])
                  , A = new Uint8Array([16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15])
                  , k = 512
                  , R = new Array(2 * (f + 2));
                i(R);
                var x = new Array(2 * p);
                i(x);
                var I = new Array(k);
                i(I);
                var O = new Array(c - u + 1);
                i(O);
                var N = new Array(l);
                i(N);
                var P, L, M, U = new Array(p);
                function D(t, e, n, r, i) {
                    this.static_tree = t,
                    this.extra_bits = e,
                    this.extra_base = n,
                    this.elems = r,
                    this.max_length = i,
                    this.has_stree = t && t.length
                }
                function F(t, e) {
                    this.dyn_tree = t,
                    this.max_code = 0,
                    this.stat_desc = e
                }
                i(U);
                var B = function(t) {
                    return t < 256 ? I[t] : I[256 + (t >>> 7)]
                }
                  , z = function(t, e) {
                    t.pending_buf[t.pending++] = 255 & e,
                    t.pending_buf[t.pending++] = e >>> 8 & 255
                }
                  , H = function(t, e, n) {
                    t.bi_valid > g - n ? (t.bi_buf |= e << t.bi_valid & 65535,
                    z(t, t.bi_buf),
                    t.bi_buf = e >> g - t.bi_valid,
                    t.bi_valid += n - g) : (t.bi_buf |= e << t.bi_valid & 65535,
                    t.bi_valid += n)
                }
                  , j = function(t, e, n) {
                    H(t, n[2 * e], n[2 * e + 1])
                }
                  , q = function(t, e) {
                    var n = 0;
                    do {
                        n |= 1 & t,
                        t >>>= 1,
                        n <<= 1
                    } while (--e > 0);
                    return n >>> 1
                }
                  , V = function(t) {
                    16 === t.bi_valid ? (z(t, t.bi_buf),
                    t.bi_buf = 0,
                    t.bi_valid = 0) : t.bi_valid >= 8 && (t.pending_buf[t.pending++] = 255 & t.bi_buf,
                    t.bi_buf >>= 8,
                    t.bi_valid -= 8)
                }
                  , Z = function(t, e) {
                    var n, r, i, o, a, s, u = e.dyn_tree, c = e.max_code, l = e.stat_desc.static_tree, d = e.stat_desc.has_stree, f = e.stat_desc.extra_bits, p = e.stat_desc.extra_base, h = e.stat_desc.max_length, g = 0;
                    for (o = 0; o <= m; o++)
                        t.bl_count[o] = 0;
                    for (u[2 * t.heap[t.heap_max] + 1] = 0,
                    n = t.heap_max + 1; n < v; n++)
                        (o = u[2 * u[2 * (r = t.heap[n]) + 1] + 1] + 1) > h && (o = h,
                        g++),
                        u[2 * r + 1] = o,
                        r > c || (t.bl_count[o]++,
                        a = 0,
                        r >= p && (a = f[r - p]),
                        s = u[2 * r],
                        t.opt_len += s * (o + a),
                        d && (t.static_len += s * (l[2 * r + 1] + a)));
                    if (0 !== g) {
                        do {
                            for (o = h - 1; 0 === t.bl_count[o]; )
                                o--;
                            t.bl_count[o]--,
                            t.bl_count[o + 1] += 2,
                            t.bl_count[h]--,
                            g -= 2
                        } while (g > 0);
                        for (o = h; 0 !== o; o--)
                            for (r = t.bl_count[o]; 0 !== r; )
                                (i = t.heap[--n]) > c || (u[2 * i + 1] !== o && (t.opt_len += (o - u[2 * i + 1]) * u[2 * i],
                                u[2 * i + 1] = o),
                                r--)
                    }
                }
                  , G = function(t, e, n) {
                    var r, i, o = new Array(m + 1), a = 0;
                    for (r = 1; r <= m; r++)
                        o[r] = a = a + n[r - 1] << 1;
                    for (i = 0; i <= e; i++) {
                        var s = t[2 * i + 1];
                        0 !== s && (t[2 * i] = q(o[s]++, s))
                    }
                }
                  , W = function() {
                    var t, e, n, r, i, o = new Array(m + 1);
                    for (n = 0,
                    r = 0; r < l - 1; r++)
                        for (N[r] = n,
                        t = 0; t < 1 << S[r]; t++)
                            O[n++] = r;
                    for (O[n - 1] = r,
                    i = 0,
                    r = 0; r < 16; r++)
                        for (U[r] = i,
                        t = 0; t < 1 << T[r]; t++)
                            I[i++] = r;
                    for (i >>= 7; r < p; r++)
                        for (U[r] = i << 7,
                        t = 0; t < 1 << T[r] - 7; t++)
                            I[256 + i++] = r;
                    for (e = 0; e <= m; e++)
                        o[e] = 0;
                    for (t = 0; t <= 143; )
                        R[2 * t + 1] = 8,
                        t++,
                        o[8]++;
                    for (; t <= 255; )
                        R[2 * t + 1] = 9,
                        t++,
                        o[9]++;
                    for (; t <= 279; )
                        R[2 * t + 1] = 7,
                        t++,
                        o[7]++;
                    for (; t <= 287; )
                        R[2 * t + 1] = 8,
                        t++,
                        o[8]++;
                    for (G(R, f + 1, o),
                    t = 0; t < p; t++)
                        x[2 * t + 1] = 5,
                        x[2 * t] = q(t, 5);
                    P = new D(R,S,d + 1,f,m),
                    L = new D(x,T,0,p,m),
                    M = new D(new Array(0),C,0,h,_)
                }
                  , Y = function(t) {
                    var e;
                    for (e = 0; e < f; e++)
                        t.dyn_ltree[2 * e] = 0;
                    for (e = 0; e < p; e++)
                        t.dyn_dtree[2 * e] = 0;
                    for (e = 0; e < h; e++)
                        t.bl_tree[2 * e] = 0;
                    t.dyn_ltree[2 * y] = 1,
                    t.opt_len = t.static_len = 0,
                    t.last_lit = t.matches = 0
                }
                  , K = function(t) {
                    t.bi_valid > 8 ? z(t, t.bi_buf) : t.bi_valid > 0 && (t.pending_buf[t.pending++] = t.bi_buf),
                    t.bi_buf = 0,
                    t.bi_valid = 0
                }
                  , $ = function(t, e, n, r) {
                    K(t),
                    r && (z(t, n),
                    z(t, ~n)),
                    t.pending_buf.set(t.window.subarray(e, e + n), t.pending),
                    t.pending += n
                }
                  , X = function(t, e, n, r) {
                    var i = 2 * e
                      , o = 2 * n;
                    return t[i] < t[o] || t[i] === t[o] && r[e] <= r[n]
                }
                  , J = function(t, e, n) {
                    for (var r = t.heap[n], i = n << 1; i <= t.heap_len && (i < t.heap_len && X(e, t.heap[i + 1], t.heap[i], t.depth) && i++,
                    !X(e, r, t.heap[i], t.depth)); )
                        t.heap[n] = t.heap[i],
                        n = i,
                        i <<= 1;
                    t.heap[n] = r
                }
                  , Q = function(t, e, n) {
                    var r, i, o, a, s = 0;
                    if (0 !== t.last_lit)
                        do {
                            r = t.pending_buf[t.d_buf + 2 * s] << 8 | t.pending_buf[t.d_buf + 2 * s + 1],
                            i = t.pending_buf[t.l_buf + s],
                            s++,
                            0 === r ? j(t, i, e) : (o = O[i],
                            j(t, o + d + 1, e),
                            0 !== (a = S[o]) && (i -= N[o],
                            H(t, i, a)),
                            r--,
                            o = B(r),
                            j(t, o, n),
                            0 !== (a = T[o]) && (r -= U[o],
                            H(t, r, a)))
                        } while (s < t.last_lit);
                    j(t, y, e)
                }
                  , tt = function(t, e) {
                    var n, r, i, o = e.dyn_tree, a = e.stat_desc.static_tree, s = e.stat_desc.has_stree, u = e.stat_desc.elems, c = -1;
                    for (t.heap_len = 0,
                    t.heap_max = v,
                    n = 0; n < u; n++)
                        0 !== o[2 * n] ? (t.heap[++t.heap_len] = c = n,
                        t.depth[n] = 0) : o[2 * n + 1] = 0;
                    for (; t.heap_len < 2; )
                        o[2 * (i = t.heap[++t.heap_len] = c < 2 ? ++c : 0)] = 1,
                        t.depth[i] = 0,
                        t.opt_len--,
                        s && (t.static_len -= a[2 * i + 1]);
                    for (e.max_code = c,
                    n = t.heap_len >> 1; n >= 1; n--)
                        J(t, o, n);
                    i = u;
                    do {
                        n = t.heap[1],
                        t.heap[1] = t.heap[t.heap_len--],
                        J(t, o, 1),
                        r = t.heap[1],
                        t.heap[--t.heap_max] = n,
                        t.heap[--t.heap_max] = r,
                        o[2 * i] = o[2 * n] + o[2 * r],
                        t.depth[i] = (t.depth[n] >= t.depth[r] ? t.depth[n] : t.depth[r]) + 1,
                        o[2 * n + 1] = o[2 * r + 1] = i,
                        t.heap[1] = i++,
                        J(t, o, 1)
                    } while (t.heap_len >= 2);
                    t.heap[--t.heap_max] = t.heap[1],
                    Z(t, e),
                    G(o, c, t.bl_count)
                }
                  , et = function(t, e, n) {
                    var r, i, o = -1, a = e[1], s = 0, u = 7, c = 4;
                    for (0 === a && (u = 138,
                    c = 3),
                    e[2 * (n + 1) + 1] = 65535,
                    r = 0; r <= n; r++)
                        i = a,
                        a = e[2 * (r + 1) + 1],
                        ++s < u && i === a || (s < c ? t.bl_tree[2 * i] += s : 0 !== i ? (i !== o && t.bl_tree[2 * i]++,
                        t.bl_tree[2 * b]++) : s <= 10 ? t.bl_tree[2 * w]++ : t.bl_tree[2 * E]++,
                        s = 0,
                        o = i,
                        0 === a ? (u = 138,
                        c = 3) : i === a ? (u = 6,
                        c = 3) : (u = 7,
                        c = 4))
                }
                  , nt = function(t, e, n) {
                    var r, i, o = -1, a = e[1], s = 0, u = 7, c = 4;
                    for (0 === a && (u = 138,
                    c = 3),
                    r = 0; r <= n; r++)
                        if (i = a,
                        a = e[2 * (r + 1) + 1],
                        !(++s < u && i === a)) {
                            if (s < c)
                                do {
                                    j(t, i, t.bl_tree)
                                } while (0 != --s);
                            else
                                0 !== i ? (i !== o && (j(t, i, t.bl_tree),
                                s--),
                                j(t, b, t.bl_tree),
                                H(t, s - 3, 2)) : s <= 10 ? (j(t, w, t.bl_tree),
                                H(t, s - 3, 3)) : (j(t, E, t.bl_tree),
                                H(t, s - 11, 7));
                            s = 0,
                            o = i,
                            0 === a ? (u = 138,
                            c = 3) : i === a ? (u = 6,
                            c = 3) : (u = 7,
                            c = 4)
                        }
                }
                  , rt = function(t) {
                    var e;
                    for (et(t, t.dyn_ltree, t.l_desc.max_code),
                    et(t, t.dyn_dtree, t.d_desc.max_code),
                    tt(t, t.bl_desc),
                    e = h - 1; e >= 3 && 0 === t.bl_tree[2 * A[e] + 1]; e--)
                        ;
                    return t.opt_len += 3 * (e + 1) + 5 + 5 + 4,
                    e
                }
                  , it = function(t, e, n, r) {
                    var i;
                    for (H(t, e - 257, 5),
                    H(t, n - 1, 5),
                    H(t, r - 4, 4),
                    i = 0; i < r; i++)
                        H(t, t.bl_tree[2 * A[i] + 1], 3);
                    nt(t, t.dyn_ltree, e - 1),
                    nt(t, t.dyn_dtree, n - 1)
                }
                  , ot = function(t) {
                    var r, i = 4093624447;
                    for (r = 0; r <= 31; r++,
                    i >>>= 1)
                        if (1 & i && 0 !== t.dyn_ltree[2 * r])
                            return e;
                    if (0 !== t.dyn_ltree[18] || 0 !== t.dyn_ltree[20] || 0 !== t.dyn_ltree[26])
                        return n;
                    for (r = 32; r < d; r++)
                        if (0 !== t.dyn_ltree[2 * r])
                            return n;
                    return e
                }
                  , at = !1
                  , st = function(t, e, n, r) {
                    H(t, (o << 1) + (r ? 1 : 0), 3),
                    $(t, e, n, !0)
                }
                  , ut = function(e, n, i, o) {
                    var u, c, l = 0;
                    e.level > 0 ? (e.strm.data_type === r && (e.strm.data_type = ot(e)),
                    tt(e, e.l_desc),
                    tt(e, e.d_desc),
                    l = rt(e),
                    u = e.opt_len + 3 + 7 >>> 3,
                    (c = e.static_len + 3 + 7 >>> 3) <= u && (u = c)) : u = c = i + 5,
                    i + 4 <= u && -1 !== n ? st(e, n, i, o) : e.strategy === t || c === u ? (H(e, (a << 1) + (o ? 1 : 0), 3),
                    Q(e, R, x)) : (H(e, (s << 1) + (o ? 1 : 0), 3),
                    it(e, e.l_desc.max_code + 1, e.d_desc.max_code + 1, l + 1),
                    Q(e, e.dyn_ltree, e.dyn_dtree)),
                    Y(e),
                    o && K(e)
                }
                  , ct = {
                    _tr_init: function(t) {
                        at || (W(),
                        at = !0),
                        t.l_desc = new F(t.dyn_ltree,P),
                        t.d_desc = new F(t.dyn_dtree,L),
                        t.bl_desc = new F(t.bl_tree,M),
                        t.bi_buf = 0,
                        t.bi_valid = 0,
                        Y(t)
                    },
                    _tr_stored_block: st,
                    _tr_flush_block: ut,
                    _tr_tally: function(t, e, n) {
                        return t.pending_buf[t.d_buf + 2 * t.last_lit] = e >>> 8 & 255,
                        t.pending_buf[t.d_buf + 2 * t.last_lit + 1] = 255 & e,
                        t.pending_buf[t.l_buf + t.last_lit] = 255 & n,
                        t.last_lit++,
                        0 === e ? t.dyn_ltree[2 * n]++ : (t.matches++,
                        e--,
                        t.dyn_ltree[2 * (O[n] + d + 1)]++,
                        t.dyn_dtree[2 * B(e)]++),
                        t.last_lit === t.lit_bufsize - 1
                    },
                    _tr_align: function(t) {
                        H(t, a << 1, 3),
                        j(t, y, R),
                        V(t)
                    }
                }
                  , lt = function(t, e, n, r) {
                    for (var i = 65535 & t, o = t >>> 16 & 65535, a = 0; 0 !== n; ) {
                        n -= a = n > 2e3 ? 2e3 : n;
                        do {
                            o = o + (i = i + e[r++] | 0) | 0
                        } while (--a);
                        i %= 65521,
                        o %= 65521
                    }
                    return i | o << 16
                }
                  , dt = new Uint32Array(function() {
                    for (var t, e = [], n = 0; n < 256; n++) {
                        t = n;
                        for (var r = 0; r < 8; r++)
                            t = 1 & t ? 3988292384 ^ t >>> 1 : t >>> 1;
                        e[n] = t
                    }
                    return e
                }())
                  , ft = function(t, e, n, r) {
                    var i = dt
                      , o = r + n;
                    t ^= -1;
                    for (var a = r; a < o; a++)
                        t = t >>> 8 ^ i[255 & (t ^ e[a])];
                    return ~t
                }
                  , pt = {
                    2: "need dictionary",
                    1: "stream end",
                    0: "",
                    "-1": "file error",
                    "-2": "stream error",
                    "-3": "data error",
                    "-4": "insufficient memory",
                    "-5": "buffer error",
                    "-6": "incompatible version"
                }
                  , ht = {
                    Z_NO_FLUSH: 0,
                    Z_PARTIAL_FLUSH: 1,
                    Z_SYNC_FLUSH: 2,
                    Z_FULL_FLUSH: 3,
                    Z_FINISH: 4,
                    Z_BLOCK: 5,
                    Z_TREES: 6,
                    Z_OK: 0,
                    Z_STREAM_END: 1,
                    Z_NEED_DICT: 2,
                    Z_ERRNO: -1,
                    Z_STREAM_ERROR: -2,
                    Z_DATA_ERROR: -3,
                    Z_MEM_ERROR: -4,
                    Z_BUF_ERROR: -5,
                    Z_NO_COMPRESSION: 0,
                    Z_BEST_SPEED: 1,
                    Z_BEST_COMPRESSION: 9,
                    Z_DEFAULT_COMPRESSION: -1,
                    Z_FILTERED: 1,
                    Z_HUFFMAN_ONLY: 2,
                    Z_RLE: 3,
                    Z_FIXED: 4,
                    Z_DEFAULT_STRATEGY: 0,
                    Z_BINARY: 0,
                    Z_TEXT: 1,
                    Z_UNKNOWN: 2,
                    Z_DEFLATED: 8
                }
                  , vt = ct._tr_init
                  , mt = ct._tr_stored_block
                  , gt = ct._tr_flush_block
                  , _t = ct._tr_tally
                  , yt = ct._tr_align
                  , bt = ht.Z_NO_FLUSH
                  , wt = ht.Z_PARTIAL_FLUSH
                  , Et = ht.Z_FULL_FLUSH
                  , St = ht.Z_FINISH
                  , Tt = ht.Z_BLOCK
                  , Ct = ht.Z_OK
                  , At = ht.Z_STREAM_END
                  , kt = ht.Z_STREAM_ERROR
                  , Rt = ht.Z_DATA_ERROR
                  , xt = ht.Z_BUF_ERROR
                  , It = ht.Z_DEFAULT_COMPRESSION
                  , Ot = ht.Z_FILTERED
                  , Nt = ht.Z_HUFFMAN_ONLY
                  , Pt = ht.Z_RLE
                  , Lt = ht.Z_FIXED
                  , Mt = ht.Z_DEFAULT_STRATEGY
                  , Ut = ht.Z_UNKNOWN
                  , Dt = ht.Z_DEFLATED
                  , Ft = 9
                  , Bt = 15
                  , zt = 8
                  , Ht = 286
                  , jt = 30
                  , qt = 19
                  , Vt = 2 * Ht + 1
                  , Zt = 15
                  , Gt = 3
                  , Wt = 258
                  , Yt = Wt + Gt + 1
                  , Kt = 32
                  , $t = 42
                  , Xt = 69
                  , Jt = 73
                  , Qt = 91
                  , te = 103
                  , ee = 113
                  , ne = 666
                  , re = 1
                  , ie = 2
                  , oe = 3
                  , ae = 4
                  , se = 3
                  , ue = function(t, e) {
                    return t.msg = pt[e],
                    e
                }
                  , ce = function(t) {
                    return (t << 1) - (t > 4 ? 9 : 0)
                }
                  , le = function(t) {
                    for (var e = t.length; --e >= 0; )
                        t[e] = 0
                }
                  , de = function(t, e, n) {
                    return (e << t.hash_shift ^ n) & t.hash_mask
                }
                  , fe = function(t) {
                    var e = t.state
                      , n = e.pending;
                    n > t.avail_out && (n = t.avail_out),
                    0 !== n && (t.output.set(e.pending_buf.subarray(e.pending_out, e.pending_out + n), t.next_out),
                    t.next_out += n,
                    e.pending_out += n,
                    t.total_out += n,
                    t.avail_out -= n,
                    e.pending -= n,
                    0 === e.pending && (e.pending_out = 0))
                }
                  , pe = function(t, e) {
                    gt(t, t.block_start >= 0 ? t.block_start : -1, t.strstart - t.block_start, e),
                    t.block_start = t.strstart,
                    fe(t.strm)
                }
                  , he = function(t, e) {
                    t.pending_buf[t.pending++] = e
                }
                  , ve = function(t, e) {
                    t.pending_buf[t.pending++] = e >>> 8 & 255,
                    t.pending_buf[t.pending++] = 255 & e
                }
                  , me = function(t, e, n, r) {
                    var i = t.avail_in;
                    return i > r && (i = r),
                    0 === i ? 0 : (t.avail_in -= i,
                    e.set(t.input.subarray(t.next_in, t.next_in + i), n),
                    1 === t.state.wrap ? t.adler = lt(t.adler, e, i, n) : 2 === t.state.wrap && (t.adler = ft(t.adler, e, i, n)),
                    t.next_in += i,
                    t.total_in += i,
                    i)
                }
                  , ge = function(t, e) {
                    var n, r, i = t.max_chain_length, o = t.strstart, a = t.prev_length, s = t.nice_match, u = t.strstart > t.w_size - Yt ? t.strstart - (t.w_size - Yt) : 0, c = t.window, l = t.w_mask, d = t.prev, f = t.strstart + Wt, p = c[o + a - 1], h = c[o + a];
                    t.prev_length >= t.good_match && (i >>= 2),
                    s > t.lookahead && (s = t.lookahead);
                    do {
                        if (c[(n = e) + a] === h && c[n + a - 1] === p && c[n] === c[o] && c[++n] === c[o + 1]) {
                            o += 2,
                            n++;
                            do {} while (c[++o] === c[++n] && c[++o] === c[++n] && c[++o] === c[++n] && c[++o] === c[++n] && c[++o] === c[++n] && c[++o] === c[++n] && c[++o] === c[++n] && c[++o] === c[++n] && o < f);
                            if (r = Wt - (f - o),
                            o = f - Wt,
                            r > a) {
                                if (t.match_start = e,
                                a = r,
                                r >= s)
                                    break;
                                p = c[o + a - 1],
                                h = c[o + a]
                            }
                        }
                    } while ((e = d[e & l]) > u && 0 != --i);
                    return a <= t.lookahead ? a : t.lookahead
                }
                  , _e = function(t) {
                    var e, n, r, i, o, a = t.w_size;
                    do {
                        if (i = t.window_size - t.lookahead - t.strstart,
                        t.strstart >= a + (a - Yt)) {
                            t.window.set(t.window.subarray(a, a + a), 0),
                            t.match_start -= a,
                            t.strstart -= a,
                            t.block_start -= a,
                            e = n = t.hash_size;
                            do {
                                r = t.head[--e],
                                t.head[e] = r >= a ? r - a : 0
                            } while (--n);
                            e = n = a;
                            do {
                                r = t.prev[--e],
                                t.prev[e] = r >= a ? r - a : 0
                            } while (--n);
                            i += a
                        }
                        if (0 === t.strm.avail_in)
                            break;
                        if (n = me(t.strm, t.window, t.strstart + t.lookahead, i),
                        t.lookahead += n,
                        t.lookahead + t.insert >= Gt)
                            for (o = t.strstart - t.insert,
                            t.ins_h = t.window[o],
                            t.ins_h = de(t, t.ins_h, t.window[o + 1]); t.insert && (t.ins_h = de(t, t.ins_h, t.window[o + Gt - 1]),
                            t.prev[o & t.w_mask] = t.head[t.ins_h],
                            t.head[t.ins_h] = o,
                            o++,
                            t.insert--,
                            !(t.lookahead + t.insert < Gt)); )
                                ;
                    } while (t.lookahead < Yt && 0 !== t.strm.avail_in)
                }
                  , ye = function(t, e) {
                    for (var n, r; ; ) {
                        if (t.lookahead < Yt) {
                            if (_e(t),
                            t.lookahead < Yt && e === bt)
                                return re;
                            if (0 === t.lookahead)
                                break
                        }
                        if (n = 0,
                        t.lookahead >= Gt && (t.ins_h = de(t, t.ins_h, t.window[t.strstart + Gt - 1]),
                        n = t.prev[t.strstart & t.w_mask] = t.head[t.ins_h],
                        t.head[t.ins_h] = t.strstart),
                        0 !== n && t.strstart - n <= t.w_size - Yt && (t.match_length = ge(t, n)),
                        t.match_length >= Gt)
                            if (r = _t(t, t.strstart - t.match_start, t.match_length - Gt),
                            t.lookahead -= t.match_length,
                            t.match_length <= t.max_lazy_match && t.lookahead >= Gt) {
                                t.match_length--;
                                do {
                                    t.strstart++,
                                    t.ins_h = de(t, t.ins_h, t.window[t.strstart + Gt - 1]),
                                    n = t.prev[t.strstart & t.w_mask] = t.head[t.ins_h],
                                    t.head[t.ins_h] = t.strstart
                                } while (0 != --t.match_length);
                                t.strstart++
                            } else
                                t.strstart += t.match_length,
                                t.match_length = 0,
                                t.ins_h = t.window[t.strstart],
                                t.ins_h = de(t, t.ins_h, t.window[t.strstart + 1]);
                        else
                            r = _t(t, 0, t.window[t.strstart]),
                            t.lookahead--,
                            t.strstart++;
                        if (r && (pe(t, !1),
                        0 === t.strm.avail_out))
                            return re
                    }
                    return t.insert = t.strstart < Gt - 1 ? t.strstart : Gt - 1,
                    e === St ? (pe(t, !0),
                    0 === t.strm.avail_out ? oe : ae) : t.last_lit && (pe(t, !1),
                    0 === t.strm.avail_out) ? re : ie
                }
                  , be = function(t, e) {
                    for (var n, r, i; ; ) {
                        if (t.lookahead < Yt) {
                            if (_e(t),
                            t.lookahead < Yt && e === bt)
                                return re;
                            if (0 === t.lookahead)
                                break
                        }
                        if (n = 0,
                        t.lookahead >= Gt && (t.ins_h = de(t, t.ins_h, t.window[t.strstart + Gt - 1]),
                        n = t.prev[t.strstart & t.w_mask] = t.head[t.ins_h],
                        t.head[t.ins_h] = t.strstart),
                        t.prev_length = t.match_length,
                        t.prev_match = t.match_start,
                        t.match_length = Gt - 1,
                        0 !== n && t.prev_length < t.max_lazy_match && t.strstart - n <= t.w_size - Yt && (t.match_length = ge(t, n),
                        t.match_length <= 5 && (t.strategy === Ot || t.match_length === Gt && t.strstart - t.match_start > 4096) && (t.match_length = Gt - 1)),
                        t.prev_length >= Gt && t.match_length <= t.prev_length) {
                            i = t.strstart + t.lookahead - Gt,
                            r = _t(t, t.strstart - 1 - t.prev_match, t.prev_length - Gt),
                            t.lookahead -= t.prev_length - 1,
                            t.prev_length -= 2;
                            do {
                                ++t.strstart <= i && (t.ins_h = de(t, t.ins_h, t.window[t.strstart + Gt - 1]),
                                n = t.prev[t.strstart & t.w_mask] = t.head[t.ins_h],
                                t.head[t.ins_h] = t.strstart)
                            } while (0 != --t.prev_length);
                            if (t.match_available = 0,
                            t.match_length = Gt - 1,
                            t.strstart++,
                            r && (pe(t, !1),
                            0 === t.strm.avail_out))
                                return re
                        } else if (t.match_available) {
                            if ((r = _t(t, 0, t.window[t.strstart - 1])) && pe(t, !1),
                            t.strstart++,
                            t.lookahead--,
                            0 === t.strm.avail_out)
                                return re
                        } else
                            t.match_available = 1,
                            t.strstart++,
                            t.lookahead--
                    }
                    return t.match_available && (r = _t(t, 0, t.window[t.strstart - 1]),
                    t.match_available = 0),
                    t.insert = t.strstart < Gt - 1 ? t.strstart : Gt - 1,
                    e === St ? (pe(t, !0),
                    0 === t.strm.avail_out ? oe : ae) : t.last_lit && (pe(t, !1),
                    0 === t.strm.avail_out) ? re : ie
                }
                  , we = function(t, e) {
                    for (var n, r, i, o, a = t.window; ; ) {
                        if (t.lookahead <= Wt) {
                            if (_e(t),
                            t.lookahead <= Wt && e === bt)
                                return re;
                            if (0 === t.lookahead)
                                break
                        }
                        if (t.match_length = 0,
                        t.lookahead >= Gt && t.strstart > 0 && (r = a[i = t.strstart - 1]) === a[++i] && r === a[++i] && r === a[++i]) {
                            o = t.strstart + Wt;
                            do {} while (r === a[++i] && r === a[++i] && r === a[++i] && r === a[++i] && r === a[++i] && r === a[++i] && r === a[++i] && r === a[++i] && i < o);
                            t.match_length = Wt - (o - i),
                            t.match_length > t.lookahead && (t.match_length = t.lookahead)
                        }
                        if (t.match_length >= Gt ? (n = _t(t, 1, t.match_length - Gt),
                        t.lookahead -= t.match_length,
                        t.strstart += t.match_length,
                        t.match_length = 0) : (n = _t(t, 0, t.window[t.strstart]),
                        t.lookahead--,
                        t.strstart++),
                        n && (pe(t, !1),
                        0 === t.strm.avail_out))
                            return re
                    }
                    return t.insert = 0,
                    e === St ? (pe(t, !0),
                    0 === t.strm.avail_out ? oe : ae) : t.last_lit && (pe(t, !1),
                    0 === t.strm.avail_out) ? re : ie
                }
                  , Ee = function(t, e) {
                    for (var n; ; ) {
                        if (0 === t.lookahead && (_e(t),
                        0 === t.lookahead)) {
                            if (e === bt)
                                return re;
                            break
                        }
                        if (t.match_length = 0,
                        n = _t(t, 0, t.window[t.strstart]),
                        t.lookahead--,
                        t.strstart++,
                        n && (pe(t, !1),
                        0 === t.strm.avail_out))
                            return re
                    }
                    return t.insert = 0,
                    e === St ? (pe(t, !0),
                    0 === t.strm.avail_out ? oe : ae) : t.last_lit && (pe(t, !1),
                    0 === t.strm.avail_out) ? re : ie
                };
                function Se(t, e, n, r, i) {
                    this.good_length = t,
                    this.max_lazy = e,
                    this.nice_length = n,
                    this.max_chain = r,
                    this.func = i
                }
                var Te = [new Se(0,0,0,0,(function(t, e) {
                    var n = 65535;
                    for (n > t.pending_buf_size - 5 && (n = t.pending_buf_size - 5); ; ) {
                        if (t.lookahead <= 1) {
                            if (_e(t),
                            0 === t.lookahead && e === bt)
                                return re;
                            if (0 === t.lookahead)
                                break
                        }
                        t.strstart += t.lookahead,
                        t.lookahead = 0;
                        var r = t.block_start + n;
                        if ((0 === t.strstart || t.strstart >= r) && (t.lookahead = t.strstart - r,
                        t.strstart = r,
                        pe(t, !1),
                        0 === t.strm.avail_out))
                            return re;
                        if (t.strstart - t.block_start >= t.w_size - Yt && (pe(t, !1),
                        0 === t.strm.avail_out))
                            return re
                    }
                    return t.insert = 0,
                    e === St ? (pe(t, !0),
                    0 === t.strm.avail_out ? oe : ae) : (t.strstart > t.block_start && (pe(t, !1),
                    t.strm.avail_out),
                    re)
                }
                )), new Se(4,4,8,4,ye), new Se(4,5,16,8,ye), new Se(4,6,32,32,ye), new Se(4,4,16,16,be), new Se(8,16,32,32,be), new Se(8,16,128,128,be), new Se(8,32,128,256,be), new Se(32,128,258,1024,be), new Se(32,258,258,4096,be)]
                  , Ce = function(t) {
                    t.window_size = 2 * t.w_size,
                    le(t.head),
                    t.max_lazy_match = Te[t.level].max_lazy,
                    t.good_match = Te[t.level].good_length,
                    t.nice_match = Te[t.level].nice_length,
                    t.max_chain_length = Te[t.level].max_chain,
                    t.strstart = 0,
                    t.block_start = 0,
                    t.lookahead = 0,
                    t.insert = 0,
                    t.match_length = t.prev_length = Gt - 1,
                    t.match_available = 0,
                    t.ins_h = 0
                };
                function Ae() {
                    this.strm = null,
                    this.status = 0,
                    this.pending_buf = null,
                    this.pending_buf_size = 0,
                    this.pending_out = 0,
                    this.pending = 0,
                    this.wrap = 0,
                    this.gzhead = null,
                    this.gzindex = 0,
                    this.method = Dt,
                    this.last_flush = -1,
                    this.w_size = 0,
                    this.w_bits = 0,
                    this.w_mask = 0,
                    this.window = null,
                    this.window_size = 0,
                    this.prev = null,
                    this.head = null,
                    this.ins_h = 0,
                    this.hash_size = 0,
                    this.hash_bits = 0,
                    this.hash_mask = 0,
                    this.hash_shift = 0,
                    this.block_start = 0,
                    this.match_length = 0,
                    this.prev_match = 0,
                    this.match_available = 0,
                    this.strstart = 0,
                    this.match_start = 0,
                    this.lookahead = 0,
                    this.prev_length = 0,
                    this.max_chain_length = 0,
                    this.max_lazy_match = 0,
                    this.level = 0,
                    this.strategy = 0,
                    this.good_match = 0,
                    this.nice_match = 0,
                    this.dyn_ltree = new Uint16Array(2 * Vt),
                    this.dyn_dtree = new Uint16Array(2 * (2 * jt + 1)),
                    this.bl_tree = new Uint16Array(2 * (2 * qt + 1)),
                    le(this.dyn_ltree),
                    le(this.dyn_dtree),
                    le(this.bl_tree),
                    this.l_desc = null,
                    this.d_desc = null,
                    this.bl_desc = null,
                    this.bl_count = new Uint16Array(Zt + 1),
                    this.heap = new Uint16Array(2 * Ht + 1),
                    le(this.heap),
                    this.heap_len = 0,
                    this.heap_max = 0,
                    this.depth = new Uint16Array(2 * Ht + 1),
                    le(this.depth),
                    this.l_buf = 0,
                    this.lit_bufsize = 0,
                    this.last_lit = 0,
                    this.d_buf = 0,
                    this.opt_len = 0,
                    this.static_len = 0,
                    this.matches = 0,
                    this.insert = 0,
                    this.bi_buf = 0,
                    this.bi_valid = 0
                }
                var ke = function(t) {
                    if (!t || !t.state)
                        return ue(t, kt);
                    t.total_in = t.total_out = 0,
                    t.data_type = Ut;
                    var e = t.state;
                    return e.pending = 0,
                    e.pending_out = 0,
                    e.wrap < 0 && (e.wrap = -e.wrap),
                    e.status = e.wrap ? $t : ee,
                    t.adler = 2 === e.wrap ? 0 : 1,
                    e.last_flush = bt,
                    vt(e),
                    Ct
                }
                  , Re = function(t) {
                    var e = ke(t);
                    return e === Ct && Ce(t.state),
                    e
                }
                  , xe = function(t, e, n, r, i, o) {
                    if (!t)
                        return kt;
                    var a = 1;
                    if (e === It && (e = 6),
                    r < 0 ? (a = 0,
                    r = -r) : r > 15 && (a = 2,
                    r -= 16),
                    i < 1 || i > Ft || n !== Dt || r < 8 || r > 15 || e < 0 || e > 9 || o < 0 || o > Lt)
                        return ue(t, kt);
                    8 === r && (r = 9);
                    var s = new Ae;
                    return t.state = s,
                    s.strm = t,
                    s.wrap = a,
                    s.gzhead = null,
                    s.w_bits = r,
                    s.w_size = 1 << s.w_bits,
                    s.w_mask = s.w_size - 1,
                    s.hash_bits = i + 7,
                    s.hash_size = 1 << s.hash_bits,
                    s.hash_mask = s.hash_size - 1,
                    s.hash_shift = ~~((s.hash_bits + Gt - 1) / Gt),
                    s.window = new Uint8Array(2 * s.w_size),
                    s.head = new Uint16Array(s.hash_size),
                    s.prev = new Uint16Array(s.w_size),
                    s.lit_bufsize = 1 << i + 6,
                    s.pending_buf_size = 4 * s.lit_bufsize,
                    s.pending_buf = new Uint8Array(s.pending_buf_size),
                    s.d_buf = 1 * s.lit_bufsize,
                    s.l_buf = 3 * s.lit_bufsize,
                    s.level = e,
                    s.strategy = o,
                    s.method = n,
                    Re(t)
                }
                  , Ie = function(t, e) {
                    var n, r;
                    if (!t || !t.state || e > Tt || e < 0)
                        return t ? ue(t, kt) : kt;
                    var i = t.state;
                    if (!t.output || !t.input && 0 !== t.avail_in || i.status === ne && e !== St)
                        return ue(t, 0 === t.avail_out ? xt : kt);
                    i.strm = t;
                    var o = i.last_flush;
                    if (i.last_flush = e,
                    i.status === $t)
                        if (2 === i.wrap)
                            t.adler = 0,
                            he(i, 31),
                            he(i, 139),
                            he(i, 8),
                            i.gzhead ? (he(i, (i.gzhead.text ? 1 : 0) + (i.gzhead.hcrc ? 2 : 0) + (i.gzhead.extra ? 4 : 0) + (i.gzhead.name ? 8 : 0) + (i.gzhead.comment ? 16 : 0)),
                            he(i, 255 & i.gzhead.time),
                            he(i, i.gzhead.time >> 8 & 255),
                            he(i, i.gzhead.time >> 16 & 255),
                            he(i, i.gzhead.time >> 24 & 255),
                            he(i, 9 === i.level ? 2 : i.strategy >= Nt || i.level < 2 ? 4 : 0),
                            he(i, 255 & i.gzhead.os),
                            i.gzhead.extra && i.gzhead.extra.length && (he(i, 255 & i.gzhead.extra.length),
                            he(i, i.gzhead.extra.length >> 8 & 255)),
                            i.gzhead.hcrc && (t.adler = ft(t.adler, i.pending_buf, i.pending, 0)),
                            i.gzindex = 0,
                            i.status = Xt) : (he(i, 0),
                            he(i, 0),
                            he(i, 0),
                            he(i, 0),
                            he(i, 0),
                            he(i, 9 === i.level ? 2 : i.strategy >= Nt || i.level < 2 ? 4 : 0),
                            he(i, se),
                            i.status = ee);
                        else {
                            var a = Dt + (i.w_bits - 8 << 4) << 8;
                            a |= (i.strategy >= Nt || i.level < 2 ? 0 : i.level < 6 ? 1 : 6 === i.level ? 2 : 3) << 6,
                            0 !== i.strstart && (a |= Kt),
                            a += 31 - a % 31,
                            i.status = ee,
                            ve(i, a),
                            0 !== i.strstart && (ve(i, t.adler >>> 16),
                            ve(i, 65535 & t.adler)),
                            t.adler = 1
                        }
                    if (i.status === Xt)
                        if (i.gzhead.extra) {
                            for (n = i.pending; i.gzindex < (65535 & i.gzhead.extra.length) && (i.pending !== i.pending_buf_size || (i.gzhead.hcrc && i.pending > n && (t.adler = ft(t.adler, i.pending_buf, i.pending - n, n)),
                            fe(t),
                            n = i.pending,
                            i.pending !== i.pending_buf_size)); )
                                he(i, 255 & i.gzhead.extra[i.gzindex]),
                                i.gzindex++;
                            i.gzhead.hcrc && i.pending > n && (t.adler = ft(t.adler, i.pending_buf, i.pending - n, n)),
                            i.gzindex === i.gzhead.extra.length && (i.gzindex = 0,
                            i.status = Jt)
                        } else
                            i.status = Jt;
                    if (i.status === Jt)
                        if (i.gzhead.name) {
                            n = i.pending;
                            do {
                                if (i.pending === i.pending_buf_size && (i.gzhead.hcrc && i.pending > n && (t.adler = ft(t.adler, i.pending_buf, i.pending - n, n)),
                                fe(t),
                                n = i.pending,
                                i.pending === i.pending_buf_size)) {
                                    r = 1;
                                    break
                                }
                                r = i.gzindex < i.gzhead.name.length ? 255 & i.gzhead.name.charCodeAt(i.gzindex++) : 0,
                                he(i, r)
                            } while (0 !== r);
                            i.gzhead.hcrc && i.pending > n && (t.adler = ft(t.adler, i.pending_buf, i.pending - n, n)),
                            0 === r && (i.gzindex = 0,
                            i.status = Qt)
                        } else
                            i.status = Qt;
                    if (i.status === Qt)
                        if (i.gzhead.comment) {
                            n = i.pending;
                            do {
                                if (i.pending === i.pending_buf_size && (i.gzhead.hcrc && i.pending > n && (t.adler = ft(t.adler, i.pending_buf, i.pending - n, n)),
                                fe(t),
                                n = i.pending,
                                i.pending === i.pending_buf_size)) {
                                    r = 1;
                                    break
                                }
                                r = i.gzindex < i.gzhead.comment.length ? 255 & i.gzhead.comment.charCodeAt(i.gzindex++) : 0,
                                he(i, r)
                            } while (0 !== r);
                            i.gzhead.hcrc && i.pending > n && (t.adler = ft(t.adler, i.pending_buf, i.pending - n, n)),
                            0 === r && (i.status = te)
                        } else
                            i.status = te;
                    if (i.status === te && (i.gzhead.hcrc ? (i.pending + 2 > i.pending_buf_size && fe(t),
                    i.pending + 2 <= i.pending_buf_size && (he(i, 255 & t.adler),
                    he(i, t.adler >> 8 & 255),
                    t.adler = 0,
                    i.status = ee)) : i.status = ee),
                    0 !== i.pending) {
                        if (fe(t),
                        0 === t.avail_out)
                            return i.last_flush = -1,
                            Ct
                    } else if (0 === t.avail_in && ce(e) <= ce(o) && e !== St)
                        return ue(t, xt);
                    if (i.status === ne && 0 !== t.avail_in)
                        return ue(t, xt);
                    if (0 !== t.avail_in || 0 !== i.lookahead || e !== bt && i.status !== ne) {
                        var s = i.strategy === Nt ? Ee(i, e) : i.strategy === Pt ? we(i, e) : Te[i.level].func(i, e);
                        if (s !== oe && s !== ae || (i.status = ne),
                        s === re || s === oe)
                            return 0 === t.avail_out && (i.last_flush = -1),
                            Ct;
                        if (s === ie && (e === wt ? yt(i) : e !== Tt && (mt(i, 0, 0, !1),
                        e === Et && (le(i.head),
                        0 === i.lookahead && (i.strstart = 0,
                        i.block_start = 0,
                        i.insert = 0))),
                        fe(t),
                        0 === t.avail_out))
                            return i.last_flush = -1,
                            Ct
                    }
                    return e !== St ? Ct : i.wrap <= 0 ? At : (2 === i.wrap ? (he(i, 255 & t.adler),
                    he(i, t.adler >> 8 & 255),
                    he(i, t.adler >> 16 & 255),
                    he(i, t.adler >> 24 & 255),
                    he(i, 255 & t.total_in),
                    he(i, t.total_in >> 8 & 255),
                    he(i, t.total_in >> 16 & 255),
                    he(i, t.total_in >> 24 & 255)) : (ve(i, t.adler >>> 16),
                    ve(i, 65535 & t.adler)),
                    fe(t),
                    i.wrap > 0 && (i.wrap = -i.wrap),
                    0 !== i.pending ? Ct : At)
                }
                  , Oe = function(t, e) {
                    var n = e.length;
                    if (!t || !t.state)
                        return kt;
                    var r = t.state
                      , i = r.wrap;
                    if (2 === i || 1 === i && r.status !== $t || r.lookahead)
                        return kt;
                    if (1 === i && (t.adler = lt(t.adler, e, n, 0)),
                    r.wrap = 0,
                    n >= r.w_size) {
                        0 === i && (le(r.head),
                        r.strstart = 0,
                        r.block_start = 0,
                        r.insert = 0);
                        var o = new Uint8Array(r.w_size);
                        o.set(e.subarray(n - r.w_size, n), 0),
                        e = o,
                        n = r.w_size
                    }
                    var a = t.avail_in
                      , s = t.next_in
                      , u = t.input;
                    for (t.avail_in = n,
                    t.next_in = 0,
                    t.input = e,
                    _e(r); r.lookahead >= Gt; ) {
                        var c = r.strstart
                          , l = r.lookahead - (Gt - 1);
                        do {
                            r.ins_h = de(r, r.ins_h, r.window[c + Gt - 1]),
                            r.prev[c & r.w_mask] = r.head[r.ins_h],
                            r.head[r.ins_h] = c,
                            c++
                        } while (--l);
                        r.strstart = c,
                        r.lookahead = Gt - 1,
                        _e(r)
                    }
                    return r.strstart += r.lookahead,
                    r.block_start = r.strstart,
                    r.insert = r.lookahead,
                    r.lookahead = 0,
                    r.match_length = r.prev_length = Gt - 1,
                    r.match_available = 0,
                    t.next_in = s,
                    t.input = u,
                    t.avail_in = a,
                    r.wrap = i,
                    Ct
                }
                  , Ne = Ie
                  , Pe = {
                    deflateInit: function(t, e) {
                        return xe(t, e, Dt, Bt, zt, Mt)
                    },
                    deflateInit2: xe,
                    deflateReset: Re,
                    deflateResetKeep: ke,
                    deflateSetHeader: function(t, e) {
                        return t && t.state ? 2 !== t.state.wrap ? kt : (t.state.gzhead = e,
                        Ct) : kt
                    },
                    deflate: Ne,
                    deflateEnd: function(t) {
                        if (!t || !t.state)
                            return kt;
                        var e = t.state.status;
                        return e !== $t && e !== Xt && e !== Jt && e !== Qt && e !== te && e !== ee && e !== ne ? ue(t, kt) : (t.state = null,
                        e === ee ? ue(t, Rt) : Ct)
                    },
                    deflateSetDictionary: Oe,
                    deflateInfo: "pako deflate (from Nodeca project)"
                };
                function Le(t) {
                    for (var e = 0, n = 0, r = t.length; n < r; n++)
                        e += t[n].length;
                    for (var i = new Uint8Array(e), o = 0, a = 0, s = t.length; o < s; o++) {
                        var u = t[o];
                        i.set(u, a),
                        a += u.length
                    }
                    return i
                }
                for (var Me = new Uint8Array(256), Ue = 0; Ue < 256; Ue++)
                    Me[Ue] = Ue >= 252 ? 6 : Ue >= 248 ? 5 : Ue >= 240 ? 4 : Ue >= 224 ? 3 : Ue >= 192 ? 2 : 1;
                function De() {
                    this.input = null,
                    this.next_in = 0,
                    this.avail_in = 0,
                    this.total_in = 0,
                    this.output = null,
                    this.next_out = 0,
                    this.avail_out = 0,
                    this.total_out = 0,
                    this.msg = "",
                    this.state = null,
                    this.data_type = 2,
                    this.adler = 0
                }
                Me[254] = Me[254] = 1;
                var Fe = De
                  , Be = Object.prototype.toString
                  , ze = ht.Z_NO_FLUSH
                  , He = ht.Z_SYNC_FLUSH
                  , je = ht.Z_FULL_FLUSH
                  , qe = ht.Z_FINISH
                  , Ve = ht.Z_OK
                  , Ze = ht.Z_STREAM_END
                  , Ge = ht.Z_DEFAULT_COMPRESSION
                  , We = ht.Z_DEFAULT_STRATEGY
                  , Ye = ht.Z_DEFLATED;
                function Ke() {
                    this.options = {
                        level: Ge,
                        method: Ye,
                        chunkSize: 16384,
                        windowBits: 15,
                        memLevel: 8,
                        strategy: We
                    };
                    var t = this.options;
                    t.raw && t.windowBits > 0 ? t.windowBits = -t.windowBits : t.gzip && t.windowBits > 0 && t.windowBits < 16 && (t.windowBits += 16),
                    this.err = 0,
                    this.msg = "",
                    this.ended = !1,
                    this.chunks = [],
                    this.strm = new Fe,
                    this.strm.avail_out = 0;
                    var e = Pe.deflateInit2(this.strm, t.level, t.method, t.windowBits, t.memLevel, t.strategy);
                    if (e !== Ve)
                        throw new Error(pt[e]);
                    if (t.header && Pe.deflateSetHeader(this.strm, t.header),
                    t.dictionary) {
                        var n;
                        if (n = "[object ArrayBuffer]" === Be.call(t.dictionary) ? new Uint8Array(t.dictionary) : t.dictionary,
                        (e = Pe.deflateSetDictionary(this.strm, n)) !== Ve)
                            throw new Error(pt[e]);
                        this._dict_set = !0
                    }
                }
                function $e(t) {
                    if ("function" == typeof TextEncoder && TextEncoder.prototype.encode)
                        return (new TextEncoder).encode(t);
                    var e, n, r, i, o, a = t.length, s = 0;
                    for (i = 0; i < a; i++)
                        55296 == (64512 & (n = t.charCodeAt(i))) && i + 1 < a && 56320 == (64512 & (r = t.charCodeAt(i + 1))) && (n = 65536 + (n - 55296 << 10) + (r - 56320),
                        i++),
                        s += n < 128 ? 1 : n < 2048 ? 2 : n < 65536 ? 3 : 4;
                    for (e = new Uint8Array(s),
                    o = 0,
                    i = 0; o < s; i++)
                        55296 == (64512 & (n = t.charCodeAt(i))) && i + 1 < a && 56320 == (64512 & (r = t.charCodeAt(i + 1))) && (n = 65536 + (n - 55296 << 10) + (r - 56320),
                        i++),
                        n < 128 ? e[o++] = n : n < 2048 ? (e[o++] = 192 | n >>> 6,
                        e[o++] = 128 | 63 & n) : n < 65536 ? (e[o++] = 224 | n >>> 12,
                        e[o++] = 128 | n >>> 6 & 63,
                        e[o++] = 128 | 63 & n) : (e[o++] = 240 | n >>> 18,
                        e[o++] = 128 | n >>> 12 & 63,
                        e[o++] = 128 | n >>> 6 & 63,
                        e[o++] = 128 | 63 & n);
                    return e
                }
                return Ke.prototype.push = function(t, e) {
                    var n, r, i = this.strm, o = this.options.chunkSize;
                    if (this.ended)
                        return !1;
                    for (r = e === ~~e ? e : !0 === e ? qe : ze,
                    "[object ArrayBuffer]" === Be.call(t) ? i.input = new Uint8Array(t) : i.input = t,
                    i.next_in = 0,
                    i.avail_in = i.input.length; ; )
                        if (0 === i.avail_out && (i.output = new Uint8Array(o),
                        i.next_out = 0,
                        i.avail_out = o),
                        (r === He || r === je) && i.avail_out <= 6)
                            this.onData(i.output.subarray(0, i.next_out)),
                            i.avail_out = 0;
                        else {
                            if ((n = Pe.deflate(i, r)) === Ze)
                                return i.next_out > 0 && this.onData(i.output.subarray(0, i.next_out)),
                                n = Pe.deflateEnd(this.strm),
                                this.onEnd(n),
                                this.ended = !0,
                                n === Ve;
                            if (0 !== i.avail_out) {
                                if (r > 0 && i.next_out > 0)
                                    this.onData(i.output.subarray(0, i.next_out)),
                                    i.avail_out = 0;
                                else if (0 === i.avail_in)
                                    break
                            } else
                                this.onData(i.output)
                        }
                    return !0
                }
                ,
                Ke.prototype.onData = function(t) {
                    this.chunks.push(t)
                }
                ,
                Ke.prototype.onEnd = function(t) {
                    t === Ve && (this.result = Le(this.chunks)),
                    this.chunks = [],
                    this.err = t,
                    this.msg = this.strm.msg
                }
                ,
                {
                    Deflate: Ke,
                    constants: ht,
                    string2buf: $e
                }
            }()
              , n = e.Deflate
              , r = e.constants
              , i = e.string2buf
              , o = new n
              , a = 0;
            function s(t) {
                var e = i(t);
                return o.push(e, r.Z_SYNC_FLUSH),
                a += e.length,
                e.length
            }
            self.addEventListener("message", t((function(t) {
                var e = t.data;
                switch (e.action) {
                case "init":
                    self.postMessage({
                        type: "initialized"
                    });
                    break;
                case "write":
                    var i = s(e.data);
                    self.postMessage({
                        type: "wrote",
                        id: e.id,
                        compressedBytesCount: o.chunks.reduce((function(t, e) {
                            return t + e.length
                        }
                        ), 0),
                        additionalBytesCount: i
                    });
                    break;
                case "flush":
                    i = e.data ? s(e.data) : 0;
                    o.push("", r.Z_FINISH),
                    self.postMessage({
                        type: "flushed",
                        id: e.id,
                        result: o.result,
                        additionalBytesCount: i,
                        rawBytesCount: a
                    }),
                    o = new n,
                    a = 0
                }
            }
            )))
        }
        ))()
    }
    var _u = {
        status: 0
    };
    function yu(t, e) {
        switch (void 0 === e && (e = mu),
        _u.status) {
        case 0:
            _u = {
                status: 1,
                callbacks: [t]
            },
            function(t) {
                void 0 === t && (t = mu);
                try {
                    var e = t();
                    return Bn(e, "error", bu),
                    Bn(e, "message", (function(t) {
                        var n, r = t.data;
                        "errored" === r.type ? bu(r.error) : "initialized" === r.type && (n = e,
                        1 === _u.status && (_u.callbacks.forEach((function(t) {
                            return t(n)
                        }
                        )),
                        _u = {
                            status: 3,
                            worker: n
                        }))
                    }
                    )),
                    e.postMessage({
                        action: "init"
                    }),
                    e
                } catch (n) {
                    bu(n)
                }
            }(e);
            break;
        case 1:
            _u.callbacks.push(t);
            break;
        case 2:
            t();
            break;
        case 3:
            t(_u.worker)
        }
    }
    function bu(t) {
        var e;
        1 === _u.status ? (ut.error("Session Replay recording failed to start: an error occurred while creating the Worker:", t),
        t instanceof Event || t instanceof Error && (_t(e = t.message, "Content Security Policy") || _t(e, "requires 'TrustedScriptURL'")) ? ut.error("Please make sure CSP is correctly configured https://docs.datadoghq.com/real_user_monitoring/faq/content_security_policy") : $n(t),
        _u.callbacks.forEach((function(t) {
            return t()
        }
        )),
        _u = {
            status: 2
        }) : $n(t)
    }
    function wu() {
        return "function" == typeof Array.from && "function" == typeof CSSSupportsRule && "function" == typeof URL.createObjectURL && "forEach"in NodeList.prototype
    }
    function Eu(t, e, n, r) {
        var i = e.findTrackedSession()
          , o = function(t, e) {
            if (!wu())
                return "browser-not-supported";
            if (!t)
                return "rum-not-tracked";
            if (!t.sessionReplayAllowed)
                return "incorrect-session-plan";
            if (!e)
                return "replay-not-started"
        }(i, r);
        return Za(t, {
            viewContext: n.findView(),
            errorType: o,
            session: i
        })
    }
    var Su = function(t, e) {
        if (void 0 === e && (e = yu),
        Fe() || !wu())
            return {
                start: zt,
                stop: zt,
                getReplayStats: function() {},
                onRumStart: zt,
                isRecording: function() {
                    return !1
                },
                getSessionReplayLink: function() {}
            };
        var n = {
            status: 0
        }
          , r = function() {
            n = {
                status: 1
            }
        }
          , i = function() {
            n = {
                status: 0
            }
        };
        return {
            start: function() {
                return r()
            },
            stop: function() {
                return i()
            },
            getReplayStats: uu,
            getSessionReplayLink: function(t, e, r) {
                return Eu(t, e, r, 0 !== n.status)
            },
            onRumStart: function(o, a, s, u) {
                o.subscribe(7, (function() {
                    2 !== n.status && 3 !== n.status || (i(),
                    n = {
                        status: 1
                    })
                }
                )),
                o.subscribe(8, (function() {
                    1 === n.status && r()
                }
                )),
                r = function() {
                    var r = s.findTrackedSession();
                    r && r.sessionReplayAllowed ? 2 !== n.status && 3 !== n.status && (n = {
                        status: 2
                    },
                    tr("interactive", (function() {
                        2 === n.status && e((function(e) {
                            if (2 === n.status)
                                if (e) {
                                    var r = t(o, a, s, u, e).stop;
                                    n = {
                                        status: 3,
                                        stopRecording: r
                                    }
                                } else
                                    n = {
                                        status: 0
                                    }
                        }
                        ))
                    }
                    ))) : n = {
                        status: 1
                    }
                }
                ,
                i = function() {
                    0 !== n.status && (3 === n.status && n.stopRecording(),
                    n = {
                        status: 0
                    })
                }
                ,
                1 === n.status && r()
            },
            isRecording: function() {
                return 3 === n.status
            }
        }
    }((function(t, e, n, r, i, o) {
        var a = o || Sa(e.sessionReplayEndpointBuilder, hu, (function(e) {
            t.notify(12, {
                error: e
            })
        }
        ))
          , s = vu(t, e.applicationId, n, r, a, i)
          , u = s.addRecord
          , c = s.stop
          , l = ru({
            emit: u,
            configuration: e,
            lifeCycle: t
        })
          , d = l.stop
          , f = l.takeSubsequentFullSnapshot
          , p = l.flushMutations
          , h = t.subscribe(4, (function() {
            p(),
            u({
                timestamp: ge(),
                type: Ga.ViewEnd
            })
        }
        )).unsubscribe
          , v = t.subscribe(2, (function(t) {
            f(t.startClocks.timeStamp)
        }
        )).unsubscribe;
        return {
            stop: function() {
                h(),
                v(),
                d(),
                c()
            }
        }
    }
    ))
      , Tu = function(t, e, n) {
        var r = (void 0 === n ? {} : n).ignoreInitIfSyntheticsWillInjectRum
          , i = void 0 === r || r
          , o = !1
          , a = ne("global context")
          , s = ne("user")
          , u = function() {}
          , c = function() {}
          , l = zt
          , d = function() {}
          , f = new re
          , p = function(t, e) {
            void 0 === e && (e = ge()),
            f.add((function() {
                return p(t, e)
            }
            ))
        }
          , h = function(t, e) {
            void 0 === e && (e = ye()),
            f.add((function() {
                return h(t, e)
            }
            ))
        }
          , v = function(t, n) {
            void 0 === n && (n = Mn(a, s, e)),
            f.add((function() {
                return v(t, n)
            }
            ))
        }
          , m = function(t, n) {
            void 0 === n && (n = Mn(a, s, e)),
            f.add((function() {
                return m(t, n)
            }
            ))
        }
          , g = function(t, e) {
            f.add((function() {
                return g(t, e)
            }
            ))
        };
        function _(n, r, i) {
            var o = t(n, r, e, a, s, i);
            d = function() {
                return e.getSessionReplayLink(r, o.session, o.viewContexts)
            }
            ,
            h = o.startView,
            v = o.addAction,
            m = o.addError,
            p = o.addTiming,
            g = o.addFeatureFlagEvaluation,
            u = o.getInternalContext,
            l = o.stopSession,
            f.drain(),
            e.onRumStart(o.lifeCycle, r, o.session, o.viewContexts)
        }
        var y, b, w = vt((function(t) {
            h("object" == typeof t ? t : {
                name: t
            })
        }
        )), E = (y = {
            init: vt((function(t) {
                if (c = function() {
                    return qt(t)
                }
                ,
                !i || !Ue()) {
                    if (Fe())
                        t = function(t) {
                            return Ct({}, t, {
                                applicationId: "00000000-aaaa-0000-aaaa-000000000000",
                                clientToken: "empty",
                                sessionSampleRate: 100
                            })
                        }(t);
                    else if (!function(t) {
                        return function(t) {
                            if (void 0 === document.cookie || null === document.cookie)
                                return !1;
                            try {
                                var e = "dd_cookie_test_".concat(Ce())
                                  , n = "test";
                                Ie(e, n, le, t);
                                var r = Oe(e) === n;
                                return Ne(e, t),
                                r
                            } catch (i) {
                                return ut.error(i),
                                !1
                            }
                        }(An(t)) ? "file:" !== window.location.protocol || (ut.error("Execution is not allowed in the current context."),
                        !1) : (ut.warn("Cookies are not authorized, we will not send any data."),
                        !1)
                    }(t))
                        return;
                    if (function(t) {
                        return !o || (t.silentMultipleInit || ut.error("DD_RUM is already initialized."),
                        !1)
                    }(t)) {
                        var e = Pn(t);
                        if (e) {
                            if (e.trackViewsManually) {
                                var n = f;
                                f = new re,
                                h = function(n) {
                                    _(t, e, n)
                                }
                                ,
                                n.drain()
                            } else
                                _(t, e);
                            o = !0
                        }
                    }
                }
            }
            )),
            addRumGlobalContext: vt(a.add),
            setGlobalContextProperty: vt(a.setContextProperty),
            removeRumGlobalContext: vt(a.remove),
            removeGlobalContextProperty: vt(a.removeContextProperty),
            getRumGlobalContext: vt(a.get),
            getGlobalContext: vt(a.getContext),
            setRumGlobalContext: vt(a.set),
            setGlobalContext: vt(a.setContext),
            clearGlobalContext: vt(a.clearContext),
            getInternalContext: vt((function(t) {
                return u(t)
            }
            )),
            getInitConfiguration: vt((function() {
                return c()
            }
            )),
            addAction: vt((function(t, e) {
                v({
                    name: Rt(dt.SANITIZE_INPUTS) ? $t(t) : t,
                    context: Rt(dt.SANITIZE_INPUTS) ? $t(e) : qt(e),
                    startClocks: ye(),
                    type: "custom"
                })
            }
            )),
            addError: function(t, e) {
                var n = tn();
                mt((function() {
                    m({
                        error: t,
                        handlingStack: n,
                        context: Rt(dt.SANITIZE_INPUTS) ? $t(e) : qt(e),
                        startClocks: ye()
                    })
                }
                ))
            },
            addTiming: vt((function(t, e) {
                p(Rt(dt.SANITIZE_INPUTS) ? $t(t) : t, e)
            }
            )),
            setUser: vt((function(t) {
                (function(t) {
                    var e = "object" === Ht(t);
                    return e || ut.error("Unsupported user:", t),
                    e
                }
                )(t) && s.setContext(nn(t))
            }
            )),
            getUser: vt(s.getContext),
            setUserProperty: vt((function(t, e) {
                var n, r = nn((n = {},
                n[t] = e,
                n))[t];
                s.setContextProperty(t, r)
            }
            )),
            removeUserProperty: vt(s.removeContextProperty),
            removeUser: vt(s.clearContext),
            clearUser: vt(s.clearContext),
            startView: w,
            stopSession: vt((function() {
                l()
            }
            )),
            startSessionReplayRecording: vt(e.start),
            stopSessionReplayRecording: vt(e.stop),
            addFeatureFlagEvaluation: vt((function(t, e) {
                g(Rt(dt.SANITIZE_INPUTS) ? $t(t) : t, Rt(dt.SANITIZE_INPUTS) ? $t(e) : e)
            }
            )),
            getSessionReplayLink: vt((function() {
                return d()
            }
            ))
        },
        b = Ct({
            version: "4.40.0",
            onReady: function(t) {
                t()
            }
        }, y),
        Object.defineProperty(b, "_setDebug", {
            get: function() {
                return ht
            },
            enumerable: !1
        }),
        b);
        return E
    }((function(t, e, n, r, i, o) {
        var a = new Wr;
        a.subscribe(11, (function(t) {
            return Un("rum", t)
        }
        ));
        var s = function(t) {
            var e = Yn("browser-rum-sdk", t);
            if (Fe()) {
                var n = De();
                e.observable.subscribe((function(t) {
                    return n.send("internal_telemetry", t)
                }
                ))
            }
            return e
        }(e);
        s.setContextProvider((function() {
            var t, n;
            return {
                application: {
                    id: e.applicationId
                },
                session: {
                    id: null === (t = d.findTrackedSession()) || void 0 === t ? void 0 : t.id
                },
                view: {
                    id: null === (n = g.findView()) || void 0 === n ? void 0 : n.id
                },
                action: {
                    id: b.findActionId()
                }
            }
        }
        ));
        var u, c = function(t) {
            a.notify(12, {
                error: t
            })
        }, l = function(t, e) {
            if (void 0 === e && (e = Pt),
            !Rt(dt.FEATURE_FLAGS))
                return {
                    findFeatureFlagEvaluations: function() {},
                    getFeatureFlagBytesCount: function() {
                        return 0
                    },
                    addFeatureFlagEvaluation: zt
                };
            var n = new Jr(xa)
              , r = 0
              , i = !1;
            t.subscribe(4, (function(t) {
                var e = t.endClocks;
                n.closeActive(e.relative)
            }
            )),
            t.subscribe(2, (function(t) {
                var e = t.startClocks;
                n.add({}, e.relative),
                r = 0
            }
            ));
            var o = Bt((function(t) {
                r = e(Zt(t)),
                i || (i = te(r, "feature flag evaluation"))
            }
            ), Ia).throttled;
            return {
                findFeatureFlagEvaluations: function(t) {
                    return n.find(t)
                },
                getFeatureFlagBytesCount: function() {
                    return n.find() ? r : 0
                },
                addFeatureFlagEvaluation: function(t, e) {
                    var r = n.find();
                    r && (r[t] = e,
                    o(r))
                }
            }
        }(a), d = Fe() ? function() {
            var t = {
                id: "00000000-aaaa-0000-aaaa-000000000000",
                plan: 1,
                sessionReplayAllowed: !1,
                longTaskAllowed: !0,
                resourceAllowed: !0
            };
            return {
                findTrackedSession: function() {
                    return t
                },
                expire: zt,
                expireObservable: new Dn
            }
        }() : ca(e, a);
        if (Fe())
            !function(t) {
                var e = De();
                t.subscribe(11, (function(t) {
                    e.send("rum", t)
                }
                ))
            }(a);
        else {
            var f = (u = new Dn((function() {
                var t = Rt(dt.PAGEHIDE)
                  , e = zn(window, ["visibilitychange", "freeze", "pagehide"], (function(e) {
                    "pagehide" === e.type && t ? u.notify({
                        reason: Hn.PAGEHIDE
                    }) : "visibilitychange" === e.type && "hidden" === document.visibilityState ? u.notify({
                        reason: Hn.HIDDEN
                    }) : "freeze" === e.type && u.notify({
                        reason: Hn.FROZEN
                    })
                }
                ), {
                    capture: !0
                }).stop
                  , n = zt;
                return t || (n = Bn(window, "beforeunload", (function() {
                    u.notify({
                        reason: Hn.UNLOADING
                    })
                }
                )).stop),
                function() {
                    e(),
                    n()
                }
            }
            )),
            u);
            f.subscribe((function(t) {
                a.notify(9, t)
            }
            ));
            var p = Aa(e, a, s.observable, c, f, d.expireObservable);
            !function(t, e, n, r, i, o, a) {
                e.enabled && ie(t.customerDataTelemetrySampleRate) && (Fa(),
                Ba(),
                n.subscribe(11, (function(t) {
                    Pa = !0,
                    Ua(Na.globalContextBytes, an(r.get()) ? 0 : r.getBytesCount()),
                    Ua(Na.userContextBytes, an(i.get()) ? 0 : i.getBytesCount());
                    var e = o.findFeatureFlagEvaluations()
                      , n = _t(["view", "error"], t.type) && e && !an(e);
                    Ua(Na.featureFlagBytes, n ? o.getFeatureFlagBytesCount() : 0)
                }
                )),
                a.subscribe((function(t) {
                    var e = t.bytesCount
                      , n = t.messagesCount;
                    Pa && (Oa.batchCount += 1,
                    Ua(Oa.batchBytesCount, e),
                    Ua(Oa.batchMessagesCount, n),
                    Da(Oa.globalContextBytes, Na.globalContextBytes),
                    Da(Oa.userContextBytes, Na.userContextBytes),
                    Da(Oa.featureFlagBytes, Na.featureFlagBytes),
                    Ba())
                }
                )),
                Dt(Ma, La))
            }(e, s, a, r, i, l, p.flushObservable)
        }
        var h = function() {
            var t = Qn()
              , e = new Dn((function() {
                if (t) {
                    var n = new t(vt((function() {
                        return e.notify()
                    }
                    )));
                    return n.observe(document, {
                        attributes: !0,
                        characterData: !0,
                        childList: !0,
                        subtree: !0
                    }),
                    function() {
                        return n.disconnect()
                    }
                }
            }
            ));
            return e
        }()
          , v = Ra(location)
          , m = function(t, e, n, r, i, o, a, s) {
            var u = function(t) {
                var e = new Jr(Qr);
                return t.subscribe(2, (function(t) {
                    e.add(function(t) {
                        return {
                            service: t.service,
                            version: t.version,
                            id: t.id,
                            name: t.name,
                            startClocks: t.startClocks
                        }
                    }(t), t.startClocks.relative)
                }
                )),
                t.subscribe(4, (function(t) {
                    var n = t.endClocks;
                    e.closeActive(n.relative)
                }
                )),
                t.subscribe(8, (function() {
                    e.reset()
                }
                )),
                {
                    findView: function(t) {
                        return e.find(t)
                    },
                    stop: function() {
                        e.stop()
                    }
                }
            }(t)
              , c = function(t, e, n) {
                var r, i = new Jr(ka);
                t.subscribe(4, (function(t) {
                    var e = t.endClocks;
                    i.closeActive(e.relative)
                }
                )),
                t.subscribe(2, (function(t) {
                    var e = t.startClocks
                      , o = n.href;
                    i.add(a({
                        url: o,
                        referrer: r || document.referrer
                    }), e.relative),
                    r = o
                }
                ));
                var o = e.subscribe((function(t) {
                    var e = t.newLocation
                      , n = i.find();
                    if (n) {
                        var r = _e();
                        i.closeActive(r),
                        i.add(a({
                            url: e.href,
                            referrer: n.referrer
                        }), r)
                    }
                }
                ));
                function a(t) {
                    return {
                        url: t.url,
                        referrer: t.referrer
                    }
                }
                return {
                    findUrl: function(t) {
                        return i.find(t)
                    },
                    stop: function() {
                        o.unsubscribe(),
                        i.stop()
                    }
                }
            }(t, i, n)
              , l = Hr()
              , d = function(t, e, n, r) {
                t.subscribe(1, (function(e) {
                    return t.notify(10, ro(e, r))
                }
                ));
                var i = {
                    findActionId: zt
                };
                return n.trackUserInteractions && (i = eo(t, e, n).actionContexts),
                {
                    addAction: function(e, n) {
                        t.notify(10, Ct({
                            savedCommonContext: n
                        }, ro(e, r)))
                    },
                    actionContexts: i
                }
            }(t, o, e, l)
              , f = d.addAction
              , p = d.actionContexts;
            return Dr(e, t, r, u, c, p, a, s),
            {
                viewContexts: u,
                foregroundContexts: l,
                urlContexts: c,
                addAction: f,
                actionContexts: p,
                stop: function() {
                    u.stop(),
                    l.stop()
                }
            }
        }(a, e, location, d, v, h, (function() {
            return Mn(r, i, n)
        }
        ), c)
          , g = m.viewContexts
          , _ = m.foregroundContexts
          , y = m.urlContexts
          , b = m.actionContexts
          , w = m.addAction;
        !function(t) {
            Wn.telemetryConfigurationEnabled && Xn({
                type: Vn.configuration,
                configuration: t
            })
        }(function(t) {
            var e, n, r = function(t) {
                var e, n, r = null !== (e = t.proxy) && void 0 !== e ? e : t.proxyUrl;
                return {
                    session_sample_rate: null !== (n = t.sessionSampleRate) && void 0 !== n ? n : t.sampleRate,
                    telemetry_sample_rate: t.telemetrySampleRate,
                    telemetry_configuration_sample_rate: t.telemetryConfigurationSampleRate,
                    use_before_send: !!t.beforeSend,
                    use_cross_site_session_cookie: t.useCrossSiteSessionCookie,
                    use_secure_session_cookie: t.useSecureSessionCookie,
                    use_proxy: void 0 !== r ? !!r : void 0,
                    silent_multiple_init: t.silentMultipleInit,
                    track_session_across_subdomains: t.trackSessionAcrossSubdomains,
                    track_resources: t.trackResources,
                    track_long_task: t.trackLongTasks
                }
            }(t);
            return Ct({
                premium_sample_rate: t.premiumSampleRate,
                replay_sample_rate: t.replaySampleRate,
                session_replay_sample_rate: t.sessionReplaySampleRate,
                trace_sample_rate: null !== (e = t.traceSampleRate) && void 0 !== e ? e : t.tracingSampleRate,
                action_name_attribute: t.actionNameAttribute,
                use_allowed_tracing_origins: Array.isArray(t.allowedTracingOrigins) && t.allowedTracingOrigins.length > 0,
                use_allowed_tracing_urls: Array.isArray(t.allowedTracingUrls) && t.allowedTracingUrls.length > 0,
                selected_tracing_propagators: Ln(t),
                default_privacy_level: t.defaultPrivacyLevel,
                use_excluded_activity_urls: Array.isArray(t.allowedTracingOrigins) && t.allowedTracingOrigins.length > 0,
                track_frustrations: t.trackFrustrations,
                track_views_manually: t.trackViewsManually,
                track_user_interactions: null !== (n = t.trackUserInteractions) && void 0 !== n ? n : t.trackInteractions
            }, r)
        }(t)),
        function(t, e) {
            t.subscribe(0, (function(n) {
                for (var r = 0, i = n; r < i.length; r++) {
                    var o = i[r];
                    if ("longtask" !== o.entryType)
                        break;
                    var a = e.findTrackedSession(o.startTime);
                    if (!a || !a.longTaskAllowed)
                        break;
                    var s = pe(o.startTime)
                      , u = {
                        date: s.timeStamp,
                        long_task: {
                            id: Ce(),
                            duration: ve(o.duration)
                        },
                        type: "long_task",
                        _dd: {
                            discarded: !1
                        }
                    };
                    t.notify(10, {
                        rawRumEvent: u,
                        startTime: s.relative,
                        domainContext: {
                            performanceEntry: o.toJSON()
                        }
                    })
                }
            }
            ))
        }(a, d);
        var E = (Va(qa()),
        {
            findAll: function(t, e) {
                for (var n = [], r = Ee(t, e), i = ja.length - 1; i >= 0; i--) {
                    var o = ja[i].startTime;
                    if (!(o >= r) && (n.unshift(ja[i]),
                    o < t))
                        break
                }
                return n.length ? n : void 0
            },
            stop: zn(window, ["pageshow", "focus", "blur", "visibilitychange", "resume", "freeze", "pagehide"], (function(t) {
                t.isTrusted && ("freeze" === t.type ? Va("frozen") : "pagehide" === t.type ? Va(t.persisted ? "frozen" : "terminated") : Va(qa()))
            }
            ), {
                capture: !0
            }).stop
        });
        _o(a, e, d, E);
        var S = Lo(a, e, location, h, v, _, l, n, o)
          , T = S.addTiming
          , C = S.startView
          , A = vo(a, _, l).addError;
        pi(a, e, d),
        Er(a, e);
        var k = function(t, e, n, r, i) {
            return {
                get: function(o) {
                    var a = n.findView(o)
                      , s = i.findUrl(o)
                      , u = e.findTrackedSession(o);
                    if (u && a && s) {
                        var c = r.findActionId(o);
                        return {
                            application_id: t,
                            session_id: u.id,
                            user_action: c ? {
                                id: c
                            } : void 0,
                            view: {
                                id: a.id,
                                name: a.name,
                                referrer: s.referrer,
                                url: s.url
                            }
                        }
                    }
                }
            }
        }(e.applicationId, d, g, b, y);
        return {
            addAction: w,
            addError: A,
            addTiming: T,
            addFeatureFlagEvaluation: l.addFeatureFlagEvaluation,
            startView: C,
            lifeCycle: a,
            viewContexts: g,
            session: d,
            stopSession: function() {
                return d.expire()
            },
            getInternalContext: k.get
        }
    }
    ), Su);
    !function(t, e, n) {
        var r = t[e];
        t[e] = n,
        r && r.q && r.q.forEach((function(t) {
            return ct(t, "onReady callback threw an error:")()
        }
        ))
    }(At(), "DD_RUM", Tu);
    const Cu = "desktop-web-renderer";
    class Renderer extends HTMLElement {
        rendererId = ( () => Date.now().toString(36) + Math.random().toString(36).substring(2, 5))();
        renderCount = 0;
        parentAppTimeout = 15e3;
        constructor() {
            super(),
            i.init({
                serviceName: Cu,
                envName: m.name,
                sampleRate: m.datadogSampleRate,
                version: "0.6.2",
                applicationId: m.datadogAppId,
                clientToken: m.datadogClientToken,
                useCrossSiteSessionCookie: !0
            }),
            this.wallpaperApp = new ChildFrame(this.onWallpaperAppReady.bind(this)),
            window.setTimeout(this.onParentAppTimeout.bind(this), this.parentAppTimeout)
        }
        onWallpaperAppReady() {
            var t;
            i.addMonitorTiming(v.PARENT_APP_READY),
            A.dispatch((t = !0,
            {
                type: S.UPDATE_PARENT_CONNECTED,
                connected: t
            })),
            O.init({
                api: this.wallpaperApp,
                onStateUpdate: this.handleWallpaperAppStateUpdate.bind(this)
            }),
            document.body.addEventListener("dragover", N)
        }
        handleWallpaperAppStateUpdate(t) {
            var e;
            A.dispatch((e = t,
            {
                type: S.UPDATE_STATE_FROM_PARENT,
                stateFromParent: e
            })),
            this.connectedCallback()
        }
        connectedCallback() {
            const {creative: t} = A.getState();
            t && (this.creativeEl || (this.render(t),
            A.subscribe(( () => this.stateChanged(A.getState())))))
        }
        fireImpressions({creative: t, rotationLap: e, position: n, rendererId: r, renderCount: o}) {
            if (!t.flightId)
                return;
            O.fireImpressions(),
            P.fireSnowplowImpression();
            const a = {
                ...t,
                rotationLap: e,
                position: n,
                rendererId: r,
                visibility: document.visibilityState,
                renderCount: o
            };
            i.addMonitorAction({
                name: "impression",
                context: a
            })
        }
        render(t) {
            const {sourceUrl: e} = t
              , n = function({filename: t}) {
                const e = t.split(".").pop()?.split("?")[0]?.toLowerCase();
                switch (e) {
                case "html":
                    return s.Html;
                case "jpg":
                case "jpeg":
                case "png":
                case "gif":
                case "webp":
                    return s.Image;
                default:
                    return
                }
            }({
                filename: e
            });
            if (n === s.Html) {
                this.creativeEl = document.createElement("iframe"),
                this.creativeEl.onload = this.onIframeLoad.bind(this),
                this.creativeEl.onerror = this.onIframeError.bind(this),
                this.creativeEl.allow = "autoplay; camera; microphone",
                this.creativeEl.dataset.cy = "studio-wallpaper-iframe";
                const t = new URL(window.location.href)
                  , n = e.match(/\?/) ? "&" : "?";
                this.creativeEl.src = `${e}${n}_origin=${t.origin}&_placement=${Cu}`,
                this.appendChild(this.creativeEl)
            } else {
                const {error: e, context: r} = function({assetFormatType: t, creative: e}) {
                    return {
                        error: new Error("Creative asset format is not supported"),
                        context: {
                            assetFormatType: t,
                            creative: e
                        }
                    }
                }({
                    assetFormatType: n,
                    creative: t
                });
                Tu.addError(e, r)
            }
        }
        onIframeLoad() {
            const {creative: t, rotationLap: e, position: n} = A.getState();
            this.fireImpressions({
                creative: t,
                rotationLap: e,
                position: n,
                rendererId: this.rendererId
            }),
            at.init(this.creativeEl, t),
            $.init(this.creativeEl)
        }
        onIframeError() {
            i.trackError(new Error(h.IFRAME_FAILED_TO_LOAD_ERROR))
        }
        stateChanged(t) {
            $.send(Y.OnStateUpdate, {
                ...t
            })
        }
        onParentAppTimeout() {
            const {parentConnected: t} = A.getState();
            t || i.trackError(new Error(h.RENDERER_FAILED_TO_CONNECT_PARENT), {
                timeout: this.parentAppTimeout
            })
        }
    }
    customElements.define(Cu, Renderer)
}
)();
